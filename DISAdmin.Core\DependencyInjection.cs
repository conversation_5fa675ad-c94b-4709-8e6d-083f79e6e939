using DISAdmin.Core.Configuration;
using DISAdmin.Core.Data;
using DISAdmin.Core.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DISAdmin.Core;

public static class DependencyInjection
{
    public static IServiceCollection AddCoreServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Registrace DbContext
        services.AddDbContext<DISAdminDbContext>(options =>
            options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly("DISAdmin.Migrations")));

        // Registrace kešovacích služeb
        services.AddMemoryCache();
        services.AddScoped<CachingService>();

        // Registrace služeb
        services.AddScoped<UserService>();
        services.AddScoped<CustomerService>();
        services.AddScoped<ContactService>();
        services.AddScoped<DISVersionService>();
        services.AddScoped<DISInstanceService>();
        services.AddScoped<LoggingService>();
        services.AddScoped<SecurityTokenService>();
        services.AddScoped<CertificateService>();
        services.AddScoped<TwoFactorAuthService>();
        services.AddScoped<MonitoringService>();
        services.AddScoped<DashboardConfigService>();
        services.AddScoped<FilterService>();
        services.AddScoped<ConfigurationService>();
        services.AddScoped<PerformanceService>();
        services.AddScoped<SecurityEventFilterService>();

        // Registrace služeb pro správu nastavení aplikace
        services.AddScoped<EncryptionService>();
        services.AddScoped<IAppSettingsService, AppSettingsService>();

        // Registrace konfigurace DISApi
        services.Configure<DISApiSettings>(configuration.GetSection("DISApiSettings"));

        // Registrace služby pro kontrolu stavu DISApi
        services.AddHttpClient("DISApiHealth", (serviceProvider, client) =>
        {
            var settings = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<DISApiSettings>>().Value;
            client.Timeout = TimeSpan.FromSeconds(settings.TimeoutSeconds);
        })
        .ConfigurePrimaryHttpMessageHandler((serviceProvider) =>
        {
            var settings = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<DISApiSettings>>().Value;
            var handler = new HttpClientHandler();

            if (settings.IgnoreCertificateErrors)
            {
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
            }

            return handler;
        });

        services.AddScoped<IDISApiHealthService, DISApiHealthService>();

        return services;
    }
}

