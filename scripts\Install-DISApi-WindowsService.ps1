# Skript pro instalaci DISAdmin.DISApi jako Windows Service
# Tento skript musi byt spusten s administratorskymi opravnenimi

param(
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "DISAdmin.DISApi",
    
    [Parameter(Mandatory=$false)]
    [string]$DisplayName = "DISAdmin DIS API Service",
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "DISAdmin DIS API Service pro komunikaci s DIS aplikacemi",
    
    [Parameter(Mandatory=$false)]
    [string]$BinaryPath = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceAccount = "LocalSystem",
    
    [Parameter(Mandatory=$false)]
    [switch]$Uninstall = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Start = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false
)

# Kontrola administratorskych opravneni
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Error "Tento skript musi byt spusten s administratorskymi opravnenimi!"
    exit 1
}

# Funkce pro vypis s casovym razitkem
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Automaticke urceni cesty k binarkam
if ([string]::IsNullOrEmpty($BinaryPath)) {
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $projectDir = Split-Path -Parent $scriptDir
    $binaryPath = Join-Path $projectDir "DISAdmin.DISApi.exe"
    
    if (-not (Test-Path $binaryPath)) {
        Write-Log "Binarky DISAdmin.DISApi.exe nebyly nalezeny. Zkompilujte projekt nejprve." "ERROR"
        Write-Log "Hledano v:" "ERROR"
        Write-Log "  - $(Join-Path $projectDir "DISAdmin.DISApi.exe")" "ERROR"
        exit 1
    }
    
    $BinaryPath = $binaryPath
}

Write-Log "=== Instalace DISAdmin.DISApi Windows Service ===" "INFO"
Write-Log "Service Name: $ServiceName" "INFO"
Write-Log "Display Name: $DisplayName" "INFO"
Write-Log "Binary Path: $BinaryPath" "INFO"
Write-Log "Service Account: $ServiceAccount" "INFO"
Write-Log ""

# Funkce pro odinstalaci sluzby
function Uninstall-DISApiService {
    Write-Log "=== Odinstalace sluzby ===" "INFO"
    
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Log "Sluzba '$ServiceName' nalezena, zastavuji..." "INFO"
        
        if ($service.Status -eq 'Running') {
            Stop-Service -Name $ServiceName -Force
            Write-Log "Sluzba zastavena" "SUCCESS"
        }
        
        # Pockame na zastaveni
        $timeout = 30
        $elapsed = 0
        while ((Get-Service -Name $ServiceName).Status -ne 'Stopped' -and $elapsed -lt $timeout) {
            Start-Sleep -Seconds 1
            $elapsed++
        }
        
        if ((Get-Service -Name $ServiceName).Status -ne 'Stopped') {
            Write-Log "Sluzba se nepodařilo zastavit v casovem limitu" "WARN"
        }
        
        # Odstraneni sluzby
        sc.exe delete $ServiceName
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Sluzba '$ServiceName' byla uspesne odstranena" "SUCCESS"
        } else {
            Write-Log "Chyba pri odstranovani sluzby: $LASTEXITCODE" "ERROR"
            return $false
        }
    } else {
        Write-Log "Sluzba '$ServiceName' neexistuje" "WARN"
    }
    
    return $true
}

# Funkce pro instalaci sluzby
function Install-DISApiService {
    Write-Log "=== Instalace sluzby ===" "INFO"
    
    # Kontrola existence binárky
    if (-not (Test-Path $BinaryPath)) {
        Write-Log "Binarka nenalezena: $BinaryPath" "ERROR"
        return $false
    }
    
    # Kontrola existence sluzby
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        if ($Force) {
            Write-Log "Sluzba jiz existuje, odstranuji (Force = true)..." "WARN"
            if (-not (Uninstall-DISApiService)) {
                return $false
            }
            Start-Sleep -Seconds 2
        } else {
            Write-Log "Sluzba '$ServiceName' jiz existuje. Pouzijte -Force pro prepis nebo -Uninstall pro odstraneni." "ERROR"
            return $false
        }
    }
    
    # Vytvoreni sluzby
    Write-Log "Vytvarim sluzbu..." "INFO"
    $result = sc.exe create $ServiceName binPath= "`"$BinaryPath --service`"" DisplayName= "$DisplayName" start= auto obj= $ServiceAccount
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Sluzba '$ServiceName' byla uspesne vytvorena" "SUCCESS"
    } else {
        Write-Log "Chyba pri vytvareni sluzby: $LASTEXITCODE" "ERROR"
        Write-Log "Output: $result" "ERROR"
        return $false
    }
    
    # Nastaveni popisu sluzby
    sc.exe description $ServiceName "$Description"
    
    # Nastaveni recovery options
    sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/10000/restart/30000
    
    Write-Log "Sluzba byla uspesne nakonfigurovana" "SUCCESS"
    return $true
}

# Funkce pro spusteni sluzby
function Start-DISApiService {
    Write-Log "=== Spousteni sluzby ===" "INFO"
    
    try {
        Start-Service -Name $ServiceName
        Write-Log "Sluzba '$ServiceName' byla spustena" "SUCCESS"
        
        # Kontrola stavu
        Start-Sleep -Seconds 3
        $service = Get-Service -Name $ServiceName
        Write-Log "Stav sluzby: $($service.Status)" "INFO"
        
        return $true
    } catch {
        Write-Log "Chyba pri spousteni sluzby: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Funkce pro zobrazeni stavu sluzby
function Show-ServiceStatus {
    Write-Log "=== Stav sluzby ===" "INFO"
    
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Log "Nazev: $($service.Name)" "INFO"
        Write-Log "Zobrazovany nazev: $($service.DisplayName)" "INFO"
        Write-Log "Stav: $($service.Status)" "INFO"
        Write-Log "Typ spusteni: $($service.StartType)" "INFO"
        
        # Zobrazeni procesu pokud bezi
        if ($service.Status -eq 'Running') {
            try {
                $process = Get-Process -Id $service.ServiceHandle -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Log "PID: $($process.Id)" "INFO"
                    Write-Log "Pouzita pamet: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" "INFO"
                }
            } catch {
                # Ignorovat chyby pri ziskavani informaci o procesu
            }
        }
    } else {
        Write-Log "Sluzba '$ServiceName' neexistuje" "WARN"
    }
}

# Hlavni logika
try {
    if ($Uninstall) {
        if (Uninstall-DISApiService) {
            Write-Log "Odinstalace dokoncena uspesne" "SUCCESS"
        } else {
            Write-Log "Odinstalace selhala" "ERROR"
            exit 1
        }
    } else {
        if (Install-DISApiService) {
            if ($Start) {
                if (Start-DISApiService) {
                    Write-Log "Instalace a spusteni dokonceno uspesne" "SUCCESS"
                } else {
                    Write-Log "Instalace uspesna, ale spusteni selhalo" "WARN"
                }
            } else {
                Write-Log "Instalace dokoncena uspesne (sluzba neni spustena)" "SUCCESS"
            }
        } else {
            Write-Log "Instalace selhala" "ERROR"
            exit 1
        }
    }
    
    Write-Log "" "INFO"
    Show-ServiceStatus
    
    Write-Log "" "INFO"
    Write-Log "Uzitecne prikazy:" "INFO"
    Write-Log "  Spustit sluzbu:    Start-Service -Name '$ServiceName'" "INFO"
    Write-Log "  Zastavit sluzbu:   Stop-Service -Name '$ServiceName'" "INFO"
    Write-Log "  Stav sluzby:       Get-Service -Name '$ServiceName'" "INFO"
    Write-Log "  Logy sluzby:       Get-EventLog -LogName Application -Source '$ServiceName' -Newest 10" "INFO"
    
} catch {
    Write-Log "Neocekavana chyba: $($_.Exception.Message)" "ERROR"
    exit 1
}
