<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Dashboard</h2>
    <div class="d-flex gap-2 flex-wrap">
      <div class="btn-group" role="group">
        <button type="button" class="btn" [class.btn-primary]="selectedDays === 1" [class.btn-outline-primary]="selectedDays !== 1" (click)="onDaysChange(1)">1 den</button>
        <button type="button" class="btn" [class.btn-primary]="selectedDays === 7" [class.btn-outline-primary]="selectedDays !== 7" (click)="onDaysChange(7)">7 dní</button>
        <button type="button" class="btn" [class.btn-primary]="selectedDays === 30" [class.btn-outline-primary]="selectedDays !== 30" (click)="onDaysChange(30)">30 dní</button>
        <button type="button" class="btn" [class.btn-primary]="selectedDays === 90" [class.btn-outline-primary]="selectedDays !== 90" (click)="onDaysChange(90)">90 dní</button>
      </div>
      <div class="d-flex gap-2 flex-wrap">
        <button class="btn btn-outline-primary" (click)="refreshCharts()">
          <i class="bi bi-arrow-clockwise me-1"></i> <span class="d-none d-sm-inline">Aktualizovat</span>
        </button>
        <button class="btn" [class.btn-outline-warning]="!editMode" [class.btn-warning]="editMode" (click)="toggleEditMode()">
          <i class="bi" [ngClass]="{'bi-pencil-square': !editMode, 'bi-check-lg': editMode}"></i>
          <span class="d-none d-sm-inline">{{ editMode ? 'Dokončit úpravy' : 'Upravit dashboard' }}</span>
        </button>
        <button *ngIf="editMode" class="btn btn-outline-danger" (click)="resetDashboardConfig()">
          <i class="bi bi-arrow-counterclockwise me-1"></i> <span class="d-none d-sm-inline">Resetovat</span>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="editMode" class="alert alert-info mb-4">
    <i class="bi bi-info-circle-fill me-2"></i>
    Nyní můžete přetáhnout widgety a změnit jejich pořadí. Kliknutím na tlačítko "Dokončit úpravy" uložíte změny.
  </div>

  <div *ngIf="loading" class="d-flex justify-content-center my-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Načítání...</span>
    </div>
  </div>

  <div *ngIf="error" class="alert alert-danger">
    {{ error }}
  </div>



  <!-- Grafy -->
  <div cdkDropList class="row" (cdkDropListDropped)="onDrop($event)" [cdkDropListDisabled]="!editMode">
    <!-- Graf API volání -->
    <div class="col-lg-6 mb-4" *ngIf="isWidgetVisible('api-calls')" cdkDrag [cdkDragDisabled]="!editMode">
      <div class="card h-100" [class.draggable-card]="editMode">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-bs-toggle="popover" data-help-type="api-calls" style="cursor: help;">API volání v čase</h5>
          <div class="d-flex align-items-center">
            <button class="btn btn-sm btn-outline-info me-2" (click)="openFullscreenChart(apiCallsChart, 'API volání v čase')" title="Zobrazit graf na celou obrazovku">
              <i class="bi bi-arrows-fullscreen"></i>
            </button>
            <div *ngIf="editMode" class="widget-controls">
              <button class="btn btn-sm btn-outline-secondary" (click)="toggleWidgetVisibility('api-calls')">
                <i class="bi bi-eye-slash"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-container" style="position: relative; height: 300px;">
            <canvas #apiCallsChart></canvas>
          </div>
        </div>
        <div *ngIf="editMode" class="drag-handle" cdkDragHandle>
          <i class="bi bi-grip-horizontal"></i>
        </div>
      </div>
    </div>

    <!-- Graf výkonu API -->
    <div class="col-lg-6 mb-4" *ngIf="isWidgetVisible('api-performance')" cdkDrag [cdkDragDisabled]="!editMode">
      <div class="card h-100" [class.draggable-card]="editMode">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-bs-toggle="popover" data-help-type="api-performance" style="cursor: help;">Odezva DIS metod</h5>
          <div class="d-flex align-items-center">
            <button class="btn btn-sm btn-outline-info me-2" (click)="openFullscreenChart(apiPerformanceChart, 'Odezva DIS metod')" title="Zobrazit graf na celou obrazovku">
              <i class="bi bi-arrows-fullscreen"></i>
            </button>
            <div *ngIf="editMode" class="widget-controls">
              <button class="btn btn-sm btn-outline-secondary" (click)="toggleWidgetVisibility('api-performance')">
                <i class="bi bi-eye-slash"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-container" style="position: relative; height: 300px;">
            <canvas #apiPerformanceChart></canvas>
          </div>
        </div>
        <div *ngIf="editMode" class="drag-handle" cdkDragHandle>
          <i class="bi bi-grip-horizontal"></i>
        </div>
      </div>
    </div>

    <!-- Graf využití instancí -->
    <div class="col-lg-6 mb-4" *ngIf="isWidgetVisible('instances-usage')" cdkDrag [cdkDragDisabled]="!editMode">
      <div class="card h-100" [class.draggable-card]="editMode">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-bs-toggle="popover" data-help-type="instances-usage" style="cursor: help;">Využití instancí</h5>
          <div class="d-flex align-items-center">
            <button class="btn btn-sm btn-outline-info me-2" (click)="openFullscreenChart(instancesUsageChart, 'Využití instancí')" title="Zobrazit graf na celou obrazovku">
              <i class="bi bi-arrows-fullscreen"></i>
            </button>
            <div *ngIf="editMode" class="widget-controls">
              <button class="btn btn-sm btn-outline-secondary" (click)="toggleWidgetVisibility('instances-usage')">
                <i class="bi bi-eye-slash"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-container" style="position: relative; height: 300px;">
            <canvas #instancesUsageChart></canvas>
          </div>
        </div>
        <div *ngIf="editMode" class="drag-handle" cdkDragHandle>
          <i class="bi bi-grip-horizontal"></i>
        </div>
      </div>
    </div>

    <!-- Graf bezpečnostních událostí -->
    <div class="col-lg-6 mb-4" *ngIf="isAdmin && isWidgetVisible('security-events')" cdkDrag [cdkDragDisabled]="!editMode">
      <div class="card h-100" [class.draggable-card]="editMode">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-bs-toggle="popover" data-help-type="security-events" style="cursor: help;">Bezpečnostní události</h5>
          <div class="d-flex align-items-center">
            <button class="btn btn-sm btn-outline-info me-2" (click)="openFullscreenChart(securityEventsChart, 'Bezpečnostní události')" title="Zobrazit graf na celou obrazovku">
              <i class="bi bi-arrows-fullscreen"></i>
            </button>
            <div *ngIf="editMode" class="widget-controls">
              <button class="btn btn-sm btn-outline-secondary" (click)="toggleWidgetVisibility('security-events')">
                <i class="bi bi-eye-slash"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="chart-container" style="position: relative; height: 300px;">
            <canvas #securityEventsChart></canvas>
          </div>
        </div>
        <div *ngIf="editMode" class="drag-handle" cdkDragHandle>
          <i class="bi bi-grip-horizontal"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Systémové informace -->
  <div class="row" *ngIf="isAdmin && isWidgetVisible('system-info')" cdkDrag [cdkDragDisabled]="!editMode">
    <div class="col-12 mb-4">
      <div class="card" [class.draggable-card]="editMode">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-bs-toggle="popover" data-help-type="system-info" style="cursor: help;">Systémové informace</h5>
          <div *ngIf="editMode" class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary" (click)="toggleWidgetVisibility('system-info')">
              <i class="bi bi-eye-slash"></i>
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-6 col-md-3 mb-3">
              <div class="card bg-primary text-white">
                <div class="card-body text-center">
                  <h3 class="display-4">{{ alerts.length }}</h3>
                  <p class="mb-0">Aktivních upozornění</p>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <div class="card bg-success text-white">
                <div class="card-body text-center">
                  <h3 class="display-4">{{ systemStatistics.ApiAvailability || 100 }}%</h3>
                  <p class="mb-0">Dostupnost API</p>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <div class="card bg-info text-white">
                <div class="card-body text-center">
                  <h3 class="display-4">{{ systemStatistics.AvgApiResponseTime || 0 }}ms</h3>
                  <p class="mb-0">Průměrná odezva DIS metod</p>
                </div>
              </div>
            </div>
            <div class="col-6 col-md-3 mb-3">
              <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                  <h3 class="display-4">{{ systemStatistics.ExpiringCertificatesCount || 0 }}</h3>
                  <p class="mb-0">Expirující certifikáty</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="editMode" class="drag-handle" cdkDragHandle>
          <i class="bi bi-grip-horizontal"></i>
        </div>
      </div>
    </div>
  </div>
</div>
