{"ast": null, "code": "import { Chart, registerables } from 'chart.js';\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/chart.service\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/alert.service\";\nimport * as i4 from \"../services/dashboard-config.service\";\nimport * as i5 from \"../services/chart-modal.service\";\nimport * as i6 from \"../services/monitoring.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/cdk/drag-drop\";\nconst _c0 = [\"apiCallsChart\"];\nconst _c1 = [\"apiPerformanceChart\"];\nconst _c2 = [\"instancesUsageChart\"];\nconst _c3 = [\"securityEventsChart\"];\nfunction DashboardComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.resetDashboardConfig());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3, \"Resetovat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵtext(2, \" Nyn\\u00ED m\\u016F\\u017Eete p\\u0159et\\u00E1hnout widgety a zm\\u011Bnit jejich po\\u0159ad\\u00ED. Kliknut\\u00EDm na tla\\u010D\\u00EDtko \\\"Dokon\\u010Dit \\u00FApravy\\\" ulo\\u017E\\u00EDte zm\\u011Bny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction DashboardComponent_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.toggleWidgetVisibility(\"api-calls\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_28_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 28);\n    i0.ɵɵtext(4, \"API vol\\u00E1n\\u00ED v \\u010Dase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_28_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const _r12 = i0.ɵɵreference(12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.openFullscreenChart(_r12, \"API vol\\u00E1n\\u00ED v \\u010Dase\"));\n    });\n    i0.ɵɵelement(7, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DashboardComponent_div_28_div_8_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"div\", 34);\n    i0.ɵɵelement(11, \"canvas\", null, 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_28_div_13_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r4.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r4.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.editMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.editMode);\n  }\n}\nfunction DashboardComponent_div_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.toggleWidgetVisibility(\"api-performance\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_29_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 42);\n    i0.ɵɵtext(4, \"Odezva DIS metod\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_29_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const _r19 = i0.ɵɵreference(12);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.openFullscreenChart(_r19, \"Odezva DIS metod\"));\n    });\n    i0.ɵɵelement(7, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DashboardComponent_div_29_div_8_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"div\", 34);\n    i0.ɵɵelement(11, \"canvas\", null, 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_29_div_13_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r5.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r5.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editMode);\n  }\n}\nfunction DashboardComponent_div_30_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_30_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.toggleWidgetVisibility(\"instances-usage\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_30_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 44);\n    i0.ɵɵtext(4, \"Vyu\\u017Eit\\u00ED instanc\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_30_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const _r26 = i0.ɵɵreference(12);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.openFullscreenChart(_r26, \"Vyu\\u017Eit\\u00ED instanc\\u00ED\"));\n    });\n    i0.ɵɵelement(7, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DashboardComponent_div_30_div_8_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"div\", 34);\n    i0.ɵɵelement(11, \"canvas\", null, 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_30_div_13_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r6.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r6.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.editMode);\n  }\n}\nfunction DashboardComponent_div_31_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.toggleWidgetVisibility(\"security-events\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_31_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"h5\", 46);\n    i0.ɵɵtext(4, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_31_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const _r33 = i0.ɵɵreference(12);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.openFullscreenChart(_r33, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\"));\n    });\n    i0.ɵɵelement(7, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DashboardComponent_div_31_div_8_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"div\", 34);\n    i0.ɵɵelement(11, \"canvas\", null, 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, DashboardComponent_div_31_div_13_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r7.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r7.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.editMode);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.editMode);\n  }\n}\nfunction DashboardComponent_div_32_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_32_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.toggleWidgetVisibility(\"system-info\"));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_32_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 50)(3, \"div\", 27)(4, \"h5\", 51);\n    i0.ɵɵtext(5, \"Syst\\u00E9mov\\u00E9 informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DashboardComponent_div_32_div_6_Template, 3, 0, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 33)(8, \"div\", 52)(9, \"div\", 53)(10, \"div\", 54)(11, \"div\", 55)(12, \"h3\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 57);\n    i0.ɵɵtext(15, \"Aktivn\\u00EDch upozorn\\u011Bn\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 53)(17, \"div\", 58)(18, \"div\", 55)(19, \"h3\", 56);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 57);\n    i0.ɵɵtext(22, \"Dostupnost API\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 53)(24, \"div\", 59)(25, \"div\", 55)(26, \"h3\", 56);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 57);\n    i0.ɵɵtext(29, \"Pr\\u016Fm\\u011Brn\\u00E1 odezva DIS metod\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"div\", 53)(31, \"div\", 60)(32, \"div\", 55)(33, \"h3\", 56);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\", 57);\n    i0.ɵɵtext(36, \"Expiruj\\u00EDc\\u00ED certifik\\u00E1ty\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(37, DashboardComponent_div_32_div_37_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDragDisabled\", !ctx_r8.editMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"draggable-card\", ctx_r8.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.editMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r8.alerts.length);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r8.systemStatistics.ApiAvailability || 100, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r8.systemStatistics.AvgApiResponseTime || 0, \"ms\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r8.systemStatistics.ExpiringCertificatesCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.editMode);\n  }\n}\nconst _c4 = function (a0, a1) {\n  return {\n    \"bi-pencil-square\": a0,\n    \"bi-check-lg\": a1\n  };\n};\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\nexport class DashboardComponent {\n  constructor(chartService, authService, alertService, dashboardConfigService, chartModalService, monitoringService) {\n    this.chartService = chartService;\n    this.authService = authService;\n    this.alertService = alertService;\n    this.dashboardConfigService = dashboardConfigService;\n    this.chartModalService = chartModalService;\n    this.monitoringService = monitoringService;\n    this.loading = true;\n    this.error = null;\n    this.isAdmin = false;\n    this.alerts = [];\n    this.systemStatistics = {};\n    // Grafy\n    this.apiCallsChart = null;\n    this.apiPerformanceChart = null;\n    this.instancesUsageChart = null;\n    this.securityEventsChart = null;\n    // Filtry\n    this.selectedDays = 30;\n    this.selectedInstanceId = null;\n    // Dashboard konfigurace\n    this.dashboardConfig = null;\n    this.widgets = [];\n    this.editMode = false;\n    // Aktualizace dat\n    this.updateSubscription = null;\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n    // Načtení uložené hodnoty počtu dní z local storage\n    const savedDateRange = localStorage.getItem('monitoring_dateRange');\n    if (savedDateRange) {\n      this.selectedDays = parseInt(savedDateRange, 10);\n    }\n  }\n  ngOnInit() {\n    // Načtení konfigurace dashboardu\n    this.loadDashboardConfig();\n    this.loadAlerts();\n    this.loadSystemStatistics();\n  }\n  /**\r\n   * Načte konfiguraci dashboardu pro aktuálního uživatele\r\n   */\n  loadDashboardConfig() {\n    this.dashboardConfigService.getUserDashboardConfig().subscribe({\n      next: config => {\n        this.dashboardConfig = config;\n        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n      },\n      error: error => {\n        console.error('Chyba při načítání konfigurace dashboardu', error);\n        // Použijeme výchozí konfiguraci\n        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n        });\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Inicializace grafů po načtení view\n    setTimeout(() => {\n      this.initCharts();\n      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten\n      setTimeout(() => {\n        this.initPopovers();\n      }, 300);\n    }, 500);\n  }\n  ngOnDestroy() {\n    // Zrušení subscription při zničení komponenty\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n    // Zničení grafů\n    this.destroyCharts();\n  }\n  /**\r\n   * Načtení upozornění\r\n   */\n  loadAlerts() {\n    this.alertService.getDashboardAlerts().subscribe({\n      next: data => {\n        this.alerts = data.filter(alert => !alert.isResolved);\n      },\n      error: err => {\n        console.error('Chyba při načítání upozornění', err);\n        this.error = 'Nepodařilo se načíst upozornění';\n      }\n    });\n  }\n  /**\r\n   * Načtení systémových statistik\r\n   */\n  loadSystemStatistics() {\n    this.monitoringService.getSystemStatistics().subscribe({\n      next: data => {\n        this.systemStatistics = data;\n      },\n      error: err => {\n        console.error('Chyba při načítání systémových statistik', err);\n        this.error = 'Nepodařilo se načíst systémové statistiky';\n      }\n    });\n  }\n  /**\r\n   * Inicializace všech grafů\r\n   */\n  initCharts() {\n    this.loading = true;\n    // Načtení dat pro grafy\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n    this.loading = false;\n  }\n  /**\r\n   * Aktualizace všech grafů\r\n   */\n  refreshCharts() {\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n    this.loadAlerts();\n    this.loadSystemStatistics();\n  }\n  /**\r\n   * Zničení všech grafů\r\n   */\n  destroyCharts() {\n    if (this.apiCallsChart) {\n      this.apiCallsChart.destroy();\n      this.apiCallsChart = null;\n    }\n    if (this.apiPerformanceChart) {\n      this.apiPerformanceChart.destroy();\n      this.apiPerformanceChart = null;\n    }\n    if (this.instancesUsageChart) {\n      this.instancesUsageChart.destroy();\n      this.instancesUsageChart = null;\n    }\n    if (this.securityEventsChart) {\n      this.securityEventsChart.destroy();\n      this.securityEventsChart = null;\n    }\n  }\n  /**\r\n   * Načtení grafu API volání\r\n   */\n  loadApiCallsChart() {\n    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: data => {\n        if (this.apiCallsChart) {\n          this.apiCallsChart.data.labels = data.labels;\n          this.apiCallsChart.data.datasets[0].data = data.data;\n          this.apiCallsChart.update();\n        } else if (this.apiCallsChartRef) {\n          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet API volání',\n                data: data.data,\n                backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 2,\n                tension: 0.3,\n                fill: true\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf API volání', err);\n        this.error = 'Nepodařilo se načíst data pro graf API volání';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu výkonu API\r\n   */\n  loadApiPerformanceChart() {\n    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: data => {\n        if (this.apiPerformanceChart) {\n          this.apiPerformanceChart.data.labels = data.labels;\n          this.apiPerformanceChart.data.datasets[0].data = data.avgData;\n          this.apiPerformanceChart.data.datasets[1].data = data.maxData;\n          this.apiPerformanceChart.update();\n        } else if (this.apiPerformanceChartRef) {\n          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Průměrná doba odezvy (ms)',\n                data: data.avgData,\n                backgroundColor: 'rgba(54, 162, 235, 0.5)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 1\n              }, {\n                label: 'Maximální doba odezvy (ms)',\n                data: data.maxData,\n                backgroundColor: 'rgba(255, 99, 132, 0.5)',\n                borderColor: 'rgba(255, 99, 132, 1)',\n                borderWidth: 1\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Endpoint'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf výkonu API', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu API';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu využití instancí\r\n   */\n  loadInstancesUsageChart() {\n    this.chartService.getInstancesUsageChartData().subscribe({\n      next: data => {\n        if (this.instancesUsageChart) {\n          this.instancesUsageChart.data.labels = data.labels;\n          this.instancesUsageChart.data.datasets[0].data = data.data;\n          this.instancesUsageChart.update();\n        } else if (this.instancesUsageChartRef) {\n          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet API volání',\n                data: data.data,\n                backgroundColor: ['rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(255, 206, 86, 0.5)', 'rgba(75, 192, 192, 0.5)', 'rgba(153, 102, 255, 0.5)', 'rgba(255, 159, 64, 0.5)', 'rgba(199, 199, 199, 0.5)', 'rgba(83, 102, 255, 0.5)', 'rgba(40, 159, 64, 0.5)', 'rgba(210, 199, 199, 0.5)'],\n                borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)', 'rgba(199, 199, 199, 1)', 'rgba(83, 102, 255, 1)', 'rgba(40, 159, 64, 1)', 'rgba(210, 199, 199, 1)'],\n                borderWidth: 1\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              indexAxis: 'y',\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: false\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                x: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                y: {\n                  title: {\n                    display: true,\n                    text: 'Instance'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf využití instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf využití instancí';\n      }\n    });\n  }\n  /**\r\n   * Načtení grafu bezpečnostních událostí\r\n   */\n  loadSecurityEventsChart() {\n    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({\n      next: data => {\n        if (this.securityEventsChart) {\n          this.securityEventsChart.data.labels = data.labels;\n          this.securityEventsChart.data.datasets = data.datasets;\n          this.securityEventsChart.update();\n        } else if (this.securityEventsChartRef) {\n          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet událostí'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);\n        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';\n      }\n    });\n  }\n  /**\r\n   * Změna filtru dnů\r\n   */\n  onDaysChange(days) {\n    this.selectedDays = days;\n    // Uložení vybraného počtu dní do local storage\n    localStorage.setItem('monitoring_dateRange', days.toString());\n    this.refreshCharts();\n  }\n  /**\r\n   * Změna filtru instance\r\n   */\n  onInstanceChange(instanceId) {\n    this.selectedInstanceId = instanceId;\n    this.refreshCharts();\n  }\n  /**\r\n   * Vyřešení upozornění\r\n   */\n  resolveAlert(alertId) {\n    const resolution = prompt('Zadejte řešení upozornění:');\n    if (resolution !== null) {\n      this.alertService.resolveDashboardAlert(alertId, resolution).subscribe({\n        next: () => {\n          this.alerts = this.alerts.filter(a => a.id !== alertId);\n        },\n        error: err => {\n          console.error('Chyba při řešení upozornění', err);\n          this.error = 'Nepodařilo se vyřešit upozornění';\n        }\n      });\n    }\n  }\n  /**\r\n   * Získání textu pro typ alertu\r\n   */\n  getAlertTypeText(alertType) {\n    switch (alertType) {\n      case 'SecurityBreach':\n        return 'Bezpečnostní incident';\n      case 'CertificateExpiration':\n        return 'Expirace certifikátu';\n      case 'FailedConnectionAttempts':\n        return 'Selhané připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      default:\n        return alertType;\n    }\n  }\n  /**\r\n   * Přepnutí režimu úprav dashboardu\r\n   */\n  toggleEditMode() {\n    this.editMode = !this.editMode;\n  }\n  /**\r\n   * Zpracování přetažení widgetu\r\n   */\n  onDrop(event) {\n    if (event.previousIndex === event.currentIndex) {\n      return;\n    }\n    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM\n    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]')).map(element => {\n      // Získáme ID widgetu z atributu *ngIf\n      const widget = this.widgets.find(w => {\n        const widgetId = w.id;\n        return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);\n      });\n      return widget;\n    }).filter(widget => widget !== undefined);\n    // Provedeme přesun v poli viditelných widgetů\n    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);\n    // Aktualizujeme pozice všech widgetů\n    let position = 0;\n    // Nejprve aktualizujeme pozice viditelných widgetů\n    visibleWidgets.forEach(visibleWidget => {\n      const widget = this.widgets.find(w => w.id === visibleWidget.id);\n      if (widget) {\n        widget.position = position++;\n      }\n    });\n    // Poté aktualizujeme pozice skrytých widgetů\n    this.widgets.filter(widget => !visibleWidgets.some(vw => vw.id === widget.id)).forEach(widget => {\n      widget.position = position++;\n    });\n    // Seřadíme widgety podle pozice\n    this.widgets.sort((a, b) => a.position - b.position);\n    // Uložení konfigurace\n    this.saveDashboardConfig();\n  }\n  /**\r\n   * Změna viditelnosti widgetu\r\n   */\n  toggleWidgetVisibility(widgetId) {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    if (widget) {\n      widget.visible = !widget.visible;\n      this.saveDashboardConfig();\n    }\n  }\n  /**\r\n   * Kontrola viditelnosti widgetu\r\n   */\n  isWidgetVisible(widgetId) {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    return widget ? widget.visible : true;\n  }\n  /**\r\n   * Uložení konfigurace dashboardu\r\n   */\n  saveDashboardConfig() {\n    if (!this.dashboardConfig) {\n      return;\n    }\n    this.dashboardConfig.widgets = [...this.widgets];\n    this.dashboardConfig.lastModified = new Date();\n    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({\n      next: config => {\n        console.log('Konfigurace dashboardu byla úspěšně uložena');\n      },\n      error: error => {\n        console.error('Chyba při ukládání konfigurace dashboardu', error);\n      }\n    });\n  }\n  /**\r\n   * Reset konfigurace dashboardu na výchozí hodnoty\r\n   */\n  resetDashboardConfig() {\n    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {\n      this.dashboardConfigService.resetDashboardConfig().subscribe({\n        next: config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n          this.editMode = false;\n          this.refreshCharts();\n        },\n        error: error => {\n          console.error('Chyba při resetování konfigurace dashboardu', error);\n        }\n      });\n    }\n  }\n  /**\r\n   * Otevře modální okno s grafem v režimu \"full screen\"\r\n   * @param chart Instance grafu nebo reference na canvas element\r\n   * @param title Titulek grafu\r\n   */\n  openFullscreenChart(chart, title) {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n  /**\r\n   * Inicializace popoverů pro nápovědu\r\n   */\n  initPopovers() {\n    // Definice obsahu nápověd\n    const helpContent = {\n      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' + 'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' + 'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'api-calls': 'Graf zobrazuje počet API volání v čase. Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',\n      'instances-usage': 'Graf zobrazuje využití jednotlivých instancí podle počtu API volání. ' + 'Ukazuje, které instance jsou nejvíce zatížené a pomáhá identifikovat nerovnoměrné rozložení zátěže. ' + 'Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',\n      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' + 'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' + 'Data jsou získávána z tabulky SecurityLogs za zvolené období.',\n      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' + 'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' + 'Poskytuje rychlý přehled o celkovém stavu systému.'\n    };\n    // Nejprve zrušíme všechny existující popovery\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n      console.log('Initializing popover for element with help-type:', helpType);\n      if (helpType && helpType in helpContent) {\n        try {\n          new bootstrap.Popover(el, {\n            content: helpContent[helpType],\n            html: true,\n            trigger: 'hover',\n            placement: 'top',\n            container: 'body'\n          });\n        } catch (error) {\n          console.error('Error initializing popover:', error);\n        }\n      } else if (helpType) {\n        console.warn('Help content not found for type:', helpType);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ChartService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.AlertService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartModalService), i0.ɵɵdirectiveInject(i6.MonitoringService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      viewQuery: function DashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiCallsChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.apiPerformanceChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.instancesUsageChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.securityEventsChartRef = _t.first);\n        }\n      },\n      decls: 33,\n      vars: 35,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"d-flex\", \"gap-2\", \"flex-wrap\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"d-none\", \"d-sm-inline\"], [1, \"btn\", 3, \"click\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"btn btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-info mb-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"cdkDropList\", \"\", 1, \"row\", 3, \"cdkDropListDisabled\", \"cdkDropListDropped\"], [\"class\", \"col-lg-6 mb-4\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [\"class\", \"row\", \"cdkDrag\", \"\", 3, \"cdkDragDisabled\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-1\"], [1, \"alert\", \"alert-info\", \"mb-4\"], [1, \"bi\", \"bi-info-circle-fill\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\"], [\"cdkDrag\", \"\", 1, \"col-lg-6\", \"mb-4\", 3, \"cdkDragDisabled\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-calls\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [1, \"d-flex\", \"align-items-center\"], [\"title\", \"Zobrazit graf na celou obrazovku\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-arrows-fullscreen\"], [\"class\", \"widget-controls\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"chart-container\", 2, \"position\", \"relative\", \"height\", \"300px\"], [\"apiCallsChart\", \"\"], [\"class\", \"drag-handle\", \"cdkDragHandle\", \"\", 4, \"ngIf\"], [1, \"widget-controls\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-eye-slash\"], [\"cdkDragHandle\", \"\", 1, \"drag-handle\"], [1, \"bi\", \"bi-grip-horizontal\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"api-performance\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"apiPerformanceChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"instances-usage\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"instancesUsageChart\", \"\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"security-events\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [\"securityEventsChart\", \"\"], [\"cdkDrag\", \"\", 1, \"row\", 3, \"cdkDragDisabled\"], [1, \"col-12\", \"mb-4\"], [1, \"card\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"system-info\", 1, \"mb-0\", 2, \"cursor\", \"help\"], [1, \"row\"], [1, \"col-6\", \"col-md-3\", \"mb-3\"], [1, \"card\", \"bg-primary\", \"text-white\"], [1, \"card-body\", \"text-center\"], [1, \"display-4\"], [1, \"mb-0\"], [1, \"card\", \"bg-success\", \"text-white\"], [1, \"card\", \"bg-info\", \"text-white\"], [1, \"card\", \"bg-warning\", \"text-dark\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_6_listener() {\n            return ctx.onDaysChange(1);\n          });\n          i0.ɵɵtext(7, \"1 den\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_8_listener() {\n            return ctx.onDaysChange(7);\n          });\n          i0.ɵɵtext(9, \"7 dn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_10_listener() {\n            return ctx.onDaysChange(30);\n          });\n          i0.ɵɵtext(11, \"30 dn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_12_listener() {\n            return ctx.onDaysChange(90);\n          });\n          i0.ɵɵtext(13, \"90 dn\\u00ED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 2)(15, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_15_listener() {\n            return ctx.refreshCharts();\n          });\n          i0.ɵɵelement(16, \"i\", 6);\n          i0.ɵɵelementStart(17, \"span\", 7);\n          i0.ɵɵtext(18, \"Aktualizovat\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_19_listener() {\n            return ctx.toggleEditMode();\n          });\n          i0.ɵɵelement(20, \"i\", 9);\n          i0.ɵɵelementStart(21, \"span\", 7);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, DashboardComponent_button_23_Template, 4, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(24, DashboardComponent_div_24_Template, 3, 0, \"div\", 11);\n          i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 4, 0, \"div\", 12);\n          i0.ɵɵtemplate(26, DashboardComponent_div_26_Template, 2, 1, \"div\", 13);\n          i0.ɵɵelementStart(27, \"div\", 14);\n          i0.ɵɵlistener(\"cdkDropListDropped\", function DashboardComponent_Template_div_cdkDropListDropped_27_listener($event) {\n            return ctx.onDrop($event);\n          });\n          i0.ɵɵtemplate(28, DashboardComponent_div_28_Template, 14, 5, \"div\", 15);\n          i0.ɵɵtemplate(29, DashboardComponent_div_29_Template, 14, 5, \"div\", 15);\n          i0.ɵɵtemplate(30, DashboardComponent_div_30_Template, 14, 5, \"div\", 15);\n          i0.ɵɵtemplate(31, DashboardComponent_div_31_Template, 14, 5, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, DashboardComponent_div_32_Template, 38, 9, \"div\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 1)(\"btn-outline-primary\", ctx.selectedDays !== 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 7)(\"btn-outline-primary\", ctx.selectedDays !== 7);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 30)(\"btn-outline-primary\", ctx.selectedDays !== 30);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"btn-primary\", ctx.selectedDays === 90)(\"btn-outline-primary\", ctx.selectedDays !== 90);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"btn-outline-warning\", !ctx.editMode)(\"btn-warning\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(32, _c4, !ctx.editMode, ctx.editMode));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.editMode ? \"Dokon\\u010Dit \\u00FApravy\" : \"Upravit dashboard\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"cdkDropListDisabled\", !ctx.editMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-calls\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"api-performance\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isWidgetVisible(\"instances-usage\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"security-events\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.isWidgetVisible(\"system-info\"));\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgIf, i8.CdkDropList, i8.CdkDrag, i8.CdkDragHandle],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: auto;\\n  height: 300px;\\n  width: 100%;\\n}\\n\\n\\n.draggable-card[_ngcontent-%COMP%] {\\n  cursor: move;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n\\n.draggable-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.drag-handle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: move;\\n  border-bottom-left-radius: 0.25rem;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #6c757d;\\n}\\n\\n.widget-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n\\n.cdk-drag-preview[_ngcontent-%COMP%] {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);\\n  opacity: 0.8;\\n  z-index: 1000;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n  background-color: rgba(0, 123, 255, 0.1);\\n  border: 2px dashed #0d6efd;\\n  border-radius: 8px;\\n}\\n\\n.cdk-drag-animating[_ngcontent-%COMP%] {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n.row.cdk-drop-list-dragging[_ngcontent-%COMP%]   .col-lg-6[_ngcontent-%COMP%]:not(.cdk-drag-placeholder) {\\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  background-color: rgba(13, 110, 253, 0.2);\\n  border: 2px dashed #0d6efd;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  padding: 1rem;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  padding: 0.5em 0.75em;\\n}\\n\\n\\n.alert-type-badge[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n\\n.alert-type-security-breach[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.alert-type-certificate-expiration[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n}\\n\\n.alert-type-failed-connection[_ngcontent-%COMP%] {\\n  background-color: #fd7e14;\\n  color: white;\\n}\\n\\n.alert-type-suspicious-activity[_ngcontent-%COMP%] {\\n  background-color: #6f42c1;\\n  color: white;\\n}\\n\\n.severity-badge[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n\\n.severity-high[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.severity-medium[_ngcontent-%COMP%] {\\n  background-color: #fd7e14;\\n  color: white;\\n}\\n\\n.severity-low[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chart-container[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n\\n  .display-4[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n\\n  .row[_ngcontent-%COMP%]    > [class*=\\\"col-\\\"][_ngcontent-%COMP%] {\\n    padding-left: 8px;\\n    padding-right: 8px;\\n  }\\n\\n  .row[_ngcontent-%COMP%] {\\n    margin-left: -8px;\\n    margin-right: -8px;\\n  }\\n\\n  .btn-group.role-group[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n\\n  .btn-group.role-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n    padding: 0.375rem 0.5rem;\\n    margin-bottom: 5px;\\n  }\\n\\n  .card[_ngcontent-%COMP%]   h5.mb-0[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .card[_ngcontent-%COMP%]   p.mb-0[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n\\n  .widget-controls[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 0.5rem;\\n    right: 0.5rem;\\n  }\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  background-color: #2b3035;\\n  border-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  color: #e9ecef;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-bottom-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.075);\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0d6efd;\\n  color: #fff;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  color: #198754;\\n  border-color: #198754;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover {\\n  background-color: #198754;\\n  color: #fff;\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .draggable-card[_ngcontent-%COMP%] {\\n  background-color: #1a2530;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.05);\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .drag-handle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAOA,SAASA,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAE/C,SAAsBC,eAAe,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;ICS7DC,kCAAyF;IAAjCA;MAAAA;MAAA;MAAA,OAASA,4CAAsB;IAAA,EAAC;IACtFA,wBAAiD;IAACA,+BAAiC;IAAAA,yBAAS;IAAAA,iBAAO;;;;;IAM3GA,+BAAoD;IAClDA,wBAA2C;IAC3CA,iNACF;IAAAA,iBAAM;;;;;IAENA,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAA8C;IAC5CA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;;IAeUA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,WAAW,CAAC;IAAA,EAAC;IAC5FA,wBAA+B;IACjCA,iBAAS;;;;;IASfA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAtBVA,+BAAsG;IAGLA,gDAAiB;IAAAA,iBAAK;IACjHA,+BAAuC;IACYA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAmC,kCAAmB,CAAC;IAAA,EAAC;IAChHA,wBAAuC;IACzCA,iBAAS;IACTA,2EAIM;IACRA,iBAAM;IAERA,+BAAuB;IAEnBA,oCAAgC;IAClCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAvBgEA,kDAA6B;IAC3EA,eAAiC;IAAjCA,iDAAiC;IAO7CA,eAAc;IAAdA,sCAAc;IAYlBA,eAAc;IAAdA,sCAAc;;;;;;IAehBA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,iBAAiB,CAAC;IAAA,EAAC;IAClGA,wBAA+B;IACjCA,iBAAS;;;;;IASfA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAtBVA,+BAA4G;IAGLA,gCAAgB;IAAAA,iBAAK;IACtHA,+BAAuC;IACYA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,kBAAkB,CAAC;IAAA,EAAC;IACrHA,wBAAuC;IACzCA,iBAAS;IACTA,2EAIM;IACRA,iBAAM;IAERA,+BAAuB;IAEnBA,oCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAvBsEA,kDAA6B;IACjFA,eAAiC;IAAjCA,iDAAiC;IAO7CA,eAAc;IAAdA,sCAAc;IAYlBA,eAAc;IAAdA,sCAAc;;;;;;IAehBA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,iBAAiB,CAAC;IAAA,EAAC;IAClGA,wBAA+B;IACjCA,iBAAS;;;;;IASfA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAtBVA,+BAA4G;IAGLA,+CAAgB;IAAAA,iBAAK;IACtHA,+BAAuC;IACYA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,iCAAkB,CAAC;IAAA,EAAC;IACrHA,wBAAuC;IACzCA,iBAAS;IACTA,2EAIM;IACRA,iBAAM;IAERA,+BAAuB;IAEnBA,oCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAvBsEA,kDAA6B;IACjFA,eAAiC;IAAjCA,iDAAiC;IAO7CA,eAAc;IAAdA,sCAAc;IAYlBA,eAAc;IAAdA,sCAAc;;;;;;IAehBA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,iBAAiB,CAAC;IAAA,EAAC;IAClGA,wBAA+B;IACjCA,iBAAS;;;;;IASfA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;;IAtBVA,+BAAuH;IAGhBA,oDAAqB;IAAAA,iBAAK;IAC3HA,+BAAuC;IACYA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAyC,sCAAuB,CAAC;IAAA,EAAC;IAC1HA,wBAAuC;IACzCA,iBAAS;IACTA,2EAIM;IACRA,iBAAM;IAERA,+BAAuB;IAEnBA,oCAAsC;IACxCA,iBAAM;IAERA,6EAEM;IACRA,iBAAM;;;;IAvBiFA,kDAA6B;IAC5FA,eAAiC;IAAjCA,iDAAiC;IAO7CA,eAAc;IAAdA,sCAAc;IAYlBA,eAAc;IAAdA,sCAAc;;;;;;IAalBA,+BAA8C;IACKA;MAAAA;MAAA;MAAA,OAASA,8CAAuB,aAAa,CAAC;IAAA,EAAC;IAC9FA,wBAA+B;IACjCA,iBAAS;;;;;IAuCbA,+BAAwD;IACtDA,wBAAqC;IACvCA,iBAAM;;;;;IAjDZA,+BAAyG;IAIJA,6CAAmB;IAAAA,iBAAK;IACrHA,2EAIM;IACRA,iBAAM;IACNA,+BAAuB;IAKSA,aAAmB;IAAAA,iBAAK;IAC9CA,8BAAgB;IAAAA,oDAAoB;IAAAA,iBAAI;IAI9CA,gCAAiC;IAGLA,aAA8C;IAAAA,iBAAK;IACzEA,8BAAgB;IAAAA,+BAAc;IAAAA,iBAAI;IAIxCA,gCAAiC;IAGLA,aAAgD;IAAAA,iBAAK;IAC3EA,8BAAgB;IAAAA,yDAAyB;IAAAA,iBAAI;IAInDA,gCAAiC;IAGLA,aAAqD;IAAAA,iBAAK;IAChFA,8BAAgB;IAAAA,sDAAsB;IAAAA,iBAAI;IAMpDA,6EAEM;IACRA,iBAAM;;;;IAlDiEA,kDAA6B;IAElFA,eAAiC;IAAjCA,iDAAiC;IAGzCA,eAAc;IAAdA,sCAAc;IAWUA,eAAmB;IAAnBA,0CAAmB;IAQnBA,eAA8C;IAA9CA,8EAA8C;IAQ9CA,eAAgD;IAAhDA,gFAAgD;IAQhDA,eAAqD;IAArDA,4EAAqD;IAO/EA,eAAc;IAAdA,sCAAc;;;;;;;;;AD7L5B;AACAH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAUhC,OAAM,MAAOI,kBAAkB;EA+B7BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,YAA0B,EAC1BC,sBAA8C,EAC9CC,iBAAoC,EACpCC,iBAAoC;IALpC,iBAAY,GAAZL,YAAY;IACZ,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,2BAAsB,GAAtBC,sBAAsB;IACtB,sBAAiB,GAAjBC,iBAAiB;IACjB,sBAAiB,GAAjBC,iBAAiB;IA/B3B,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;IAE3B,YAAO,GAAG,KAAK;IACf,WAAM,GAAU,EAAE;IAClB,qBAAgB,GAAQ,EAAE;IAE1B;IACA,kBAAa,GAAiB,IAAI;IAClC,wBAAmB,GAAiB,IAAI;IACxC,wBAAmB,GAAiB,IAAI;IACxC,wBAAmB,GAAiB,IAAI;IAExC;IACA,iBAAY,GAAG,EAAE;IACjB,uBAAkB,GAAkB,IAAI;IAExC;IACA,oBAAe,GAA2B,IAAI;IAC9C,YAAO,GAAsB,EAAE;IAC/B,aAAQ,GAAG,KAAK;IAEhB;IACQ,uBAAkB,GAAwB,IAAI;IAUpD,IAAI,CAACJ,WAAW,CAACK,WAAW,CAACC,SAAS,CAACC,IAAI,IAAG;MAC5C,IAAI,CAACF,WAAW,GAAGE,IAAI;MACvB,IAAI,CAACC,OAAO,GAAGD,IAAI,EAAEC,OAAO,IAAI,KAAK;IACvC,CAAC,CAAC;IAEF;IACA,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACnE,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACG,YAAY,GAAGC,QAAQ,CAACJ,cAAc,EAAE,EAAE,CAAC;;EAEpD;EAEAK,QAAQ;IACN;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAF,mBAAmB;IACjB,IAAI,CAACb,sBAAsB,CAACgB,sBAAsB,EAAE,CAACZ,SAAS,CAAC;MAC7Da,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACC,eAAe,GAAGD,MAAM;QAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;MAC5E,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE;QACA,IAAI,CAACzB,sBAAsB,CAAC2B,oBAAoB,EAAE,CAACvB,SAAS,CAACc,MAAM,IAAG;UACpE,IAAI,CAACC,eAAe,GAAGD,MAAM;UAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;QAC5E,CAAC,CAAC;MACJ;KACD,CAAC;EACJ;EAEAI,eAAe;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,EAAE;MACjB;MACAD,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,YAAY,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAW;IACT;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACC,WAAW,EAAE;;IAGvC;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;EAGArB,UAAU;IACR,IAAI,CAACf,YAAY,CAACqC,kBAAkB,EAAE,CAAChC,SAAS,CAAC;MAC/Ca,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACE,MAAM,CAAEC,KAAU,IAAK,CAACA,KAAK,CAACC,UAAU,CAAC;MAC9D,CAAC;MACDhB,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEiB,GAAG,CAAC;QACnD,IAAI,CAACjB,KAAK,GAAG,iCAAiC;MAChD;KACD,CAAC;EACJ;EAEA;;;EAGAV,oBAAoB;IAClB,IAAI,CAACb,iBAAiB,CAACyC,mBAAmB,EAAE,CAACvC,SAAS,CAAC;MACrDa,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,CAACO,gBAAgB,GAAGP,IAAI;MAC9B,CAAC;MACDZ,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEiB,GAAG,CAAC;QAC9D,IAAI,CAACjB,KAAK,GAAG,2CAA2C;MAC1D;KACD,CAAC;EACJ;EAEA;;;EAGAK,UAAU;IACR,IAAI,CAACe,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;IAE9B,IAAI,IAAI,CAAC1C,OAAO,EAAE;MAChB,IAAI,CAAC2C,uBAAuB,EAAE;;IAGhC,IAAI,CAACJ,OAAO,GAAG,KAAK;EACtB;EAEA;;;EAGAK,aAAa;IACX,IAAI,CAACJ,iBAAiB,EAAE;IACxB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;IAE9B,IAAI,IAAI,CAAC1C,OAAO,EAAE;MAChB,IAAI,CAAC2C,uBAAuB,EAAE;;IAGhC,IAAI,CAACnC,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAoB,aAAa;IACX,IAAI,IAAI,CAACgB,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;MAC5B,IAAI,CAACD,aAAa,GAAG,IAAI;;IAG3B,IAAI,IAAI,CAACE,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACD,OAAO,EAAE;MAClC,IAAI,CAACC,mBAAmB,GAAG,IAAI;;IAGjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACF,OAAO,EAAE;MAClC,IAAI,CAACE,mBAAmB,GAAG,IAAI;;IAGjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACH,OAAO,EAAE;MAClC,IAAI,CAACG,mBAAmB,GAAG,IAAI;;EAEnC;EAEA;;;EAGAT,iBAAiB;IACf,IAAI,CAACjD,YAAY,CAAC2D,oBAAoB,CAAC,IAAI,CAACC,kBAAkB,IAAIC,SAAS,EAAE,IAAI,CAAChD,YAAY,CAAC,CAACN,SAAS,CAAC;MACxGa,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,IAAI,CAACc,aAAa,EAAE;UACtB,IAAI,CAACA,aAAa,CAACd,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACsB,MAAM;UAC5C,IAAI,CAACR,aAAa,CAACd,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACvB,IAAI,GAAGA,IAAI,CAACA,IAAI;UACpD,IAAI,CAACc,aAAa,CAACU,MAAM,EAAE;SAC5B,MAAM,IAAI,IAAI,CAACC,gBAAgB,EAAE;UAChC,IAAI,CAACX,aAAa,GAAG,IAAI7D,KAAK,CAAC,IAAI,CAACwE,gBAAgB,CAACC,aAAa,EAAE;YAClEC,IAAI,EAAE,MAAM;YACZ3B,IAAI,EAAE;cACJsB,MAAM,EAAEtB,IAAI,CAACsB,MAAM;cACnBC,QAAQ,EAAE,CAAC;gBACTK,KAAK,EAAE,kBAAkB;gBACzB5B,IAAI,EAAEA,IAAI,CAACA,IAAI;gBACf6B,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE,CAAC;gBACdC,OAAO,EAAE,GAAG;gBACZC,IAAI,EAAE;eACP;aACF;YACDC,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAExC,IAAI,CAACsC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACbpD,QAAQ,EAAE;iBACX;gBACDyD,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACDpD,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEiB,GAAG,CAAC;QAChE,IAAI,CAACjB,KAAK,GAAG,+CAA+C;MAC9D;KACD,CAAC;EACJ;EAEA;;;EAGAsB,uBAAuB;IACrB,IAAI,CAAClD,YAAY,CAAC2F,0BAA0B,CAAC,IAAI,CAAC/B,kBAAkB,IAAIC,SAAS,EAAE,IAAI,CAAChD,YAAY,CAAC,CAACN,SAAS,CAAC;MAC9Ga,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,IAAI,CAACgB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAAChB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACsB,MAAM;UAClD,IAAI,CAACN,mBAAmB,CAAChB,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACvB,IAAI,GAAGA,IAAI,CAACoD,OAAO;UAC7D,IAAI,CAACpC,mBAAmB,CAAChB,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACvB,IAAI,GAAGA,IAAI,CAACqD,OAAO;UAC7D,IAAI,CAACrC,mBAAmB,CAACQ,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAAC8B,sBAAsB,EAAE;UACtC,IAAI,CAACtC,mBAAmB,GAAG,IAAI/D,KAAK,CAAC,IAAI,CAACqG,sBAAsB,CAAC5B,aAAa,EAAE;YAC9EC,IAAI,EAAE,KAAK;YACX3B,IAAI,EAAE;cACJsB,MAAM,EAAEtB,IAAI,CAACsB,MAAM;cACnBC,QAAQ,EAAE,CACR;gBACEK,KAAK,EAAE,2BAA2B;gBAClC5B,IAAI,EAAEA,IAAI,CAACoD,OAAO;gBAClBvB,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE;eACd,EACD;gBACEH,KAAK,EAAE,4BAA4B;gBACnC5B,IAAI,EAAEA,IAAI,CAACqD,OAAO;gBAClBxB,eAAe,EAAE,yBAAyB;gBAC1CC,WAAW,EAAE,uBAAuB;gBACpCC,WAAW,EAAE;eACd;aAEJ;YACDG,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAExC,IAAI,CAACsC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACbpD,QAAQ,EAAE;iBACX;gBACDyD,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACDpD,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEiB,GAAG,CAAC;QAChE,IAAI,CAACjB,KAAK,GAAG,+CAA+C;MAC9D;KACD,CAAC;EACJ;EAEA;;;EAGAuB,uBAAuB;IACrB,IAAI,CAACnD,YAAY,CAAC+F,0BAA0B,EAAE,CAACxF,SAAS,CAAC;MACvDa,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,IAAI,CAACiB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAACjB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACsB,MAAM;UAClD,IAAI,CAACL,mBAAmB,CAACjB,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACvB,IAAI,GAAGA,IAAI,CAACA,IAAI;UAC1D,IAAI,CAACiB,mBAAmB,CAACO,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAACgC,sBAAsB,EAAE;UACtC,IAAI,CAACvC,mBAAmB,GAAG,IAAIhE,KAAK,CAAC,IAAI,CAACuG,sBAAsB,CAAC9B,aAAa,EAAE;YAC9EC,IAAI,EAAE,KAAK;YACX3B,IAAI,EAAE;cACJsB,MAAM,EAAEtB,IAAI,CAACsB,MAAM;cACnBC,QAAQ,EAAE,CAAC;gBACTK,KAAK,EAAE,kBAAkB;gBACzB5B,IAAI,EAAEA,IAAI,CAACA,IAAI;gBACf6B,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,EACzB,wBAAwB,EACxB,0BAA0B,CAC3B;gBACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,sBAAsB,EACtB,wBAAwB,CACzB;gBACDC,WAAW,EAAE;eACd;aACF;YACDG,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BqB,SAAS,EAAE,GAAG;cACdpB,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAExC,IAAI,CAACsC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE;iBACV;gBACDK,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNG,CAAC,EAAE;kBACDD,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDQ,CAAC,EAAE;kBACDV,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACDpD,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEiB,GAAG,CAAC;QACtE,IAAI,CAACjB,KAAK,GAAG,qDAAqD;MACpE;KACD,CAAC;EACJ;EAEA;;;EAGAwB,uBAAuB;IACrB,IAAI,CAACpD,YAAY,CAACkG,0BAA0B,CAAC,IAAI,CAACrF,YAAY,CAAC,CAACN,SAAS,CAAC;MACxEa,IAAI,EAAGoB,IAAI,IAAI;QACb,IAAI,IAAI,CAACkB,mBAAmB,EAAE;UAC5B,IAAI,CAACA,mBAAmB,CAAClB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACsB,MAAM;UAClD,IAAI,CAACJ,mBAAmB,CAAClB,IAAI,CAACuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ;UACtD,IAAI,CAACL,mBAAmB,CAACM,MAAM,EAAE;SAClC,MAAM,IAAI,IAAI,CAACmC,sBAAsB,EAAE;UACtC,IAAI,CAACzC,mBAAmB,GAAG,IAAIjE,KAAK,CAAC,IAAI,CAAC0G,sBAAsB,CAACjC,aAAa,EAAE;YAC9EC,IAAI,EAAE,MAAM;YACZ3B,IAAI,EAAE;cACJsB,MAAM,EAAEtB,IAAI,CAACsB,MAAM;cACnBC,QAAQ,EAAEvB,IAAI,CAACuB;aAChB;YACDW,OAAO,EAAE;cACPC,UAAU,EAAE,IAAI;cAChBC,mBAAmB,EAAE,KAAK;cAC1BC,OAAO,EAAE;gBACPC,KAAK,EAAE;kBACLC,OAAO,EAAE,IAAI;kBACbC,IAAI,EAAExC,IAAI,CAACsC,KAAK;kBAChBG,IAAI,EAAE;oBACJC,IAAI,EAAE;;iBAET;gBACDC,MAAM,EAAE;kBACNJ,OAAO,EAAE,IAAI;kBACbpD,QAAQ,EAAE;iBACX;gBACDyD,OAAO,EAAE;kBACPC,IAAI,EAAE,OAAO;kBACbC,SAAS,EAAE;;eAEd;cACDC,MAAM,EAAE;gBACNC,CAAC,EAAE;kBACDC,WAAW,EAAE,IAAI;kBACjBX,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;iBAET;gBACDU,CAAC,EAAE;kBACDZ,KAAK,EAAE;oBACLC,OAAO,EAAE,IAAI;oBACbC,IAAI,EAAE;;;;;WAKf,CAAC;;MAEN,CAAC;MACDpD,KAAK,EAAGiB,GAAG,IAAI;QACbhB,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEiB,GAAG,CAAC;QAC7E,IAAI,CAACjB,KAAK,GAAG,4DAA4D;MAC3E;KACD,CAAC;EACJ;EAEA;;;EAGAwE,YAAY,CAACC,IAAY;IACvB,IAAI,CAACxF,YAAY,GAAGwF,IAAI;IAExB;IACA1F,YAAY,CAAC2F,OAAO,CAAC,sBAAsB,EAAED,IAAI,CAACE,QAAQ,EAAE,CAAC;IAE7D,IAAI,CAAClD,aAAa,EAAE;EACtB;EAEA;;;EAGAmD,gBAAgB,CAACC,UAAyB;IACxC,IAAI,CAAC7C,kBAAkB,GAAG6C,UAAU;IACpC,IAAI,CAACpD,aAAa,EAAE;EACtB;EAEA;;;EAGAqD,YAAY,CAACC,OAAe;IAC1B,MAAMC,UAAU,GAAGC,MAAM,CAAC,4BAA4B,CAAC;IACvD,IAAID,UAAU,KAAK,IAAI,EAAE;MACvB,IAAI,CAAC1G,YAAY,CAAC4G,qBAAqB,CAACH,OAAO,EAAEC,UAAU,CAAC,CAACrG,SAAS,CAAC;QACrEa,IAAI,EAAE,MAAK;UACT,IAAI,CAACqB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,MAAM,CAACjB,CAAC,IAAIA,CAAC,CAACsF,EAAE,KAAKJ,OAAO,CAAC;QACzD,CAAC;QACD/E,KAAK,EAAGiB,GAAG,IAAI;UACbhB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEiB,GAAG,CAAC;UACjD,IAAI,CAACjB,KAAK,GAAG,kCAAkC;QACjD;OACD,CAAC;;EAEN;EAEA;;;EAGAoF,gBAAgB,CAACC,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,gBAAgB;QACnB,OAAO,uBAAuB;MAChC,KAAK,uBAAuB;QAC1B,OAAO,sBAAsB;MAC/B,KAAK,0BAA0B;QAC7B,OAAO,mBAAmB;MAC5B,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEA;;;EAGAC,cAAc;IACZ,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAChC;EAEA;;;EAGAC,MAAM,CAACC,KAAqC;IAC1C,IAAIA,KAAK,CAACC,aAAa,KAAKD,KAAK,CAACE,YAAY,EAAE;MAC9C;;IAGF;IACA,MAAMC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAACC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,CAC/EC,GAAG,CAACC,OAAO,IAAG;MACb;MACA,MAAMC,MAAM,GAAG,IAAI,CAACxG,OAAO,CAACyG,IAAI,CAACC,CAAC,IAAG;QACnC,MAAMC,QAAQ,GAAGD,CAAC,CAAClB,EAAE;QACrB,OAAOe,OAAO,CAACK,SAAS,CAACC,QAAQ,CAAC,oBAAoBF,QAAQ,IAAI,CAAC;MACrE,CAAC,CAAC;MACF,OAAOH,MAAM;IACf,CAAC,CAAC,CACDrF,MAAM,CAACqF,MAAM,IAAIA,MAAM,KAAKlE,SAAS,CAAsB;IAE9D;IACAlE,eAAe,CAAC6H,cAAc,EAAEH,KAAK,CAACC,aAAa,EAAED,KAAK,CAACE,YAAY,CAAC;IAExE;IACA,IAAI5F,QAAQ,GAAG,CAAC;IAEhB;IACA6F,cAAc,CAACa,OAAO,CAACC,aAAa,IAAG;MACrC,MAAMP,MAAM,GAAG,IAAI,CAACxG,OAAO,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,aAAa,CAACvB,EAAE,CAAC;MAChE,IAAIgB,MAAM,EAAE;QACVA,MAAM,CAACpG,QAAQ,GAAGA,QAAQ,EAAE;;IAEhC,CAAC,CAAC;IAEF;IACA,IAAI,CAACJ,OAAO,CACTmB,MAAM,CAACqF,MAAM,IAAI,CAACP,cAAc,CAACe,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACzB,EAAE,KAAKgB,MAAM,CAAChB,EAAE,CAAC,CAAC,CACjEsB,OAAO,CAACN,MAAM,IAAG;MAChBA,MAAM,CAACpG,QAAQ,GAAGA,QAAQ,EAAE;IAC9B,CAAC,CAAC;IAEJ;IACA,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;IAEpD;IACA,IAAI,CAAC8G,mBAAmB,EAAE;EAC5B;EAEA;;;EAGAC,sBAAsB,CAACR,QAAgB;IACrC,MAAMH,MAAM,GAAG,IAAI,CAACxG,OAAO,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKmB,QAAQ,CAAC;IACxD,IAAIH,MAAM,EAAE;MACVA,MAAM,CAACY,OAAO,GAAG,CAACZ,MAAM,CAACY,OAAO;MAChC,IAAI,CAACF,mBAAmB,EAAE;;EAE9B;EAEA;;;EAGAG,eAAe,CAACV,QAAgB;IAC9B,MAAMH,MAAM,GAAG,IAAI,CAACxG,OAAO,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKmB,QAAQ,CAAC;IACxD,OAAOH,MAAM,GAAGA,MAAM,CAACY,OAAO,GAAG,IAAI;EACvC;EAEA;;;EAGAF,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAACnH,eAAe,EAAE;MACzB;;IAGF,IAAI,CAACA,eAAe,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI,CAACA,OAAO,CAAC;IAChD,IAAI,CAACD,eAAe,CAACuH,YAAY,GAAG,IAAIC,IAAI,EAAE;IAE9C,IAAI,CAAC3I,sBAAsB,CAACsI,mBAAmB,CAAC,IAAI,CAACnH,eAAe,CAAC,CAACf,SAAS,CAAC;MAC9Ea,IAAI,EAAGC,MAAM,IAAI;QACfQ,OAAO,CAACkH,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC;MACDnH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE;KACD,CAAC;EACJ;EAEA;;;EAGAE,oBAAoB;IAClB,IAAIkH,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAAC7I,sBAAsB,CAAC2B,oBAAoB,EAAE,CAACvB,SAAS,CAAC;QAC3Da,IAAI,EAAGC,MAAM,IAAI;UACf,IAAI,CAACC,eAAe,GAAGD,MAAM;UAC7B,IAAI,CAACE,OAAO,GAAG,CAAC,GAAGF,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC;UAC1E,IAAI,CAACwF,QAAQ,GAAG,KAAK;UACrB,IAAI,CAAC9D,aAAa,EAAE;QACtB,CAAC;QACDzB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACrE;OACD,CAAC;;EAEN;EAEA;;;;;EAKAqH,mBAAmB,CAACC,KAAuC,EAAEpE,KAAa;IACxE;IACA,IAAIoE,KAAK,YAAYC,iBAAiB,EAAE;MACtC;MACA,MAAMC,aAAa,GAAG3J,KAAK,CAAC4J,QAAQ,CAACH,KAAK,CAAC;MAC3C,IAAI,CAAC9I,iBAAiB,CAACkJ,cAAc,CAACF,aAAa,IAAI,IAAI,EAAEtE,KAAK,CAAC;KACpE,MAAM;MACL,IAAI,CAAC1E,iBAAiB,CAACkJ,cAAc,CAACJ,KAAK,EAAEpE,KAAK,CAAC;;EAEvD;EAEA;;;EAGQ5C,YAAY;IAClB;IACA,MAAMqH,WAAW,GAA2B;MAC1C,iBAAiB,EAAE,0FAA0F,GAC3G,gIAAgI,GAChI,qEAAqE;MACvE,WAAW,EAAE,yGAAyG;MACtH,iBAAiB,EAAE,uEAAuE,GACxF,sGAAsG,GACtG,iEAAiE;MACnE,iBAAiB,EAAE,uDAAuD,GACxE,mGAAmG,GACnG,+DAA+D;MACjE,aAAa,EAAE,yFAAyF,GACtG,+EAA+E,GAC/E;KACH;IAED;IACA5B,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAACS,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMC,OAAO,GAAGC,SAAS,CAACC,OAAO,CAACC,WAAW,CAACJ,EAAE,CAAC;MACjD,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACI,OAAO,EAAE;;IAErB,CAAC,CAAC;IAEF;IACAlC,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAACS,OAAO,CAACmB,EAAE,IAAG;MACnE,MAAMM,QAAQ,GAAGN,EAAE,CAACO,YAAY,CAAC,gBAAgB,CAAC;MAClDlI,OAAO,CAACkH,GAAG,CAAC,kDAAkD,EAAEe,QAAQ,CAAC;MAEzE,IAAIA,QAAQ,IAAIA,QAAQ,IAAIP,WAAW,EAAE;QACvC,IAAI;UACF,IAAIG,SAAS,CAACC,OAAO,CAACH,EAAE,EAAE;YACxBQ,OAAO,EAAET,WAAW,CAACO,QAAoC,CAAC;YAC1DG,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,OAAO;YAChBC,SAAS,EAAE,KAAK;YAChBC,SAAS,EAAE;WACZ,CAAC;SACH,CAAC,OAAOxI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;OAEtD,MAAM,IAAIkI,QAAQ,EAAE;QACnBjI,OAAO,CAACwI,IAAI,CAAC,kCAAkC,EAAEP,QAAQ,CAAC;;IAE9D,CAAC,CAAC;EACJ;;;uBAztBWhK,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAwK;MAAAC;QAAA;;;;;;;;;;;;;;;;;;;UCvB/B3K,8BAAuB;UAEfA,yBAAS;UAAAA,iBAAK;UAClBA,8BAAoC;UAE4FA;YAAA,OAAS4K,iBAAa,CAAC,CAAC;UAAA,EAAC;UAAC5K,qBAAK;UAAAA,iBAAS;UACpKA,iCAAsJ;UAA1BA;YAAA,OAAS4K,iBAAa,CAAC,CAAC;UAAA,EAAC;UAAC5K,0BAAK;UAAAA,iBAAS;UACpKA,kCAAyJ;UAA3BA;YAAA,OAAS4K,iBAAa,EAAE,CAAC;UAAA,EAAC;UAAC5K,4BAAM;UAAAA,iBAAS;UACxKA,kCAAyJ;UAA3BA;YAAA,OAAS4K,iBAAa,EAAE,CAAC;UAAA,EAAC;UAAC5K,4BAAM;UAAAA,iBAAS;UAE1KA,+BAAoC;UACMA;YAAA,OAAS4K,mBAAe;UAAA,EAAC;UAC/D5K,wBAA0C;UAACA,gCAAiC;UAAAA,6BAAY;UAAAA,iBAAO;UAEjGA,kCAAsH;UAA3BA;YAAA,OAAS4K,oBAAgB;UAAA,EAAC;UACnH5K,wBAAuF;UACvFA,gCAAiC;UAAAA,aAAwD;UAAAA,iBAAO;UAElGA,4EAES;UACXA,iBAAM;UAIVA,sEAGM;UAENA,sEAIM;UAENA,sEAEM;UAKNA,gCAAqG;UAAxEA;YAAA,OAAsB4K,kBAAc;UAAA,EAAC;UAEhE5K,uEAwBM;UAGNA,uEAwBM;UAGNA,uEAwBM;UAGNA,uEAwBM;UACRA,iBAAM;UAGNA,uEAoDM;UACRA,iBAAM;;;UA1MoCA,eAAwC;UAAxCA,qDAAwC;UACxCA,eAAwC;UAAxCA,qDAAwC;UACxCA,eAAyC;UAAzCA,sDAAyC;UACzCA,eAAyC;UAAzCA,sDAAyC;UAMvDA,eAAuC;UAAvCA,oDAAuC;UAC3CA,eAAoE;UAApEA,kFAAoE;UACjDA,eAAwD;UAAxDA,sFAAwD;UAElFA,eAAc;UAAdA,mCAAc;UAOvBA,eAAc;UAAdA,mCAAc;UAKdA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAOkDA,eAAiC;UAAjCA,mDAAiC;UAEtEA,eAAkC;UAAlCA,uDAAkC;UA2BlCA,eAAwC;UAAxCA,6DAAwC;UA2BxCA,eAAwC;UAAxCA,6DAAwC;UA2BxCA,eAAmD;UAAnDA,4EAAmD;UA4B/DA,eAA+C;UAA/CA,wEAA+C", "names": ["Chart", "registerables", "moveItemInArray", "i0", "register", "DashboardComponent", "constructor", "chartService", "authService", "alertService", "dashboardConfigService", "chartModalService", "monitoringService", "currentUser", "subscribe", "user", "isAdmin", "savedDateRange", "localStorage", "getItem", "selectedDays", "parseInt", "ngOnInit", "loadDashboardConfig", "loadAlerts", "loadSystemStatistics", "getUserDashboardConfig", "next", "config", "dashboardConfig", "widgets", "sort", "a", "b", "position", "error", "console", "resetDashboardConfig", "ngAfterViewInit", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initPopovers", "ngOnDestroy", "updateSubscription", "unsubscribe", "<PERSON><PERSON><PERSON><PERSON>", "getDashboardAlerts", "data", "alerts", "filter", "alert", "isResolved", "err", "getSystemStatistics", "systemStatistics", "loading", "loadApiCallsChart", "loadApiPerformanceChart", "loadInstancesUsageChart", "loadSecurityEventsChart", "refresh<PERSON><PERSON><PERSON>", "apiCallsChart", "destroy", "apiPerformanceChart", "instancesUsageChart", "securityEventsChart", "getApiCallsChartData", "selectedInstanceId", "undefined", "labels", "datasets", "update", "apiCallsChartRef", "nativeElement", "type", "label", "backgroundColor", "borderColor", "borderWidth", "tension", "fill", "options", "responsive", "maintainAspectRatio", "plugins", "title", "display", "text", "font", "size", "legend", "tooltip", "mode", "intersect", "scales", "y", "beginAtZero", "x", "getApiPerformanceChartData", "avgData", "maxData", "apiPerformanceChartRef", "getInstancesUsageChartData", "instancesUsageChartRef", "indexAxis", "getSecurityEventsChartData", "securityEventsChartRef", "onDaysChange", "days", "setItem", "toString", "onInstanceChange", "instanceId", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "resolution", "prompt", "resolveDashboardAlert", "id", "getAlertTypeText", "alertType", "toggleEditMode", "editMode", "onDrop", "event", "previousIndex", "currentIndex", "visibleWidgets", "Array", "from", "document", "querySelectorAll", "map", "element", "widget", "find", "w", "widgetId", "innerHTML", "includes", "for<PERSON>ach", "visibleWidget", "some", "vw", "saveDashboardConfig", "toggleWidgetVisibility", "visible", "isWidgetVisible", "lastModified", "Date", "log", "confirm", "openFullscreenChart", "chart", "HTMLCanvasElement", "chartInstance", "<PERSON><PERSON><PERSON>", "openChartModal", "helpContent", "el", "popover", "bootstrap", "Popover", "getInstance", "dispose", "helpType", "getAttribute", "content", "html", "trigger", "placement", "container", "warn", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';\nimport { ChartService } from '../services/chart.service';\nimport { AuthService } from '../services/auth.service';\nimport { AlertService } from '../services/alert.service';\nimport { DashboardConfigService } from '../services/dashboard-config.service';\nimport { ChartModalService } from '../services/chart-modal.service';\nimport { MonitoringService } from '../services/monitoring.service';\nimport { Chart, registerables } from 'chart.js';\nimport { Subscription, interval } from 'rxjs';\nimport { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';\nimport { DashboardConfig, DashboardWidget } from '../models/dashboard-config.model';\n\n// Registrace všech komponent Chart.js\nChart.register(...registerables);\n\n// Import Bootstrap pro popover\ndeclare var bootstrap: any;\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('apiCallsChart') apiCallsChartRef!: ElementRef;\n  @ViewChild('apiPerformanceChart') apiPerformanceChartRef!: ElementRef;\n  @ViewChild('instancesUsageChart') instancesUsageChartRef!: ElementRef;\n  @ViewChild('securityEventsChart') securityEventsChartRef!: ElementRef;\n\n  loading = true;\n  error: string | null = null;\n  currentUser: any;\n  isAdmin = false;\n  alerts: any[] = [];\n  systemStatistics: any = {};\n\n  // Grafy\n  apiCallsChart: Chart | null = null;\n  apiPerformanceChart: Chart | null = null;\n  instancesUsageChart: Chart | null = null;\n  securityEventsChart: Chart | null = null;\n\n  // Filtry\n  selectedDays = 30;\n  selectedInstanceId: number | null = null;\n\n  // Dashboard konfigurace\n  dashboardConfig: DashboardConfig | null = null;\n  widgets: DashboardWidget[] = [];\n  editMode = false;\n\n  // Aktualizace dat\n  private updateSubscription: Subscription | null = null;\n\n  constructor(\n    private chartService: ChartService,\n    private authService: AuthService,\n    private alertService: AlertService,\n    private dashboardConfigService: DashboardConfigService,\n    private chartModalService: ChartModalService,\n    private monitoringService: MonitoringService\n  ) {\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n\n    // Načtení uložené hodnoty počtu dní z local storage\n    const savedDateRange = localStorage.getItem('monitoring_dateRange');\n    if (savedDateRange) {\n      this.selectedDays = parseInt(savedDateRange, 10);\n    }\n  }\n\n  ngOnInit(): void {\n    // Načtení konfigurace dashboardu\n    this.loadDashboardConfig();\n\n    this.loadAlerts();\n    this.loadSystemStatistics();\n  }\n\n  /**\n   * Načte konfiguraci dashboardu pro aktuálního uživatele\n   */\n  loadDashboardConfig(): void {\n    this.dashboardConfigService.getUserDashboardConfig().subscribe({\n      next: (config) => {\n        this.dashboardConfig = config;\n        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n      },\n      error: (error) => {\n        console.error('Chyba při načítání konfigurace dashboardu', error);\n        // Použijeme výchozí konfiguraci\n        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n        });\n      }\n    });\n  }\n\n  ngAfterViewInit(): void {\n    // Inicializace grafů po načtení view\n    setTimeout(() => {\n      this.initCharts();\n      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten\n      setTimeout(() => {\n        this.initPopovers();\n      }, 300);\n    }, 500);\n  }\n\n  ngOnDestroy(): void {\n    // Zrušení subscription při zničení komponenty\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n\n    // Zničení grafů\n    this.destroyCharts();\n  }\n\n  /**\n   * Načtení upozornění\n   */\n  loadAlerts(): void {\n    this.alertService.getDashboardAlerts().subscribe({\n      next: (data) => {\n        this.alerts = data.filter((alert: any) => !alert.isResolved);\n      },\n      error: (err) => {\n        console.error('Chyba při načítání upozornění', err);\n        this.error = 'Nepodařilo se načíst upozornění';\n      }\n    });\n  }\n\n  /**\n   * Načtení systémových statistik\n   */\n  loadSystemStatistics(): void {\n    this.monitoringService.getSystemStatistics().subscribe({\n      next: (data) => {\n        this.systemStatistics = data;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání systémových statistik', err);\n        this.error = 'Nepodařilo se načíst systémové statistiky';\n      }\n    });\n  }\n\n  /**\n   * Inicializace všech grafů\n   */\n  initCharts(): void {\n    this.loading = true;\n\n    // Načtení dat pro grafy\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n\n    this.loading = false;\n  }\n\n  /**\n   * Aktualizace všech grafů\n   */\n  refreshCharts(): void {\n    this.loadApiCallsChart();\n    this.loadApiPerformanceChart();\n    this.loadInstancesUsageChart();\n\n    if (this.isAdmin) {\n      this.loadSecurityEventsChart();\n    }\n\n    this.loadAlerts();\n    this.loadSystemStatistics();\n  }\n\n  /**\n   * Zničení všech grafů\n   */\n  destroyCharts(): void {\n    if (this.apiCallsChart) {\n      this.apiCallsChart.destroy();\n      this.apiCallsChart = null;\n    }\n\n    if (this.apiPerformanceChart) {\n      this.apiPerformanceChart.destroy();\n      this.apiPerformanceChart = null;\n    }\n\n    if (this.instancesUsageChart) {\n      this.instancesUsageChart.destroy();\n      this.instancesUsageChart = null;\n    }\n\n    if (this.securityEventsChart) {\n      this.securityEventsChart.destroy();\n      this.securityEventsChart = null;\n    }\n  }\n\n  /**\n   * Načtení grafu API volání\n   */\n  loadApiCallsChart(): void {\n    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.apiCallsChart) {\n          this.apiCallsChart.data.labels = data.labels;\n          this.apiCallsChart.data.datasets[0].data = data.data;\n          this.apiCallsChart.update();\n        } else if (this.apiCallsChartRef) {\n          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet API volání',\n                data: data.data,\n                backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n                borderWidth: 2,\n                tension: 0.3,\n                fill: true\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf API volání', err);\n        this.error = 'Nepodařilo se načíst data pro graf API volání';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu výkonu API\n   */\n  loadApiPerformanceChart(): void {\n    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.apiPerformanceChart) {\n          this.apiPerformanceChart.data.labels = data.labels;\n          this.apiPerformanceChart.data.datasets[0].data = data.avgData;\n          this.apiPerformanceChart.data.datasets[1].data = data.maxData;\n          this.apiPerformanceChart.update();\n        } else if (this.apiPerformanceChartRef) {\n          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [\n                {\n                  label: 'Průměrná doba odezvy (ms)',\n                  data: data.avgData,\n                  backgroundColor: 'rgba(54, 162, 235, 0.5)',\n                  borderColor: 'rgba(54, 162, 235, 1)',\n                  borderWidth: 1\n                },\n                {\n                  label: 'Maximální doba odezvy (ms)',\n                  data: data.maxData,\n                  backgroundColor: 'rgba(255, 99, 132, 0.5)',\n                  borderColor: 'rgba(255, 99, 132, 1)',\n                  borderWidth: 1\n                }\n              ]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Doba odezvy (ms)'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Endpoint'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf výkonu API', err);\n        this.error = 'Nepodařilo se načíst data pro graf výkonu API';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu využití instancí\n   */\n  loadInstancesUsageChart(): void {\n    this.chartService.getInstancesUsageChartData().subscribe({\n      next: (data) => {\n        if (this.instancesUsageChart) {\n          this.instancesUsageChart.data.labels = data.labels;\n          this.instancesUsageChart.data.datasets[0].data = data.data;\n          this.instancesUsageChart.update();\n        } else if (this.instancesUsageChartRef) {\n          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {\n            type: 'bar',\n            data: {\n              labels: data.labels,\n              datasets: [{\n                label: 'Počet API volání',\n                data: data.data,\n                backgroundColor: [\n                  'rgba(255, 99, 132, 0.5)',\n                  'rgba(54, 162, 235, 0.5)',\n                  'rgba(255, 206, 86, 0.5)',\n                  'rgba(75, 192, 192, 0.5)',\n                  'rgba(153, 102, 255, 0.5)',\n                  'rgba(255, 159, 64, 0.5)',\n                  'rgba(199, 199, 199, 0.5)',\n                  'rgba(83, 102, 255, 0.5)',\n                  'rgba(40, 159, 64, 0.5)',\n                  'rgba(210, 199, 199, 0.5)'\n                ],\n                borderColor: [\n                  'rgba(255, 99, 132, 1)',\n                  'rgba(54, 162, 235, 1)',\n                  'rgba(255, 206, 86, 1)',\n                  'rgba(75, 192, 192, 1)',\n                  'rgba(153, 102, 255, 1)',\n                  'rgba(255, 159, 64, 1)',\n                  'rgba(199, 199, 199, 1)',\n                  'rgba(83, 102, 255, 1)',\n                  'rgba(40, 159, 64, 1)',\n                  'rgba(210, 199, 199, 1)'\n                ],\n                borderWidth: 1\n              }]\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              indexAxis: 'y',\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: false\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                x: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet volání'\n                  }\n                },\n                y: {\n                  title: {\n                    display: true,\n                    text: 'Instance'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf využití instancí', err);\n        this.error = 'Nepodařilo se načíst data pro graf využití instancí';\n      }\n    });\n  }\n\n  /**\n   * Načtení grafu bezpečnostních událostí\n   */\n  loadSecurityEventsChart(): void {\n    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({\n      next: (data) => {\n        if (this.securityEventsChart) {\n          this.securityEventsChart.data.labels = data.labels;\n          this.securityEventsChart.data.datasets = data.datasets;\n          this.securityEventsChart.update();\n        } else if (this.securityEventsChartRef) {\n          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {\n            type: 'line',\n            data: {\n              labels: data.labels,\n              datasets: data.datasets\n            },\n            options: {\n              responsive: true,\n              maintainAspectRatio: false,\n              plugins: {\n                title: {\n                  display: true,\n                  text: data.title,\n                  font: {\n                    size: 16\n                  }\n                },\n                legend: {\n                  display: true,\n                  position: 'top'\n                },\n                tooltip: {\n                  mode: 'index',\n                  intersect: false\n                }\n              },\n              scales: {\n                y: {\n                  beginAtZero: true,\n                  title: {\n                    display: true,\n                    text: 'Počet událostí'\n                  }\n                },\n                x: {\n                  title: {\n                    display: true,\n                    text: 'Datum'\n                  }\n                }\n              }\n            }\n          });\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);\n        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';\n      }\n    });\n  }\n\n  /**\n   * Změna filtru dnů\n   */\n  onDaysChange(days: number): void {\n    this.selectedDays = days;\n\n    // Uložení vybraného počtu dní do local storage\n    localStorage.setItem('monitoring_dateRange', days.toString());\n\n    this.refreshCharts();\n  }\n\n  /**\n   * Změna filtru instance\n   */\n  onInstanceChange(instanceId: number | null): void {\n    this.selectedInstanceId = instanceId;\n    this.refreshCharts();\n  }\n\n  /**\n   * Vyřešení upozornění\n   */\n  resolveAlert(alertId: number): void {\n    const resolution = prompt('Zadejte řešení upozornění:');\n    if (resolution !== null) {\n      this.alertService.resolveDashboardAlert(alertId, resolution).subscribe({\n        next: () => {\n          this.alerts = this.alerts.filter(a => a.id !== alertId);\n        },\n        error: (err) => {\n          console.error('Chyba při řešení upozornění', err);\n          this.error = 'Nepodařilo se vyřešit upozornění';\n        }\n      });\n    }\n  }\n\n  /**\n   * Získání textu pro typ alertu\n   */\n  getAlertTypeText(alertType: string): string {\n    switch (alertType) {\n      case 'SecurityBreach':\n        return 'Bezpečnostní incident';\n      case 'CertificateExpiration':\n        return 'Expirace certifikátu';\n      case 'FailedConnectionAttempts':\n        return 'Selhané připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      default:\n        return alertType;\n    }\n  }\n\n  /**\n   * Přepnutí režimu úprav dashboardu\n   */\n  toggleEditMode(): void {\n    this.editMode = !this.editMode;\n  }\n\n  /**\n   * Zpracování přetažení widgetu\n   */\n  onDrop(event: CdkDragDrop<DashboardWidget[]>): void {\n    if (event.previousIndex === event.currentIndex) {\n      return;\n    }\n\n    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM\n    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]'))\n      .map(element => {\n        // Získáme ID widgetu z atributu *ngIf\n        const widget = this.widgets.find(w => {\n          const widgetId = w.id;\n          return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);\n        });\n        return widget;\n      })\n      .filter(widget => widget !== undefined) as DashboardWidget[];\n\n    // Provedeme přesun v poli viditelných widgetů\n    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);\n\n    // Aktualizujeme pozice všech widgetů\n    let position = 0;\n\n    // Nejprve aktualizujeme pozice viditelných widgetů\n    visibleWidgets.forEach(visibleWidget => {\n      const widget = this.widgets.find(w => w.id === visibleWidget.id);\n      if (widget) {\n        widget.position = position++;\n      }\n    });\n\n    // Poté aktualizujeme pozice skrytých widgetů\n    this.widgets\n      .filter(widget => !visibleWidgets.some(vw => vw.id === widget.id))\n      .forEach(widget => {\n        widget.position = position++;\n      });\n\n    // Seřadíme widgety podle pozice\n    this.widgets.sort((a, b) => a.position - b.position);\n\n    // Uložení konfigurace\n    this.saveDashboardConfig();\n  }\n\n  /**\n   * Změna viditelnosti widgetu\n   */\n  toggleWidgetVisibility(widgetId: string): void {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    if (widget) {\n      widget.visible = !widget.visible;\n      this.saveDashboardConfig();\n    }\n  }\n\n  /**\n   * Kontrola viditelnosti widgetu\n   */\n  isWidgetVisible(widgetId: string): boolean {\n    const widget = this.widgets.find(w => w.id === widgetId);\n    return widget ? widget.visible : true;\n  }\n\n  /**\n   * Uložení konfigurace dashboardu\n   */\n  saveDashboardConfig(): void {\n    if (!this.dashboardConfig) {\n      return;\n    }\n\n    this.dashboardConfig.widgets = [...this.widgets];\n    this.dashboardConfig.lastModified = new Date();\n\n    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({\n      next: (config) => {\n        console.log('Konfigurace dashboardu byla úspěšně uložena');\n      },\n      error: (error) => {\n        console.error('Chyba při ukládání konfigurace dashboardu', error);\n      }\n    });\n  }\n\n  /**\n   * Reset konfigurace dashboardu na výchozí hodnoty\n   */\n  resetDashboardConfig(): void {\n    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {\n      this.dashboardConfigService.resetDashboardConfig().subscribe({\n        next: (config) => {\n          this.dashboardConfig = config;\n          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);\n          this.editMode = false;\n          this.refreshCharts();\n        },\n        error: (error) => {\n          console.error('Chyba při resetování konfigurace dashboardu', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Otevře modální okno s grafem v režimu \"full screen\"\n   * @param chart Instance grafu nebo reference na canvas element\n   * @param title Titulek grafu\n   */\n  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {\n    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart\n    if (chart instanceof HTMLCanvasElement) {\n      // Najdeme instanci Chart pro daný canvas element\n      const chartInstance = Chart.getChart(chart);\n      this.chartModalService.openChartModal(chartInstance || null, title);\n    } else {\n      this.chartModalService.openChartModal(chart, title);\n    }\n  }\n\n  /**\n   * Inicializace popoverů pro nápovědu\n   */\n  private initPopovers(): void {\n    // Definice obsahu nápověd\n    const helpContent: Record<string, string> = {\n      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' +\n        'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' +\n        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',\n      'api-calls': 'Graf zobrazuje počet API volání v čase. Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',\n      'instances-usage': 'Graf zobrazuje využití jednotlivých instancí podle počtu API volání. ' +\n        'Ukazuje, které instance jsou nejvíce zatížené a pomáhá identifikovat nerovnoměrné rozložení zátěže. ' +\n        'Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',\n      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' +\n        'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' +\n        'Data jsou získávána z tabulky SecurityLogs za zvolené období.',\n      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' +\n        'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' +\n        'Poskytuje rychlý přehled o celkovém stavu systému.'\n    };\n\n    // Nejprve zrušíme všechny existující popovery\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n      console.log('Initializing popover for element with help-type:', helpType);\n\n      if (helpType && helpType in helpContent) {\n        try {\n          new bootstrap.Popover(el, {\n            content: helpContent[helpType as keyof typeof helpContent],\n            html: true,\n            trigger: 'hover',\n            placement: 'top',\n            container: 'body'\n          });\n        } catch (error) {\n          console.error('Error initializing popover:', error);\n        }\n      } else if (helpType) {\n        console.warn('Help content not found for type:', helpType);\n      }\n    });\n  }\n\n\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Dashboard</h2>\n    <div class=\"d-flex gap-2 flex-wrap\">\n      <div class=\"btn-group\" role=\"group\">\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 1\" [class.btn-outline-primary]=\"selectedDays !== 1\" (click)=\"onDaysChange(1)\">1 den</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 7\" [class.btn-outline-primary]=\"selectedDays !== 7\" (click)=\"onDaysChange(7)\">7 dní</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 30\" [class.btn-outline-primary]=\"selectedDays !== 30\" (click)=\"onDaysChange(30)\">30 dní</button>\n        <button type=\"button\" class=\"btn\" [class.btn-primary]=\"selectedDays === 90\" [class.btn-outline-primary]=\"selectedDays !== 90\" (click)=\"onDaysChange(90)\">90 dní</button>\n      </div>\n      <div class=\"d-flex gap-2 flex-wrap\">\n        <button class=\"btn btn-outline-primary\" (click)=\"refreshCharts()\">\n          <i class=\"bi bi-arrow-clockwise me-1\"></i> <span class=\"d-none d-sm-inline\">Aktualizovat</span>\n        </button>\n        <button class=\"btn\" [class.btn-outline-warning]=\"!editMode\" [class.btn-warning]=\"editMode\" (click)=\"toggleEditMode()\">\n          <i class=\"bi\" [ngClass]=\"{'bi-pencil-square': !editMode, 'bi-check-lg': editMode}\"></i>\n          <span class=\"d-none d-sm-inline\">{{ editMode ? 'Dokončit úpravy' : 'Upravit dashboard' }}</span>\n        </button>\n        <button *ngIf=\"editMode\" class=\"btn btn-outline-danger\" (click)=\"resetDashboardConfig()\">\n          <i class=\"bi bi-arrow-counterclockwise me-1\"></i> <span class=\"d-none d-sm-inline\">Resetovat</span>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div *ngIf=\"editMode\" class=\"alert alert-info mb-4\">\n    <i class=\"bi bi-info-circle-fill me-2\"></i>\n    Nyní můžete přetáhnout widgety a změnit jejich pořadí. Kliknutím na tlačítko \"Dokončit úpravy\" uložíte změny.\n  </div>\n\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger\">\n    {{ error }}\n  </div>\n\n\n\n  <!-- Grafy -->\n  <div cdkDropList class=\"row\" (cdkDropListDropped)=\"onDrop($event)\" [cdkDropListDisabled]=\"!editMode\">\n    <!-- Graf API volání -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('api-calls')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"api-calls\" style=\"cursor: help;\">API volání v čase</h5>\n          <div class=\"d-flex align-items-center\">\n            <button class=\"btn btn-sm btn-outline-info me-2\" (click)=\"openFullscreenChart(apiCallsChart, 'API volání v čase')\" title=\"Zobrazit graf na celou obrazovku\">\n              <i class=\"bi bi-arrows-fullscreen\"></i>\n            </button>\n            <div *ngIf=\"editMode\" class=\"widget-controls\">\n              <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('api-calls')\">\n                <i class=\"bi bi-eye-slash\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #apiCallsChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf výkonu API -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('api-performance')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"api-performance\" style=\"cursor: help;\">Odezva DIS metod</h5>\n          <div class=\"d-flex align-items-center\">\n            <button class=\"btn btn-sm btn-outline-info me-2\" (click)=\"openFullscreenChart(apiPerformanceChart, 'Odezva DIS metod')\" title=\"Zobrazit graf na celou obrazovku\">\n              <i class=\"bi bi-arrows-fullscreen\"></i>\n            </button>\n            <div *ngIf=\"editMode\" class=\"widget-controls\">\n              <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('api-performance')\">\n                <i class=\"bi bi-eye-slash\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #apiPerformanceChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf využití instancí -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isWidgetVisible('instances-usage')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"instances-usage\" style=\"cursor: help;\">Využití instancí</h5>\n          <div class=\"d-flex align-items-center\">\n            <button class=\"btn btn-sm btn-outline-info me-2\" (click)=\"openFullscreenChart(instancesUsageChart, 'Využití instancí')\" title=\"Zobrazit graf na celou obrazovku\">\n              <i class=\"bi bi-arrows-fullscreen\"></i>\n            </button>\n            <div *ngIf=\"editMode\" class=\"widget-controls\">\n              <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('instances-usage')\">\n                <i class=\"bi bi-eye-slash\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #instancesUsageChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n\n    <!-- Graf bezpečnostních událostí -->\n    <div class=\"col-lg-6 mb-4\" *ngIf=\"isAdmin && isWidgetVisible('security-events')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n      <div class=\"card h-100\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"security-events\" style=\"cursor: help;\">Bezpečnostní události</h5>\n          <div class=\"d-flex align-items-center\">\n            <button class=\"btn btn-sm btn-outline-info me-2\" (click)=\"openFullscreenChart(securityEventsChart, 'Bezpečnostní události')\" title=\"Zobrazit graf na celou obrazovku\">\n              <i class=\"bi bi-arrows-fullscreen\"></i>\n            </button>\n            <div *ngIf=\"editMode\" class=\"widget-controls\">\n              <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('security-events')\">\n                <i class=\"bi bi-eye-slash\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"chart-container\" style=\"position: relative; height: 300px;\">\n            <canvas #securityEventsChart></canvas>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Systémové informace -->\n  <div class=\"row\" *ngIf=\"isAdmin && isWidgetVisible('system-info')\" cdkDrag [cdkDragDisabled]=\"!editMode\">\n    <div class=\"col-12 mb-4\">\n      <div class=\"card\" [class.draggable-card]=\"editMode\">\n        <div class=\"card-header d-flex justify-content-between align-items-center\">\n          <h5 class=\"mb-0\" data-bs-toggle=\"popover\" data-help-type=\"system-info\" style=\"cursor: help;\">Systémové informace</h5>\n          <div *ngIf=\"editMode\" class=\"widget-controls\">\n            <button class=\"btn btn-sm btn-outline-secondary\" (click)=\"toggleWidgetVisibility('system-info')\">\n              <i class=\"bi bi-eye-slash\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"row\">\n            <div class=\"col-6 col-md-3 mb-3\">\n              <div class=\"card bg-primary text-white\">\n                <div class=\"card-body text-center\">\n                  <h3 class=\"display-4\">{{ alerts.length }}</h3>\n                  <p class=\"mb-0\">Aktivních upozornění</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-3\">\n              <div class=\"card bg-success text-white\">\n                <div class=\"card-body text-center\">\n                  <h3 class=\"display-4\">{{ systemStatistics.ApiAvailability || 100 }}%</h3>\n                  <p class=\"mb-0\">Dostupnost API</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-3\">\n              <div class=\"card bg-info text-white\">\n                <div class=\"card-body text-center\">\n                  <h3 class=\"display-4\">{{ systemStatistics.AvgApiResponseTime || 0 }}ms</h3>\n                  <p class=\"mb-0\">Průměrná odezva DIS metod</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-3\">\n              <div class=\"card bg-warning text-dark\">\n                <div class=\"card-body text-center\">\n                  <h3 class=\"display-4\">{{ systemStatistics.ExpiringCertificatesCount || 0 }}</h3>\n                  <p class=\"mb-0\">Expirující certifikáty</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div *ngIf=\"editMode\" class=\"drag-handle\" cdkDragHandle>\n          <i class=\"bi bi-grip-horizontal\"></i>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}