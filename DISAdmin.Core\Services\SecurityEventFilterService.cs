using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

/// <summary>
/// Služba pro správu filtrů bezpečnostních událostí
/// </summary>
public class SecurityEventFilterService
{
    private readonly DISAdminDbContext _context;
    private readonly CachingService _cachingService;
    private readonly ILogger<SecurityEventFilterService> _logger;

    // Konstanty pro klíče keše
    private const string CACHE_KEY_ALL_FILTERS = "securityeventfilters:all";

    public SecurityEventFilterService(
        DISAdminDbContext context,
        CachingService cachingService,
        ILogger<SecurityEventFilterService> logger)
    {
        _context = context;
        _cachingService = cachingService;
        _logger = logger;
    }

    /// <summary>
    /// Získá všechny aktivní filtry
    /// </summary>
    public async Task<List<SecurityEventFilter>> GetAllFiltersAsync()
    {
        return await _cachingService.GetOrCreateAsync(
            CACHE_KEY_ALL_FILTERS,
            async () =>
            {
                return await _context.SecurityEventFilters
                    .OrderByDescending(f => f.CreatedAt)
                    .ToListAsync();
            },
            30, // 30 minut absolutní expirace
            15  // 15 minut sliding expirace
        ) ?? new List<SecurityEventFilter>();
    }

    /// <summary>
    /// Vytvoří nový filtr
    /// </summary>
    public async Task<SecurityEventFilter> CreateFilterAsync(SecurityEventType eventType, string? description, string? ipAddress, string createdBy)
    {
        var filter = new SecurityEventFilter
        {
            EventType = eventType,
            Description = description,
            IpAddress = ipAddress,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = createdBy
        };

        _context.SecurityEventFilters.Add(filter);
        await _context.SaveChangesAsync();

        // Invalidace keše
        await InvalidateFiltersCache();

        _logger.LogInformation("Vytvořen nový filtr bezpečnostních událostí: {EventType}, {Description}, {IpAddress} od {CreatedBy}",
            eventType, description, ipAddress, createdBy);

        return filter;
    }

    /// <summary>
    /// Smaže filtr podle ID
    /// </summary>
    public async Task<bool> DeleteFilterAsync(int filterId)
    {
        var filter = await _context.SecurityEventFilters.FindAsync(filterId);
        if (filter == null)
        {
            return false;
        }

        _context.SecurityEventFilters.Remove(filter);
        await _context.SaveChangesAsync();

        // Invalidace keše
        await InvalidateFiltersCache();

        _logger.LogInformation("Smazán filtr bezpečnostních událostí: {FilterId}", filterId);

        return true;
    }

    /// <summary>
    /// Zkontroluje, zda bezpečnostní událost odpovídá některému z filtrů
    /// </summary>
    public async Task<bool> ShouldFilterEventAsync(SecurityEventType eventType, string? description, string? ipAddress)
    {
        var filters = await GetAllFiltersAsync();

        foreach (var filter in filters)
        {
            // Přesná shoda pro EventType
            if (filter.EventType != eventType)
                continue;

            // Kontrola Description (case-insensitive obsahuje)
            if (!string.IsNullOrEmpty(filter.Description) && !string.IsNullOrEmpty(description))
            {
                if (!description.Contains(filter.Description, StringComparison.OrdinalIgnoreCase))
                    continue;
            }
            else if (!string.IsNullOrEmpty(filter.Description) && string.IsNullOrEmpty(description))
            {
                continue;
            }

            // Kontrola IpAddress (case-insensitive obsahuje)
            if (!string.IsNullOrEmpty(filter.IpAddress) && !string.IsNullOrEmpty(ipAddress))
            {
                if (!ipAddress.Contains(filter.IpAddress, StringComparison.OrdinalIgnoreCase))
                    continue;
            }
            else if (!string.IsNullOrEmpty(filter.IpAddress) && string.IsNullOrEmpty(ipAddress))
            {
                continue;
            }

            // Pokud jsme se dostali sem, událost odpovídá filtru
            _logger.LogDebug("Bezpečnostní událost filtrována: {EventType}, {Description}, {IpAddress}", 
                eventType, description, ipAddress);
            return true;
        }

        return false;
    }

    /// <summary>
    /// Smaže existující bezpečnostní události podle vzoru
    /// </summary>
    public async Task<int> DeleteEventsByPatternAsync(SecurityEventType eventType, string? description, string? ipAddress)
    {
        var query = _context.SecurityEvents.Where(e => e.EventType == eventType);

        // Filtrování podle Description (case-insensitive obsahuje)
        if (!string.IsNullOrEmpty(description))
        {
            query = query.Where(e => e.Description.ToLower().Contains(description.ToLower()));
        }

        // Filtrování podle IpAddress (case-insensitive obsahuje)
        if (!string.IsNullOrEmpty(ipAddress))
        {
            query = query.Where(e => e.IpAddress.ToLower().Contains(ipAddress.ToLower()));
        }

        var eventsToDelete = await query.ToListAsync();
        var count = eventsToDelete.Count;

        if (count > 0)
        {
            _context.SecurityEvents.RemoveRange(eventsToDelete);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Smazáno {Count} bezpečnostních událostí podle vzoru: {EventType}, {Description}, {IpAddress}",
                count, eventType, description, ipAddress);
        }

        return count;
    }

    /// <summary>
    /// Invaliduje keš filtrů
    /// </summary>
    private async Task InvalidateFiltersCache()
    {
        await _cachingService.RemoveAsync(CACHE_KEY_ALL_FILTERS);
    }
}
