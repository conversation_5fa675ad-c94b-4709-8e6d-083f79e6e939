using System.Collections.Concurrent;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

public class SecurityMonitoringService
{
    private readonly DISAdminDbContext _context;
    private readonly SecurityEventFilterService _filterService;
    private readonly ILogger<SecurityMonitoringService> _logger;

    // Cache pro sledování pokusů o přístup
    private readonly ConcurrentDictionary<string, List<DateTime>> _accessAttempts = new();

    // Nastavení limitů
    private readonly int _maxFailedAttempts = 5;
    private readonly TimeSpan _failedAttemptsWindow = TimeSpan.FromMinutes(15);
    private readonly int _maxRequestsPerMinute = 60;

    // Cache pro blokované IP adresy
    private readonly ConcurrentDictionary<string, DateTime> _blockedIps = new();

    public SecurityMonitoringService(DISAdminDbContext context, SecurityEventFilterService filterService, ILogger<SecurityMonitoringService> logger)
    {
        _context = context;
        _filterService = filterService;
        _logger = logger;
    }

    /// <summary>
    /// Kontroluje, zda IP adresa není blokována
    /// </summary>
    public bool IsIpBlocked(string ipAddress)
    {
        if (_blockedIps.TryGetValue(ipAddress, out var blockedUntil))
        {
            if (blockedUntil > DateTime.UtcNow)
            {
                return true;
            }

            // Odblokování IP adresy po uplynutí času
            _blockedIps.TryRemove(ipAddress, out _);
        }

        return false;
    }

    /// <summary>
    /// Zaznamenává neúspěšný pokus o přístup
    /// </summary>
    public async Task RecordFailedAccessAttempt(string ipAddress, string? username, string resource)
    {
        // Zaznamenání pokusu do cache
        if (!_accessAttempts.TryGetValue(ipAddress, out var attempts))
        {
            attempts = new List<DateTime>();
            _accessAttempts[ipAddress] = attempts;
        }

        attempts.Add(DateTime.UtcNow);

        // Odstranění starých pokusů
        var cutoffTime = DateTime.UtcNow.Subtract(_failedAttemptsWindow);
        attempts.RemoveAll(a => a < cutoffTime);

        // Kontrola počtu pokusů
        if (attempts.Count >= _maxFailedAttempts)
        {
            await BlockIpAddress(ipAddress, TimeSpan.FromHours(1));
            _logger.LogWarning("IP address {IpAddress} blocked for 1 hour due to too many failed access attempts", ipAddress);
        }

        // Zaznamenání pokusu do databáze
        var description = $"Failed access attempt from {ipAddress}" + (username != null ? $" for user {username}" : "");

        // Kontrola, zda událost odpovídá některému z filtrů
        var shouldFilter = await _filterService.ShouldFilterEventAsync(
            SecurityEventType.FailedAccessAttempt, description, ipAddress);

        if (!shouldFilter)
        {
            var securityEvent = new SecurityEvent
            {
                Timestamp = DateTime.UtcNow,
                EventType = SecurityEventType.FailedAccessAttempt,
                IpAddress = ipAddress,
                Username = username,
                Resource = resource,
                Description = description
            };

            _context.SecurityEvents.Add(securityEvent);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Kontroluje, zda počet požadavků nepřekračuje limit
    /// </summary>
    public bool CheckRateLimit(string ipAddress)
    {
        if (!_accessAttempts.TryGetValue(ipAddress, out var attempts))
        {
            attempts = new List<DateTime>();
            _accessAttempts[ipAddress] = attempts;
        }

        // Odstranění starých požadavků
        var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromMinutes(1));
        attempts.RemoveAll(a => a < cutoffTime);

        // Přidání nového požadavku
        attempts.Add(DateTime.UtcNow);

        // Kontrola počtu požadavků
        return attempts.Count <= _maxRequestsPerMinute;
    }

    /// <summary>
    /// Blokuje IP adresu na určitou dobu
    /// </summary>
    public async Task BlockIpAddress(string ipAddress, TimeSpan duration)
    {
        _blockedIps[ipAddress] = DateTime.UtcNow.Add(duration);

        // Zaznamenání blokování do databáze
        var description = $"IP address {ipAddress} blocked for {duration.TotalHours} hours";

        // Kontrola, zda událost odpovídá některému z filtrů
        var shouldFilter = await _filterService.ShouldFilterEventAsync(
            SecurityEventType.IpBlocked, description, ipAddress);

        if (!shouldFilter)
        {
            var securityEvent = new SecurityEvent
            {
                Timestamp = DateTime.UtcNow,
                EventType = SecurityEventType.IpBlocked,
                IpAddress = ipAddress,
                Description = description
            };

            _context.SecurityEvents.Add(securityEvent);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Detekuje podezřelé chování na základě analýzy logů
    /// </summary>
    public async Task<IEnumerable<SecurityEvent>> DetectSuspiciousActivity()
    {
        var suspiciousEvents = new List<SecurityEvent>();
        var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(24));

        // 1. Detekce velkého počtu neúspěšných přihlášení z jedné IP adresy
        var failedLogins = await _context.ActivityLogs
            .Where(a => a.ActivityType == ActivityType.Login && a.Timestamp > cutoffTime)
            .GroupBy(a => a.IpAddress)
            .Select(g => new { IpAddress = g.Key, Count = g.Count() })
            .Where(g => g.Count > 10)
            .ToListAsync();

        foreach (var ip in failedLogins)
        {
            var description = $"High number of failed login attempts ({ip.Count}) from IP {ip.IpAddress}";

            // Kontrola, zda událost odpovídá některému z filtrů
            var shouldFilter = await _filterService.ShouldFilterEventAsync(
                SecurityEventType.SuspiciousActivity, description, ip.IpAddress);

            if (!shouldFilter)
            {
                var securityEvent = new SecurityEvent
                {
                    Timestamp = DateTime.UtcNow,
                    EventType = SecurityEventType.SuspiciousActivity,
                    IpAddress = ip.IpAddress,
                    Description = description
                };

                suspiciousEvents.Add(securityEvent);
                _context.SecurityEvents.Add(securityEvent);
            }
        }

        // 2. Detekce přístupu k API z neobvyklých IP adres
        var apiAccesses = await _context.ActivityLogs
            .Where(a => a.ActivityType == ActivityType.ApiAccess && a.Timestamp > cutoffTime)
            .GroupBy(a => a.IpAddress)
            .Select(g => new { IpAddress = g.Key, Count = g.Count() })
            .ToListAsync();

        // Získání seznamu známých IP adres instancí
        var knownIps = await _context.DISInstances
            .Where(i => i.LastKnownIpAddress != null)
            .Select(i => i.LastKnownIpAddress)
            .ToListAsync();

        foreach (var ip in apiAccesses)
        {
            if (!knownIps.Contains(ip.IpAddress))
            {
                var description = $"API access from unknown IP address {ip.IpAddress}";

                // Kontrola, zda událost odpovídá některému z filtrů
                var shouldFilter = await _filterService.ShouldFilterEventAsync(
                    SecurityEventType.SuspiciousActivity, description, ip.IpAddress);

                if (!shouldFilter)
                {
                    var securityEvent = new SecurityEvent
                    {
                        Timestamp = DateTime.UtcNow,
                        EventType = SecurityEventType.SuspiciousActivity,
                        IpAddress = ip.IpAddress,
                        Description = description
                    };

                    suspiciousEvents.Add(securityEvent);
                    _context.SecurityEvents.Add(securityEvent);
                }
            }
        }

        // 3. Detekce přístupu k certifikátům mimo pracovní dobu
        var certificateAccesses = await _context.ActivityLogs
            .Where(a => a.EntityName == "Certificate" && a.Timestamp > cutoffTime)
            .ToListAsync();

        foreach (var access in certificateAccesses)
        {
            var hour = access.Timestamp.Hour;
            if (hour < 6 || hour > 22) // Mimo 6:00 - 22:00
            {
                var description = $"Certificate access outside business hours by {access.Username} from {access.IpAddress}";

                // Kontrola, zda událost odpovídá některému z filtrů
                var shouldFilter = await _filterService.ShouldFilterEventAsync(
                    SecurityEventType.SuspiciousActivity, description, access.IpAddress);

                if (!shouldFilter)
                {
                    var securityEvent = new SecurityEvent
                    {
                        Timestamp = DateTime.UtcNow,
                        EventType = SecurityEventType.SuspiciousActivity,
                        IpAddress = access.IpAddress,
                        Username = access.Username,
                        Description = description
                    };

                    suspiciousEvents.Add(securityEvent);
                    _context.SecurityEvents.Add(securityEvent);
                }
            }
        }

        await _context.SaveChangesAsync();
        return suspiciousEvents;
    }
}
