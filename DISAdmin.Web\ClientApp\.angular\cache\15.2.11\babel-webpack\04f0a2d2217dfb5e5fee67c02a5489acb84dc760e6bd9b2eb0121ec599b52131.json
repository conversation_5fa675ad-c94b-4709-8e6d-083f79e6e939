{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { InstanceStatus } from '../../models/instance.model';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/customer.service\";\nimport * as i3 from \"../../services/contact.service\";\nimport * as i4 from \"../../services/instance.service\";\nimport * as i5 from \"../../services/instance-version.service\";\nimport * as i6 from \"../../services/version.service\";\nimport * as i7 from \"../../services/user.service\";\nimport * as i8 from \"../../services/certificate.service\";\nimport * as i9 from \"../../services/breadcrumb.service\";\nimport * as i10 from \"../../services/modal.service\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"../../services/auth.service\";\nimport * as i13 from \"../../services/clipboard.service\";\nimport * as i14 from \"@angular/common\";\nimport * as i15 from \"../../shared/certificate-modal/certificate-modal.component\";\nimport * as i16 from \"../../shared/instance-detail/instance-detail.component\";\nimport * as i17 from \"../../shared/pipes/local-date.pipe\";\nfunction CustomerDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.deleteCustomer());\n    });\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵtext(3, \" Smazat z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.editCustomer());\n    });\n    i0.ɵɵelement(5, \"i\", 84);\n    i0.ɵɵtext(6, \" Upravit z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_11_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_11_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r30.customerForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r30.customerForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zkratka je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zkratka nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_16_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_16_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r31.customerForm.get(\"abbreviation\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r31.customerForm.get(\"abbreviation\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_22_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"I\\u010CO nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_22_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r32.customerForm.get(\"companyId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_27_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"DI\\u010C nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_27_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r33.customerForm.get(\"taxId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_33_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_33_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r34.customerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r34.customerForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_38_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Telefon nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_38_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r35.customerForm.get(\"phone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_44_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Web nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_44_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r36.customerForm.get(\"website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Ulice je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Ulice nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_51_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_51_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r37.customerForm.get(\"street\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r37.customerForm.get(\"street\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"M\\u011Bsto je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"M\\u011Bsto nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_57_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_57_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r38.customerForm.get(\"city\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r38.customerForm.get(\"city\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"PS\\u010C je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"PS\\u010C nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_62_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_62_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r39.customerForm.get(\"postalCode\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r39.customerForm.get(\"postalCode\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zem\\u011B je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zem\\u011B nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_67_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_67_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r40.customerForm.get(\"country\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r40.customerForm.get(\"country\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_72_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_72_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r41.customerForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction CustomerDetailComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90)(2, \"h5\", 91);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"form\", 18);\n    i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_div_10_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.saveCustomer());\n    });\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"div\", 20)(8, \"label\", 46);\n    i0.ɵɵtext(9, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 93);\n    i0.ɵɵtemplate(11, CustomerDetailComponent_div_10_div_11_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"label\", 94);\n    i0.ɵɵtext(14, \"Zkratka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 95);\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_10_div_16_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"div\", 20)(19, \"label\", 96);\n    i0.ɵɵtext(20, \"I\\u010CO\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 97);\n    i0.ɵɵtemplate(22, CustomerDetailComponent_div_10_div_22_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 20)(24, \"label\", 98);\n    i0.ɵɵtext(25, \"DI\\u010C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 99);\n    i0.ɵɵtemplate(27, CustomerDetailComponent_div_10_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"label\", 100);\n    i0.ɵɵtext(31, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 101);\n    i0.ɵɵtemplate(33, CustomerDetailComponent_div_10_div_33_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 20)(35, \"label\", 102);\n    i0.ɵɵtext(36, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 103);\n    i0.ɵɵtemplate(38, CustomerDetailComponent_div_10_div_38_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 20)(41, \"label\", 104);\n    i0.ɵɵtext(42, \"Web\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 105);\n    i0.ɵɵtemplate(44, CustomerDetailComponent_div_10_div_44_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"div\", 106)(48, \"label\", 107);\n    i0.ɵɵtext(49, \"Ulice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"input\", 108);\n    i0.ɵɵtemplate(51, CustomerDetailComponent_div_10_div_51_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 19)(53, \"div\", 109)(54, \"label\", 110);\n    i0.ɵɵtext(55, \"M\\u011Bsto\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 111);\n    i0.ɵɵtemplate(57, CustomerDetailComponent_div_10_div_57_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 109)(59, \"label\", 112);\n    i0.ɵɵtext(60, \"PS\\u010C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"input\", 113);\n    i0.ɵɵtemplate(62, CustomerDetailComponent_div_10_div_62_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 109)(64, \"label\", 114);\n    i0.ɵɵtext(65, \"Zem\\u011B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"input\", 115);\n    i0.ɵɵtemplate(67, CustomerDetailComponent_div_10_div_67_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 26)(69, \"label\", 55);\n    i0.ɵɵtext(70, \"Pozn\\u00E1mky\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"textarea\", 56);\n    i0.ɵɵtemplate(72, CustomerDetailComponent_div_10_div_72_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 38)(74, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_10_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.cancelEdit());\n    });\n    i0.ɵɵtext(75, \"Zru\\u0161it\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_10_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.saveCustomer());\n    });\n    i0.ɵɵtemplate(77, CustomerDetailComponent_div_10_span_77_Template, 1, 0, \"span\", 41);\n    i0.ɵɵtext(78, \" Ulo\\u017Eit \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    let tmp_17_0;\n    let tmp_18_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.mode === \"create\" ? \"P\\u0159idat z\\u00E1kazn\\u00EDka\" : \"Upravit z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.customerForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r3.customerForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r3.customerForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ((tmp_3_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ((tmp_5_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_5_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ((tmp_7_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_7_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r3.customerForm.get(\"email\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r3.customerForm.get(\"email\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r3.customerForm.get(\"phone\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r3.customerForm.get(\"phone\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ((tmp_11_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_11_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r3.customerForm.get(\"street\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r3.customerForm.get(\"street\")) == null ? null : tmp_13_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r3.customerForm.get(\"city\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx_r3.customerForm.get(\"city\")) == null ? null : tmp_14_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r3.customerForm.get(\"postalCode\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r3.customerForm.get(\"postalCode\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r3.customerForm.get(\"country\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r3.customerForm.get(\"country\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ((tmp_17_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_17_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_18_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.customerForm.invalid || ctx_r3.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.saving);\n  }\n}\nfunction CustomerDetailComponent_div_11_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"I\\u010CO:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r66.customer.companyId, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"DI\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r67.customer.taxId, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r68.customer.email, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Telefon:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.customer.phone, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Web:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r70 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r70.customer.website, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 2)(2, \"p\")(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozn\\u00E1mky:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r71.customer.notes);\n  }\n}\nfunction CustomerDetailComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Detail z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"div\", 119)(6, \"div\", 120)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Zkratka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CustomerDetailComponent_div_11_p_13_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(14, CustomerDetailComponent_div_11_p_14_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_11_p_15_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_11_p_16_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(17, CustomerDetailComponent_div_11_p_17_Template, 4, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 120)(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"Ulice:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25, \"M\\u011Bsto:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n    i0.ɵɵtext(29, \"PS\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\")(32, \"strong\");\n    i0.ɵɵtext(33, \"Zem\\u011B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\")(36, \"strong\");\n    i0.ɵɵtext(37, \"Vytvo\\u0159eno:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\")(41, \"strong\");\n    i0.ɵɵtext(42, \"Aktualizov\\u00E1no:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"localDate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, CustomerDetailComponent_div_11_div_45_Template, 7, 1, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.customer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.abbreviation, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.companyId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.taxId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.website);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.street, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.postalCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.country, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(39, 14, ctx_r4.customer.createdAt, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(44, 17, ctx_r4.customer.updatedAt, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.notes);\n  }\n}\nfunction CustomerDetailComponent_div_12_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r76.openAddContactModal());\n    });\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵtext(2, \" P\\u0159idat kontakt \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" Tento z\\u00E1kazn\\u00EDk nem\\u00E1 \\u017E\\u00E1dn\\u00E9 kontakty. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1, \"Ano\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 141);\n    i0.ɵɵtext(1, \"Ne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r86);\n      const contact_r79 = i0.ɵɵnextContext().$implicit;\n      const ctx_r84 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r84.editContact(contact_r79));\n    });\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const contact_r79 = i0.ɵɵnextContext().$implicit;\n      const ctx_r87 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r87.deleteContact(contact_r79));\n    });\n    i0.ɵɵelement(1, \"i\", 145);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, CustomerDetailComponent_div_12_div_8_tr_17_span_10_Template, 2, 0, \"span\", 135);\n    i0.ɵɵtemplate(11, CustomerDetailComponent_div_12_div_8_tr_17_span_11_Template, 2, 0, \"span\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 137);\n    i0.ɵɵtemplate(14, CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template, 2, 0, \"button\", 138);\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template, 2, 0, \"button\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r79 = ctx.$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", contact_r79.firstName, \" \", contact_r79.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.position || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.email || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.phone || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r79.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r79.isPrimary);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.isAdmin);\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"table\", 133)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, CustomerDetailComponent_div_12_div_8_tr_17_Template, 16, 9, \"tr\", 134);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r75.contacts);\n  }\n}\nfunction CustomerDetailComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 123)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Kontakty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerDetailComponent_div_12_button_4_Template, 3, 0, \"button\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92);\n    i0.ɵɵtemplate(6, CustomerDetailComponent_div_12_div_6_Template, 4, 0, \"div\", 125);\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_12_div_7_Template, 2, 0, \"div\", 126);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_12_div_8_Template, 18, 1, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loadingContacts && ctx_r5.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loadingContacts && ctx_r5.contacts.length > 0);\n  }\n}\nfunction CustomerDetailComponent_div_13_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r94 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r94.openAddInstanceModal());\n    });\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵtext(2, \" P\\u0159idat instanci \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" Tento z\\u00E1kazn\\u00EDk nem\\u00E1 \\u017E\\u00E1dn\\u00E9 instance DIS. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r97 = i0.ɵɵnextContext().$implicit;\n    const ctx_r98 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r98.instanceVersions[instance_r97.id][0].versionNumber || \"N/A\", \" \");\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"N/A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const instance_r97 = i0.ɵɵnextContext().$implicit;\n      const ctx_r102 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r102.editInstance(instance_r97));\n    });\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r106 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 146);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_13_div_8_tr_15_span_7_Template, 2, 1, \"span\", 6);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_13_div_8_tr_15_span_8_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 137)(14, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_div_8_tr_15_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r106);\n      const instance_r97 = restoredCtx.$implicit;\n      const ctx_r105 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r105.viewInstanceDetail(instance_r97));\n    });\n    i0.ɵɵelement(15, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template, 2, 0, \"button\", 138);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const instance_r97 = ctx.$implicit;\n    const ctx_r96 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r97.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r96.getInstanceStatusClass(instance_r97.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r96.getInstanceStatusName(instance_r97.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.instanceVersions[instance_r97.id] == null ? null : ctx_r96.instanceVersions[instance_r97.id].length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r96.instanceVersions[instance_r97.id] == null ? null : ctx_r96.instanceVersions[instance_r97.id].length));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", instance_r97.lastConnectionDate ? i0.ɵɵpipeBind2(11, 7, instance_r97.lastConnectionDate, \"dd.MM.yyyy HH:mm\") : \"Nikdy\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.isAdmin);\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"table\", 133)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Posledn\\u00ED p\\u0159ipojen\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_13_div_8_tr_15_Template, 17, 10, \"tr\", 134);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r93 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r93.instances);\n  }\n}\nfunction CustomerDetailComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 123)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Instance DIS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerDetailComponent_div_13_button_4_Template, 3, 0, \"button\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92);\n    i0.ɵɵtemplate(6, CustomerDetailComponent_div_13_div_6_Template, 4, 0, \"div\", 125);\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_13_div_7_Template, 2, 0, \"div\", 126);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_13_div_8_Template, 16, 1, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.loadingInstances && ctx_r6.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.loadingInstances && ctx_r6.instances.length > 0);\n  }\n}\nfunction CustomerDetailComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.contactError, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_29_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Jm\\u00E9no je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_29_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Jm\\u00E9no nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_29_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_29_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.contactForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r8.contactForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_34_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"P\\u0159\\u00EDjmen\\u00ED je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_34_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"P\\u0159\\u00EDjmen\\u00ED nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_34_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_34_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.contactForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.contactForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_39_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozice nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_39_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.contactForm.get(\"position\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_45_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_45_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_45_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_45_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r11.contactForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r11.contactForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_50_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Telefon nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_50_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.contactForm.get(\"phone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_55_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_55_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r13.contactForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nfunction CustomerDetailComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.instanceError, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_82_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_82_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_82_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_82_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r16.instanceForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r16.instanceForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_102_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"URL serveru je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_102_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"URL serveru nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_102_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_102_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r17.instanceForm.get(\"serverUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r17.instanceForm.get(\"serverUrl\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_111_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_111_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r18.instanceForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nfunction CustomerDetailComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.instanceVersionError, \" \");\n  }\n}\nfunction CustomerDetailComponent_option_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r121 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", version_r121.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(version_r121.versionNumber);\n  }\n}\nfunction CustomerDetailComponent_div_162_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Verze je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_162_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_162_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r22.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction CustomerDetailComponent_option_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r123 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r123.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", user_r123.firstName, \" \", user_r123.lastName, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_170_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"U\\u017Eivatel je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_170_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_170_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r24.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction CustomerDetailComponent_div_175_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_175_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r25.instanceVersionForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_180_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nconst _c1 = function () {\n  return [];\n};\nexport let CustomerDetailComponent = /*#__PURE__*/(() => {\n  class CustomerDetailComponent {\n    constructor(route, router, customerService, contactService, instanceService, instanceVersionService, versionService, userService, certificateService, breadcrumbService, modalService, formBuilder, authService, clipboardService) {\n      this.route = route;\n      this.router = router;\n      this.customerService = customerService;\n      this.contactService = contactService;\n      this.instanceService = instanceService;\n      this.instanceVersionService = instanceVersionService;\n      this.versionService = versionService;\n      this.userService = userService;\n      this.certificateService = certificateService;\n      this.breadcrumbService = breadcrumbService;\n      this.modalService = modalService;\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.clipboardService = clipboardService;\n      // Zpřístupnění enumu InstanceStatus pro šablonu\n      this.InstanceStatus = InstanceStatus;\n      this.customerId = 0;\n      this.customer = null;\n      this.contacts = [];\n      this.instances = [];\n      this.instanceVersions = {};\n      this.displayedInstanceVersions = {};\n      this.showAllVersions = {};\n      this.certificateInfo = {};\n      this.pendingInstanceId = null; // ID instance, kterou chceme otevřít po načtení dat\n      this.users = [];\n      this.versions = [];\n      // Režim komponenty (vytvoření, editace, zobrazení)\n      this.mode = 'view';\n      this.loading = false;\n      this.loadingContacts = false;\n      this.loadingInstances = false;\n      this.loadingVersions = false;\n      this.loadingUsers = false;\n      this.saving = false;\n      this.savingContact = false;\n      this.savingInstance = false;\n      this.savingInstanceVersion = false;\n      this.isEditMode = false;\n      this.isEditContactMode = false;\n      this.isEditInstanceMode = false;\n      this.isEditInstanceVersionMode = false;\n      this.error = null;\n      this.contactError = null;\n      this.instanceError = null;\n      this.instanceVersionError = null;\n      // Callback pro akce po načtení detailu\n      this.loadCustomerDetailCallback = null;\n      // Příznak, zda je přihlášený uživatel administrátor\n      this.isAdmin = false;\n      this.selectedContact = null;\n      this.selectedInstance = null;\n      this.selectedInstanceForVersion = null;\n      // Vygenerovaný certifikát\n      this.generatedCertificate = null;\n      // Inicializace formulářů\n      this.customerForm = this.formBuilder.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n        companyId: ['', Validators.maxLength(20)],\n        taxId: ['', Validators.maxLength(20)],\n        street: ['', [Validators.required, Validators.maxLength(255)]],\n        city: ['', [Validators.required, Validators.maxLength(255)]],\n        postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n        country: ['Česká republika', [Validators.required, Validators.maxLength(100)]],\n        email: ['', [Validators.email, Validators.maxLength(255)]],\n        phone: ['', Validators.maxLength(50)],\n        website: ['', Validators.maxLength(255)],\n        notes: ['', Validators.maxLength(500)]\n      });\n      this.contactForm = this.formBuilder.group({\n        firstName: ['', [Validators.required, Validators.maxLength(100)]],\n        lastName: ['', [Validators.required, Validators.maxLength(100)]],\n        position: ['', Validators.maxLength(100)],\n        email: ['', [Validators.email, Validators.maxLength(255)]],\n        phone: ['', Validators.maxLength(50)],\n        notes: ['', Validators.maxLength(100)],\n        isPrimary: [false]\n      });\n      this.instanceForm = this.formBuilder.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n        expirationDate: [''],\n        notes: ['', Validators.maxLength(500)],\n        status: ['Active'],\n        blockReason: ['', Validators.maxLength(500)],\n        moduleReporting: [true],\n        moduleAdvancedSecurity: [false],\n        moduleApiIntegration: [false],\n        moduleDataExport: [false],\n        moduleCustomization: [false]\n      });\n      this.instanceVersionForm = this.formBuilder.group({\n        versionId: ['', Validators.required],\n        installedByUserId: ['', Validators.required],\n        notes: ['', Validators.maxLength(500)]\n      });\n    }\n    ngOnInit() {\n      // Kontrola, zda je uživatel administrátor\n      this.isAdmin = this.authService.isAdmin();\n      // Načtení verzí a uživatelů pro formuláře\n      this.loadVersions();\n      this.loadUsers();\n      // Zjištění, zda jsme na cestě /customers/add\n      if (this.router.url === '/customers/add') {\n        // Jsme v režimu vytváření nového zákazníka\n        this.mode = 'create';\n        this.isEditMode = true;\n        // Nastavení breadcrumbs pro vytvoření\n        this.breadcrumbService.setBreadcrumbs([{\n          label: 'Zákazníci',\n          url: '/customers',\n          icon: 'building-fill'\n        }, {\n          label: 'Přidat zákazníka',\n          url: '/customers/add',\n          icon: 'plus-circle-fill'\n        }]);\n        // Reset formuláře\n        this.customerForm.reset({\n          country: 'Česká republika'\n        });\n      } else {\n        // Získání ID zákazníka z URL pro režim zobrazení nebo editace\n        this.route.params.subscribe(params => {\n          if (params['id']) {\n            this.customerId = +params['id'];\n            this.loadCustomerDetail(this.customerId);\n            // Kontrola, zda máme otevřít detail v režimu editace\n            this.route.queryParams.subscribe(queryParams => {\n              console.log('Query params:', queryParams);\n              if (queryParams['edit'] === 'true') {\n                console.log('Edit mode detected from query params');\n                this.mode = 'edit';\n                // Po načtení detailu přepneme do režimu editace\n                this.loadCustomerDetailCallback = () => {\n                  console.log('loadCustomerDetailCallback executing, customer:', this.customer);\n                  if (this.customer) {\n                    console.log('Calling editCustomer() from callback');\n                    this.editCustomer();\n                  } else {\n                    console.log('Customer data not available in callback');\n                  }\n                };\n              } else {\n                console.log('View mode set');\n                this.mode = 'view';\n              }\n            });\n            // Kontrola, zda máme otevřít detail instance (z localStorage)\n            try {\n              console.log('Kontroluji localStorage pro otevření detailu instance');\n              const openInstanceDetailJson = localStorage.getItem('openInstanceDetail');\n              console.log('Data z localStorage:', openInstanceDetailJson);\n              if (openInstanceDetailJson) {\n                const openInstanceDetail = JSON.parse(openInstanceDetailJson);\n                console.log('Parsovaná data:', openInstanceDetail);\n                // Kontrola, zda data v localStorage odpovídají aktuálnímu zákazníkovi\n                // a zda nejsou starší než 10 sekund\n                const currentTime = new Date().getTime();\n                const dataTime = openInstanceDetail.timestamp || 0;\n                const isRecent = currentTime - dataTime < 10000; // 10 sekund\n                console.log('Aktuální customerId:', this.customerId);\n                console.log('CustomerId z localStorage:', openInstanceDetail.customerId);\n                console.log('Je časově aktuální:', isRecent, '(rozdíl:', currentTime - dataTime, 'ms)');\n                if (openInstanceDetail.customerId === this.customerId && isRecent) {\n                  const instanceId = openInstanceDetail.instanceId;\n                  console.log('Nastavuji otevření instance ID:', instanceId);\n                  // Uložíme ID instance, kterou chceme otevřít\n                  this.pendingInstanceId = instanceId;\n                  // Odstraníme data z localStorage, aby se modální okno neotevíralo opakovaně\n                  localStorage.removeItem('openInstanceDetail');\n                  console.log('Data z localStorage byla odstraněna');\n                } else {\n                  console.log('Data v localStorage neodpovídají aktuálnímu zákazníkovi nebo nejsou aktuální');\n                }\n              } else {\n                console.log('Žádná data pro otevření instance v localStorage');\n              }\n            } catch (error) {\n              console.error('Chyba při zpracování dat z localStorage:', error);\n            }\n          }\n        });\n      }\n    }\n    /**\r\n     * Načtení detailu zákazníka\r\n     */\n    loadCustomerDetail(customerId) {\n      console.log('loadCustomerDetail() called with ID:', customerId);\n      this.loading = true;\n      this.loadingContacts = true;\n      this.loadingInstances = true;\n      this.customerService.getCustomer(customerId).pipe(first()).subscribe({\n        next: customer => {\n          console.log('Customer data loaded:', customer);\n          this.customer = customer;\n          // Nastavení breadcrumbs\n          this.breadcrumbService.setBreadcrumbs([{\n            label: 'Zákazníci',\n            url: '/customers',\n            icon: 'building-fill'\n          }, {\n            label: customer.name,\n            url: `/customers/${customerId}`,\n            icon: 'info-circle-fill'\n          }]);\n          this.loading = false;\n          // Načtení kontaktů zákazníka\n          this.loadContacts(customerId);\n          // Načtení instancí DIS zákazníka\n          this.loadInstances(customerId);\n          // Volat callback po načtení detailu, pokud existuje\n          if (this.loadCustomerDetailCallback) {\n            console.log('Executing loadCustomerDetailCallback');\n            this.loadCustomerDetailCallback();\n            this.loadCustomerDetailCallback = null; // Použít jen jednou\n          }\n        },\n\n        error: err => {\n          this.error = `Chyba při načítání zákazníka: ${err.message}`;\n          this.loading = false;\n          this.loadingContacts = false;\n          this.loadingInstances = false;\n        }\n      });\n    }\n    /**\r\n     * Načtení kontaktů zákazníka\r\n     */\n    loadContacts(customerId) {\n      this.contactService.getContactsByCustomerId(customerId).pipe(first()).subscribe({\n        next: contacts => {\n          this.contacts = contacts;\n          this.loadingContacts = false;\n        },\n        error: err => {\n          this.error = `Chyba při načítání kontaktů: ${err.message}`;\n          this.loadingContacts = false;\n        }\n      });\n    }\n    /**\r\n     * Načtení instancí DIS zákazníka\r\n     */\n    loadInstances(customerId) {\n      this.instanceService.getInstancesByCustomerId(customerId).pipe(first()).subscribe({\n        next: instances => {\n          this.instances = instances;\n          this.loadingInstances = false;\n          // Načtení verzí pro každou instanci\n          instances.forEach(instance => {\n            this.loadInstanceVersions(instance.id);\n          });\n          // Pokud máme čekající ID instance, otevřeme její detail\n          if (this.pendingInstanceId) {\n            console.log('Máme čekající ID instance:', this.pendingInstanceId);\n            // Najdeme instanci podle ID\n            const instance = instances.find(i => i.id === this.pendingInstanceId);\n            console.log('Nalezená instance:', instance);\n            if (instance) {\n              console.log('Otevírám modální okno s detailem instance:', instance.id);\n              // Počkáme, až se stránka načte\n              setTimeout(() => {\n                // Nastavíme vybranou instanci\n                this.selectedInstanceForVersion = instance;\n                // Načtení informací o certifikátu\n                this.loadCertificateInfo(instance.id);\n                // Otevřít modal přímo pomocí Bootstrap API\n                console.log('Otevírám modální okno přímo pomocí Bootstrap API');\n                const modalElement = document.getElementById('instanceDetailModal');\n                if (modalElement) {\n                  console.log('Modal element nalezen, otevírám...');\n                  const modal = new window.bootstrap.Modal(modalElement);\n                  modal.show();\n                } else {\n                  console.error('Modal element s ID instanceDetailModal nebyl nalezen');\n                }\n              }, 500);\n            } else {\n              console.log('Instance nebyla nalezena v seznamu načtených instancí');\n            }\n            // Resetujeme čekající ID instance\n            this.pendingInstanceId = null;\n          }\n        },\n        error: err => {\n          this.error = `Chyba při načítání instancí DIS: ${err.message}`;\n          this.loadingInstances = false;\n        }\n      });\n    }\n    /**\r\n     * Načtení verzí instance\r\n     */\n    loadInstanceVersions(instanceId) {\n      this.instanceVersionService.getInstanceVersions(instanceId).pipe(first()).subscribe({\n        next: versions => {\n          // Uložení všech verzí\n          this.instanceVersions[instanceId] = versions;\n          // Výchozí zobrazení pouze posledních 5 verzí\n          this.displayedInstanceVersions[instanceId] = versions.slice(0, 5);\n          // Výchozí stav zobrazení všech verzí\n          this.showAllVersions[instanceId] = false;\n        },\n        error: err => {\n          console.error(`Chyba při načítání verzí instance ${instanceId}:`, err);\n        }\n      });\n    }\n    /**\r\n     * Přepnutí zobrazení všech verzí instance\r\n     */\n    toggleAllVersions(instanceId) {\n      this.showAllVersions[instanceId] = !this.showAllVersions[instanceId];\n      if (this.showAllVersions[instanceId]) {\n        // Zobrazit všechny verze\n        this.displayedInstanceVersions[instanceId] = [...this.instanceVersions[instanceId]];\n      } else {\n        // Zobrazit pouze posledních 5 verzí\n        this.displayedInstanceVersions[instanceId] = this.instanceVersions[instanceId].slice(0, 5);\n      }\n    }\n    /**\r\n     * Načtení informací o certifikátu instance\r\n     */\n    loadCertificateInfo(instanceId) {\n      this.certificateService.getInstanceCertificateInfo(instanceId).pipe(first()).subscribe({\n        next: info => {\n          this.certificateInfo[instanceId] = info;\n        },\n        error: err => {\n          console.error(`Chyba při načítání informací o certifikátu instance ${instanceId}:`, err);\n        }\n      });\n    }\n    /**\r\n     * Načtení verzí pro formuláře\r\n     */\n    loadVersions() {\n      this.loadingVersions = true;\n      this.versionService.getVersions().pipe(first()).subscribe({\n        next: versions => {\n          this.versions = versions;\n          this.loadingVersions = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání verzí:', err);\n          this.loadingVersions = false;\n        }\n      });\n    }\n    /**\r\n     * Načtení uživatelů pro formuláře\r\n     */\n    loadUsers() {\n      this.loadingUsers = true;\n      this.userService.getUsers().pipe(first()).subscribe({\n        next: users => {\n          this.users = users;\n          this.loadingUsers = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání uživatelů:', err);\n          this.loadingUsers = false;\n        }\n      });\n    }\n    /**\r\n     * Přepnutí do režimu editace zákazníka\r\n     */\n    editCustomer() {\n      console.log('editCustomer() called');\n      if (!this.customer) {\n        console.log('No customer data available');\n        return;\n      }\n      console.log('Switching to edit mode for customer:', this.customer);\n      this.isEditMode = true;\n      this.mode = 'edit'; // Explicitně nastavíme režim na 'edit'\n      // Naplnění formuláře daty zákazníka\n      console.log('Patching form with customer data');\n      this.customerForm.patchValue({\n        name: this.customer.name,\n        abbreviation: this.customer.abbreviation,\n        companyId: this.customer.companyId,\n        taxId: this.customer.taxId,\n        street: this.customer.street,\n        city: this.customer.city,\n        postalCode: this.customer.postalCode,\n        country: this.customer.country,\n        email: this.customer.email,\n        phone: this.customer.phone,\n        website: this.customer.website,\n        notes: this.customer.notes\n      });\n      console.log('Form after patching:', this.customerForm.value);\n    }\n    /**\r\n     * Uložení změn zákazníka nebo vytvoření nového\r\n     */\n    saveCustomer() {\n      console.log('saveCustomer() called in customer-detail.component.ts');\n      console.log('Form valid:', this.customerForm.valid);\n      console.log('Form values:', this.customerForm.value);\n      console.log('Mode:', this.mode);\n      console.log('isEditMode:', this.isEditMode);\n      console.log('customer:', this.customer);\n      if (this.customerForm.invalid) {\n        console.log('Form is invalid, returning');\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.customerForm.controls).forEach(key => {\n          const control = this.customerForm.get(key);\n          control?.markAsTouched();\n          if (control?.invalid) {\n            console.log(`Field ${key} is invalid:`, control.errors);\n          }\n        });\n        return;\n      }\n      console.log('Form is valid, proceeding with save');\n      this.saving = true;\n      const formData = this.customerForm.value;\n      // Vytvoření objektu zákazníka z formuláře\n      const customerData = {\n        name: formData.name,\n        abbreviation: formData.abbreviation,\n        companyId: formData.companyId,\n        taxId: formData.taxId,\n        street: formData.street,\n        city: formData.city,\n        postalCode: formData.postalCode,\n        country: formData.country,\n        email: formData.email,\n        phone: formData.phone,\n        website: formData.website,\n        notes: formData.notes\n      };\n      if (this.mode === 'create') {\n        // Vytvoření nového zákazníka\n        this.customerService.createCustomer(customerData).pipe(first()).subscribe({\n          next: createdCustomer => {\n            this.saving = false;\n            // Přesměrování na detail nově vytvořeného zákazníka\n            this.router.navigate(['/customers', createdCustomer.id]);\n          },\n          error: err => {\n            this.error = `Chyba při vytváření zákazníka: ${err.message}`;\n            this.saving = false;\n          }\n        });\n      } else if (this.mode === 'edit' && this.customer) {\n        // Aktualizace existujícího zákazníka\n        console.log('Updating customer with ID:', this.customer.id);\n        console.log('Customer data to send:', customerData);\n        this.customerService.updateCustomer(this.customer.id, customerData).pipe(first()).subscribe({\n          next: response => {\n            console.log('Customer updated successfully:', response);\n            this.saving = false;\n            this.isEditMode = false;\n            this.loadCustomerDetail(this.customerId);\n          },\n          error: err => {\n            console.error('Error updating customer:', err);\n            this.error = `Chyba při ukládání zákazníka: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.saving = false;\n          }\n        });\n      }\n    }\n    /**\r\n     * Zrušení editace zákazníka\r\n     */\n    cancelEdit() {\n      if (this.mode === 'create') {\n        // Při vytváření nového zákazníka se vrátíme na seznam zákazníků\n        this.router.navigate(['/customers']);\n      } else {\n        // Při editaci existujícího zákazníka se vrátíme do režimu zobrazení\n        this.isEditMode = false;\n      }\n    }\n    /**\r\n     * Otevření modálu pro přidání kontaktu\r\n     */\n    openAddContactModal() {\n      this.isEditContactMode = false;\n      this.selectedContact = null;\n      this.contactError = null;\n      // Reset formuláře\n      this.contactForm.reset({\n        isPrimary: false\n      });\n      // Otevření modálu\n      this.modalService.open('contactModal');\n    }\n    /**\r\n     * Otevření modálu pro editaci kontaktu\r\n     */\n    editContact(contact) {\n      this.isEditContactMode = true;\n      this.selectedContact = contact;\n      this.contactError = null;\n      // Naplnění formuláře daty kontaktu\n      this.contactForm.patchValue({\n        firstName: contact.firstName,\n        lastName: contact.lastName,\n        position: contact.position || '',\n        email: contact.email || '',\n        phone: contact.phone || '',\n        notes: contact.notes || '',\n        isPrimary: contact.isPrimary\n      });\n      // Otevření modálu\n      this.modalService.open('contactModal');\n    }\n    /**\r\n     * Uložení kontaktu\r\n     */\n    saveContact() {\n      if (this.contactForm.invalid) {\n        return;\n      }\n      this.savingContact = true;\n      const formData = this.contactForm.value;\n      if (this.isEditContactMode && this.selectedContact) {\n        // Editace existujícího kontaktu\n        const updatedContact = {\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          position: formData.position,\n          email: formData.email,\n          phone: formData.phone,\n          notes: formData.notes,\n          isPrimary: formData.isPrimary\n        };\n        this.contactService.updateContact(this.selectedContact.id, updatedContact).pipe(first()).subscribe({\n          next: () => {\n            this.savingContact = false;\n            this.closeContactModal();\n            this.loadContacts(this.customerId);\n          },\n          error: err => {\n            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n            this.savingContact = false;\n          }\n        });\n      } else {\n        // Přidání nového kontaktu\n        const newContact = {\n          customerId: this.customerId,\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          position: formData.position,\n          email: formData.email,\n          phone: formData.phone,\n          notes: formData.notes,\n          isPrimary: formData.isPrimary\n        };\n        this.contactService.createContact(newContact).pipe(first()).subscribe({\n          next: () => {\n            this.savingContact = false;\n            this.closeContactModal();\n            this.loadContacts(this.customerId);\n          },\n          error: err => {\n            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n            this.savingContact = false;\n          }\n        });\n      }\n    }\n    /**\r\n     * Zavření modálu pro kontakty\r\n     */\n    closeContactModal() {\n      this.modalService.close('contactModal');\n    }\n    /**\r\n     * Smazání kontaktu\r\n     */\n    deleteContact(contact) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`, 'Smazání kontaktu', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this.contactService.deleteContact(contact.id).pipe(first()).subscribe({\n            next: () => {\n              _this.loadContacts(_this.customerId);\n            },\n            error: err => {\n              _this.error = `Chyba při mazání kontaktu: ${err.message}`;\n              _this.modalService.alert(`Chyba při mazání kontaktu: ${err.message}`, 'Chyba', 'Zavřít', 'btn-danger');\n            }\n          });\n        }\n      })();\n    }\n    /**\r\n     * Otevření modálu pro přidání instance\r\n     */\n    openAddInstanceModal() {\n      this.isEditInstanceMode = false;\n      this.selectedInstance = null;\n      this.instanceError = null;\n      // Reset formuláře\n      this.instanceForm.reset({\n        status: InstanceStatus.Active,\n        moduleReporting: true,\n        moduleAdvancedSecurity: false,\n        moduleApiIntegration: false,\n        moduleDataExport: false,\n        moduleCustomization: false\n      });\n      // Otevření modálu\n      this.modalService.open('instanceModal');\n    }\n    /**\r\n     * Otevření modálu pro editaci instance\r\n     */\n    editInstance(instance) {\n      this.isEditInstanceMode = true;\n      this.selectedInstance = instance;\n      this.instanceError = null;\n      // Naplnění formuláře daty instance\n      this.instanceForm.patchValue({\n        name: instance.name,\n        serverUrl: instance.serverUrl,\n        expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n        notes: instance.notes,\n        // Převod řetězcového statusu na číselnou hodnotu enumu\n        status: this.getStatusEnumValue(instance.status),\n        moduleReporting: instance.moduleReporting,\n        moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n        moduleApiIntegration: instance.moduleApiIntegration,\n        moduleDataExport: instance.moduleDataExport,\n        moduleCustomization: instance.moduleCustomization\n      });\n      // Otevření modálu\n      this.modalService.open('instanceModal');\n    }\n    /**\r\n     * Zobrazení detailu instance\r\n     */\n    viewInstanceDetail(instance) {\n      this.selectedInstanceForVersion = instance;\n      // Načtení verzí instance\n      this.loadInstanceVersions(instance.id);\n      // Načtení informací o certifikátu\n      this.loadCertificateInfo(instance.id);\n      // Otevřít modal\n      this.modalService.open('instanceDetailModal');\n    }\n    /**\r\n     * Zavření modálu pro detail instance\r\n     */\n    closeInstanceDetailModal() {\n      this.modalService.close('instanceDetailModal');\n    }\n    /**\r\n     * Editace instance z detailu instance\r\n     */\n    editInstanceFromDetail(instance) {\n      // Nejprve zavřeme modál s detailem instance\n      this.closeInstanceDetailModal();\n      // Poté otevřeme modál pro editaci instance\n      setTimeout(() => {\n        this.editInstance(instance);\n      }, 500); // Počkáme 500ms, aby se první modál stihl zavřít\n    }\n    /**\r\n     * Zavření modálu pro přidání/úpravu instance\r\n     */\n    closeInstanceModal() {\n      this.modalService.close('instanceModal');\n    }\n    /**\r\n     * Převod řetězcové hodnoty statusu na enum InstanceStatus\r\n     */\n    convertStatusToEnum(status) {\n      // Pokud je status číselná hodnota jako řetězec (např. \"3\"), převedeme ji na číslo\n      if (!isNaN(Number(status))) {\n        const numericStatus = Number(status);\n        return numericStatus;\n      }\n      // Jinak zpracujeme řetězcové hodnoty\n      switch (status) {\n        case 'Active':\n          return InstanceStatus.Active;\n        case 'Blocked':\n          return InstanceStatus.Blocked;\n        case 'Expired':\n          return InstanceStatus.Expired;\n        case 'Trial':\n          return InstanceStatus.Trial;\n        case 'Maintenance':\n          return InstanceStatus.Maintenance;\n        default:\n          return InstanceStatus.Active;\n        // Výchozí hodnota je Active\n      }\n    }\n    /**\r\n     * Převod řetězcového statusu na číselnou hodnotu enumu\r\n     */\n    getStatusEnumValue(status) {\n      if (typeof status === 'number') {\n        return status;\n      }\n      switch (status) {\n        case 'Active':\n          return InstanceStatus.Active;\n        case 'Blocked':\n          return InstanceStatus.Blocked;\n        case 'Expired':\n          return InstanceStatus.Expired;\n        case 'Trial':\n          return InstanceStatus.Trial;\n        case 'Maintenance':\n          return InstanceStatus.Maintenance;\n        default:\n          return InstanceStatus.Active;\n        // Výchozí hodnota je Active\n      }\n    }\n    /**\r\n     * Uložení instance\r\n     */\n    saveInstance() {\n      if (this.instanceForm.invalid) {\n        return;\n      }\n      this.savingInstance = true;\n      const formData = this.instanceForm.value;\n      console.log('Hodnoty formuláře před úpravou:', formData);\n      if (this.isEditInstanceMode && this.selectedInstance) {\n        // Editace existující instance\n        const updatedInstance = {\n          name: formData.name,\n          serverUrl: formData.serverUrl,\n          expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n          notes: formData.notes,\n          status: this.convertStatusToEnum(formData.status),\n          moduleReporting: formData.moduleReporting,\n          moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n          moduleApiIntegration: formData.moduleApiIntegration,\n          moduleDataExport: formData.moduleDataExport,\n          moduleCustomization: formData.moduleCustomization\n        };\n        console.log('Odesílaná data instance:', updatedInstance);\n        this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance).pipe(first()).subscribe({\n          next: response => {\n            console.log('Instance úspěšně aktualizována:', response);\n            this.savingInstance = false;\n            this.closeInstanceModal();\n            this.loadInstances(this.customerId);\n          },\n          error: err => {\n            console.error('Chyba při ukládání instance:', err);\n            this.instanceError = `Chyba při ukládání instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.savingInstance = false;\n          }\n        });\n      } else {\n        // Přidání nové instance\n        const newInstance = {\n          customerId: this.customerId,\n          name: formData.name,\n          serverUrl: formData.serverUrl,\n          expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n          notes: formData.notes,\n          status: this.convertStatusToEnum(formData.status),\n          moduleReporting: formData.moduleReporting,\n          moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n          moduleApiIntegration: formData.moduleApiIntegration,\n          moduleDataExport: formData.moduleDataExport,\n          moduleCustomization: formData.moduleCustomization\n        };\n        console.log('Odesílaná data nové instance:', newInstance);\n        this.instanceService.createInstance(newInstance).pipe(first()).subscribe({\n          next: response => {\n            console.log('Instance úspěšně vytvořena:', response);\n            this.savingInstance = false;\n            this.closeInstanceModal();\n            this.loadInstances(this.customerId);\n          },\n          error: err => {\n            console.error('Chyba při vytváření instance:', err);\n            this.instanceError = `Chyba při vytváření instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.savingInstance = false;\n          }\n        });\n      }\n    }\n    /**\r\n     * Smazání zákazníka\r\n     */\n    deleteCustomer() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.customer) return;\n        const confirmed = yield _this2.modalService.confirm(`Opravdu chcete smazat zákazníka ${_this2.customer.name}?`, 'Smazání zákazníka', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this2.loading = true;\n          _this2.customerService.deleteCustomer(_this2.customer.id).pipe(first()).subscribe({\n            next: () => {\n              _this2.router.navigate(['/customers']);\n            },\n            error: err => {\n              _this2.error = `Chyba při mazání zákazníka: ${err.message}`;\n              _this2.loading = false;\n              _this2.modalService.alert(`Chyba při mazání zákazníka: ${err.message}`, 'Chyba', 'Zavřít', 'btn-danger');\n            }\n          });\n        }\n      })();\n    }\n    /**\r\n     * Návrat na seznam zákazníků\r\n     */\n    goBack() {\n      this.router.navigate(['/customers']);\n    }\n    /**\r\n     * Kopírování API klíče do schránky\r\n     */\n    copyApiKey(inputElement) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        yield _this3.clipboardService.copyFromInput(inputElement, 'API klíč byl zkopírován do schránky', 'Nepodařilo se zkopírovat API klíč');\n      })();\n    }\n    /**\r\n     * Generování nového certifikátu pro instanci\r\n     */\n    generateCertificate(instanceId) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const confirmed = yield _this4.modalService.confirm('Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.', 'Generování certifikátu', 'Generovat', 'Zrušit', 'btn-success', 'btn-secondary');\n        if (!confirmed) {\n          return;\n        }\n        _this4.certificateService.generateCertificate(instanceId).subscribe({\n          next: response => {\n            // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n            // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n            const certificatePassword = response.password || 'password';\n            _this4.generatedCertificate = {\n              certificate: response.certificate,\n              privateKey: response.privateKey,\n              thumbprint: response.thumbprint,\n              expirationDate: response.expirationDate,\n              password: certificatePassword,\n              certificatePassword: certificatePassword\n            };\n            // Aktualizace informací o certifikátu\n            _this4.loadCertificateInfo(instanceId);\n            // Explicitní nastavení hesla pro zobrazení v modálním okně\n            const passwordElement = document.getElementById('certificatePassword');\n            if (passwordElement && _this4.generatedCertificate) {\n              passwordElement.textContent = _this4.generatedCertificate.password || 'Heslo není k dispozici';\n            }\n            // Zobrazení modálního okna s informacemi o certifikátu\n            _this4.modalService.open('certificateGeneratedModal');\n          },\n          error: err => {\n            console.error('Chyba při generování certifikátu', err);\n            _this4.modalService.alert(`Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      })();\n    }\n    /**\r\n     * Stažení vygenerovaného certifikátu\r\n     */\n    downloadCertificate() {\n      if (!this.generatedCertificate) {\n        return;\n      }\n      // Vytvoření a stažení souboru .pfx\n      const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    }\n    /**\r\n     * Pomocná metoda pro konverzi Base64 na Blob\r\n     */\n    base64ToBlob(base64, contentType) {\n      const byteCharacters = atob(base64);\n      const byteArrays = [];\n      for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      return new Blob(byteArrays, {\n        type: contentType\n      });\n    }\n    /**\r\n     * Zavření modálního okna s certifikátem\r\n     */\n    closeCertificateModal() {\n      this.modalService.close('certificateGeneratedModal');\n    }\n    /**\r\n     * Otevření modálního okna pro přidání verze instance\r\n     */\n    openAddInstanceVersionModal() {\n      if (!this.selectedInstanceForVersion) {\n        console.error('Není vybrána žádná instance');\n        return;\n      }\n      this.isEditInstanceVersionMode = false;\n      this.instanceVersionError = null;\n      // Získání ID aktuálně přihlášeného uživatele\n      const currentUserId = this.authService.getCurrentUserId();\n      // Reset formuláře s předvyplněným aktuálním uživatelem\n      this.instanceVersionForm.reset({\n        versionId: '',\n        installedByUserId: currentUserId,\n        notes: ''\n      });\n      // Otevření modálu\n      this.modalService.open('instanceVersionModal');\n    }\n    /**\r\n     * Zavření modálního okna pro přidání/úpravu verze instance\r\n     */\n    closeInstanceVersionModal() {\n      this.modalService.close('instanceVersionModal');\n    }\n    /**\r\n     * Uložení verze instance\r\n     */\n    saveInstanceVersion() {\n      if (this.instanceVersionForm.invalid) {\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.instanceVersionForm.controls).forEach(key => {\n          const control = this.instanceVersionForm.get(key);\n          control?.markAsTouched();\n        });\n        return;\n      }\n      if (!this.selectedInstanceForVersion) {\n        this.instanceVersionError = 'Není vybrána žádná instance.';\n        return;\n      }\n      this.savingInstanceVersion = true;\n      const formData = this.instanceVersionForm.value;\n      // Vytvoření nové verze instance\n      const newInstanceVersion = {\n        versionId: formData.versionId,\n        installedByUserId: formData.installedByUserId,\n        notes: formData.notes || ''\n      };\n      this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion).pipe(first()).subscribe({\n        next: response => {\n          console.log('Verze instance úspěšně vytvořena:', response);\n          this.savingInstanceVersion = false;\n          this.closeInstanceVersionModal();\n          // Aktualizace seznamu verzí instance\n          this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n        },\n        error: err => {\n          console.error('Chyba při vytváření verze instance:', err);\n          this.instanceVersionError = `Chyba při vytváření verze instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n    }\n    /**\r\n     * Testovací metoda pro zobrazení hesla\r\n     */\n    testPassword() {\n      if (this.generatedCertificate) {\n        this.modalService.alert(`Heslo k certifikátu: <strong>${this.generatedCertificate.password}</strong>`, 'Heslo k certifikátu', 'Zavřít', 'btn-primary');\n      } else {\n        this.modalService.alert('Certifikát není k dispozici', 'Informace', 'Zavřít', 'btn-secondary');\n      }\n    }\n    /**\r\n     * Pomocná metoda pro získání jména statusu instance\r\n     */\n    getInstanceStatusName(status) {\n      if (typeof status === 'string') {\n        switch (status) {\n          case 'Active':\n            return 'Aktivní';\n          case 'Blocked':\n            return 'Blokovaná';\n          case 'Expired':\n            return 'Expirovaná';\n          case 'Trial':\n            return 'Zkušební';\n          case 'Maintenance':\n            return 'Údržba';\n          default:\n            return status;\n        }\n      } else {\n        switch (status) {\n          case InstanceStatus.Active:\n            return 'Aktivní';\n          case InstanceStatus.Blocked:\n            return 'Blokovaná';\n          case InstanceStatus.Expired:\n            return 'Expirovaná';\n          case InstanceStatus.Trial:\n            return 'Zkušební';\n          case InstanceStatus.Maintenance:\n            return 'Údržba';\n          default:\n            return String(status);\n        }\n      }\n    }\n    /**\r\n     * Pomocná metoda pro získání třídy pro status instance\r\n     */\n    getInstanceStatusClass(status) {\n      if (typeof status === 'string') {\n        switch (status) {\n          case 'Active':\n            return 'bg-success';\n          case 'Blocked':\n            return 'bg-danger';\n          case 'Expired':\n            return 'bg-warning text-dark';\n          case 'Trial':\n            return 'bg-info text-dark';\n          case 'Maintenance':\n            return 'bg-secondary';\n          default:\n            return 'bg-secondary';\n        }\n      } else {\n        switch (status) {\n          case InstanceStatus.Active:\n            return 'bg-success';\n          case InstanceStatus.Blocked:\n            return 'bg-danger';\n          case InstanceStatus.Expired:\n            return 'bg-warning text-dark';\n          case InstanceStatus.Trial:\n            return 'bg-info text-dark';\n          case InstanceStatus.Maintenance:\n            return 'bg-secondary';\n          default:\n            return 'bg-secondary';\n        }\n      }\n    }\n    static {\n      this.ɵfac = function CustomerDetailComponent_Factory(t) {\n        return new (t || CustomerDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContactService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.InstanceVersionService), i0.ɵɵdirectiveInject(i6.VersionService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.CertificateService), i0.ɵɵdirectiveInject(i9.BreadcrumbService), i0.ɵɵdirectiveInject(i10.ModalService), i0.ɵɵdirectiveInject(i11.FormBuilder), i0.ɵɵdirectiveInject(i12.AuthService), i0.ɵɵdirectiveInject(i13.ClipboardService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerDetailComponent,\n        selectors: [[\"app-customer-detail\"]],\n        decls: 183,\n        vars: 65,\n        consts: [[1, \"container-fluid\"], [1, \"row\", \"mb-3\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-1\"], [4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [\"id\", \"contactModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"contactModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"contactModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"firstName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", \"placeholder\", \"Jm\\u00E9no\", 1, \"form-control\"], [\"class\", \"text-danger mt-1\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", \"placeholder\", \"P\\u0159\\u00EDjmen\\u00ED\", 1, \"form-control\"], [1, \"mb-3\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", \"placeholder\", \"Pozice\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"contactEmail\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"contactEmail\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"contactPhone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"contactPhone\", \"formControlName\", \"phone\", \"placeholder\", \"Telefon\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"contactNotes\", 1, \"form-label\"], [\"id\", \"contactNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isPrimary\", \"formControlName\", \"isPrimary\", 1, \"form-check-input\"], [\"for\", \"isPrimary\", 1, \"form-check-label\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"modalId\", \"instanceDetailModal\", 3, \"instance\", \"certificateInfo\", \"instanceVersions\", \"showAddVersionButton\", \"close\", \"edit\", \"generateCertificate\", \"addVersion\"], [\"id\", \"instanceModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [\"id\", \"instanceModalLabel\", 1, \"modal-title\"], [\"for\", \"name\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"N\\u00E1zev instance\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\", \"required-field\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [3, \"value\"], [\"for\", \"serverUrl\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"serverUrl\", \"formControlName\", \"serverUrl\", \"placeholder\", \"URL serveru\", 1, \"form-control\"], [\"for\", \"expirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"expirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky\", 1, \"form-control\", 3, \"ngClass\"], [1, \"form-label\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"id\", \"instanceVersionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceVersionModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceVersionModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceName\", \"disabled\", \"\", 1, \"form-control\", 3, \"value\"], [\"for\", \"versionId\", 1, \"form-label\", \"required-field\"], [\"id\", \"versionId\", \"formControlName\", \"versionId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"installedByUserId\", 1, \"form-label\", \"required-field\"], [\"id\", \"installedByUserId\", \"formControlName\", \"installedByUserId\", 1, \"form-select\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky k instalaci verze\", 1, \"form-control\", 3, \"ngClass\"], [\"modalId\", \"certificateGeneratedModal\", 3, \"certificate\", \"instanceName\", \"close\", \"download\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-1\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"N\\u00E1zev spole\\u010Dnosti\", 1, \"form-control\"], [\"for\", \"abbreviation\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"abbreviation\", \"formControlName\", \"abbreviation\", \"placeholder\", \"Zkratka\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"companyId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"companyId\", \"formControlName\", \"companyId\", \"placeholder\", \"I\\u010CO\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"taxId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taxId\", \"formControlName\", \"taxId\", \"placeholder\", \"DI\\u010C\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"Telefon\", 1, \"form-control\"], [\"for\", \"website\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"website\", \"formControlName\", \"website\", \"placeholder\", \"Web\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"street\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"street\", \"formControlName\", \"street\", \"placeholder\", \"Ulice\", 1, \"form-control\"], [1, \"col-md-4\", \"mb-3\"], [\"for\", \"city\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"city\", \"formControlName\", \"city\", \"placeholder\", \"M\\u011Bsto\", 1, \"form-control\"], [\"for\", \"postalCode\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"postalCode\", \"formControlName\", \"postalCode\", \"placeholder\", \"PS\\u010C\", 1, \"form-control\"], [\"for\", \"country\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"country\", \"formControlName\", \"country\", \"placeholder\", \"Zem\\u011B\", 1, \"form-control\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"text-danger\", \"mt-1\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [1, \"row\", \"mb-0\"], [1, \"col-md-6\"], [\"class\", \"row mt-0\", 4, \"ngIf\"], [1, \"row\", \"mt-0\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"btn btn-sm btn-light\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-3\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-light\", 3, \"click\"], [1, \"bi\", \"bi-plus-lg\", \"me-1\"], [1, \"d-flex\", \"justify-content-center\", \"my-3\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-secondary\", 4, \"ngIf\"], [1, \"btn-group\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Upravit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", \"title\", \"Smazat\", 3, \"click\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-secondary\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"badge\", 3, \"ngClass\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-eye-fill\"], [1, \"alert\", \"alert-danger\", \"mb-3\"]],\n        template: function CustomerDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_4_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵtext(6, \" Zp\\u011Bt na seznam z\\u00E1kazn\\u00EDk\\u016F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(7, CustomerDetailComponent_div_7_Template, 7, 0, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(8, CustomerDetailComponent_div_8_Template, 4, 0, \"div\", 7);\n            i0.ɵɵtemplate(9, CustomerDetailComponent_div_9_Template, 2, 1, \"div\", 8);\n            i0.ɵɵtemplate(10, CustomerDetailComponent_div_10_Template, 79, 31, \"div\", 9);\n            i0.ɵɵtemplate(11, CustomerDetailComponent_div_11_Template, 46, 20, \"div\", 9);\n            i0.ɵɵtemplate(12, CustomerDetailComponent_div_12_Template, 9, 4, \"div\", 9);\n            i0.ɵɵtemplate(13, CustomerDetailComponent_div_13_Template, 9, 4, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11)(16, \"div\", 12)(17, \"div\", 13)(18, \"h5\", 14);\n            i0.ɵɵtext(19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_20_listener() {\n              return ctx.closeContactModal();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 16);\n            i0.ɵɵtemplate(22, CustomerDetailComponent_div_22_Template, 2, 1, \"div\", 17);\n            i0.ɵɵelementStart(23, \"form\", 18);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_23_listener() {\n              return ctx.saveContact();\n            });\n            i0.ɵɵelementStart(24, \"div\", 19)(25, \"div\", 20)(26, \"label\", 21);\n            i0.ɵɵtext(27, \"Jm\\u00E9no\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(28, \"input\", 22);\n            i0.ɵɵtemplate(29, CustomerDetailComponent_div_29_Template, 3, 2, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 20)(31, \"label\", 24);\n            i0.ɵɵtext(32, \"P\\u0159\\u00EDjmen\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"input\", 25);\n            i0.ɵɵtemplate(34, CustomerDetailComponent_div_34_Template, 3, 2, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 26)(36, \"label\", 27);\n            i0.ɵɵtext(37, \"Pozice\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"input\", 28);\n            i0.ɵɵtemplate(39, CustomerDetailComponent_div_39_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 19)(41, \"div\", 20)(42, \"label\", 29);\n            i0.ɵɵtext(43, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 30);\n            i0.ɵɵtemplate(45, CustomerDetailComponent_div_45_Template, 3, 2, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\", 20)(47, \"label\", 31);\n            i0.ɵɵtext(48, \"Telefon\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(49, \"input\", 32);\n            i0.ɵɵtemplate(50, CustomerDetailComponent_div_50_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\", 26)(52, \"label\", 33);\n            i0.ɵɵtext(53, \"Pozn\\u00E1mky\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(54, \"textarea\", 34);\n            i0.ɵɵtemplate(55, CustomerDetailComponent_div_55_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"div\", 35);\n            i0.ɵɵelement(57, \"input\", 36);\n            i0.ɵɵelementStart(58, \"label\", 37);\n            i0.ɵɵtext(59, \"Prim\\u00E1rn\\u00ED kontakt\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"div\", 38)(61, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_61_listener() {\n              return ctx.closeContactModal();\n            });\n            i0.ɵɵtext(62, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"button\", 40);\n            i0.ɵɵtemplate(64, CustomerDetailComponent_span_64_Template, 1, 0, \"span\", 41);\n            i0.ɵɵtext(65, \" Ulo\\u017Eit \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(66, \"app-instance-detail\", 42);\n            i0.ɵɵlistener(\"close\", function CustomerDetailComponent_Template_app_instance_detail_close_66_listener() {\n              return ctx.closeInstanceDetailModal();\n            })(\"edit\", function CustomerDetailComponent_Template_app_instance_detail_edit_66_listener($event) {\n              return ctx.editInstanceFromDetail($event);\n            })(\"generateCertificate\", function CustomerDetailComponent_Template_app_instance_detail_generateCertificate_66_listener($event) {\n              return ctx.generateCertificate($event);\n            })(\"addVersion\", function CustomerDetailComponent_Template_app_instance_detail_addVersion_66_listener() {\n              return ctx.openAddInstanceVersionModal();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"div\", 43)(68, \"div\", 44)(69, \"div\", 12)(70, \"div\", 13)(71, \"h5\", 45);\n            i0.ɵɵtext(72);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_73_listener() {\n              return ctx.closeInstanceModal();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 16);\n            i0.ɵɵtemplate(75, CustomerDetailComponent_div_75_Template, 2, 1, \"div\", 17);\n            i0.ɵɵelementStart(76, \"form\", 18);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_76_listener() {\n              return ctx.saveInstance();\n            });\n            i0.ɵɵelementStart(77, \"div\", 19)(78, \"div\", 20)(79, \"label\", 46);\n            i0.ɵɵtext(80, \"N\\u00E1zev\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(81, \"input\", 47);\n            i0.ɵɵtemplate(82, CustomerDetailComponent_div_82_Template, 3, 2, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 20)(84, \"label\", 48);\n            i0.ɵɵtext(85, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"select\", 49)(87, \"option\", 50);\n            i0.ɵɵtext(88, \"Aktivn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"option\", 50);\n            i0.ɵɵtext(90, \"Blokovan\\u00E1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"option\", 50);\n            i0.ɵɵtext(92, \"Expirovan\\u00E1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"option\", 50);\n            i0.ɵɵtext(94, \"Zku\\u0161ebn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"option\", 50);\n            i0.ɵɵtext(96, \"\\u00DAdr\\u017Eba\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(97, \"div\", 19)(98, \"div\", 20)(99, \"label\", 51);\n            i0.ɵɵtext(100, \"URL serveru\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(101, \"input\", 52);\n            i0.ɵɵtemplate(102, CustomerDetailComponent_div_102_Template, 3, 2, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"div\", 20)(104, \"label\", 53);\n            i0.ɵɵtext(105, \"Datum expirace\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(106, \"input\", 54);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(107, \"div\", 26)(108, \"label\", 55);\n            i0.ɵɵtext(109, \"Pozn\\u00E1mky\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(110, \"textarea\", 56);\n            i0.ɵɵtemplate(111, CustomerDetailComponent_div_111_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"div\", 26)(113, \"label\", 57);\n            i0.ɵɵtext(114, \"Moduly\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 58);\n            i0.ɵɵelement(116, \"input\", 59);\n            i0.ɵɵelementStart(117, \"label\", 60);\n            i0.ɵɵtext(118, \"Reporting\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(119, \"div\", 58);\n            i0.ɵɵelement(120, \"input\", 61);\n            i0.ɵɵelementStart(121, \"label\", 62);\n            i0.ɵɵtext(122, \"Pokro\\u010Dil\\u00E9 zabezpe\\u010Den\\u00ED\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(123, \"div\", 58);\n            i0.ɵɵelement(124, \"input\", 63);\n            i0.ɵɵelementStart(125, \"label\", 64);\n            i0.ɵɵtext(126, \"API integrace\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(127, \"div\", 58);\n            i0.ɵɵelement(128, \"input\", 65);\n            i0.ɵɵelementStart(129, \"label\", 66);\n            i0.ɵɵtext(130, \"Export dat\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(131, \"div\", 58);\n            i0.ɵɵelement(132, \"input\", 67);\n            i0.ɵɵelementStart(133, \"label\", 68);\n            i0.ɵɵtext(134, \"Customizace\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(135, \"div\", 38)(136, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_136_listener() {\n              return ctx.closeInstanceModal();\n            });\n            i0.ɵɵtext(137, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"button\", 40);\n            i0.ɵɵtemplate(139, CustomerDetailComponent_span_139_Template, 1, 0, \"span\", 41);\n            i0.ɵɵtext(140, \" Ulo\\u017Eit \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(141, \"div\", 69)(142, \"div\", 11)(143, \"div\", 12)(144, \"div\", 13)(145, \"h5\", 70);\n            i0.ɵɵtext(146);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(147, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_147_listener() {\n              return ctx.closeInstanceVersionModal();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(148, \"div\", 16);\n            i0.ɵɵtemplate(149, CustomerDetailComponent_div_149_Template, 2, 1, \"div\", 17);\n            i0.ɵɵelementStart(150, \"form\", 18);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_150_listener() {\n              return ctx.saveInstanceVersion();\n            });\n            i0.ɵɵelementStart(151, \"div\", 26)(152, \"label\", 71);\n            i0.ɵɵtext(153, \"Instance:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(154, \"input\", 72);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(155, \"div\", 26)(156, \"label\", 73);\n            i0.ɵɵtext(157, \"Verze:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(158, \"select\", 74)(159, \"option\", 75);\n            i0.ɵɵtext(160, \"-- Vyberte verzi --\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(161, CustomerDetailComponent_option_161_Template, 2, 2, \"option\", 76);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(162, CustomerDetailComponent_div_162_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(163, \"div\", 26)(164, \"label\", 77);\n            i0.ɵɵtext(165, \"Instaloval:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(166, \"select\", 78)(167, \"option\", 75);\n            i0.ɵɵtext(168, \"-- Vyberte u\\u017Eivatele --\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(169, CustomerDetailComponent_option_169_Template, 2, 3, \"option\", 76);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(170, CustomerDetailComponent_div_170_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(171, \"div\", 26)(172, \"label\", 55);\n            i0.ɵɵtext(173, \"Pozn\\u00E1mky:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(174, \"textarea\", 79);\n            i0.ɵɵtemplate(175, CustomerDetailComponent_div_175_Template, 2, 1, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(176, \"div\", 38)(177, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_177_listener() {\n              return ctx.closeInstanceVersionModal();\n            });\n            i0.ɵɵtext(178, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(179, \"button\", 40);\n            i0.ɵɵtemplate(180, CustomerDetailComponent_span_180_Template, 1, 0, \"span\", 41);\n            i0.ɵɵtext(181, \" Ulo\\u017Eit \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(182, \"app-certificate-modal\", 80);\n            i0.ɵɵlistener(\"close\", function CustomerDetailComponent_Template_app_certificate_modal_close_182_listener() {\n              return ctx.closeCertificateModal();\n            })(\"download\", function CustomerDetailComponent_Template_app_certificate_modal_download_182_listener() {\n              return ctx.downloadCertificate();\n            });\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            let tmp_10_0;\n            let tmp_11_0;\n            let tmp_12_0;\n            let tmp_13_0;\n            let tmp_14_0;\n            let tmp_15_0;\n            let tmp_16_0;\n            let tmp_17_0;\n            let tmp_18_0;\n            let tmp_28_0;\n            let tmp_34_0;\n            let tmp_35_0;\n            let tmp_36_0;\n            let tmp_44_0;\n            let tmp_46_0;\n            let tmp_47_0;\n            let tmp_48_0;\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer && ctx.isAdmin);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditContactMode ? \"Upravit kontakt\" : \"P\\u0159idat kontakt\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.contactError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.contactForm);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_10_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_11_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c0, ((tmp_12_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_12_0.touched)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_13_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_14_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c0, ((tmp_15_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_15_0.touched)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_16_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c0, ((tmp_17_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_17_0.touched)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_18_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"disabled\", ctx.contactForm.invalid || ctx.savingContact);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingContact);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"instance\", ctx.selectedInstanceForVersion)(\"certificateInfo\", ctx.selectedInstanceForVersion ? ctx.certificateInfo[ctx.selectedInstanceForVersion.id] : null)(\"instanceVersions\", ctx.selectedInstanceForVersion ? ctx.instanceVersions[ctx.selectedInstanceForVersion.id] || i0.ɵɵpureFunction0(59, _c1) : i0.ɵɵpureFunction0(60, _c1))(\"showAddVersionButton\", true);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditInstanceMode ? \"Upravit instanci DIS\" : \"P\\u0159idat instanci DIS\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.instanceError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.instanceForm);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_28_0.invalid) && ((tmp_28_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_28_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Active);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Blocked);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Expired);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Trial);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Maintenance);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_34_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_34_0.invalid) && ((tmp_34_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_34_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c0, ((tmp_35_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_35_0.invalid) && ((tmp_35_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_35_0.touched)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_36_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_36_0.invalid) && ((tmp_36_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_36_0.touched));\n            i0.ɵɵadvance(27);\n            i0.ɵɵproperty(\"disabled\", ctx.instanceForm.invalid || ctx.savingInstance);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingInstance);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.isEditInstanceVersionMode ? \"Upravit verzi instance\" : \"P\\u0159idat verzi instance\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.instanceVersionError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.instanceVersionForm);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.versions);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_44_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_44_0.invalid) && ((tmp_44_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_44_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.users);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_46_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_46_0.invalid) && ((tmp_46_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_46_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c0, ((tmp_47_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_47_0.invalid) && ((tmp_47_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_47_0.touched)));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_48_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_48_0.invalid) && ((tmp_48_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_48_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.instanceVersionForm.invalid || ctx.savingInstanceVersion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingInstanceVersion);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"certificate\", ctx.generatedCertificate)(\"instanceName\", (ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name) || \"\");\n          }\n        },\n        dependencies: [i14.NgClass, i14.NgForOf, i14.NgIf, i11.ɵNgNoValidate, i11.NgSelectOption, i11.ɵNgSelectMultipleOption, i11.DefaultValueAccessor, i11.CheckboxControlValueAccessor, i11.SelectControlValueAccessor, i11.NgControlStatus, i11.NgControlStatusGroup, i11.FormGroupDirective, i11.FormControlName, i15.CertificateModalComponent, i16.InstanceDetailComponent, i17.LocalDatePipe],\n        styles: [\".required-field[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:#dc3545;font-weight:700}.valid-field[_ngcontent-%COMP%]:after{content:\\\"\\\"}.card[_ngcontent-%COMP%]{border-radius:.5rem;box-shadow:0 .125rem .25rem #00000013;margin-bottom:1.5rem}.card-header[_ngcontent-%COMP%]{border-top-left-radius:.5rem;border-top-right-radius:.5rem}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:var(--bs-light);font-weight:600}body.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}body.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:var(--bs-dark);color:var(--bs-light)}body.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{background-color:#212529;border-color:#495057;color:#e9ecef}body.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus, body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus{background-color:#2b3035;color:#e9ecef}body.dark-theme[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:#adb5bd!important}\"]\n      });\n    }\n  }\n  return CustomerDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}