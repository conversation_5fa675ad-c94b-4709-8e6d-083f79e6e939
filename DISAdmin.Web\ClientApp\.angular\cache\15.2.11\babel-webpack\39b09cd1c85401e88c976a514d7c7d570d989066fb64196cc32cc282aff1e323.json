{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { environment } from '../../environments/environment';\nimport { InstanceStatus } from '../models/instance.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/customer.service\";\nimport * as i3 from \"../services/contact.service\";\nimport * as i4 from \"../services/instance.service\";\nimport * as i5 from \"../services/instance-version.service\";\nimport * as i6 from \"../services/version.service\";\nimport * as i7 from \"../services/user.service\";\nimport * as i8 from \"../services/auth.service\";\nimport * as i9 from \"../services/certificate.service\";\nimport * as i10 from \"../services/modal.service\";\nimport * as i11 from \"@angular/common/http\";\nimport * as i12 from \"../services/monitoring.service\";\nimport * as i13 from \"@angular/router\";\nimport * as i14 from \"ngx-toastr\";\nimport * as i15 from \"@angular/common\";\nimport * as i16 from \"../shared/advanced-filter/advanced-filter.component\";\nconst _c0 = function () {\n  return [\"/instance-wizard\"];\n};\nfunction CustomersComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 136);\n    i0.ɵɵelement(1, \"i\", 137);\n    i0.ɵɵelementStart(2, \"span\", 138);\n    i0.ɵɵtext(3, \"Pr\\u016Fvodce vytvo\\u0159en\\u00EDm instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 139);\n    i0.ɵɵtext(5, \"Pr\\u016Fvodce\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nconst _c1 = function () {\n  return [\"/customers/add\"];\n};\nfunction CustomersComponent_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 140);\n    i0.ɵɵelement(1, \"i\", 141);\n    i0.ɵɵelementStart(2, \"span\", 138);\n    i0.ɵɵtext(3, \"P\\u0159idat z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 139);\n    i0.ɵɵtext(5, \"P\\u0159idat\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction CustomersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction CustomersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00ED z\\u00E1kazn\\u00EDci nebyli nalezeni. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const customer_r42 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.editCustomer(customer_r42));\n    });\n    i0.ɵɵelement(1, \"i\", 166);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const customer_r42 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.deleteCustomer(customer_r42));\n    });\n    i0.ɵɵelement(1, \"i\", 168);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 156);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 156);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 157);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 158);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 158);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 158);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 159);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"div\", 160)(22, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const customer_r42 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.viewCustomerDetail(customer_r42));\n    });\n    i0.ɵɵelement(23, \"i\", 162);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, CustomersComponent_div_13_tr_22_button_24_Template, 2, 0, \"button\", 163);\n    i0.ɵɵtemplate(25, CustomersComponent_div_13_tr_22_button_25_Template, 2, 0, \"button\", 164);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const customer_r42 = ctx.$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(customer_r42.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Zkratka: \", customer_r42.abbreviation, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"I\\u010C: \", customer_r42.companyId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Kontakty: \", customer_r42.contactsCount || 0, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Instance: \", customer_r42.instancesCount || 0, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.abbreviation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.companyId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.contactsCount || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.instancesCount || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isAdmin);\n  }\n}\nfunction CustomersComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\", 151);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_4_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onSort(\"name\"));\n    });\n    i0.ɵɵtext(5, \" N\\u00E1zev \");\n    i0.ɵɵelement(6, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_7_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onSort(\"abbreviation\"));\n    });\n    i0.ɵɵtext(8, \" Zkratka \");\n    i0.ɵɵelement(9, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.onSort(\"companyId\"));\n    });\n    i0.ɵɵtext(11, \" I\\u010C \");\n    i0.ɵɵelement(12, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_13_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onSort(\"contactsCount\"));\n    });\n    i0.ɵɵtext(14, \" Kontakty \");\n    i0.ɵɵelement(15, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 154);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onSort(\"instancesCount\"));\n    });\n    i0.ɵɵtext(17, \" Instance \");\n    i0.ɵɵelement(18, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"tbody\");\n    i0.ɵɵtemplate(22, CustomersComponent_div_13_tr_22_Template, 26, 11, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"name\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"name\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"name\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"abbreviation\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"abbreviation\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"abbreviation\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"companyId\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"companyId\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"companyId\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"contactsCount\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"contactsCount\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"contactsCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"instancesCount\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"instancesCount\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"instancesCount\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.paginatedCustomers);\n  }\n}\nfunction CustomersComponent_div_14_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 173)(1, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_li_11_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const page_r60 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.onPageChange(page_r60));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r60 = ctx.$implicit;\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r60 === ctx_r59.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r60);\n  }\n}\nfunction CustomersComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 169)(1, \"div\", 170);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nav\", 171)(4, \"ul\", 172)(5, \"li\", 173)(6, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onPageChange(1));\n    });\n    i0.ɵɵelement(7, \"i\", 175);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"li\", 173)(9, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.onPageChange(ctx_r65.currentPage - 1));\n    });\n    i0.ɵɵelement(10, \"i\", 176);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, CustomersComponent_div_14_li_11_Template, 3, 3, \"li\", 177);\n    i0.ɵɵelementStart(12, \"li\", 173)(13, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onPageChange(ctx_r66.currentPage + 1));\n    });\n    i0.ɵɵelement(14, \"i\", 178);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\", 173)(16, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_16_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.onPageChange(ctx_r67.totalPages));\n    });\n    i0.ɵɵelement(17, \"i\", 179);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" Zobrazeno \", (ctx_r6.currentPage - 1) * ctx_r6.pageSize + 1, \" - \", ctx_r6.Math.min(ctx_r6.currentPage * ctx_r6.pageSize, ctx_r6.filteredCustomers.length), \" z \", ctx_r6.filteredCustomers.length, \" z\\u00E1znam\\u016F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.pageRange);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n  }\n}\nfunction CustomersComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.error, \" \");\n  }\n}\nfunction CustomersComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" N\\u00E1zev je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Zkratka je povinn\\u00E1 a nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_45_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtemplate(1, CustomersComponent_div_45_span_1_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.customerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n  }\n}\nfunction CustomersComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Ulice je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" M\\u011Bsto je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" PS\\u010C je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Zem\\u011B je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_81_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 kontaktn\\u00ED osoby nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 197);\n    i0.ɵɵtext(1, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r73 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r73.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r73.email);\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_a_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r73 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r73.phone, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r73.phone);\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersComponent_div_81_div_9_tr_15_span_4_Template, 2, 0, \"span\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtemplate(8, CustomersComponent_div_81_div_9_tr_15_a_8_Template, 2, 2, \"a\", 194);\n    i0.ɵɵtemplate(9, CustomersComponent_div_81_div_9_tr_15_span_9_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_81_div_9_tr_15_a_11_Template, 2, 2, \"a\", 194);\n    i0.ɵɵtemplate(12, CustomersComponent_div_81_div_9_tr_15_span_12_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"div\", 160)(15, \"button\", 195);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_div_9_tr_15_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const contact_r73 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r81.editContact(contact_r73));\n    });\n    i0.ɵɵelement(16, \"i\", 166);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_div_9_tr_15_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const contact_r73 = restoredCtx.$implicit;\n      const ctx_r83 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r83.deleteContact(contact_r73));\n    });\n    i0.ɵɵelement(18, \"i\", 168);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const contact_r73 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fw-bold\", contact_r73.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", contact_r73.firstName, \" \", contact_r73.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.isPrimary);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r73.position || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r73.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r73.phone);\n  }\n}\nfunction CustomersComponent_div_81_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomersComponent_div_81_div_9_tr_15_Template, 19, 10, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r71.contacts);\n  }\n}\nfunction CustomersComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"div\", 183)(2, \"h5\", 184);\n    i0.ɵɵtext(3, \"Kontaktn\\u00ED osoby\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.openAddContactModal());\n    });\n    i0.ɵɵelement(5, \"i\", 186);\n    i0.ɵɵtext(6, \"P\\u0159idat kontakt \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CustomersComponent_div_81_div_7_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(8, CustomersComponent_div_81_div_8_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(9, CustomersComponent_div_81_div_9_Template, 16, 1, \"div\", 189);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.loadingContacts && ctx_r15.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.loadingContacts && ctx_r15.contacts.length > 0);\n  }\n}\nfunction CustomersComponent_div_82_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_82_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 instance DIS nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_82_div_8_tr_21_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 206)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomersComponent_div_82_div_8_tr_21_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_8_tr_21_span_12_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const instance_r90 = i0.ɵɵnextContext().$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r93.openAddInstanceVersionModal(instance_r90));\n    });\n    i0.ɵɵelement(3, \"i\", 200);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const instance_r90 = i0.ɵɵnextContext().$implicit;\n    const ctx_r92 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r92.getLatestVersion(instance_r90.id), \" \");\n  }\n}\nconst _c2 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"bg-success\": a0,\n    \"bg-danger\": a1,\n    \"bg-warning\": a2,\n    \"bg-secondary\": a3,\n    \"bg-dark\": a4\n  };\n};\nconst _c3 = function (a1) {\n  return [\"/instance-metrics\", a1];\n};\nfunction CustomersComponent_div_82_div_8_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 201);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_8_tr_21_Template_a_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r97 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r97.viewInstanceDetail(instance_r90));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"td\");\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 202);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_82_div_8_tr_21_span_11_Template, 3, 0, \"span\", 203);\n    i0.ɵɵtemplate(12, CustomersComponent_div_82_div_8_tr_21_span_12_Template, 4, 1, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\")(20, \"div\", 160)(21, \"button\", 195);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_8_tr_21_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r99 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r99.editInstance(instance_r90));\n    });\n    i0.ɵɵelement(22, \"i\", 166);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 204);\n    i0.ɵɵelement(24, \"i\", 205);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_8_tr_21_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r100 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r100.deleteInstance(instance_r90));\n    });\n    i0.ɵɵelement(26, \"i\", 168);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const instance_r90 = ctx.$implicit;\n    const ctx_r89 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r90.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r90.serverUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(15, _c2, instance_r90.status === ctx_r89.InstanceStatus.Active, instance_r90.status === ctx_r89.InstanceStatus.Blocked, instance_r90.status === ctx_r89.InstanceStatus.Trial, instance_r90.status === ctx_r89.InstanceStatus.Maintenance, instance_r90.status === ctx_r89.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r89.getInstanceStatusText(instance_r90.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r89.loadingInstanceVersions[instance_r90.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r89.loadingInstanceVersions[instance_r90.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, instance_r90.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r90.expirationDate ? i0.ɵɵpipeBind2(18, 12, instance_r90.expirationDate, \"dd.MM.yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(21, _c3, instance_r90.id));\n  }\n}\nfunction CustomersComponent_div_82_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Datab\\u00E1ze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Datum expirace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, CustomersComponent_div_82_div_8_tr_21_Template, 27, 23, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r88.instances);\n  }\n}\nfunction CustomersComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"div\", 183)(2, \"h5\", 184);\n    i0.ɵɵtext(3, \"Instance DIS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 199);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.openAddInstanceModal());\n    });\n    i0.ɵɵelement(5, \"i\", 200);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, CustomersComponent_div_82_div_6_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(7, CustomersComponent_div_82_div_7_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(8, CustomersComponent_div_82_div_8_Template, 22, 1, \"div\", 189);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.loadingInstances && ctx_r16.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.loadingInstances && ctx_r16.instances.length > 0);\n  }\n}\nfunction CustomersComponent_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_98_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"I\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r103 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r103.selectedCustomer.companyId, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"DI\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r104 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r104.selectedCustomer.taxId, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r105.selectedCustomer.email, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Telefon:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r106 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r106.selectedCustomer.phone, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Web:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r107 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r107.selectedCustomer.website, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Pozn\\u00E1mka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r108 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r108.selectedCustomer.notes, \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 217)(2, \"div\", 218)(3, \"div\", 219)(4, \"h3\", 220);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 184);\n    i0.ɵɵtext(7, \"Celkem instanc\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 217)(9, \"div\", 221)(10, \"div\", 219)(11, \"h3\", 220);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 184);\n    i0.ɵɵtext(14, \"Aktivn\\u00EDch instanc\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 217)(16, \"div\", 222)(17, \"div\", 219)(18, \"h3\", 220);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 184);\n    i0.ɵɵtext(21, \"API vol\\u00E1n\\u00ED (24h)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 217)(23, \"div\", 223)(24, \"div\", 219)(25, \"h3\", 220);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 184);\n    i0.ɵɵtext(28, \"Expiruj\\u00EDc\\u00ED certifik\\u00E1ty\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 23)(30, \"div\", 5)(31, \"div\", 6)(32, \"h5\", 224);\n    i0.ɵɵtext(33, \"V\\u00FDkon API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\", 184)(35, \"strong\");\n    i0.ɵɵtext(36, \"Pr\\u016Fm\\u011Brn\\u00E1 doba odezvy:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 184)(40, \"strong\");\n    i0.ɵɵtext(41, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti (24h):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 23)(44, \"div\", 5)(45, \"div\", 6)(46, \"h5\", 224);\n    i0.ɵɵtext(47, \"Nejpou\\u017E\\u00EDvan\\u011Bj\\u0161\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\", 184)(49, \"strong\");\n    i0.ɵɵtext(50, \"Verze:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"p\", 184)(53, \"strong\");\n    i0.ɵɵtext(54, \"Po\\u010Det instanc\\u00ED:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r110 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.instancesCount);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.activeInstancesCount);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.apiCallsLast24h);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.expiringCertificatesCount);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(38, 8, ctx_r110.customerStatistics.avgApiResponseTime, \"1.2-2\"), \" ms\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.securityEventsCount, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.mostUsedVersion || \"Nen\\u00ED k dispozici\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.mostUsedVersionCount || 0, \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r111 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r111.statisticsError, \" \");\n  }\n}\nfunction CustomersComponent_div_98_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 kontaktn\\u00ED osoby nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 197);\n    i0.ɵɵtext(1, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r119 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r119.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r119.email);\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r119 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r119.phone, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r119.phone);\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersComponent_div_98_div_45_tr_13_span_4_Template, 2, 0, \"span\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtemplate(8, CustomersComponent_div_98_div_45_tr_13_a_8_Template, 2, 2, \"a\", 194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, CustomersComponent_div_98_div_45_tr_13_a_10_Template, 2, 2, \"a\", 194);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r119 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fw-bold\", contact_r119.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", contact_r119.firstName, \" \", contact_r119.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.isPrimary);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r119.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.phone);\n  }\n}\nfunction CustomersComponent_div_98_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, CustomersComponent_div_98_div_45_tr_13_Template, 11, 8, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r114 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r114.contacts);\n  }\n}\nfunction CustomersComponent_div_98_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 instance DIS nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 206)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r126 = i0.ɵɵnextContext().$implicit;\n    const ctx_r128 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r128.getLatestVersion(instance_r126.id));\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 201);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_div_51_tr_19_Template_a_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r131);\n      const instance_r126 = restoredCtx.$implicit;\n      const ctx_r130 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r130.viewInstanceDetail(instance_r126));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"td\");\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 202);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_98_div_51_tr_19_span_11_Template, 3, 0, \"span\", 203);\n    i0.ɵɵtemplate(12, CustomersComponent_div_98_div_51_tr_19_span_12_Template, 2, 1, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const instance_r126 = ctx.$implicit;\n    const ctx_r125 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r126.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r126.serverUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(14, _c2, instance_r126.status === ctx_r125.InstanceStatus.Active, instance_r126.status === ctx_r125.InstanceStatus.Blocked, instance_r126.status === ctx_r125.InstanceStatus.Trial, instance_r126.status === ctx_r125.InstanceStatus.Maintenance, instance_r126.status === ctx_r125.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r125.getInstanceStatusText(instance_r126.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r125.loadingInstanceVersions[instance_r126.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r125.loadingInstanceVersions[instance_r126.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 8, instance_r126.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r126.expirationDate ? i0.ɵɵpipeBind2(18, 11, instance_r126.expirationDate, \"dd.MM.yyyy\") : \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Datab\\u00E1ze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Datum expirace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, CustomersComponent_div_98_div_51_tr_19_Template, 19, 20, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r117 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r117.instances);\n  }\n}\nfunction CustomersComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r133 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 210)(2, \"div\", 211)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Zkratka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, CustomersComponent_div_98_p_9_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(10, CustomersComponent_div_98_p_10_Template, 4, 1, \"p\", 124);\n    i0.ɵɵelementStart(11, \"p\")(12, \"strong\");\n    i0.ɵɵtext(13, \"Ulice:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\")(16, \"strong\");\n    i0.ɵɵtext(17, \"M\\u011Bsto:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"PS\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25, \"Zem\\u011B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 211);\n    i0.ɵɵtemplate(28, CustomersComponent_div_98_p_28_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(29, CustomersComponent_div_98_p_29_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(30, CustomersComponent_div_98_p_30_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(31, CustomersComponent_div_98_p_31_Template, 4, 1, \"p\", 124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 212)(33, \"div\", 213)(34, \"h5\", 184);\n    i0.ɵɵtext(35, \"Statistiky z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 6);\n    i0.ɵɵtemplate(37, CustomersComponent_div_98_div_37_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(38, CustomersComponent_div_98_div_38_Template, 56, 11, \"div\", 214);\n    i0.ɵɵtemplate(39, CustomersComponent_div_98_div_39_Template, 2, 1, \"div\", 215);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 47)(41, \"h5\", 184);\n    i0.ɵɵtext(42, \"Kontaktn\\u00ED osoby\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(43, CustomersComponent_div_98_div_43_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(44, CustomersComponent_div_98_div_44_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(45, CustomersComponent_div_98_div_45_Template, 14, 1, \"div\", 189);\n    i0.ɵɵelementStart(46, \"div\", 216)(47, \"h5\", 184);\n    i0.ɵɵtext(48, \"Instance DIS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(49, CustomersComponent_div_98_div_49_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(50, CustomersComponent_div_98_div_50_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(51, CustomersComponent_div_98_div_51_Template, 20, 1, \"div\", 189);\n    i0.ɵɵelementStart(52, \"div\", 125)(53, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r133);\n      const ctx_r132 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r132.closeCustomerDetailModal());\n    });\n    i0.ɵɵelement(54, \"i\", 53);\n    i0.ɵɵtext(55, \"Zav\\u0159\\u00EDt \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r133);\n      const ctx_r134 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r134.editSelectedCustomer());\n    });\n    i0.ɵɵelement(57, \"i\", 129);\n    i0.ɵɵtext(58, \"Upravit \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r19.selectedCustomer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.abbreviation, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.companyId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.taxId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.street, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.postalCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.country, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.website);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.notes);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingStatistics);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingStatistics && ctx_r19.customerStatistics);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingStatistics && ctx_r19.statisticsError);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingContacts && ctx_r19.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingContacts && ctx_r19.contacts.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingInstances && ctx_r19.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingInstances && ctx_r19.instances.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r19.selectedCustomer);\n  }\n}\nfunction CustomersComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.contactError, \" \");\n  }\n}\nfunction CustomersComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226);\n    i0.ɵɵtext(1, \" Jm\\u00E9no je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226);\n    i0.ɵɵtext(1, \" P\\u0159\\u00EDjmen\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_128_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtemplate(1, CustomersComponent_div_128_span_1_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r23.contactForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n  }\n}\nfunction CustomersComponent_span_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_147_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.instanceError, \" \");\n  }\n}\nfunction CustomersComponent_div_163_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" N\\u00E1zev instance je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_168_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" URL serveru je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_187_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"label\", 227);\n    i0.ɵɵtext(2, \"D\\u016Fvod blokace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 228);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_span_220_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_221_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_231_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.instanceVersionError, \" \");\n  }\n}\nfunction CustomersComponent_option_243_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 229);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r136 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", version_r136.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", version_r136.versionNumber, \" (\", i0.ɵɵpipeBind2(2, 3, version_r136.releaseDate, \"dd.MM.yyyy\"), \")\");\n  }\n}\nfunction CustomersComponent_div_244_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Verze je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_option_251_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 229);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r137 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r137.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", user_r137.firstName, \" \", user_r137.lastName, \"\");\n  }\n}\nfunction CustomersComponent_div_252_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" U\\u017Eivatel je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_span_262_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_263_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_273_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 206);\n  }\n}\nfunction CustomersComponent_div_273_i_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 250);\n  }\n}\nfunction CustomersComponent_div_273_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 251);\n  }\n}\nfunction CustomersComponent_div_273_i_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 252);\n  }\n}\nfunction CustomersComponent_div_273_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_273_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵelement(1, \"i\", 253);\n    i0.ɵɵtext(2, \" Instance nem\\u00E1 p\\u0159i\\u0159azen\\u00FD certifik\\u00E1t. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 258);\n    i0.ɵɵtext(1, \"Platn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 259);\n    i0.ɵɵtext(1, \"Neplatn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 260);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r151 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r151.getCertificateExpirationClass(ctx_r151.certificateInfo.daysToExpiration));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r151.certificateInfo.daysToExpiration, \" dn\\u00ED do expirace) \");\n  }\n}\nconst _c4 = function (a1) {\n  return [\"/certificate-rotation/instance\", a1];\n};\nfunction CustomersComponent_div_273_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r153 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 22)(3, \"div\", 211)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Subject:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Issuer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 211)(17, \"p\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\")(23, \"strong\");\n    i0.ɵɵtext(24, \"Posledn\\u00ED validace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\")(28, \"a\", 241);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_div_71_Template_a_click_28_listener() {\n      i0.ɵɵrestoreView(_r153);\n      const ctx_r152 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r152.closeInstanceDetailModal());\n    });\n    i0.ɵɵelement(29, \"i\", 254);\n    i0.ɵɵtext(30, \"Nastaven\\u00ED automatick\\u00E9 rotace \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"p\")(32, \"strong\");\n    i0.ɵɵtext(33, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CustomersComponent_div_273_div_71_span_34_Template, 2, 0, \"span\", 255);\n    i0.ɵɵtemplate(35, CustomersComponent_div_273_div_71_span_35_Template, 2, 0, \"span\", 256);\n    i0.ɵɵtemplate(36, CustomersComponent_div_273_div_71_span_36_Template, 2, 2, \"span\", 257);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r145 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.thumbprint, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.subject, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.issuer, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 9, ctx_r145.certificateInfo.expirationDate, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(26, 12, ctx_r145.certificateInfo.lastValidation, \"dd.MM.yyyy HH:mm\" || \"-\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c4, ctx_r145.selectedInstanceForVersion.id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r145.certificateInfo.isValid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r145.certificateInfo.isValid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r145.certificateInfo.daysToExpiration > 0);\n  }\n}\nfunction CustomersComponent_div_273_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_273_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 verze nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_81_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const version_r155 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(version_r155.versionNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 4, version_r155.installedAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(version_r155.installedByUserName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(version_r155.notes || \"-\");\n  }\n}\nfunction CustomersComponent_div_273_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 261);\n    i0.ɵɵtext(7, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Instaloval\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Pozn\\u00E1mka\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, CustomersComponent_div_273_div_81_tr_13_Template, 10, 7, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r148 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r148.instanceVersions[ctx_r148.selectedInstanceForVersion.id]);\n  }\n}\nconst _c5 = function (a1) {\n  return [\"/ip-whitelisting\", a1];\n};\nfunction CustomersComponent_div_273_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r157 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 230)(2, \"div\", 211)(3, \"h6\");\n    i0.ɵɵtext(4, \"N\\u00E1zev:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 211)(8, \"h6\");\n    i0.ɵɵtext(9, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"span\", 202);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 230)(14, \"div\", 211)(15, \"h6\");\n    i0.ɵɵtext(16, \"URL serveru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 211)(20, \"h6\");\n    i0.ɵɵtext(21, \"Datum posledn\\u00ED komunikace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 230)(26, \"div\", 231)(27, \"h6\");\n    i0.ɵɵtext(28, \"API kl\\u00ED\\u010D:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 232);\n    i0.ɵɵelement(30, \"input\", 233, 234);\n    i0.ɵɵelementStart(32, \"button\", 235);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const _r138 = i0.ɵɵreference(31);\n      const ctx_r156 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r156.copyApiKey(_r138));\n    });\n    i0.ɵɵelement(33, \"i\", 236);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 237);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r158 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r158.regenerateApiKey(ctx_r158.selectedInstanceForVersion.id));\n    });\n    i0.ɵɵtemplate(35, CustomersComponent_div_273_span_35_Template, 1, 0, \"span\", 203);\n    i0.ɵɵtemplate(36, CustomersComponent_div_273_i_36_Template, 1, 0, \"i\", 238);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 239)(38, \"small\", 240);\n    i0.ɵɵtext(39, \"Tento kl\\u00ED\\u010D je pot\\u0159eba zadat do aplikace DIS pro komunikaci s DIS Admin.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 241);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_a_click_40_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r159 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r159.closeInstanceDetailModal());\n    });\n    i0.ɵɵelement(41, \"i\", 242);\n    i0.ɵɵtext(42, \"IP Whitelisting \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 230)(44, \"div\", 211)(45, \"h6\");\n    i0.ɵɵtext(46, \"Datum instalace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\");\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 211)(51, \"h6\");\n    i0.ɵɵtext(52, \"Datum expirace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"p\");\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"div\", 47)(57, \"h6\");\n    i0.ɵɵtext(58, \"Pozn\\u00E1mka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\");\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 243)(62, \"div\", 183)(63, \"h5\", 184);\n    i0.ɵɵtext(64, \"Certifik\\u00E1t\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 244);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r160 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r160.generateCertificate(ctx_r160.selectedInstanceForVersion.id));\n    });\n    i0.ɵɵtemplate(66, CustomersComponent_div_273_span_66_Template, 1, 0, \"span\", 245);\n    i0.ɵɵtemplate(67, CustomersComponent_div_273_i_67_Template, 1, 0, \"i\", 246);\n    i0.ɵɵtext(68, \"Vygenerovat nov\\u00FD certifik\\u00E1t \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(69, CustomersComponent_div_273_div_69_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(70, CustomersComponent_div_273_div_70_Template, 3, 0, \"div\", 188);\n    i0.ɵɵtemplate(71, CustomersComponent_div_273_div_71_Template, 37, 17, \"div\", 247);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 47)(73, \"div\", 183)(74, \"h5\", 184);\n    i0.ɵɵtext(75, \"Historie verz\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 248);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r161 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r161.openAddInstanceVersionModal(ctx_r161.selectedInstanceForVersion));\n    });\n    i0.ɵɵelement(77, \"i\", 249);\n    i0.ɵɵtext(78, \"P\\u0159idat verzi \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, CustomersComponent_div_273_div_79_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(80, CustomersComponent_div_273_div_80_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(81, CustomersComponent_div_273_div_81_Template, 14, 1, \"div\", 189);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(31, _c2, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Active, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Blocked, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Trial, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Maintenance, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.getInstanceStatusText(ctx_r39.selectedInstanceForVersion.status), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.serverUrl || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.lastConnectionDate ? i0.ɵɵpipeBind2(24, 22, ctx_r39.selectedInstanceForVersion.lastConnectionDate, \"dd.MM.yyyy HH:mm\") : \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r39.selectedInstanceForVersion.apiKey || \"-\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(37, _c5, ctx_r39.selectedInstanceForVersion.id));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(49, 25, ctx_r39.selectedInstanceForVersion.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.expirationDate ? i0.ɵɵpipeBind2(55, 28, ctx_r39.selectedInstanceForVersion.expirationDate, \"dd.MM.yyyy\") : \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.notes || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loadingCertificateInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingCertificateInfo && !ctx_r39.certificateInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingCertificateInfo && ctx_r39.certificateInfo);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id] && (!ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id] || ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id].length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id] && ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id] && ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id].length > 0);\n  }\n}\nfunction CustomersComponent_div_295_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r163 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 262);\n    i0.ɵɵtext(2, \"Informace o certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 263)(4, \"li\", 264)(5, \"span\");\n    i0.ɵɵtext(6, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 240);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 264)(10, \"span\");\n    i0.ɵɵtext(11, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 240);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\", 264)(16, \"span\");\n    i0.ɵɵtext(17, \"Heslo k certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 265);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 266);\n    i0.ɵɵelement(21, \"i\", 267);\n    i0.ɵɵelementStart(22, \"strong\");\n    i0.ɵɵtext(23, \"D\\u016Fle\\u017Eit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Toto heslo si poznamenejte. Budete ho pot\\u0159ebovat p\\u0159i instalaci certifik\\u00E1tu. Z bezpe\\u010Dnostn\\u00EDch d\\u016Fvod\\u016F nen\\u00ED heslo nikde ulo\\u017Eeno a nebude mo\\u017En\\u00E9 ho pozd\\u011Bji zobrazit. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 262)(26, \"button\", 268);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_295_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r163);\n      const ctx_r162 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r162.downloadCertificate());\n    });\n    i0.ɵɵelement(27, \"i\", 269);\n    i0.ɵɵtext(28, \"St\\u00E1hnout certifik\\u00E1t \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r40.generatedCertificate.thumbprint);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 3, ctx_r40.generatedCertificate.expirationDate, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r40.generatedCertificate.password);\n  }\n}\nconst _c6 = function (a0, a1) {\n  return {\n    \"required-field\": a0,\n    \"valid-field\": a1\n  };\n};\nexport let CustomersComponent = /*#__PURE__*/(() => {\n  class CustomersComponent {\n    /**\r\n     * Pomocná metoda pro správné zavření modálu\r\n     * @param modalId ID modálu, který chceme zavřít\r\n     */\n    closeModal(modalId) {\n      this.modalService.close(modalId);\n    }\n    /**\r\n     * Pomocná metoda pro otevření modálu\r\n     * @param modalId ID modálu, který chceme otevřít\r\n     */\n    openModal(modalId) {\n      this.modalService.open(modalId);\n    }\n    closeCustomerModal() {\n      this.closeModal('customerModal');\n    }\n    closeContactModal() {\n      this.closeModal('contactModal');\n    }\n    closeCustomerDetailModal() {\n      this.closeModal('customerDetailModal');\n    }\n    editSelectedCustomer() {\n      if (this.selectedCustomer) {\n        this.editCustomer(this.selectedCustomer);\n      }\n    }\n    openAddInstanceModal() {\n      console.log('Open add instance modal');\n      if (!this.selectedCustomer) {\n        console.error('No customer selected');\n        return;\n      }\n      // Reset formuláře a chybové zprávy\n      this.instanceForm.reset({\n        name: '',\n        serverUrl: '',\n        notes: ''\n      });\n      this.isEditInstanceMode = false;\n      this.instanceError = null;\n      // Otevřít modal\n      this.openModal('instanceModal');\n    }\n    editInstance(instance) {\n      console.log('Edit instance', instance);\n      // Naplnění formuláře daty instance\n      this.instanceForm.patchValue({\n        name: instance.name,\n        serverUrl: instance.serverUrl || '',\n        expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n        status: instance.status || 'Active',\n        blockReason: instance.blockReason || '',\n        moduleReporting: instance.moduleReporting,\n        moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n        moduleApiIntegration: instance.moduleApiIntegration,\n        moduleDataExport: instance.moduleDataExport,\n        moduleCustomization: instance.moduleCustomization,\n        notes: instance.notes || ''\n      });\n      this.isEditInstanceMode = true;\n      this.selectedInstance = instance;\n      this.instanceError = null;\n      // Otevřít modal\n      this.openModal('instanceModal');\n    }\n    deleteInstance(instance) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        console.log('Delete instance', instance);\n        const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat instanci ${instance.name}?`, 'Smazání instance', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this.instanceService.deleteInstance(instance.id).subscribe({\n            next: () => {\n              console.log('Instance deleted successfully');\n              _this.toastr.success(`Instance ${instance.name} byla úspěšně smazána`, 'Úspěch');\n              // Odstranit instanci ze seznamu\n              _this.instances = _this.instances.filter(i => i.id !== instance.id);\n            },\n            error: err => {\n              console.error('Chyba při mazání instance', err);\n              _this.modalService.alert(`Chyba při mazání instance: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n            }\n          });\n        }\n      })();\n    }\n    saveInstance() {\n      console.log('Save instance', this.instanceForm.value);\n      if (this.instanceForm.invalid) {\n        this.instanceError = 'Formulář obsahuje chyby. Opravte je prosím.';\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.instanceForm.controls).forEach(key => {\n          const control = this.instanceForm.get(key);\n          control?.markAsTouched();\n        });\n        return;\n      }\n      if (!this.selectedCustomer) {\n        this.instanceError = 'Není vybrán žádný zákazník.';\n        return;\n      }\n      this.savingInstance = true;\n      const instanceData = this.instanceForm.value;\n      if (this.isEditInstanceMode && this.selectedInstance) {\n        // Aktualizace existující instance\n        const updatedInstance = {\n          name: instanceData.name || '',\n          serverUrl: instanceData.serverUrl || '',\n          expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n          status: instanceData.status,\n          blockReason: instanceData.blockReason,\n          moduleReporting: instanceData.moduleReporting,\n          moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n          moduleApiIntegration: instanceData.moduleApiIntegration,\n          moduleDataExport: instanceData.moduleDataExport,\n          moduleCustomization: instanceData.moduleCustomization,\n          notes: instanceData.notes || ''\n        };\n        console.log('Sending updated instance data:', updatedInstance);\n        this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance).subscribe({\n          next: updatedInstance => {\n            console.log('Instance updated successfully:', updatedInstance);\n            this.savingInstance = false;\n            // Aktualizovat instanci v seznamu\n            const index = this.instances.findIndex(i => i.id === updatedInstance.id);\n            if (index !== -1) {\n              this.instances[index] = updatedInstance;\n            }\n            // Zavřít modal\n            this.closeInstanceModal();\n          },\n          error: err => {\n            console.error('Chyba při aktualizaci instance', err);\n            this.instanceError = `Chyba při aktualizaci instance: ${err.status} ${err.statusText}`;\n            this.savingInstance = false;\n          }\n        });\n      } else {\n        // Vytvoření nové instance\n        const newInstance = {\n          customerId: this.selectedCustomer.id,\n          name: instanceData.name || '',\n          serverUrl: instanceData.serverUrl || '',\n          expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n          status: instanceData.status,\n          moduleReporting: instanceData.moduleReporting,\n          moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n          moduleApiIntegration: instanceData.moduleApiIntegration,\n          moduleDataExport: instanceData.moduleDataExport,\n          moduleCustomization: instanceData.moduleCustomization,\n          notes: instanceData.notes || ''\n        };\n        console.log('Sending new instance data:', newInstance);\n        this.instanceService.createInstance(newInstance).subscribe({\n          next: createdInstance => {\n            console.log('Instance created successfully:', createdInstance);\n            this.savingInstance = false;\n            // Přidat novou instanci do seznamu\n            this.instances.push(createdInstance);\n            // Zavřít modal\n            this.closeInstanceModal();\n          },\n          error: err => {\n            console.error('Chyba při vytváření instance', err);\n            this.instanceError = `Chyba při vytváření instance: ${err.status} ${err.statusText}`;\n            this.savingInstance = false;\n          }\n        });\n      }\n    }\n    closeInstanceModal() {\n      this.closeModal('instanceModal');\n    }\n    getInstanceStatusText(status) {\n      if (typeof status === 'string') {\n        switch (status) {\n          case 'Active':\n            return 'Aktivní';\n          case 'Blocked':\n            return 'Blokovaná';\n          case 'Expired':\n            return 'Expirovaná';\n          case 'Trial':\n            return 'Zkušební';\n          case 'Maintenance':\n            return 'Údržba';\n          default:\n            return status;\n        }\n      } else {\n        switch (status) {\n          case InstanceStatus.Active:\n            return 'Aktivní';\n          case InstanceStatus.Blocked:\n            return 'Blokovaná';\n          case InstanceStatus.Expired:\n            return 'Expirovaná';\n          case InstanceStatus.Trial:\n            return 'Zkušební';\n          case InstanceStatus.Maintenance:\n            return 'Údržba';\n          default:\n            return String(status);\n        }\n      }\n    }\n    openAddContactModal() {\n      console.log('Open add contact modal');\n      if (!this.selectedCustomer) {\n        console.error('No customer selected');\n        return;\n      }\n      // Reset formuláře a chybové zprávy\n      this.contactForm.reset({\n        firstName: '',\n        lastName: '',\n        position: '',\n        email: '',\n        phone: '',\n        notes: '',\n        isPrimary: false\n      });\n      this.isEditContactMode = false;\n      this.contactError = null;\n      // Otevřít modal\n      this.openModal('contactModal');\n    }\n    editContact(contact) {\n      console.log('Edit contact', contact);\n      // Naplnění formuláře daty kontaktu\n      this.contactForm.setValue({\n        firstName: contact.firstName,\n        lastName: contact.lastName,\n        position: contact.position || '',\n        email: contact.email || '',\n        phone: contact.phone || '',\n        notes: contact.notes || '',\n        isPrimary: contact.isPrimary\n      });\n      this.isEditContactMode = true;\n      this.selectedContact = contact;\n      this.contactError = null;\n      // Otevřít modal\n      this.openModal('contactModal');\n    }\n    deleteContact(contact) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        console.log('Delete contact', contact);\n        const confirmed = yield _this2.modalService.confirm(`Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`, 'Smazání kontaktu', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this2.contactService.deleteContact(contact.id).subscribe({\n            next: () => {\n              console.log('Contact deleted successfully');\n              _this2.toastr.success(`Kontakt ${contact.firstName} ${contact.lastName} byl úspěšně smazán`, 'Úspěch');\n              // Odstranit kontakt ze seznamu\n              _this2.contacts = _this2.contacts.filter(c => c.id !== contact.id);\n            },\n            error: err => {\n              console.error('Chyba při mazání kontaktu', err);\n              _this2.modalService.alert(`Chyba při mazání kontaktu: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n            }\n          });\n        }\n      })();\n    }\n    saveContact() {\n      console.log('Save contact', this.contactForm.value);\n      if (this.contactForm.invalid) {\n        this.contactError = 'Formulář obsahuje chyby. Opravte je prosím.';\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.contactForm.controls).forEach(key => {\n          const control = this.contactForm.get(key);\n          control?.markAsTouched();\n        });\n        return;\n      }\n      if (!this.selectedCustomer) {\n        this.contactError = 'Není vybrán žádný zákazník.';\n        return;\n      }\n      this.savingContact = true;\n      const contactData = this.contactForm.value;\n      if (this.isEditContactMode && this.selectedContact) {\n        // Aktualizace existujícího kontaktu\n        const updatedContact = {\n          firstName: contactData.firstName || '',\n          lastName: contactData.lastName || '',\n          position: contactData.position || '',\n          email: contactData.email || '',\n          phone: contactData.phone || '',\n          notes: contactData.notes || '',\n          isPrimary: contactData.isPrimary || false\n        };\n        console.log('Sending updated contact data:', updatedContact);\n        this.contactService.updateContact(this.selectedContact.id, updatedContact).subscribe({\n          next: updatedContact => {\n            console.log('Contact updated successfully:', updatedContact);\n            this.savingContact = false;\n            // Aktualizovat kontakt v seznamu\n            const index = this.contacts.findIndex(c => c.id === updatedContact.id);\n            if (index !== -1) {\n              this.contacts[index] = updatedContact;\n            }\n            // Zavřít modal\n            this.closeModal('contactModal');\n          },\n          error: err => {\n            console.error('Chyba při aktualizaci kontaktu', err);\n            this.contactError = `Chyba při aktualizaci kontaktu: ${err.status} ${err.statusText}`;\n            this.savingContact = false;\n          }\n        });\n      } else {\n        // Vytvoření nového kontaktu\n        const newContact = {\n          customerId: this.selectedCustomer.id,\n          firstName: contactData.firstName || '',\n          lastName: contactData.lastName || '',\n          position: contactData.position || '',\n          email: contactData.email || '',\n          phone: contactData.phone || '',\n          notes: contactData.notes || '',\n          isPrimary: contactData.isPrimary || false\n        };\n        console.log('Sending contact data:', newContact);\n        this.contactService.createContact(newContact).subscribe({\n          next: createdContact => {\n            console.log('Contact created successfully:', createdContact);\n            this.savingContact = false;\n            // Přidat kontakt do seznamu\n            this.contacts.push(createdContact);\n            this.closeContactModal();\n          },\n          error: err => {\n            console.error('Chyba při vytváření kontaktu', err);\n            console.error('Error details:', err.error);\n            // Zobrazit detailnější chybovou zprávu\n            if (err.error && err.error.errors) {\n              const errorMessages = [];\n              for (const key in err.error.errors) {\n                if (err.error.errors.hasOwnProperty(key)) {\n                  errorMessages.push(`${key}: ${err.error.errors[key].join(', ')}`);\n                }\n              }\n              this.contactError = `Chyba při vytváření kontaktu: ${errorMessages.join('; ')}`;\n            } else {\n              this.contactError = `Chyba při vytváření kontaktu: ${err.status} ${err.statusText}`;\n            }\n            this.savingContact = false;\n          }\n        });\n      }\n    }\n    // Vlastní validátor pro email - kontroluje formát pouze pokud je pole vyplněno\n    emailValidator() {\n      return control => {\n        const value = control.value;\n        if (!value || value.length === 0) {\n          return null; // Pokud je pole prázdné, validace projde\n        }\n        // Použijeme standardní validátor emailu\n        return Validators.email(control);\n      };\n    }\n    constructor(fb, customerService, contactService, instanceService, instanceVersionService, versionService, userService, authService, certificateService, modalService, http, monitoringService, router, toastr) {\n      this.fb = fb;\n      this.customerService = customerService;\n      this.contactService = contactService;\n      this.instanceService = instanceService;\n      this.instanceVersionService = instanceVersionService;\n      this.versionService = versionService;\n      this.userService = userService;\n      this.authService = authService;\n      this.certificateService = certificateService;\n      this.modalService = modalService;\n      this.http = http;\n      this.monitoringService = monitoringService;\n      this.router = router;\n      this.toastr = toastr;\n      // Zpřístupnění enumu InstanceStatus pro šablonu\n      this.InstanceStatus = InstanceStatus;\n      this.loading = false;\n      this.error = null;\n      this.customers = [];\n      this.filteredCustomers = [];\n      this.paginatedCustomers = [];\n      this.selectedCustomer = null;\n      this.contacts = [];\n      this.instances = [];\n      this.loadingContacts = false;\n      this.loadingInstances = false;\n      this.savingContact = false;\n      this.savingInstance = false;\n      this.isEditContactMode = false;\n      this.isEditInstanceMode = false;\n      this.contactError = null;\n      this.instanceError = null;\n      this.selectedContact = null;\n      this.selectedInstance = null;\n      // Filtrování a řazení\n      this.filterFields = [{\n        name: 'name',\n        label: 'Název',\n        type: 'text'\n      }, {\n        name: 'contact',\n        label: 'Kontakt',\n        type: 'text',\n        isCustom: true\n      }];\n      this.currentFilters = {};\n      this.sortColumn = 'name';\n      this.sortDirection = 'asc';\n      // Stránkování\n      this.currentPage = 1;\n      this.pageSize = 10;\n      this.Math = Math; // Pro použití v šabloně\n      this.isEditMode = false;\n      this.saving = false;\n      this.isAdmin = false;\n      // Proměnné pro správu verzí instancí\n      this.instanceVersions = {};\n      this.loadingInstanceVersions = {};\n      this.availableVersions = [];\n      this.loadingAvailableVersions = false;\n      this.availableUsers = [];\n      this.loadingAvailableUsers = false;\n      this.selectedInstanceVersion = null;\n      this.isEditInstanceVersionMode = false;\n      this.instanceVersionError = null;\n      this.savingInstanceVersion = false;\n      this.selectedInstanceForVersion = null;\n      // Proměnné pro práci s certifikáty\n      this.loadingCertificateInfo = false;\n      this.certificateInfo = null;\n      this.generatingCertificate = false;\n      this.generatedCertificate = null;\n      // Proměnné pro práci s API klíčem\n      this.regeneratingApiKey = false;\n      // Proměnné pro statistiky\n      this.loadingStatistics = false;\n      this.customerStatistics = null;\n      this.statisticsError = null;\n      this.customerForm = this.fb.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n        companyId: ['', Validators.maxLength(20)],\n        taxId: ['', Validators.maxLength(20)],\n        email: ['', [Validators.email, Validators.maxLength(255)]],\n        phone: ['', Validators.maxLength(50)],\n        website: ['', Validators.maxLength(255)],\n        street: ['', [Validators.required, Validators.maxLength(255)]],\n        city: ['', [Validators.required, Validators.maxLength(255)]],\n        postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n        country: ['', [Validators.required, Validators.maxLength(100)]],\n        notes: ['', Validators.maxLength(500)] // Changed from note to notes to match API model\n      });\n\n      this.contactForm = this.fb.group({\n        firstName: ['', [Validators.required, Validators.maxLength(100)]],\n        lastName: ['', [Validators.required, Validators.maxLength(100)]],\n        position: ['', Validators.maxLength(100)],\n        email: ['', [this.emailValidator(), Validators.maxLength(255)]],\n        phone: ['', Validators.maxLength(50)],\n        notes: ['', Validators.maxLength(100)],\n        isPrimary: [false]\n      });\n      this.instanceForm = this.fb.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n        expirationDate: [''],\n        status: ['Active'],\n        blockReason: ['', Validators.maxLength(500)],\n        moduleReporting: [true],\n        moduleAdvancedSecurity: [false],\n        moduleApiIntegration: [false],\n        moduleDataExport: [false],\n        moduleCustomization: [false],\n        notes: ['', Validators.maxLength(500)]\n      });\n      this.instanceVersionForm = this.fb.group({\n        versionId: ['', Validators.required],\n        installedByUserId: ['', Validators.required],\n        notes: ['', Validators.maxLength(500)]\n      });\n      this.loadCustomers();\n    }\n    openAddCustomerModal() {\n      // Přesměrujeme na stránku pro vytvoření nového zákazníka\n      this.router.navigate(['/customers/add']);\n    }\n    viewCustomerDetail(customer) {\n      // Místo otevření modálu přesměrujeme na detailní stránku\n      this.router.navigate(['/customers', customer.id]);\n    }\n    loadInstanceVersions(instanceId) {\n      this.loadingInstanceVersions[instanceId] = true;\n      this.instanceVersions[instanceId] = [];\n      this.instanceVersionService.getInstanceVersions(instanceId).subscribe({\n        next: versions => {\n          console.log(`Loaded versions for instance ${instanceId}:`, versions);\n          this.instanceVersions[instanceId] = versions;\n          this.loadingInstanceVersions[instanceId] = false;\n        },\n        error: err => {\n          console.error(`Chyba při načítání verzí instance ${instanceId}`, err);\n          this.loadingInstanceVersions[instanceId] = false;\n        }\n      });\n    }\n    getLatestVersion(instanceId) {\n      if (!this.instanceVersions[instanceId] || this.instanceVersions[instanceId].length === 0) {\n        return '-';\n      }\n      // Verze jsou seřazeny podle data instalace sestupně, takže první je nejnovější\n      return this.instanceVersions[instanceId][0].versionNumber;\n    }\n    openAddInstanceVersionModal(instance) {\n      console.log('Open add instance version modal', instance);\n      this.selectedInstanceForVersion = instance;\n      this.isEditInstanceVersionMode = false;\n      this.instanceVersionError = null;\n      // Reset formuláře\n      this.instanceVersionForm.reset({\n        versionId: '',\n        installedByUserId: '',\n        notes: ''\n      });\n      // Načtení dostupných verzí\n      this.loadAvailableVersions();\n      // Načtení dostupných uživatelů\n      this.loadAvailableUsers();\n      // Otevření modalu\n      this.openModal('instanceVersionModal');\n    }\n    loadAvailableVersions() {\n      this.loadingAvailableVersions = true;\n      this.availableVersions = [];\n      this.versionService.getVersions().subscribe({\n        next: versions => {\n          console.log('Loaded available versions:', versions);\n          this.availableVersions = versions;\n          this.loadingAvailableVersions = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání dostupných verzí', err);\n          this.loadingAvailableVersions = false;\n          this.instanceVersionError = `Chyba při načítání dostupných verzí: ${err.status} ${err.statusText}`;\n        }\n      });\n    }\n    loadAvailableUsers() {\n      this.loadingAvailableUsers = true;\n      this.availableUsers = [];\n      this.userService.getUsers().subscribe({\n        next: users => {\n          console.log('Loaded available users:', users);\n          this.availableUsers = users;\n          this.loadingAvailableUsers = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání dostupných uživatelů', err);\n          this.loadingAvailableUsers = false;\n          this.instanceVersionError = `Chyba při načítání dostupných uživatelů: ${err.status} ${err.statusText}`;\n        }\n      });\n    }\n    saveInstanceVersion() {\n      if (this.instanceVersionForm.invalid) {\n        this.instanceVersionError = 'Formulář obsahuje chyby. Opravte je prosím.';\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.instanceVersionForm.controls).forEach(key => {\n          const control = this.instanceVersionForm.get(key);\n          control?.markAsTouched();\n        });\n        return;\n      }\n      if (!this.selectedInstanceForVersion) {\n        this.instanceVersionError = 'Není vybrána žádná instance.';\n        return;\n      }\n      this.savingInstanceVersion = true;\n      const instanceVersionData = this.instanceVersionForm.value;\n      if (this.isEditInstanceVersionMode && this.selectedInstanceVersion) {\n        // Aktualizace existující verze instance\n        const updatedInstanceVersion = {\n          notes: instanceVersionData.notes || ''\n        };\n        console.log('Sending updated instance version data:', updatedInstanceVersion);\n        this.instanceVersionService.updateInstanceVersion(this.selectedInstanceForVersion.id, this.selectedInstanceVersion.id, updatedInstanceVersion).subscribe({\n          next: updatedInstanceVersion => {\n            console.log('Instance version updated successfully:', updatedInstanceVersion);\n            this.savingInstanceVersion = false;\n            // Aktualizovat verzi v seznamu\n            this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n            // Zavřít modal\n            this.closeInstanceVersionModal();\n          },\n          error: err => {\n            console.error('Chyba při aktualizaci verze instance', err);\n            this.instanceVersionError = `Chyba při aktualizaci verze instance: ${err.status} ${err.statusText}`;\n            this.savingInstanceVersion = false;\n          }\n        });\n      } else {\n        // Vytvoření nové verze instance\n        const newInstanceVersion = {\n          versionId: parseInt(instanceVersionData.versionId),\n          installedByUserId: parseInt(instanceVersionData.installedByUserId),\n          notes: instanceVersionData.notes || ''\n        };\n        console.log('Sending new instance version data:', newInstanceVersion);\n        this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion).subscribe({\n          next: createdInstanceVersion => {\n            console.log('Instance version created successfully:', createdInstanceVersion);\n            this.savingInstanceVersion = false;\n            // Aktualizovat seznam verzí\n            this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n            // Zavřít modal\n            this.closeInstanceVersionModal();\n          },\n          error: err => {\n            console.error('Chyba při vytváření verze instance', err);\n            this.instanceVersionError = `Chyba při vytváření verze instance: ${err.status} ${err.statusText}`;\n            this.savingInstanceVersion = false;\n          }\n        });\n      }\n    }\n    closeInstanceVersionModal() {\n      this.closeModal('instanceVersionModal');\n    }\n    viewInstanceDetail(instance) {\n      console.log('View instance detail', instance);\n      this.selectedInstanceForVersion = instance;\n      // Načtení verzí instance\n      this.loadInstanceVersions(instance.id);\n      // Načtení informací o certifikátu\n      console.log('Loading certificate info for instance ID:', instance.id);\n      this.loadCertificateInfo(instance.id);\n      // Otevřít modal\n      this.openModal('instanceDetailModal');\n    }\n    closeInstanceDetailModal() {\n      this.closeModal('instanceDetailModal');\n    }\n    // Metoda pro načtení informací o certifikátu\n    loadCertificateInfo(instanceId) {\n      console.log('loadCertificateInfo called with instanceId:', instanceId);\n      this.loadingCertificateInfo = true;\n      this.certificateInfo = null;\n      const url = `${environment.apiUrl}/certificates/instance/${instanceId}`;\n      console.log('Calling API endpoint:', url);\n      // Použijeme přímo HttpClient místo service\n      this.http.get(url).subscribe({\n        next: response => {\n          console.log('Certificate info loaded:', response);\n          this.certificateInfo = response;\n          this.loadingCertificateInfo = false;\n        },\n        error: err => {\n          console.error('Error loading certificate info', err);\n          console.error('Error details:', err.status, err.statusText);\n          if (err.error) {\n            console.error('Error message:', err.error.message || err.error);\n          }\n          this.loadingCertificateInfo = false;\n          // Pokud je chyba 404, znamená to, že instance nemá certifikát\n          if (err.status === 404) {\n            console.log('Instance nemá certifikát (404)');\n            this.certificateInfo = null;\n          } else {\n            // Pro ostatní chyby zkusíme vytvořit prázdný objekt, aby se zobrazila sekce\n            console.log('Vytvářím prázdný objekt certifikátu pro zobrazení UI');\n            this.certificateInfo = {\n              instanceId: this.selectedInstanceForVersion?.id || 0,\n              instanceName: this.selectedInstanceForVersion?.name || '',\n              customerName: '',\n              thumbprint: 'Nedostupné',\n              subject: 'Nedostupné',\n              issuer: 'Nedostupné',\n              isValid: false,\n              daysToExpiration: 0\n            };\n          }\n        }\n      });\n    }\n    // Metoda pro generování nového certifikátu\n    generateCertificate(instanceId) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const confirmed = yield _this3.modalService.confirm('Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.', 'Generování certifikátu', 'Generovat', 'Zrušit', 'btn-success', 'btn-secondary');\n        if (!confirmed) {\n          return;\n        }\n        _this3.generatingCertificate = true;\n        _this3.generatedCertificate = null;\n        const url = `${environment.apiUrl}/certificates/instance/${instanceId}/generate`;\n        console.log('Generating certificate using URL:', url);\n        _this3.http.post(url, {}).subscribe({\n          next: response => {\n            console.log('Certificate generated:', response);\n            _this3.generatedCertificate = response;\n            _this3.generatingCertificate = false;\n            // Aktualizace informací o certifikátu\n            _this3.loadCertificateInfo(instanceId);\n            // Zobrazení modálu s informacemi o vygenerovaném certifikátu\n            _this3.modalService.open('certificateGeneratedModal');\n          },\n          error: err => {\n            console.error('Error generating certificate', err);\n            _this3.generatingCertificate = false;\n            _this3.modalService.alert(`Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      })();\n    }\n    // Metoda pro stažení certifikátu\n    downloadCertificate() {\n      if (!this.generatedCertificate) {\n        return;\n      }\n      // Vytvoření a stažení souboru .pfx\n      const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    }\n    // Pomocná metoda pro konverzi Base64 na Blob\n    base64ToBlob(base64, contentType) {\n      const byteCharacters = atob(base64);\n      const byteArrays = [];\n      for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      return new Blob(byteArrays, {\n        type: contentType\n      });\n    }\n    // Metoda pro získání CSS třídy podle počtu dní do expirace certifikátu\n    getCertificateExpirationClass(daysToExpiration) {\n      if (daysToExpiration <= 7) {\n        return 'text-danger';\n      } else if (daysToExpiration <= 30) {\n        return 'text-warning';\n      } else {\n        return 'text-info';\n      }\n    }\n    editCustomer(customer) {\n      // Přesměrujeme na detailní stránku s parametrem edit=true\n      this.router.navigate(['/customers', customer.id], {\n        queryParams: {\n          edit: 'true'\n        }\n      });\n    }\n    deleteCustomer(customer) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        console.log('Delete customer', customer);\n        const confirmed = yield _this4.modalService.confirm(`Opravdu chcete smazat zákazníka ${customer.name}?`, 'Smazání zákazníka', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this4.loading = true;\n          _this4.customerService.deleteCustomer(customer.id).subscribe({\n            next: () => {\n              console.log('Customer deleted successfully');\n              _this4.toastr.success(`Zákazník ${customer.name} byl úspěšně smazán`, 'Úspěch');\n              _this4.loadCustomers();\n            },\n            error: err => {\n              console.error('Chyba při mazání zákazníka', err);\n              _this4.modalService.alert(`Chyba při mazání zákazníka: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n              _this4.loading = false;\n            }\n          });\n        }\n      })();\n    }\n    ngOnInit() {\n      this.isAdmin = this.authService.isAdmin();\n      console.log('User is admin:', this.isAdmin);\n      // Načtení posledního použitého filtru z localStorage\n      try {\n        const lastFilterKey = `last_filter_customers`;\n        const lastFilterJson = localStorage.getItem(lastFilterKey);\n        if (lastFilterJson) {\n          this.currentFilters = JSON.parse(lastFilterJson);\n          console.log('Načten poslední filtr z localStorage:', this.currentFilters);\n        }\n      } catch (error) {\n        console.error('Chyba při načítání posledního filtru z localStorage', error);\n      }\n      this.loadCustomers();\n    }\n    /**\r\n     * Metody pro filtrování a řazení\r\n     */\n    onFilterChange(filters) {\n      this.currentFilters = filters;\n      this.currentPage = 1; // Reset na první stránku při změně filtru\n      // Uložení aktuálního filtru do localStorage\n      try {\n        const lastFilterKey = `last_filter_customers`;\n        if (Object.keys(filters).length > 0) {\n          localStorage.setItem(lastFilterKey, JSON.stringify(filters));\n          console.log('Uložen aktuální filtr do localStorage:', filters);\n        } else {\n          // Pokud je filtr prázdný, odstraníme ho z localStorage\n          localStorage.removeItem(lastFilterKey);\n          console.log('Odstraněn filtr z localStorage');\n        }\n      } catch (error) {\n        console.error('Chyba při ukládání filtru do localStorage', error);\n      }\n      // Načtení dat z backendu s filtrem\n      this.loadCustomers();\n    }\n    onSort(column) {\n      if (this.sortColumn === column) {\n        // Pokud klikneme na stejný sloupec, změníme směr řazení\n        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n      } else {\n        // Pokud klikneme na jiný sloupec, nastavit výchozí směr řazení\n        this.sortColumn = column;\n        this.sortDirection = 'asc';\n      }\n      this.applyFiltersAndSort();\n      this.updatePaginatedCustomers();\n    }\n    applyFiltersAndSort() {\n      // Filtrování se provádí na backendu, zde pouze řadíme\n      this.filteredCustomers = [...this.customers];\n      // Řazení\n      this.filteredCustomers.sort((a, b) => {\n        const valueA = a[this.sortColumn]?.toString().toLowerCase() || '';\n        const valueB = b[this.sortColumn]?.toString().toLowerCase() || '';\n        if (this.sortDirection === 'asc') {\n          return valueA.localeCompare(valueB);\n        } else {\n          return valueB.localeCompare(valueA);\n        }\n      });\n    }\n    /**\r\n     * Metody pro stránkování\r\n     */\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updatePaginatedCustomers();\n    }\n    updatePaginatedCustomers() {\n      const startIndex = (this.currentPage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);\n    }\n    get totalPages() {\n      return Math.ceil(this.filteredCustomers.length / this.pageSize);\n    }\n    get pageRange() {\n      const range = [];\n      const maxPages = 5;\n      const startPage = Math.max(1, this.currentPage - Math.floor(maxPages / 2));\n      const endPage = Math.min(this.totalPages, startPage + maxPages - 1);\n      for (let i = startPage; i <= endPage; i++) {\n        range.push(i);\n      }\n      return range;\n    }\n    saveCustomer() {\n      console.log('saveCustomer() called');\n      if (!this.isAdmin) {\n        this.error = 'Pouze administrátor může vytvářet zákazníky';\n        console.log('User is not admin, returning');\n        return;\n      }\n      if (this.customerForm.invalid) {\n        this.error = 'Formulář obsahuje chyby. Opravte je prosím.';\n        console.log('Form is invalid, marking fields as touched');\n        // Označit všechna pole jako touched, aby se zobrazily chyby\n        Object.keys(this.customerForm.controls).forEach(key => {\n          const control = this.customerForm.get(key);\n          control?.markAsTouched();\n          if (control?.invalid) {\n            console.log(`Field ${key} is invalid:`, control.errors);\n          }\n        });\n        return;\n      }\n      const customerData = this.customerForm.value;\n      console.log('Form is valid, saving customer data:', customerData);\n      this.saving = true;\n      if (this.isEditMode && this.selectedCustomer) {\n        // Aktualizace existujícího zákazníka\n        console.log('Updating customer with ID:', this.selectedCustomer.id);\n        this.customerService.updateCustomer(this.selectedCustomer.id, customerData).subscribe({\n          next: updatedCustomer => {\n            console.log('Customer updated successfully:', updatedCustomer);\n            this.saving = false;\n            this.closeCustomerModal();\n            // Obnovit seznam\n            this.loadCustomers();\n          },\n          error: err => {\n            console.error('Chyba při aktualizaci zákazníka', err);\n            console.error('Status:', err.status);\n            console.error('Status text:', err.statusText);\n            console.error('Error details:', err.error);\n            this.error = `Chyba při aktualizaci zákazníka: ${err.status} ${err.statusText}`;\n            this.saving = false;\n          }\n        });\n      } else {\n        // Vytvoření nového zákazníka\n        this.customerService.createCustomer(customerData).subscribe({\n          next: createdCustomer => {\n            console.log('Customer created successfully:', createdCustomer);\n            this.saving = false;\n            // Zavřít modal\n            this.closeCustomerModal();\n            // Obnovit seznam\n            this.loadCustomers();\n          },\n          error: err => {\n            console.error('Chyba při ukládání zákazníka', err);\n            console.error('Status:', err.status);\n            console.error('Status text:', err.statusText);\n            console.error('Error details:', err.error);\n            this.error = `Chyba při ukládání zákazníka: ${err.status} ${err.statusText}`;\n            this.saving = false;\n          }\n        });\n      }\n    }\n    loadCustomers() {\n      this.loading = true;\n      // Použití filtrů při načítání dat\n      this.customerService.getCustomers(this.currentFilters).subscribe({\n        next: customers => {\n          console.log('Načteni zákazníci:', customers);\n          this.customers = customers || [];\n          // Kontakty jsou již načteny z backendu, není potřeba je načítat znovu\n          // Pouze logujeme počet kontaktů pro kontrolu\n          this.customers.forEach(customer => {\n            if (customer.contacts) {\n              console.log(`Zákazník ${customer.name} má ${customer.contacts.length} kontaktů`);\n            }\n          });\n          this.filteredCustomers = [...this.customers];\n          // Řazení stále provádíme na klientské straně\n          this.applyFiltersAndSort();\n          this.updatePaginatedCustomers();\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání zákazníků', err);\n          this.loading = false;\n        }\n      });\n    }\n    /**\r\n     * Kopírování API klíče do schránky\r\n     * @param inputElement Reference na input element s API klíčem\r\n     */\n    copyApiKey(inputElement) {\n      inputElement.select();\n      document.execCommand('copy');\n      this.modalService.alert('API klíč byl zkopírován do schránky.', 'Informace', 'OK', 'btn-success');\n    }\n    /**\r\n     * Regenerace API klíče pro instanci\r\n     * @param instanceId ID instance\r\n     */\n    regenerateApiKey(instanceId) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        const confirmed = yield _this5.modalService.confirm('Opravdu chcete regenerovat API klíč? Všechny aplikace používající stávající klíč budou muset být aktualizovány.', 'Regenerace API klíče', 'Regenerovat', 'Zrušit', 'btn-warning', 'btn-secondary');\n        if (confirmed) {\n          _this5.regeneratingApiKey = true;\n          _this5.instanceService.regenerateApiKey(instanceId).subscribe({\n            next: () => {\n              // Po úspěšné regeneraci načti aktualizovaná data instance\n              _this5.instanceService.getInstance(instanceId).subscribe({\n                next: updatedInstance => {\n                  // Aktualizovat instanci v seznamu\n                  const index = _this5.instances.findIndex(i => i.id === instanceId);\n                  if (index !== -1) {\n                    _this5.instances[index] = updatedInstance;\n                  }\n                  // Aktualizovat vybranou instanci\n                  if (_this5.selectedInstanceForVersion && _this5.selectedInstanceForVersion.id === instanceId) {\n                    _this5.selectedInstanceForVersion = updatedInstance;\n                  }\n                  _this5.regeneratingApiKey = false;\n                  _this5.modalService.alert('API klíč byl úspěšně regenerován.', 'Informace', 'OK', 'btn-success');\n                },\n                error: err => {\n                  console.error('Chyba při získávání aktualizované instance', err);\n                  _this5.regeneratingApiKey = false;\n                  _this5.modalService.alert(`Chyba při získávání aktualizované instance: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n                }\n              });\n            },\n            error: err => {\n              console.error('Chyba při regeneraci API klíče', err);\n              _this5.regeneratingApiKey = false;\n              _this5.modalService.alert(`Chyba při regeneraci API klíče: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n            }\n          });\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function CustomersComponent_Factory(t) {\n        return new (t || CustomersComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContactService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.InstanceVersionService), i0.ɵɵdirectiveInject(i6.VersionService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i9.CertificateService), i0.ɵɵdirectiveInject(i10.ModalService), i0.ɵɵdirectiveInject(i11.HttpClient), i0.ɵɵdirectiveInject(i12.MonitoringService), i0.ɵɵdirectiveInject(i13.Router), i0.ɵɵdirectiveInject(i14.ToastrService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomersComponent,\n        selectors: [[\"app-customers\"]],\n        decls: 299,\n        vars: 127,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"btn btn-success text-white\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"routerLink\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-body\"], [3, \"entityType\", \"fields\", \"filterChange\"], [\"class\", \"d-flex justify-content-center mt-4\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-4\", 4, \"ngIf\"], [\"class\", \"alert alert-info mt-4\", 4, \"ngIf\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-between align-items-center mt-3\", 4, \"ngIf\"], [\"id\", \"customerModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"customerModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"customerModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"name\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"abbreviation\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"abbreviation\", \"formControlName\", \"abbreviation\", 1, \"form-control\"], [\"for\", \"taxId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taxId\", \"formControlName\", \"taxId\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"website\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"website\", \"formControlName\", \"website\", 1, \"form-control\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"street\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"street\", \"formControlName\", \"street\", 1, \"form-control\"], [1, \"col-md-4\", \"mb-3\"], [\"for\", \"city\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"city\", \"formControlName\", \"city\", 1, \"form-control\"], [\"for\", \"postalCode\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"postalCode\", \"formControlName\", \"postalCode\", 1, \"form-control\"], [\"for\", \"country\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"country\", \"formControlName\", \"country\", 1, \"form-control\"], [1, \"mb-3\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"class\", \"mt-4 mb-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"bi\", \"bi-x-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", 4, \"ngIf\"], [\"class\", \"bi bi-save me-1\", 4, \"ngIf\"], [\"id\", \"customerDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"customerDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"customerDetailModalLabel\", 1, \"modal-title\"], [\"class\", \"modal-body\", 4, \"ngIf\"], [\"id\", \"contactModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"contactModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [\"id\", \"contactModalLabel\", 1, \"modal-title\"], [\"for\", \"firstName\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", 1, \"form-control\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", 1, \"form-control\"], [\"for\", \"contactEmail\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"contactEmail\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"contactPhone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"contactPhone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"contactNote\", 1, \"form-label\"], [\"id\", \"contactNote\", \"formControlName\", \"notes\", \"rows\", \"2\", 1, \"form-control\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isPrimary\", \"formControlName\", \"isPrimary\", 1, \"form-check-input\"], [\"for\", \"isPrimary\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"id\", \"instanceModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"instanceName\", \"formControlName\", \"name\", 1, \"form-control\"], [\"for\", \"instanceServerUrl\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"instanceServerUrl\", \"formControlName\", \"serverUrl\", 1, \"form-control\"], [\"for\", \"instanceExpirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"instanceExpirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"instanceStatus\", 1, \"form-label\"], [\"id\", \"instanceStatus\", \"formControlName\", \"status\", 1, \"form-select\"], [\"value\", \"Active\"], [\"value\", \"Blocked\"], [\"value\", \"Expired\"], [\"value\", \"Trial\"], [\"value\", \"Maintenance\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"form-label\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"for\", \"instanceNotes\", 1, \"form-label\"], [\"id\", \"instanceNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"id\", \"instanceVersionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceVersionModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\", 2, \"z-index\", \"1070 !important\"], [\"id\", \"instanceVersionModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceName\", \"disabled\", \"\", 1, \"form-control\", 3, \"value\"], [\"for\", \"versionId\", 1, \"form-label\", \"required-field\"], [\"id\", \"versionId\", \"formControlName\", \"versionId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"installedByUserId\", 1, \"form-label\", \"required-field\"], [\"id\", \"installedByUserId\", \"formControlName\", \"installedByUserId\", 1, \"form-select\"], [\"for\", \"instanceVersionNotes\", 1, \"form-label\"], [\"id\", \"instanceVersionNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"id\", \"instanceDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceDetailModalLabel\", 1, \"modal-title\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"mt-3\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"me-2\", 3, \"disabled\", \"routerLink\", \"click\"], [1, \"bi\", \"bi-graph-up\", \"me-1\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\", \"me-1\"], [\"id\", \"certificateGeneratedModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"certificateGeneratedModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [\"id\", \"certificateGeneratedModalLabel\", 1, \"modal-title\"], [1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"btn\", \"btn-success\", \"text-white\", 3, \"routerLink\"], [1, \"bi\", \"bi-magic\", \"me-2\"], [1, \"d-none\", \"d-md-inline\"], [1, \"d-inline\", \"d-md-none\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [1, \"bi\", \"bi-building-fill-add\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mt-4\"], [1, \"alert\", \"alert-info\", \"mt-4\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [1, \"sortable-header\", 3, \"click\"], [1, \"bi\"], [1, \"sortable-header\", \"d-none\", \"d-md-table-cell\", 3, \"click\"], [1, \"sortable-header\", \"d-none\", \"d-lg-table-cell\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-md-none\", \"small\", \"text-muted\"], [1, \"d-lg-none\", \"small\", \"text-muted\"], [1, \"d-none\", \"d-md-table-cell\"], [1, \"d-none\", \"d-lg-table-cell\"], [1, \"btn-group\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-eye-fill\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Upravit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", \"title\", \"Smazat\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"pagination-info\"], [\"aria-label\", \"Str\\u00E1nkov\\u00E1n\\u00ED\"], [1, \"pagination\", \"mb-0\"], [1, \"page-item\"], [\"href\", \"javascript:void(0)\", 1, \"page-link\", 3, \"click\"], [1, \"bi\", \"bi-chevron-double-left\"], [1, \"bi\", \"bi-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-chevron-right\"], [1, \"bi\", \"bi-chevron-double-right\"], [1, \"alert\", \"alert-danger\", \"mb-3\"], [1, \"invalid-feedback\"], [1, \"mt-4\", \"mb-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-person-plus-fill\", \"me-1\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [\"class\", \"badge bg-primary ms-2\", 4, \"ngIf\"], [3, \"href\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"badge\", \"bg-primary\", \"ms-2\"], [3, \"href\"], [\"type\", \"button\", \"title\", \"P\\u0159idat instanci\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Zobrazit metriky\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"routerLink\"], [1, \"bi\", \"bi-graph-up\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [\"type\", \"button\", \"title\", \"P\\u0159idat verzi\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"ms-2\", 3, \"click\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [1, \"bi\", \"bi-save\", \"me-1\"], [1, \"row\", \"mb-4\"], [1, \"col-md-6\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-light\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"mb-3\", \"mt-4\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"bg-primary\", \"text-white\"], [1, \"card-body\", \"text-center\"], [1, \"display-4\"], [1, \"card\", \"bg-success\", \"text-white\"], [1, \"card\", \"bg-info\", \"text-white\"], [1, \"card\", \"bg-warning\", \"text-dark\"], [1, \"card-title\"], [1, \"alert\", \"alert-danger\"], [1, \"text-danger\"], [\"for\", \"instanceBlockReason\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceBlockReason\", \"formControlName\", \"blockReason\", 1, \"form-control\"], [3, \"value\"], [1, \"row\", \"mb-3\"], [1, \"col-md-12\"], [1, \"input-group\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"value\"], [\"apiKeyInput\", \"\"], [\"type\", \"button\", 1, \"btn\", \"input-group-button\", 3, \"click\"], [1, \"bi\", \"bi-clipboard\"], [\"type\", \"button\", 1, \"btn\", \"input-group-button\", 3, \"disabled\", \"click\"], [\"class\", \"bi bi-arrow-repeat\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-2\"], [1, \"text-muted\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"routerLink\", \"click\"], [1, \"bi\", \"bi-shield-lock\", \"me-1\"], [1, \"mb-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"bi bi-shield-plus me-2\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\", \"me-2\"], [1, \"bi\", \"bi-arrow-repeat\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"bi\", \"bi-shield-plus\", \"me-2\"], [1, \"bi\", \"bi-info-circle\", \"me-2\"], [1, \"bi\", \"bi-gear\", \"me-1\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [\"class\", \"ms-2\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [1, \"ms-2\", 3, \"ngClass\"], [1, \"text-nowrap\"], [1, \"mt-3\"], [1, \"list-group\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-monospace\", \"font-weight-bold\"], [1, \"alert\", \"alert-warning\", \"mt-3\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-download\", \"me-2\"]],\n        template: function CustomersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Spr\\u00E1va z\\u00E1kazn\\u00EDk\\u016F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2);\n            i0.ɵɵtemplate(5, CustomersComponent_a_5_Template, 6, 2, \"a\", 3);\n            i0.ɵɵtemplate(6, CustomersComponent_a_6_Template, 6, 2, \"a\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"app-advanced-filter\", 7);\n            i0.ɵɵlistener(\"filterChange\", function CustomersComponent_Template_app_advanced_filter_filterChange_9_listener($event) {\n              return ctx.onFilterChange($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, CustomersComponent_div_10_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(11, CustomersComponent_div_11_Template, 2, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, CustomersComponent_div_12_Template, 2, 0, \"div\", 10);\n            i0.ɵɵtemplate(13, CustomersComponent_div_13_Template, 23, 31, \"div\", 11);\n            i0.ɵɵtemplate(14, CustomersComponent_div_14_Template, 18, 12, \"div\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\", 14)(17, \"div\", 15)(18, \"div\", 16)(19, \"h5\", 17);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 19);\n            i0.ɵɵtemplate(23, CustomersComponent_div_23_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementStart(24, \"form\", 21);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_24_listener() {\n              return ctx.saveCustomer();\n            });\n            i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"label\", 24);\n            i0.ɵɵtext(28, \"N\\u00E1zev\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"input\", 25);\n            i0.ɵɵtemplate(30, CustomersComponent_div_30_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 23)(32, \"label\", 27);\n            i0.ɵɵtext(33, \"Zkratka\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(34, \"input\", 28);\n            i0.ɵɵtemplate(35, CustomersComponent_div_35_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23)(38, \"label\", 29);\n            i0.ɵɵtext(39, \"DI\\u010C\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 30);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 23)(42, \"label\", 31);\n            i0.ɵɵtext(43, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 32);\n            i0.ɵɵtemplate(45, CustomersComponent_div_45_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 22)(47, \"div\", 23)(48, \"label\", 33);\n            i0.ɵɵtext(49, \"Telefon\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(50, \"input\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 23)(52, \"label\", 35);\n            i0.ɵɵtext(53, \"Webov\\u00E9 str\\u00E1nky\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(54, \"input\", 36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 22)(56, \"div\", 37)(57, \"label\", 38);\n            i0.ɵɵtext(58, \"Ulice\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(59, \"input\", 39);\n            i0.ɵɵtemplate(60, CustomersComponent_div_60_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(61, \"div\", 22)(62, \"div\", 40)(63, \"label\", 41);\n            i0.ɵɵtext(64, \"M\\u011Bsto\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(65, \"input\", 42);\n            i0.ɵɵtemplate(66, CustomersComponent_div_66_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"div\", 40)(68, \"label\", 43);\n            i0.ɵɵtext(69, \"PS\\u010C\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(70, \"input\", 44);\n            i0.ɵɵtemplate(71, CustomersComponent_div_71_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"div\", 40)(73, \"label\", 45);\n            i0.ɵɵtext(74, \"Zem\\u011B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(75, \"input\", 46);\n            i0.ɵɵtemplate(76, CustomersComponent_div_76_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(77, \"div\", 47)(78, \"label\", 48);\n            i0.ɵɵtext(79, \"Pozn\\u00E1mka\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(80, \"textarea\", 49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(81, CustomersComponent_div_81_Template, 10, 3, \"div\", 50);\n            i0.ɵɵtemplate(82, CustomersComponent_div_82_Template, 9, 3, \"div\", 50);\n            i0.ɵɵelementStart(83, \"div\", 51)(84, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_84_listener() {\n              return ctx.closeCustomerModal();\n            });\n            i0.ɵɵelement(85, \"i\", 53);\n            i0.ɵɵtext(86, \"Zav\\u0159\\u00EDt \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"button\", 54);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_87_listener() {\n              return ctx.saveCustomer();\n            });\n            i0.ɵɵtemplate(88, CustomersComponent_span_88_Template, 1, 0, \"span\", 55);\n            i0.ɵɵtemplate(89, CustomersComponent_i_89_Template, 1, 0, \"i\", 56);\n            i0.ɵɵtext(90);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(91, \"div\", 57)(92, \"div\", 14)(93, \"div\", 15)(94, \"div\", 16)(95, \"h5\", 58);\n            i0.ɵɵtext(96, \"Detail z\\u00E1kazn\\u00EDka\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(97, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(98, CustomersComponent_div_98_Template, 59, 22, \"div\", 59);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(99, \"div\", 60)(100, \"div\", 61)(101, \"div\", 15)(102, \"div\", 16)(103, \"h5\", 62);\n            i0.ɵɵtext(104);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(105, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"div\", 19);\n            i0.ɵɵtemplate(107, CustomersComponent_div_107_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementStart(108, \"form\", 21);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_108_listener() {\n              return ctx.saveContact();\n            });\n            i0.ɵɵelementStart(109, \"div\", 22)(110, \"div\", 23)(111, \"label\", 63);\n            i0.ɵɵtext(112, \"Jm\\u00E9no\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(113, \"input\", 64);\n            i0.ɵɵtemplate(114, CustomersComponent_div_114_Template, 2, 0, \"div\", 65);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 23)(116, \"label\", 66);\n            i0.ɵɵtext(117, \"P\\u0159\\u00EDjmen\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(118, \"input\", 67);\n            i0.ɵɵtemplate(119, CustomersComponent_div_119_Template, 2, 0, \"div\", 65);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(120, \"div\", 47)(121, \"label\", 68);\n            i0.ɵɵtext(122, \"Pozice\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(123, \"input\", 69);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(124, \"div\", 47)(125, \"label\", 70);\n            i0.ɵɵtext(126, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(127, \"input\", 71);\n            i0.ɵɵtemplate(128, CustomersComponent_div_128_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"div\", 47)(130, \"label\", 72);\n            i0.ɵɵtext(131, \"Telefon\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(132, \"input\", 73);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"div\", 47)(134, \"label\", 74);\n            i0.ɵɵtext(135, \"Pozn\\u00E1mka\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(136, \"textarea\", 75);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"div\", 76);\n            i0.ɵɵelement(138, \"input\", 77);\n            i0.ɵɵelementStart(139, \"label\", 78);\n            i0.ɵɵtext(140, \"Hlavn\\u00ED kontakt\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(141, \"div\", 51)(142, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_142_listener() {\n              return ctx.closeContactModal();\n            });\n            i0.ɵɵelement(143, \"i\", 53);\n            i0.ɵɵtext(144, \"Zav\\u0159\\u00EDt \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(145, \"button\", 79);\n            i0.ɵɵtemplate(146, CustomersComponent_span_146_Template, 1, 0, \"span\", 55);\n            i0.ɵɵtemplate(147, CustomersComponent_i_147_Template, 1, 0, \"i\", 56);\n            i0.ɵɵtext(148);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(149, \"div\", 80)(150, \"div\", 61)(151, \"div\", 15)(152, \"div\", 16)(153, \"h5\", 81);\n            i0.ɵɵtext(154);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(155, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(156, \"div\", 19);\n            i0.ɵɵtemplate(157, CustomersComponent_div_157_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementStart(158, \"form\", 21);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_158_listener() {\n              return ctx.saveInstance();\n            });\n            i0.ɵɵelementStart(159, \"div\", 47)(160, \"label\", 82);\n            i0.ɵɵtext(161, \"N\\u00E1zev instance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(162, \"input\", 83);\n            i0.ɵɵtemplate(163, CustomersComponent_div_163_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(164, \"div\", 47)(165, \"label\", 84);\n            i0.ɵɵtext(166, \"URL serveru\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(167, \"input\", 85);\n            i0.ɵɵtemplate(168, CustomersComponent_div_168_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(169, \"div\", 47)(170, \"label\", 86);\n            i0.ɵɵtext(171, \"Datum expirace\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(172, \"input\", 87);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(173, \"div\", 47)(174, \"label\", 88);\n            i0.ɵɵtext(175, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(176, \"select\", 89)(177, \"option\", 90);\n            i0.ɵɵtext(178, \"Aktivn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(179, \"option\", 91);\n            i0.ɵɵtext(180, \"Blokovan\\u00E1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(181, \"option\", 92);\n            i0.ɵɵtext(182, \"Expirovan\\u00E1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(183, \"option\", 93);\n            i0.ɵɵtext(184, \"Zku\\u0161ebn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(185, \"option\", 94);\n            i0.ɵɵtext(186, \"\\u00DAdr\\u017Eba\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(187, CustomersComponent_div_187_Template, 4, 0, \"div\", 95);\n            i0.ɵɵelementStart(188, \"div\", 47)(189, \"label\", 96);\n            i0.ɵɵtext(190, \"Povolen\\u00E9 moduly\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(191, \"div\", 97);\n            i0.ɵɵelement(192, \"input\", 98);\n            i0.ɵɵelementStart(193, \"label\", 99);\n            i0.ɵɵtext(194, \"Reportov\\u00E1n\\u00ED\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(195, \"div\", 97);\n            i0.ɵɵelement(196, \"input\", 100);\n            i0.ɵɵelementStart(197, \"label\", 101);\n            i0.ɵɵtext(198, \"Pokro\\u010Dil\\u00E1 bezpe\\u010Dnost\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(199, \"div\", 97);\n            i0.ɵɵelement(200, \"input\", 102);\n            i0.ɵɵelementStart(201, \"label\", 103);\n            i0.ɵɵtext(202, \"API integrace\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(203, \"div\", 97);\n            i0.ɵɵelement(204, \"input\", 104);\n            i0.ɵɵelementStart(205, \"label\", 105);\n            i0.ɵɵtext(206, \"Export dat\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(207, \"div\", 97);\n            i0.ɵɵelement(208, \"input\", 106);\n            i0.ɵɵelementStart(209, \"label\", 107);\n            i0.ɵɵtext(210, \"P\\u0159izp\\u016Fsoben\\u00ED\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(211, \"div\", 47)(212, \"label\", 108);\n            i0.ɵɵtext(213, \"Pozn\\u00E1mka\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(214, \"textarea\", 109);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(215, \"div\", 51)(216, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_216_listener() {\n              return ctx.closeInstanceModal();\n            });\n            i0.ɵɵelement(217, \"i\", 53);\n            i0.ɵɵtext(218, \"Zav\\u0159\\u00EDt \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(219, \"button\", 79);\n            i0.ɵɵtemplate(220, CustomersComponent_span_220_Template, 1, 0, \"span\", 55);\n            i0.ɵɵtemplate(221, CustomersComponent_i_221_Template, 1, 0, \"i\", 56);\n            i0.ɵɵtext(222);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(223, \"div\", 110)(224, \"div\", 61)(225, \"div\", 15)(226, \"div\", 16)(227, \"h5\", 111);\n            i0.ɵɵtext(228);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(229, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(230, \"div\", 19);\n            i0.ɵɵtemplate(231, CustomersComponent_div_231_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementStart(232, \"form\", 21);\n            i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_232_listener() {\n              return ctx.saveInstanceVersion();\n            });\n            i0.ɵɵelementStart(233, \"div\", 47)(234, \"label\", 112);\n            i0.ɵɵtext(235, \"Instance:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(236, \"input\", 113);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(237, \"div\", 47)(238, \"label\", 114);\n            i0.ɵɵtext(239, \"Verze:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(240, \"select\", 115)(241, \"option\", 116);\n            i0.ɵɵtext(242, \"-- Vyberte verzi --\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(243, CustomersComponent_option_243_Template, 3, 6, \"option\", 117);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(244, CustomersComponent_div_244_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(245, \"div\", 47)(246, \"label\", 118);\n            i0.ɵɵtext(247, \"Instaloval:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(248, \"select\", 119)(249, \"option\", 116);\n            i0.ɵɵtext(250, \"-- Vyberte u\\u017Eivatele --\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(251, CustomersComponent_option_251_Template, 2, 3, \"option\", 117);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(252, CustomersComponent_div_252_Template, 2, 0, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(253, \"div\", 47)(254, \"label\", 120);\n            i0.ɵɵtext(255, \"Pozn\\u00E1mka:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(256, \"textarea\", 121);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(257, \"div\", 51)(258, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_258_listener() {\n              return ctx.closeInstanceVersionModal();\n            });\n            i0.ɵɵelement(259, \"i\", 53);\n            i0.ɵɵtext(260, \"Zav\\u0159\\u00EDt \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(261, \"button\", 79);\n            i0.ɵɵtemplate(262, CustomersComponent_span_262_Template, 1, 0, \"span\", 55);\n            i0.ɵɵtemplate(263, CustomersComponent_i_263_Template, 1, 0, \"i\", 56);\n            i0.ɵɵtext(264);\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(265, \"div\", 122)(266, \"div\", 14)(267, \"div\", 15)(268, \"div\", 16)(269, \"h5\", 123);\n            i0.ɵɵtext(270);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(271, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(272, \"div\", 19);\n            i0.ɵɵtemplate(273, CustomersComponent_div_273_Template, 82, 39, \"div\", 124);\n            i0.ɵɵelementStart(274, \"div\", 125)(275, \"button\", 126);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_275_listener() {\n              return ctx.closeInstanceDetailModal();\n            });\n            i0.ɵɵelement(276, \"i\", 127);\n            i0.ɵɵtext(277, \"Metriky \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(278, \"button\", 128);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_278_listener() {\n              return ctx.closeInstanceDetailModal();\n            });\n            i0.ɵɵelement(279, \"i\", 53);\n            i0.ɵɵtext(280, \"Zav\\u0159\\u00EDt \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(281, \"button\", 54);\n            i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_281_listener() {\n              return ctx.editInstance(ctx.selectedInstanceForVersion);\n            });\n            i0.ɵɵelement(282, \"i\", 129);\n            i0.ɵɵtext(283, \"Upravit \");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(284, \"div\", 130)(285, \"div\", 61)(286, \"div\", 15)(287, \"div\", 131)(288, \"h5\", 132);\n            i0.ɵɵtext(289, \"Certifik\\u00E1t vygenerov\\u00E1n\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(290, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(291, \"div\", 19)(292, \"div\", 133);\n            i0.ɵɵelement(293, \"i\", 134);\n            i0.ɵɵtext(294, \" Certifik\\u00E1t byl \\u00FAsp\\u011B\\u0161n\\u011B vygenerov\\u00E1n. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(295, CustomersComponent_div_295_Template, 29, 6, \"div\", 124);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(296, \"div\", 51)(297, \"button\", 135);\n            i0.ɵɵtext(298, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_12_0;\n            let tmp_13_0;\n            let tmp_14_0;\n            let tmp_15_0;\n            let tmp_16_0;\n            let tmp_17_0;\n            let tmp_18_0;\n            let tmp_19_0;\n            let tmp_20_0;\n            let tmp_21_0;\n            let tmp_22_0;\n            let tmp_23_0;\n            let tmp_24_0;\n            let tmp_25_0;\n            let tmp_26_0;\n            let tmp_27_0;\n            let tmp_28_0;\n            let tmp_29_0;\n            let tmp_30_0;\n            let tmp_31_0;\n            let tmp_32_0;\n            let tmp_43_0;\n            let tmp_44_0;\n            let tmp_45_0;\n            let tmp_46_0;\n            let tmp_47_0;\n            let tmp_48_0;\n            let tmp_56_0;\n            let tmp_57_0;\n            let tmp_58_0;\n            let tmp_59_0;\n            let tmp_60_0;\n            let tmp_69_0;\n            let tmp_71_0;\n            let tmp_72_0;\n            let tmp_74_0;\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"entityType\", \"customers\")(\"fields\", ctx.filterFields);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length > 0);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Upravit z\\u00E1kazn\\u00EDka\" : \"P\\u0159idat z\\u00E1kazn\\u00EDka\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.customerForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(98, _c6, (tmp_12_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_12_0.invalid, (tmp_12_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_12_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_13_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_13_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_14_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(101, _c6, (tmp_15_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_15_0.invalid, (tmp_15_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_15_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_16_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_16_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_17_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(104, _c6, (tmp_18_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_18_0.invalid, (tmp_18_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_18_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_19_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_19_0.invalid) && ((tmp_19_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_19_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_20_0.touched));\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(107, _c6, (tmp_21_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_21_0.invalid, (tmp_21_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_21_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_22_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_22_0.invalid) && ((tmp_22_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_22_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_23_0.invalid) && ((tmp_23_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_23_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(110, _c6, (tmp_24_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_24_0.invalid, (tmp_24_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_24_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_25_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_25_0.invalid) && ((tmp_25_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_25_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_26_0.invalid) && ((tmp_26_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_26_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(113, _c6, (tmp_27_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_27_0.invalid, (tmp_27_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_27_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_28_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_28_0.invalid) && ((tmp_28_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_28_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_29_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_29_0.invalid) && ((tmp_29_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_29_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(116, _c6, (tmp_30_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_30_0.invalid, (tmp_30_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_30_0.valid));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_31_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_31_0.invalid) && ((tmp_31_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_31_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_32_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_32_0.invalid) && ((tmp_32_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_32_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.customerForm.invalid || ctx.saving);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.saving);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.saving);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\"\", ctx.isEditMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedCustomer);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditContactMode ? \"Upravit kontakt\" : \"P\\u0159idat kontakt\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.contactError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.contactForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(119, _c6, (tmp_43_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_43_0.invalid, (tmp_43_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_43_0.valid));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_44_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_44_0.invalid) && ((tmp_44_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_44_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(122, _c6, (tmp_45_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_45_0.invalid, (tmp_45_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_45_0.valid));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_46_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_46_0.invalid) && ((tmp_46_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_46_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_47_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_47_0.invalid) && ((tmp_47_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_47_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_48_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_48_0.invalid) && ((tmp_48_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_48_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"disabled\", ctx.contactForm.invalid || ctx.savingContact);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingContact);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.savingContact);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\"\", ctx.isEditContactMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditInstanceMode ? \"Upravit instanci DIS\" : \"P\\u0159idat instanci DIS\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.instanceError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.instanceForm);\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_56_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_56_0.invalid) && ((tmp_56_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_56_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_57_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_57_0.invalid) && ((tmp_57_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_57_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_58_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_58_0.invalid) && ((tmp_58_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_58_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_59_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_59_0.invalid) && ((tmp_59_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_59_0.touched));\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_60_0 = ctx.instanceForm.get(\"status\")) == null ? null : tmp_60_0.value) === \"Blocked\");\n            i0.ɵɵadvance(32);\n            i0.ɵɵproperty(\"disabled\", ctx.instanceForm.invalid || ctx.savingInstance);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingInstance);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.savingInstance);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\"\", ctx.isEditInstanceMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.isEditInstanceVersionMode ? \"Upravit verzi instance\" : \"P\\u0159idat verzi instance\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.instanceVersionError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.instanceVersionForm);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name);\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_69_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_69_0.invalid) && ((tmp_69_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_69_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableVersions);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_71_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_71_0.invalid) && ((tmp_71_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_71_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_72_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_72_0.invalid) && ((tmp_72_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_72_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableUsers);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_74_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_74_0.invalid) && ((tmp_74_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_74_0.touched));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"disabled\", ctx.instanceVersionForm.invalid || ctx.savingInstanceVersion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.savingInstanceVersion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.savingInstanceVersion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\"\", ctx.isEditInstanceVersionMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate2(\"Detail instance \", ctx.selectedCustomer == null ? null : ctx.selectedCustomer.abbreviation, \" - \", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name, \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedInstanceForVersion);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedInstanceForVersion)(\"routerLink\", i0.ɵɵpureFunction1(125, _c3, ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.id));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedInstanceForVersion);\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"ngIf\", ctx.generatedCertificate);\n          }\n        },\n        dependencies: [i15.NgClass, i15.NgForOf, i15.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i13.RouterLink, i16.AdvancedFilterComponent, i15.DecimalPipe, i15.DatePipe]\n      });\n    }\n  }\n  return CustomersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}