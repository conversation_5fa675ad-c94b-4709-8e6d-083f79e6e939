{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/security.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../services/modal.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/common\";\nfunction SecurityComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction SecurityComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E1 aktivn\\u00ED upozorn\\u011Bn\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_8_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\", 48);\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 50)(6, \"span\", 49);\n    i0.ɵɵelement(7, \"i\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_8_tr_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const alert_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.openResolveAlertModal(alert_r15));\n    });\n    i0.ɵɵelement(20, \"i\", 17);\n    i0.ɵɵtext(21, \"Vy\\u0159e\\u0161it \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const alert_r15 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getSeverityClass(alert_r15.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getSeverityIcon(alert_r15.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getSeverityText(alert_r15.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getAlertTypeClass(alert_r15.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getAlertTypeIcon(alert_r15.alertType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.getAlertTypeText(alert_r15.alertType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, alert_r15.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(alert_r15.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r15.instanceName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r15.customerName || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_8_tr_19_Template, 22, 13, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.activeAlerts);\n  }\n}\nfunction SecurityComponent_div_9_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 ne\\u00FAsp\\u011B\\u0161n\\u00E9 pokusy o p\\u0159ipojen\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_16_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r19.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", stat_r19.failedCertificateValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r19.failedCertificateValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 8, stat_r19.lastFailedCertificateValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", stat_r19.failedApiKeyValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r19.failedApiKeyValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 11, stat_r19.lastFailedApiKeyValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r19.lastKnownIpAddress || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\", 50);\n    i0.ɵɵtext(5, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 50);\n    i0.ɵɵtext(7, \"Ne\\u00FAsp. validace cert.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 50);\n    i0.ɵɵtext(9, \"Posledn\\u00ED ne\\u00FAsp. validace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 50);\n    i0.ɵɵtext(11, \"Ne\\u00FAsp. validace API kl\\u00ED\\u010De\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"Posledn\\u00ED ne\\u00FAsp. validace API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 50);\n    i0.ɵɵtext(15, \"Posledn\\u00ED IP\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_16_tr_17_Template, 17, 14, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.failedConnectionStats);\n  }\n}\nfunction SecurityComponent_div_9_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_27_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\", 49);\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 50)(6, \"span\", 49);\n    i0.ɵɵelement(7, \"i\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_27_tr_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const event_r21 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.openBlockEventModal(event_r21));\n    });\n    i0.ɵɵelement(20, \"i\", 53);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const event_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getSeverityClass(event_r21.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getSeverityIcon(event_r21.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getSeverityText(event_r21.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getEventTypeClass(event_r21.eventType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getEventTypeIcon(event_r21.eventType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r20.getEventTypeText(event_r21.eventType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, event_r21.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r21.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r21.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r21.username || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_27_tr_19_Template, 21, 13, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.securityEvents);\n  }\n}\nfunction SecurityComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 34)(2, \"div\", 35)(3, \"h5\", 36);\n    i0.ɵɵelement(4, \"i\", 37);\n    i0.ɵɵtext(5, \"Aktivn\\u00ED upozorn\\u011Bn\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtemplate(7, SecurityComponent_div_9_div_7_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(8, SecurityComponent_div_9_div_8_Template, 20, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 34)(10, \"div\", 35)(11, \"h5\", 36);\n    i0.ɵɵelement(12, \"i\", 39);\n    i0.ɵɵtext(13, \"Statistiky ne\\u00FAsp\\u011B\\u0161n\\u00FDch p\\u0159ipojen\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 38);\n    i0.ɵɵtemplate(15, SecurityComponent_div_9_div_15_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(16, SecurityComponent_div_9_div_16_Template, 18, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 34)(18, \"div\", 40)(19, \"h5\", 36);\n    i0.ɵɵelement(20, \"i\", 41);\n    i0.ɵɵtext(21, \"Ned\\u00E1vn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.openManageFiltersModal());\n    });\n    i0.ɵɵelement(23, \"i\", 43);\n    i0.ɵɵtext(24, \"Spravovat filtry \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 38);\n    i0.ɵɵtemplate(26, SecurityComponent_div_9_div_26_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(27, SecurityComponent_div_9_div_27_Template, 20, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length > 0);\n  }\n}\nfunction SecurityComponent_div_18_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1, \" \\u0158e\\u0161en\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 54);\n    i0.ɵɵelement(2, \"i\", 49);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"form\", 55);\n    i0.ɵɵlistener(\"ngSubmit\", function SecurityComponent_div_18_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.resolveAlert());\n    });\n    i0.ɵɵelementStart(7, \"div\", 56)(8, \"label\", 57);\n    i0.ɵɵtext(9, \"\\u0158e\\u0161en\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"textarea\", 58);\n    i0.ɵɵtemplate(11, SecurityComponent_div_18_div_11_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_5_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeClass(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeIcon(ctx_r3.selectedAlert.alertType) + \" me-1\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getAlertTypeText(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\": \", ctx_r3.selectedAlert.description, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.resolveAlertForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.touched));\n  }\n}\nfunction SecurityComponent_div_33_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"label\", 73)(2, \"strong\");\n    i0.ɵɵtext(3, \"IP adresa:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"input\", 74);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_div_24_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.filterIpAddress = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.filterIpAddress);\n  }\n}\nfunction SecurityComponent_div_33_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"small\");\n    i0.ɵɵtext(2, \"Filtr bude aplikov\\u00E1n na v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 61);\n    i0.ɵɵelement(2, \"i\", 62);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozor!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Vytvo\\u0159\\u00EDte filtr, kter\\u00FD zablokuje podobn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti v budoucnu. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\");\n    i0.ɵɵtext(7, \"N\\u00E1hled filtru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 63)(9, \"div\", 38)(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Typ ud\\u00E1losti:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\")(15, \"strong\");\n    i0.ɵɵtext(16, \"Popis:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 56)(19, \"div\", 64)(20, \"input\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.applyToAllIps = $event);\n    })(\"change\", function SecurityComponent_div_33_Template_input_change_20_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onApplyToAllIpsChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"label\", 66)(22, \"strong\");\n    i0.ɵɵtext(23, \"Pou\\u017E\\u00EDt pro v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, SecurityComponent_div_33_div_24_Template, 5, 1, \"div\", 67);\n    i0.ɵɵtemplate(25, SecurityComponent_div_33_div_25_Template, 3, 0, \"div\", 68);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 69)(27, \"input\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.deleteExistingEvents = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"label\", 71);\n    i0.ɵɵtext(29, \" Smazat tak\\u00E9 existuj\\u00EDc\\u00ED ud\\u00E1losti odpov\\u00EDdaj\\u00EDc\\u00ED tomuto vzoru \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getEventTypeText(ctx_r4.selectedEvent.eventType), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedEvent.description, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.deleteExistingEvents);\n  }\n}\nfunction SecurityComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 filtry nejsou nastaveny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_50_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_50_tr_17_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const filter_r38 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.confirmDeleteFilter(filter_r38));\n    });\n    i0.ɵɵelement(14, \"i\", 79);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const filter_r38 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.eventTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.description || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.ipAddress || \"V\\u0161echny IP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, filter_r38.createdAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r38.createdBy);\n  }\n}\nfunction SecurityComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\", 50);\n    i0.ɵɵtext(5, \"Typ ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 50);\n    i0.ɵɵtext(7, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 50);\n    i0.ɵɵtext(9, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 50);\n    i0.ɵɵtext(11, \"Vytvo\\u0159eno\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"Vytvo\\u0159il\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 50);\n    i0.ɵɵtext(15, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_50_tr_17_Template, 15, 8, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.securityEventFilters);\n  }\n}\nexport class SecurityComponent {\n  constructor(securityService, fb, modalService, toastr) {\n    this.securityService = securityService;\n    this.fb = fb;\n    this.modalService = modalService;\n    this.toastr = toastr;\n    this.loading = false;\n    this.error = null;\n    this.securityEvents = [];\n    this.activeAlerts = [];\n    this.failedConnectionStats = [];\n    this.selectedAlert = null;\n    // Security Event Filters\n    this.securityEventFilters = [];\n    this.selectedEvent = null;\n    this.deleteExistingEvents = false;\n    this.loadingFilters = false;\n    this.resolveAlertForm = this.fb.group({\n      resolution: ['', Validators.required]\n    });\n  }\n  ngOnInit() {\n    this.loadSecurityDashboard();\n  }\n  loadSecurityDashboard() {\n    this.loading = true;\n    this.error = null;\n    this.securityService.getSecurityDashboard().subscribe({\n      next: response => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n        this.activeAlerts = response.activeAlerts.map(alert => ({\n          ...alert,\n          timestamp: new Date(alert.timestamp),\n          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined\n        }));\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n  openResolveAlertModal(alert) {\n    this.selectedAlert = alert;\n    this.resolveAlertForm.reset({\n      resolution: ''\n    });\n    this.modalService.open('resolveAlertModal');\n  }\n  resolveAlert() {\n    if (this.resolveAlertForm.invalid || !this.selectedAlert) {\n      return;\n    }\n    const formData = this.resolveAlertForm.value;\n    this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({\n      next: () => {\n        // Zavření modálu\n        this.modalService.close('resolveAlertModal');\n        // Aktualizace dat\n        this.loadSecurityDashboard();\n      },\n      error: err => {\n        console.error('Error resolving alert', err);\n        this.error = 'Chyba při řešení upozornění';\n      }\n    });\n  }\n  getSeverityClass(severity) {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n  getSeverityIcon(severity) {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n  getSeverityText(severity) {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n  getEventTypeClass(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n  getEventTypeIcon(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n  getEventTypeText(eventType) {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n  getAlertTypeClass(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'text-warning';\n      case 'FailedConnectionAttempts':\n        return 'text-danger';\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'ApiKeyMisuse':\n        return 'text-danger';\n      case 'SystemError':\n        return 'text-danger';\n      case 'Error':\n        return 'text-danger';\n      case 'Warning':\n        return 'text-warning';\n      case 'Information':\n        return 'text-info';\n      case 'Other':\n        return 'text-secondary';\n      default:\n        return 'text-info';\n    }\n  }\n  getAlertTypeIcon(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'bi-clock-history';\n      case 'FailedConnectionAttempts':\n        return 'bi-x-circle-fill';\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'SystemError':\n        return 'bi-exclamation-octagon-fill';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      case 'Information':\n        return 'bi-info-circle-fill';\n      case 'Warning':\n        return 'bi-exclamation-triangle-fill';\n      case 'Error':\n        return 'bi-x-octagon-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n  getAlertTypeText(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'Expirující certifikát';\n      case 'FailedConnectionAttempts':\n        return 'Neúspěšné připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'ApiKeyMisuse':\n        return 'Chyba použití API klíče';\n      case 'SystemError':\n        return 'Systémová chyba';\n      case 'Other':\n        return 'Ostatní';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      default:\n        return alertType;\n    }\n  }\n  // Security Event Filter methods\n  openBlockEventModal(event) {\n    this.selectedEvent = event;\n    this.deleteExistingEvents = false;\n    const modal = new bootstrap.Modal(document.getElementById('blockEventModal'));\n    modal.show();\n  }\n  confirmBlockEvent() {\n    if (!this.selectedEvent) return;\n    const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n    const request = {\n      eventType: eventTypeNumber,\n      description: this.selectedEvent.description,\n      ipAddress: this.selectedEvent.ipAddress,\n      deleteExistingEvents: this.deleteExistingEvents\n    };\n    this.securityService.createSecurityEventFilter(request).subscribe({\n      next: response => {\n        if (response.success) {\n          this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n          this.loadSecurityDashboard(); // Refresh data\n          const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal'));\n          modal?.hide();\n        } else {\n          this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n        }\n      },\n      error: error => {\n        console.error('Error creating filter:', error);\n        this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n      }\n    });\n  }\n  openManageFiltersModal() {\n    this.loadSecurityEventFilters();\n    const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal'));\n    modal.show();\n  }\n  loadSecurityEventFilters() {\n    this.loadingFilters = true;\n    this.securityService.getSecurityEventFilters().subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.securityEventFilters = response.data.map(filter => ({\n            ...filter,\n            createdAt: new Date(filter.createdAt)\n          }));\n        } else {\n          this.securityEventFilters = [];\n        }\n        this.loadingFilters = false;\n      },\n      error: error => {\n        console.error('Error loading filters:', error);\n        this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n        this.loadingFilters = false;\n      }\n    });\n  }\n  confirmDeleteFilter(filter) {\n    if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n      this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n            this.loadSecurityEventFilters(); // Refresh filters\n          } else {\n            this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n          }\n        },\n        error: error => {\n          console.error('Error deleting filter:', error);\n          this.toastr.error('Chyba při mazání filtru', 'Chyba');\n        }\n      });\n    }\n  }\n  getEventTypeNumber(eventType) {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 0;\n      case 'SuspiciousActivity':\n        return 1;\n      case 'IpBlocked':\n        return 2;\n      case 'CertificateValidationFailure':\n        return 3;\n      case 'ApiKeyMisuse':\n        return 4;\n      case 'UnauthorizedAccess':\n        return 5;\n      case 'Other':\n        return 6;\n      default:\n        return 6;\n      // Other\n    }\n  }\n\n  static {\n    this.ɵfac = function SecurityComponent_Factory(t) {\n      return new (t || SecurityComponent)(i0.ɵɵdirectiveInject(i1.SecurityService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ModalService), i0.ɵɵdirectiveInject(i4.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SecurityComponent,\n      selectors: [[\"app-security\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 9,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"resolveAlertModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"resolveAlertModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"resolveAlertModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-1\"], [\"id\", \"blockEventModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"blockEventModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-header\", \"bg-warning\", \"text-dark\"], [\"id\", \"blockEventModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\", \"me-1\"], [\"id\", \"manageFiltersModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"manageFiltersModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [\"id\", \"manageFiltersModalLabel\", 1, \"modal-title\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"bi\", \"bi-bell-fill\", \"me-2\"], [1, \"card-body\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-funnel\", \"me-1\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-nowrap\", 3, \"ngClass\"], [3, \"ngClass\"], [1, \"text-nowrap\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Blokovat ud\\u00E1lost\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\"], [1, \"alert\", 3, \"ngClass\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"resolution\", 1, \"form-label\"], [\"id\", \"resolution\", \"formControlName\", \"resolution\", \"rows\", \"3\", \"placeholder\", \"Popi\\u0161te, jak bylo upozorn\\u011Bn\\u00ED vy\\u0159e\\u0161eno\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"alert\", \"alert-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"card\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"applyToAllIps\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"applyToAllIps\", 1, \"form-check-label\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"mt-2 text-muted\", 4, \"ngIf\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", \"id\", \"deleteExistingEvents\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"deleteExistingEvents\", 1, \"form-check-label\"], [1, \"mt-2\"], [\"for\", \"filterIpAddress\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterIpAddress\", \"placeholder\", \"Zadejte IP adresu\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\", \"text-muted\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"spinner-border\"], [\"type\", \"button\", \"title\", \"Smazat filtr\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-trash\"]],\n      template: function SecurityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_4_listener() {\n            return ctx.loadSecurityDashboard();\n          });\n          i0.ɵɵelement(5, \"i\", 3);\n          i0.ɵɵtext(6, \"Aktualizovat \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, SecurityComponent_div_7_Template, 4, 0, \"div\", 4);\n          i0.ɵɵtemplate(8, SecurityComponent_div_8_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(9, SecurityComponent_div_9_Template, 28, 6, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"h5\", 11);\n          i0.ɵɵtext(15, \"Vy\\u0159e\\u0161it upozorn\\u011Bn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"button\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtemplate(18, SecurityComponent_div_18_Template, 12, 6, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 14)(20, \"button\", 15);\n          i0.ɵɵtext(21, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_22_listener() {\n            return ctx.resolveAlert();\n          });\n          i0.ɵɵelement(23, \"i\", 17);\n          i0.ɵɵtext(24, \"Vy\\u0159e\\u0161it \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 8)(27, \"div\", 9)(28, \"div\", 19)(29, \"h5\", 20);\n          i0.ɵɵtext(30, \"Blokovat bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"button\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 13);\n          i0.ɵɵtemplate(33, SecurityComponent_div_33_Template, 30, 6, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 14)(35, \"button\", 15);\n          i0.ɵɵtext(36, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_37_listener() {\n            return ctx.confirmBlockEvent();\n          });\n          i0.ɵɵelement(38, \"i\", 23);\n          i0.ɵɵtext(39, \"Blokovat ud\\u00E1lost \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(40, \"div\", 24)(41, \"div\", 25)(42, \"div\", 9)(43, \"div\", 10)(44, \"h5\", 26);\n          i0.ɵɵtext(45, \"Spravovat filtry bezpe\\u010Dnostn\\u00EDch ud\\u00E1lost\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"button\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 13);\n          i0.ɵɵtemplate(48, SecurityComponent_div_48_Template, 4, 0, \"div\", 27);\n          i0.ɵɵtemplate(49, SecurityComponent_div_49_Template, 2, 0, \"div\", 28);\n          i0.ɵɵtemplate(50, SecurityComponent_div_50_Template, 18, 1, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 14)(52, \"button\", 15);\n          i0.ɵɵtext(53, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedAlert);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.resolveAlertForm.invalid);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEvent);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingFilters);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length > 0);\n        }\n      },\n      dependencies: [LocalDatePipe, CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, ReactiveFormsModule, i2.FormGroupDirective, i2.FormControlName],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  overflow: hidden;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  vertical-align: middle;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  border-radius: 0.25rem;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #198754 !important;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .card[_ngcontent-%COMP%] {\\n    background-color: #2b3035;\\n    border-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-bottom-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-top-color: #373b3e;\\n  }\\n  \\n  .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(255, 255, 255, 0.075);\\n  }\\n  \\n  .alert-info[_ngcontent-%COMP%] {\\n    background-color: #0d3b66;\\n    border-color: #0d3b66;\\n    color: #e9ecef;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AASA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAO,KAAKC,SAAS,MAAM,WAAW;AACtC,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;ICP/DC,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAWMA,+BAAgE;IAC9DA,yEACF;IAAAA,iBAAM;;;;;;IAeAA,0BAAuC;IAGjCA,wBAA6D;IAC7DA,YACF;IAAAA,iBAAO;IAETA,8BAAwB;IAEpBA,wBAA+D;IAAAA,YACjE;IAAAA,iBAAO;IAETA,8BAAwB;IAAAA,aAAoD;;IAAAA,iBAAK;IACjFA,2BAAI;IAAAA,aAAuB;IAAAA,iBAAK;IAChCA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IACqCA;MAAA;MAAA;MAAA;MAAA,OAASA,uDAA4B;IAAA,EAAC;IAC3EA,yBAA4C;IAAAA,mCAC9C;IAAAA,iBAAS;;;;;IAjBiBA,eAA4C;IAA5CA,sEAA4C;IACjEA,eAAqD;IAArDA,+EAAqD;IACxDA,eACF;IADEA,4EACF;IAGMA,eAA8C;IAA9CA,wEAA8C;IAC/CA,eAAuD;IAAvDA,iFAAuD;IAAKA,eACjE;IADiEA,6EACjE;IAEsBA,eAAoD;IAApDA,qFAAoD;IACxEA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAA+B;IAA/BA,mDAA+B;IAC/BA,eAA+B;IAA/BA,mDAA+B;;;;;IA7B3CA,+BAA8D;IAIlDA,mCAAS;IAAAA,iBAAK;IAClBA,0BAAI;IAAAA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,wBAAG;IAAAA,iBAAK;IACZA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,yBAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,mCAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,iFAqBK;IACPA,iBAAQ;;;;IAtBgBA,gBAAe;IAAfA,6CAAe;;;;;IAoC3CA,+BAAyE;IACvEA,kGACF;IAAAA,iBAAM;;;;;IAcAA,0BAA+C;IACzCA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAEAA,YACF;IAAAA,iBAAO;IAETA,0BAAI;IAAAA,YAAgF;;IAAAA,iBAAK;IACzFA,0BAAI;IAEAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAAAA,aAA2E;;IAAAA,iBAAK;IACpFA,2BAAI;IAAAA,aAAoC;IAAAA,iBAAK;;;;IAbzCA,eAAuB;IAAvBA,2CAAuB;IAEnBA,eAAuF;IAAvFA,yGAAuF;IAC3FA,eACF;IADEA,0EACF;IAEEA,eAAgF;IAAhFA,+GAAgF;IAE5EA,eAAkF;IAAlFA,oGAAkF;IACtFA,eACF;IADEA,qEACF;IAEEA,eAA2E;IAA3EA,4GAA2E;IAC3EA,eAAoC;IAApCA,wDAAoC;;;;;IA3BhDA,+BAAuE;IAIvCA,wBAAQ;IAAAA,iBAAK;IACrCA,8BAAwB;IAAAA,0CAAqB;IAAAA,iBAAK;IAClDA,8BAAwB;IAAAA,kDAAwB;IAAAA,iBAAK;IACrDA,+BAAwB;IAAAA,yDAAyB;IAAAA,iBAAK;IACtDA,+BAAwB;IAAAA,uDAA4B;IAAAA,iBAAK;IACzDA,+BAAwB;IAAAA,iCAAW;IAAAA,iBAAK;IAG5CA,8BAAO;IACLA,kFAeK;IACPA,iBAAQ;;;;IAhBeA,gBAAwB;IAAxBA,uDAAwB;;;;;IAiCnDA,+BAAkE;IAChEA,4EACF;IAAAA,iBAAM;;;;;;IAeAA,0BAAyC;IAGnCA,wBAA6D;IAC7DA,YACF;IAAAA,iBAAO;IAETA,8BAAwB;IAEpBA,wBAA+D;IAAAA,YACjE;IAAAA,iBAAO;IAETA,8BAAwB;IAAAA,aAAoD;;IAAAA,iBAAK;IACjFA,2BAAI;IAAAA,aAAuB;IAAAA,iBAAK;IAChCA,2BAAI;IAAAA,aAAqB;IAAAA,iBAAK;IAC9BA,2BAAI;IAAAA,aAA2B;IAAAA,iBAAK;IACpCA,2BAAI;IAEMA;MAAA;MAAA;MAAA;MAAA,OAASA,qDAA0B;IAAA,EAAC;IAE1CA,yBAAkC;IACpCA,iBAAS;;;;;IAnBHA,eAA4C;IAA5CA,sEAA4C;IAC7CA,eAAqD;IAArDA,+EAAqD;IACxDA,eACF;IADEA,4EACF;IAGMA,eAA8C;IAA9CA,wEAA8C;IAC/CA,eAAuD;IAAvDA,iFAAuD;IAAKA,eACjE;IADiEA,6EACjE;IAEsBA,eAAoD;IAApDA,qFAAoD;IACxEA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAqB;IAArBA,yCAAqB;IACrBA,eAA2B;IAA3BA,+CAA2B;;;;;IA7BvCA,+BAAgE;IAIpDA,mCAAS;IAAAA,iBAAK;IAClBA,0BAAI;IAAAA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,wBAAG;IAAAA,iBAAK;IACZA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,+BAAwB;IAAAA,0BAAS;IAAAA,iBAAK;IACtCA,2BAAI;IAAAA,8BAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,kFAuBK;IACPA,iBAAQ;;;;IAxBgBA,gBAAiB;IAAjBA,gDAAiB;;;;;;IAhInDA,2BAAgC;IAKxBA,wBAAoC;IAAAA,kDACtC;IAAAA,iBAAK;IAEPA,+BAAuB;IACrBA,yEAEM;IACNA,0EAsCM;IACRA,iBAAM;IAIRA,+BAAuB;IAGjBA,yBAA6C;IAAAA,gFAC/C;IAAAA,iBAAK;IAEPA,gCAAuB;IACrBA,2EAEM;IACNA,4EA+BM;IACRA,iBAAM;IAIRA,gCAAuB;IAGjBA,yBAAuC;IAAAA,wEACzC;IAAAA,iBAAK;IACLA,mCAA8F;IAAnCA;MAAAA;MAAA;MAAA,OAASA,+CAAwB;IAAA,EAAC;IAC3FA,yBAAiC;IAAAA,kCACnC;IAAAA,iBAAS;IAEXA,gCAAuB;IACrBA,2EAEM;IACNA,4EAwCM;IACRA,iBAAM;;;;IAlJEA,eAA+B;IAA/BA,uDAA+B;IAG/BA,eAA6B;IAA7BA,qDAA6B;IAkD7BA,eAAwC;IAAxCA,gEAAwC;IAGxCA,eAAsC;IAAtCA,8DAAsC;IA8CtCA,gBAAiC;IAAjCA,yDAAiC;IAGjCA,eAA+B;IAA/BA,uDAA+B;;;;;IAuE/BA,+BAA4H;IAC1HA,uDACF;IAAAA,iBAAM;;;;;;IAlBZA,2BAA2B;IAEvBA,wBAAuE;IACvEA,8BAAQ;IAAAA,YAA+C;IAAAA,iBAAS;IAAAA,YAClE;IAAAA,iBAAM;IAENA,gCAAiE;IAA5BA;MAAAA;MAAA;MAAA,OAAYA,qCAAc;IAAA,EAAC;IAC9DA,+BAAkB;IAC2BA,qCAAM;IAAAA,iBAAQ;IACzDA,gCAMY;IACZA,4EAEM;IACRA,iBAAM;;;;;IAlBWA,eAAsD;IAAtDA,kFAAsD;IACpEA,eAA+D;IAA/DA,2FAA+D;IAC1DA,eAA+C;IAA/CA,6EAA+C;IAASA,eAClE;IADkEA,kEAClE;IAEMA,eAA8B;IAA9BA,mDAA8B;IAU1BA,eAAgG;IAAhGA,2MAAgG;;;;;;IA4CpGA,+BAAyC;IACiBA,0BAAU;IAAAA,iBAAS;IAC3EA,iCAA2H;IAA9DA;MAAAA;MAAA;MAAA;IAAA,EAA6B;IAA1FA,iBAA2H;;;;IAA9DA,eAA6B;IAA7BA,iDAA6B;;;;;IAE5FA,+BAAmD;IAC1CA,mEAAyC;IAAAA,iBAAQ;;;;;;IAvBlEA,2BAA2B;IAEvBA,wBAA+C;IAC/CA,8BAAQ;IAAAA,sBAAM;IAAAA,iBAAS;IAACA,+HAC1B;IAAAA,iBAAM;IAENA,0BAAI;IAAAA,mCAAc;IAAAA,iBAAK;IACvBA,+BAAkB;IAEHA,mCAAa;IAAAA,iBAAS;IAACA,aAA+C;IAAAA,iBAAI;IACrFA,0BAAG;IAAQA,uBAAM;IAAAA,iBAAS;IAACA,aAA+B;IAAAA,iBAAI;IAC9DA,gCAAkB;IAEqDA;MAAAA;MAAA;MAAA;IAAA,EAA2B;MAAAA;MAAA;MAAA,OAAWA,8CAAuB;IAAA,EAAlC;IAA9FA,iBAAkI;IAClIA,kCAAoD;IAC1CA,4DAA4B;IAAAA,iBAAS;IAGjDA,4EAGM;IACNA,4EAEM;IACRA,iBAAM;IAIVA,gCAA6B;IAC+CA;MAAAA;MAAA;MAAA;IAAA,EAAkC;IAA5GA,iBAA6G;IAC7GA,kCAA2D;IACzDA,+GACF;IAAAA,iBAAQ;;;;IAxB4BA,gBAA+C;IAA/CA,uFAA+C;IACtDA,eAA+B;IAA/BA,gEAA+B;IAGaA,eAA2B;IAA3BA,8CAA2B;IAK1FA,eAAoB;IAApBA,4CAAoB;IAIpBA,eAAmB;IAAnBA,2CAAmB;IAQ6CA,eAAkC;IAAlCA,qDAAkC;;;;;IA0BhHA,+BAAgD;IAEdA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAA2F;IACzFA,+DACF;IAAAA,iBAAM;;;;;;IAeAA,0BAAgD;IAC1CA,YAA0B;IAAAA,iBAAK;IACnCA,0BAAI;IAAAA,YAA+B;IAAAA,iBAAK;IACxCA,0BAAI;IAAAA,YAAsC;IAAAA,iBAAK;IAC/CA,0BAAI;IAAAA,YAAqD;;IAAAA,iBAAK;IAC9DA,2BAAI;IAAAA,aAAsB;IAAAA,iBAAK;IAC/BA,2BAAI;IAEMA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAA2B;IAAA,EAAC;IAE3CA,yBAA2B;IAC7BA,iBAAS;;;;IAVPA,eAA0B;IAA1BA,8CAA0B;IAC1BA,eAA+B;IAA/BA,mDAA+B;IAC/BA,eAAsC;IAAtCA,+DAAsC;IACtCA,eAAqD;IAArDA,oFAAqD;IACrDA,eAAsB;IAAtBA,0CAAsB;;;;;IAlBlCA,+BAAyF;IAIzDA,iCAAY;IAAAA,iBAAK;IACzCA,8BAAwB;IAAAA,qBAAK;IAAAA,iBAAK;IAClCA,8BAAwB;IAAAA,yBAAS;IAAAA,iBAAK;IACtCA,+BAAwB;IAAAA,+BAAS;IAAAA,iBAAK;IACtCA,+BAAwB;IAAAA,8BAAQ;IAAAA,iBAAK;IACrCA,+BAAwB;IAAAA,qBAAI;IAAAA,iBAAK;IAGrCA,8BAAO;IACLA,2EAaK;IACPA,iBAAQ;;;;IAdiBA,gBAAuB;IAAvBA,qDAAuB;;;AD5R5D,OAAM,MAAOC,iBAAiB;EAiB5BC,YACUC,eAAgC,EAChCC,EAAe,EACfC,YAA0B,EAC1BC,MAAqB;IAHrB,oBAAe,GAAfH,eAAe;IACf,OAAE,GAAFC,EAAE;IACF,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IApBhB,YAAO,GAAY,KAAK;IACxB,UAAK,GAAkB,IAAI;IAE3B,mBAAc,GAA4B,EAAE;IAC5C,iBAAY,GAAoB,EAAE;IAClC,0BAAqB,GAAoC,EAAE;IAE3D,kBAAa,GAAyB,IAAI;IAG1C;IACA,yBAAoB,GAAkC,EAAE;IACxD,kBAAa,GAAiC,IAAI;IAClD,yBAAoB,GAAY,KAAK;IACrC,mBAAc,GAAY,KAAK;IAQ7B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAEf,UAAU,CAACgB,QAAQ;KACrC,CAAC;EACJ;EAEAC,QAAQ;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAA,qBAAqB;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACX,eAAe,CAACY,oBAAoB,EAAE,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACA,IAAI,CAACC,cAAc,GAAGD,QAAQ,CAACE,oBAAoB,CAACC,GAAG,CAACC,KAAK,KAAK;UAChE,GAAGA,KAAK;UACRC,SAAS,EAAE,IAAIC,IAAI,CAACF,KAAK,CAACC,SAAS;SACpC,CAAC,CAAC;QAEH,IAAI,CAACE,YAAY,GAAGP,QAAQ,CAACO,YAAY,CAACJ,GAAG,CAACK,KAAK,KAAK;UACtD,GAAGA,KAAK;UACRH,SAAS,EAAE,IAAIC,IAAI,CAACE,KAAK,CAACH,SAAS,CAAC;UACpCI,UAAU,EAAED,KAAK,CAACC,UAAU,GAAG,IAAIH,IAAI,CAACE,KAAK,CAACC,UAAU,CAAC,GAAGC;SAC7D,CAAC,CAAC;QAEH,IAAI,CAACC,qBAAqB,GAAGX,QAAQ,CAACW,qBAAqB,CAACR,GAAG,CAACS,IAAI,KAAK;UACvE,GAAGA,IAAI;UACPC,+BAA+B,EAAED,IAAI,CAACC,+BAA+B,GAAG,IAAIP,IAAI,CAACM,IAAI,CAACC,+BAA+B,CAAC,GAAGH,SAAS;UAClII,0BAA0B,EAAEF,IAAI,CAACE,0BAA0B,GAAG,IAAIR,IAAI,CAACM,IAAI,CAACE,0BAA0B,CAAC,GAAGJ;SAC3G,CAAC,CAAC;QAEH,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGmB,GAAG,IAAI;QACbC,OAAO,CAACpB,KAAK,CAAC,kCAAkC,EAAEmB,GAAG,CAAC;QACtD,IAAI,CAACnB,KAAK,GAAG,8CAA8C;QAC3D,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAsB,qBAAqB,CAACT,KAAoB;IACxC,IAAI,CAACU,aAAa,GAAGV,KAAK;IAC1B,IAAI,CAACnB,gBAAgB,CAAC8B,KAAK,CAAC;MAC1B5B,UAAU,EAAE;KACb,CAAC;IAEF,IAAI,CAACJ,YAAY,CAACiC,IAAI,CAAC,mBAAmB,CAAC;EAC7C;EAEAC,YAAY;IACV,IAAI,IAAI,CAAChC,gBAAgB,CAACiC,OAAO,IAAI,CAAC,IAAI,CAACJ,aAAa,EAAE;MACxD;;IAGF,MAAMK,QAAQ,GAAG,IAAI,CAAClC,gBAAgB,CAACmC,KAAK;IAE5C,IAAI,CAACvC,eAAe,CAACoC,YAAY,CAAC,IAAI,CAACH,aAAa,CAACO,EAAE,EAAEF,QAAQ,CAAChC,UAAU,CAAC,CAACO,SAAS,CAAC;MACtFC,IAAI,EAAE,MAAK;QACT;QACA,IAAI,CAACZ,YAAY,CAACuC,KAAK,CAAC,mBAAmB,CAAC;QAE5C;QACA,IAAI,CAAChC,qBAAqB,EAAE;MAC9B,CAAC;MACDE,KAAK,EAAGmB,GAAG,IAAI;QACbC,OAAO,CAACpB,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;QAC3C,IAAI,CAACnB,KAAK,GAAG,6BAA6B;MAC5C;KACD,CAAC;EACJ;EAEA+B,gBAAgB,CAACC,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,aAAa;MACtB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB,KAAK,CAAC;QACJ,OAAO,WAAW;MACpB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;IAAC;EAE1B;EAEAC,eAAe,CAACD,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAO,8BAA8B;MACvC,KAAK,CAAC;QACJ,OAAO,4BAA4B;MACrC,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,sBAAsB;IAAC;EAEpC;EAEAE,eAAe,CAACF,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,UAAU;MACnB,KAAK,CAAC;QACJ,OAAO,QAAQ;MACjB,KAAK,CAAC;QACJ,OAAO,SAAS;MAClB,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,YAAY;MACrB;QACE,OAAO,SAAS;IAAC;EAEvB;EAEAG,iBAAiB,CAACC,SAAiB;IACjC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,8BAA8B;QACjC,OAAO,cAAc;MACvB,KAAK,WAAW;QACd,OAAO,aAAa;MACtB,KAAK,qBAAqB;QACxB,OAAO,cAAc;MACvB;QACE,OAAO,WAAW;IAAC;EAEzB;EAEAC,gBAAgB,CAACD,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,8BAA8B;MACvC,KAAK,8BAA8B;QACjC,OAAO,uBAAuB;MAChC,KAAK,WAAW;QACd,OAAO,sBAAsB;MAC/B,KAAK,qBAAqB;QACxB,OAAO,kBAAkB;MAC3B,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC;QACE,OAAO,qBAAqB;IAAC;EAEnC;EAEAE,gBAAgB,CAACF,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,2BAA2B;MACpC,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,8BAA8B;QACjC,OAAO,8BAA8B;MACvC,KAAK,cAAc;QACjB,OAAO,6BAA6B;MACtC,KAAK,oBAAoB;QACvB,OAAO,wBAAwB;MACjC,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEAG,iBAAiB,CAACC,SAAiB;IACjC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,cAAc;MACvB,KAAK,0BAA0B;QAC7B,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,aAAa;QAChB,OAAO,WAAW;MACpB,KAAK,OAAO;QACV,OAAO,gBAAgB;MACzB;QACE,OAAO,WAAW;IAAC;EAEzB;EAEAC,gBAAgB,CAACD,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,kBAAkB;MAC3B,KAAK,0BAA0B;QAC7B,OAAO,kBAAkB;MAC3B,KAAK,oBAAoB;QACvB,OAAO,8BAA8B;MACvC,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,6BAA6B;MACtC,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC,KAAK,aAAa;QAChB,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,8BAA8B;MACvC,KAAK,OAAO;QACV,OAAO,mBAAmB;MAC5B;QACE,OAAO,qBAAqB;IAAC;EAEnC;EAEAE,gBAAgB,CAACF,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,uBAAuB;MAChC,KAAK,0BAA0B;QAC7B,OAAO,qBAAqB;MAC9B,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,cAAc;QACjB,OAAO,yBAAyB;MAClC,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,aAAa;QAChB,OAAO,WAAW;MACpB,KAAK,SAAS;QACZ,OAAO,UAAU;MACnB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEA;EACAG,mBAAmB,CAACnC,KAA4B;IAC9C,IAAI,CAACoC,aAAa,GAAGpC,KAAK;IAC1B,IAAI,CAACqC,oBAAoB,GAAG,KAAK;IACjC,MAAMC,KAAK,GAAG,IAAIjE,SAAS,CAACkE,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAE,CAAC;IAC9EH,KAAK,CAACI,IAAI,EAAE;EACd;EAEAC,iBAAiB;IACf,IAAI,CAAC,IAAI,CAACP,aAAa,EAAE;IAEzB,MAAMQ,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACT,aAAa,CAACR,SAAS,CAAC;IAE7E,MAAMkB,OAAO,GAAqC;MAChDlB,SAAS,EAAEgB,eAAe;MAC1BG,WAAW,EAAE,IAAI,CAACX,aAAa,CAACW,WAAW;MAC3CC,SAAS,EAAE,IAAI,CAACZ,aAAa,CAACY,SAAS;MACvCX,oBAAoB,EAAE,IAAI,CAACA;KAC5B;IAED,IAAI,CAACxD,eAAe,CAACoE,yBAAyB,CAACH,OAAO,CAAC,CAACpD,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACsD,OAAO,EAAE;UACpB,IAAI,CAAClE,MAAM,CAACkE,OAAO,CAAC,4BAA4B,EAAE,QAAQ,CAAC;UAC3D,IAAI,CAAC5D,qBAAqB,EAAE,CAAC,CAAC;UAC9B,MAAMgD,KAAK,GAAGjE,SAAS,CAACkE,KAAK,CAACY,WAAW,CAACX,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAE,CAAC;UACtFH,KAAK,EAAEc,IAAI,EAAE;SACd,MAAM;UACL,IAAI,CAACpE,MAAM,CAACQ,KAAK,CAACI,QAAQ,CAACyD,OAAO,IAAI,4BAA4B,EAAE,OAAO,CAAC;;MAEhF,CAAC;MACD7D,KAAK,EAAGA,KAAK,IAAI;QACfoB,OAAO,CAACpB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,4BAA4B,EAAE,OAAO,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA8D,sBAAsB;IACpB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,MAAMjB,KAAK,GAAG,IAAIjE,SAAS,CAACkE,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAE,CAAC;IACjFH,KAAK,CAACI,IAAI,EAAE;EACd;EAEAa,wBAAwB;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC3E,eAAe,CAAC4E,uBAAuB,EAAE,CAAC/D,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACsD,OAAO,IAAItD,QAAQ,CAAC8D,IAAI,EAAE;UACrC,IAAI,CAACC,oBAAoB,GAAG/D,QAAQ,CAAC8D,IAAI,CAAC3D,GAAG,CAAC6D,MAAM,KAAK;YACvD,GAAGA,MAAM;YACTC,SAAS,EAAE,IAAI3D,IAAI,CAAC0D,MAAM,CAACC,SAAS;WACrC,CAAC,CAAC;SACJ,MAAM;UACL,IAAI,CAACF,oBAAoB,GAAG,EAAE;;QAEhC,IAAI,CAACH,cAAc,GAAG,KAAK;MAC7B,CAAC;MACDhE,KAAK,EAAGA,KAAK,IAAI;QACfoB,OAAO,CAACpB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,2BAA2B,EAAE,OAAO,CAAC;QACvD,IAAI,CAACgE,cAAc,GAAG,KAAK;MAC7B;KACD,CAAC;EACJ;EAEAM,mBAAmB,CAACF,MAAmC;IACrD,IAAIG,OAAO,CAAC,oCAAoCH,MAAM,CAACI,aAAa,IAAI,CAAC,EAAE;MACzE,IAAI,CAACnF,eAAe,CAACoF,yBAAyB,CAACL,MAAM,CAACvC,EAAE,CAAC,CAAC3B,SAAS,CAAC;QAClEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACsD,OAAO,EAAE;YACpB,IAAI,CAAClE,MAAM,CAACkE,OAAO,CAAC,0BAA0B,EAAE,QAAQ,CAAC;YACzD,IAAI,CAACK,wBAAwB,EAAE,CAAC,CAAC;WAClC,MAAM;YACL,IAAI,CAACvE,MAAM,CAACQ,KAAK,CAACI,QAAQ,CAACyD,OAAO,IAAI,yBAAyB,EAAE,OAAO,CAAC;;QAE7E,CAAC;QACD7D,KAAK,EAAGA,KAAK,IAAI;UACfoB,OAAO,CAACpB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,yBAAyB,EAAE,OAAO,CAAC;QACvD;OACD,CAAC;;EAEN;EAEQqD,kBAAkB,CAACjB,SAAiB;IAC1C,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,CAAC;MACV,KAAK,oBAAoB;QACvB,OAAO,CAAC;MACV,KAAK,WAAW;QACd,OAAO,CAAC;MACV,KAAK,8BAA8B;QACjC,OAAO,CAAC;MACV,KAAK,cAAc;QACjB,OAAO,CAAC;MACV,KAAK,oBAAoB;QACvB,OAAO,CAAC;MACV,KAAK,OAAO;QACV,OAAO,CAAC;MACV;QACE,OAAO,CAAC;MAAE;IAAA;EAEhB;;;;uBA7XWjD,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAuF;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCxB9B9F,8BAAuB;UAEfA,oDAAqB;UAAAA,iBAAK;UAC9BA,iCAAkE;UAAlCA;YAAA,OAAS+F,2BAAuB;UAAA,EAAC;UAC/D/F,uBAA0C;UAAAA,6BAC5C;UAAAA,iBAAS;UAGXA,kEAIM;UAENA,kEAEM;UAENA,mEA6JM;UACRA,iBAAM;UAGNA,+BAAyH;UAI7DA,uDAAkB;UAAAA,iBAAK;UAC3EA,8BAA6G;UAC/GA,iBAAM;UACNA,gCAAwB;UACtBA,qEAqBM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,iCAAM;UAAAA,iBAAS;UACvFA,mCAA6G;UAAzBA;YAAA,OAAS+F,kBAAc;UAAA,EAAC;UAC1G/F,yBAA4C;UAAAA,mCAC9C;UAAAA,iBAAS;UAOjBA,gCAAqH;UAI3DA,6DAA6B;UAAAA,iBAAK;UACpFA,8BAA6F;UAC/FA,iBAAM;UACNA,gCAAwB;UACtBA,qEAmCM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,4BAAM;UAAAA,iBAAS;UACvFA,mCAA4E;UAA9BA;YAAA,OAAS+F,uBAAmB;UAAA,EAAC;UACzE/F,yBAAuC;UAAAA,uCACzC;UAAAA,iBAAS;UAOjBA,gCAA2H;UAI9DA,6EAAwC;UAAAA,iBAAK;UAClGA,8BAA6G;UAC/GA,iBAAM;UACNA,gCAAwB;UACtBA,qEAIM;UAENA,qEAEM;UAENA,sEA6BM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,iCAAM;UAAAA,iBAAS;;;UA/TvFA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAIXA,eAAwB;UAAxBA,iDAAwB;UAyKlBA,eAAmB;UAAnBA,wCAAmB;UAyBqBA,eAAqC;UAArCA,uDAAqC;UAiB7EA,gBAAmB;UAAnBA,wCAAmB;UAwDnBA,gBAAoB;UAApBA,yCAAoB;UAMpBA,eAA0D;UAA1DA,mFAA0D;UAI1DA,eAAwD;UAAxDA,iFAAwD;;;qBDlR1DJ,aAAa,EAAEC,YAAY,mCAAEC,WAAW,uIAAEC,mBAAmB;MAAAiG;IAAA;EAAA", "names": ["Validators", "bootstrap", "LocalDatePipe", "CommonModule", "FormsModule", "ReactiveFormsModule", "i0", "SecurityComponent", "constructor", "securityService", "fb", "modalService", "toastr", "resolveAlertForm", "group", "resolution", "required", "ngOnInit", "loadSecurityDashboard", "loading", "error", "getSecurityDashboard", "subscribe", "next", "response", "securityEvents", "recentSecurityEvents", "map", "event", "timestamp", "Date", "active<PERSON>lerts", "alert", "resolvedAt", "undefined", "failedConnectionStats", "stat", "lastFailedCertificateValidation", "lastFailedApiKeyValidation", "err", "console", "openResolveAlertModal", "<PERSON><PERSON><PERSON><PERSON>", "reset", "open", "<PERSON><PERSON><PERSON><PERSON>", "invalid", "formData", "value", "id", "close", "getSeverityClass", "severity", "getSeverityIcon", "getSeverityText", "getEventTypeClass", "eventType", "getEventTypeIcon", "getEventTypeText", "getAlertTypeClass", "alertType", "getAlertTypeIcon", "getAlertTypeText", "openBlockEventModal", "selectedEvent", "deleteExistingEvents", "modal", "Modal", "document", "getElementById", "show", "confirmBlockEvent", "eventTypeNumber", "getEventTypeNumber", "request", "description", "ip<PERSON><PERSON><PERSON>", "createSecurityEventFilter", "success", "getInstance", "hide", "message", "openManageFiltersModal", "loadSecurityEventFilters", "loadingFilters", "getSecurityEventFilters", "data", "securityEventFilters", "filter", "createdAt", "confirmDeleteFilter", "confirm", "eventTypeName", "deleteSecurityEventFilter", "selectors", "standalone", "features", "decls", "vars", "consts", "template", "ctx", "styles"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { SecurityService } from '../services/security.service';\nimport {\n  AlertResponse,\n  SecurityEventResponse,\n  FailedConnectionStatsResponse,\n  SecurityEventFilterResponse,\n  CreateSecurityEventFilterRequest\n} from '../models/security.model';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ModalService } from '../services/modal.service';\nimport { ToastrService } from 'ngx-toastr';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-security',\n  templateUrl: './security.component.html',\n  styleUrls: ['./security.component.css'],\n  imports: [LocalDatePipe, CommonModule, FormsModule, ReactiveFormsModule],\n  standalone: true\n})\nexport class SecurityComponent implements OnInit {\n  loading: boolean = false;\n  error: string | null = null;\n\n  securityEvents: SecurityEventResponse[] = [];\n  activeAlerts: AlertResponse[] = [];\n  failedConnectionStats: FailedConnectionStatsResponse[] = [];\n\n  selectedAlert: AlertResponse | null = null;\n  resolveAlertForm: FormGroup;\n\n  // Security Event Filters\n  securityEventFilters: SecurityEventFilterResponse[] = [];\n  selectedEvent: SecurityEventResponse | null = null;\n  deleteExistingEvents: boolean = false;\n  loadingFilters: boolean = false;\n\n  constructor(\n    private securityService: SecurityService,\n    private fb: FormBuilder,\n    private modalService: ModalService,\n    private toastr: ToastrService\n  ) {\n    this.resolveAlertForm = this.fb.group({\n      resolution: ['', Validators.required]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadSecurityDashboard();\n  }\n\n  loadSecurityDashboard(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.securityService.getSecurityDashboard().subscribe({\n      next: (response) => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n\n        this.activeAlerts = response.activeAlerts.map(alert => ({\n          ...alert,\n          timestamp: new Date(alert.timestamp),\n          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined\n        }));\n\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n\n  openResolveAlertModal(alert: AlertResponse): void {\n    this.selectedAlert = alert;\n    this.resolveAlertForm.reset({\n      resolution: ''\n    });\n\n    this.modalService.open('resolveAlertModal');\n  }\n\n  resolveAlert(): void {\n    if (this.resolveAlertForm.invalid || !this.selectedAlert) {\n      return;\n    }\n\n    const formData = this.resolveAlertForm.value;\n\n    this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({\n      next: () => {\n        // Zavření modálu\n        this.modalService.close('resolveAlertModal');\n\n        // Aktualizace dat\n        this.loadSecurityDashboard();\n      },\n      error: (err) => {\n        console.error('Error resolving alert', err);\n        this.error = 'Chyba při řešení upozornění';\n      }\n    });\n  }\n\n  getSeverityClass(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n\n  getSeverityIcon(severity: number): string {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n\n  getSeverityText(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n\n  getEventTypeClass(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n\n  getEventTypeIcon(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n\n  getEventTypeText(eventType: string): string {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n\n  getAlertTypeClass(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'text-warning';\n      case 'FailedConnectionAttempts':\n        return 'text-danger';\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'ApiKeyMisuse':\n        return 'text-danger';\n      case 'SystemError':\n        return 'text-danger';\n      case 'Error':\n        return 'text-danger';\n      case 'Warning':\n        return 'text-warning';\n      case 'Information':\n        return 'text-info';\n      case 'Other':\n        return 'text-secondary';\n      default:\n        return 'text-info';\n    }\n  }\n\n  getAlertTypeIcon(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'bi-clock-history';\n      case 'FailedConnectionAttempts':\n        return 'bi-x-circle-fill';\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'SystemError':\n        return 'bi-exclamation-octagon-fill';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      case 'Information':\n        return 'bi-info-circle-fill';\n      case 'Warning':\n        return 'bi-exclamation-triangle-fill';\n      case 'Error':\n        return 'bi-x-octagon-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n\n  getAlertTypeText(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'Expirující certifikát';\n      case 'FailedConnectionAttempts':\n        return 'Neúspěšné připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'ApiKeyMisuse':\n        return 'Chyba použití API klíče';\n      case 'SystemError':\n        return 'Systémová chyba';\n      case 'Other':\n        return 'Ostatní';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      default:\n        return alertType;\n    }\n  }\n\n  // Security Event Filter methods\n  openBlockEventModal(event: SecurityEventResponse): void {\n    this.selectedEvent = event;\n    this.deleteExistingEvents = false;\n    const modal = new bootstrap.Modal(document.getElementById('blockEventModal')!);\n    modal.show();\n  }\n\n  confirmBlockEvent(): void {\n    if (!this.selectedEvent) return;\n\n    const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n\n    const request: CreateSecurityEventFilterRequest = {\n      eventType: eventTypeNumber,\n      description: this.selectedEvent.description,\n      ipAddress: this.selectedEvent.ipAddress,\n      deleteExistingEvents: this.deleteExistingEvents\n    };\n\n    this.securityService.createSecurityEventFilter(request).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n          this.loadSecurityDashboard(); // Refresh data\n          const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal')!);\n          modal?.hide();\n        } else {\n          this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n        }\n      },\n      error: (error) => {\n        console.error('Error creating filter:', error);\n        this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n      }\n    });\n  }\n\n  openManageFiltersModal(): void {\n    this.loadSecurityEventFilters();\n    const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal')!);\n    modal.show();\n  }\n\n  loadSecurityEventFilters(): void {\n    this.loadingFilters = true;\n    this.securityService.getSecurityEventFilters().subscribe({\n      next: (response) => {\n        if (response.success && response.data) {\n          this.securityEventFilters = response.data.map(filter => ({\n            ...filter,\n            createdAt: new Date(filter.createdAt)\n          }));\n        } else {\n          this.securityEventFilters = [];\n        }\n        this.loadingFilters = false;\n      },\n      error: (error) => {\n        console.error('Error loading filters:', error);\n        this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n        this.loadingFilters = false;\n      }\n    });\n  }\n\n  confirmDeleteFilter(filter: SecurityEventFilterResponse): void {\n    if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n      this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n            this.loadSecurityEventFilters(); // Refresh filters\n          } else {\n            this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n          }\n        },\n        error: (error) => {\n          console.error('Error deleting filter:', error);\n          this.toastr.error('Chyba při mazání filtru', 'Chyba');\n        }\n      });\n    }\n  }\n\n  private getEventTypeNumber(eventType: string): number {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 0;\n      case 'SuspiciousActivity':\n        return 1;\n      case 'IpBlocked':\n        return 2;\n      case 'CertificateValidationFailure':\n        return 3;\n      case 'ApiKeyMisuse':\n        return 4;\n      case 'UnauthorizedAccess':\n        return 5;\n      case 'Other':\n        return 6;\n      default:\n        return 6; // Other\n    }\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Bezpečnostní u<PERSON></h2>\n    <button class=\"btn btn-primary\" (click)=\"loadSecurityDashboard()\">\n      <i class=\"bi bi-arrow-clockwise me-2\"></i>Aktualizovat\n    </button>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger mb-4\">\n    {{ error }}\n  </div>\n\n  <div *ngIf=\"!loading && !error\">\n    <!-- Aktivní upozornění -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-bell-fill me-2\"></i>Aktivní upozornění\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"activeAlerts.length === 0\" class=\"alert alert-info\">\n          Žádná aktivní upozornění.\n        </div>\n        <div *ngIf=\"activeAlerts.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Závažnost</th>\n                <th>Typ</th>\n                <th>Čas</th>\n                <th>Popis</th>\n                <th>Instance</th>\n                <th>Zákazník</th>\n                <th>Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let alert of activeAlerts\">\n                <td>\n                  <span class=\"text-nowrap\" [ngClass]=\"getSeverityClass(alert.severity)\">\n                    <i [ngClass]=\"getSeverityIcon(alert.severity) + ' me-1'\"></i>\n                    {{ getSeverityText(alert.severity) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getAlertTypeClass(alert.alertType)\">\n                    <i [ngClass]=\"getAlertTypeIcon(alert.alertType) + ' me-1'\"></i>{{ getAlertTypeText(alert.alertType) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">{{ alert.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ alert.description }}</td>\n                <td>{{ alert.instanceName || '-' }}</td>\n                <td>{{ alert.customerName || '-' }}</td>\n                <td>\n                  <button class=\"btn btn-sm btn-primary\" (click)=\"openResolveAlertModal(alert)\">\n                    <i class=\"bi bi-check-circle-fill me-1\"></i>Vyřešit\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiky neúspěšných připojení -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-shield-exclamation me-2\"></i>Statistiky neúspěšných připojení\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"failedConnectionStats.length === 0\" class=\"alert alert-info\">\n          Žádné neúspěšné pokusy o připojení.\n        </div>\n        <div *ngIf=\"failedConnectionStats.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th class=\"text-nowrap\">Instance</th>\n                <th class=\"text-nowrap\">Neúsp. validace cert.</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace</th>\n                <th class=\"text-nowrap\">Neúsp. validace API klíče</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace API</th>\n                <th class=\"text-nowrap\">Poslední IP</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let stat of failedConnectionStats\">\n                <td>{{ stat.instanceName }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedCertificateValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedCertificateValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedCertificateValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedApiKeyValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedApiKeyValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedApiKeyValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>{{ stat.lastKnownIpAddress || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Nedávné bezpečnostní události -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white d-flex justify-content-between align-items-center\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-journal-text me-2\"></i>Nedávné bezpečnostní události\n        </h5>\n        <button type=\"button\" class=\"btn btn-outline-light btn-sm\" (click)=\"openManageFiltersModal()\">\n          <i class=\"bi bi-funnel me-1\"></i>Spravovat filtry\n        </button>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"securityEvents.length === 0\" class=\"alert alert-info\">\n          Žádné bezpečnostní události.\n        </div>\n        <div *ngIf=\"securityEvents.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Závažnost</th>\n                <th>Typ</th>\n                <th>Čas</th>\n                <th>Popis</th>\n                <th class=\"text-nowrap\">IP adresa</th>\n                <th>Uživatel</th>\n                <th>Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let event of securityEvents\">\n                <td>\n                  <span [ngClass]=\"getSeverityClass(event.severity)\">\n                    <i [ngClass]=\"getSeverityIcon(event.severity) + ' me-1'\"></i>\n                    {{ getSeverityText(event.severity) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getEventTypeClass(event.eventType)\">\n                    <i [ngClass]=\"getEventTypeIcon(event.eventType) + ' me-1'\"></i>{{ getEventTypeText(event.eventType) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">{{ event.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ event.description }}</td>\n                <td>{{ event.ipAddress }}</td>\n                <td>{{ event.username || '-' }}</td>\n                <td>\n                  <button type=\"button\" class=\"btn btn-outline-danger btn-sm\"\n                          (click)=\"openBlockEventModal(event)\"\n                          title=\"Blokovat událost\">\n                    <i class=\"bi bi-slash-circle\"></i>\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro řešení upozornění -->\n<div class=\"modal fade\" id=\"resolveAlertModal\" tabindex=\"-1\" aria-labelledby=\"resolveAlertModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"resolveAlertModalLabel\">Vyřešit upozornění</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"selectedAlert\">\n          <div class=\"alert\" [ngClass]=\"getAlertTypeClass(selectedAlert.alertType)\">\n            <i [ngClass]=\"getAlertTypeIcon(selectedAlert.alertType) + ' me-1'\"></i>\n            <strong>{{ getAlertTypeText(selectedAlert.alertType) }}</strong>: {{ selectedAlert.description }}\n          </div>\n\n          <form [formGroup]=\"resolveAlertForm\" (ngSubmit)=\"resolveAlert()\">\n            <div class=\"mb-3\">\n              <label for=\"resolution\" class=\"form-label\">Řešení</label>\n              <textarea\n                id=\"resolution\"\n                formControlName=\"resolution\"\n                class=\"form-control\"\n                rows=\"3\"\n                placeholder=\"Popište, jak bylo upozornění vyřešeno\"\n              ></textarea>\n              <div *ngIf=\"resolveAlertForm.get('resolution')?.invalid && resolveAlertForm.get('resolution')?.touched\" class=\"text-danger\">\n                Řešení je povinné\n              </div>\n            </div>\n          </form>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zavřít</button>\n        <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"resolveAlertForm.invalid\" (click)=\"resolveAlert()\">\n          <i class=\"bi bi-check-circle-fill me-1\"></i>Vyřešit\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro blokování události -->\n<div class=\"modal fade\" id=\"blockEventModal\" tabindex=\"-1\" aria-labelledby=\"blockEventModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-warning text-dark\">\n        <h5 class=\"modal-title\" id=\"blockEventModalLabel\">Blokovat bezpečnostní událost</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"selectedEvent\">\n          <div class=\"alert alert-warning\">\n            <i class=\"bi bi-exclamation-triangle me-1\"></i>\n            <strong>Pozor!</strong> Vytvoříte filtr, který zablokuje podobné bezpečnostní události v budoucnu.\n          </div>\n\n          <h6>Náhled filtru:</h6>\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <p><strong>Typ události:</strong> {{ getEventTypeText(selectedEvent.eventType) }}</p>\n              <p><strong>Popis:</strong> {{ selectedEvent.description }}</p>\n              <div class=\"mb-3\">\n                <div class=\"form-check\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"applyToAllIps\" [(ngModel)]=\"applyToAllIps\" (change)=\"onApplyToAllIpsChange()\">\n                  <label class=\"form-check-label\" for=\"applyToAllIps\">\n                    <strong>Použít pro všechny IP adresy</strong>\n                  </label>\n                </div>\n                <div *ngIf=\"!applyToAllIps\" class=\"mt-2\">\n                  <label for=\"filterIpAddress\" class=\"form-label\"><strong>IP adresa:</strong></label>\n                  <input type=\"text\" class=\"form-control\" id=\"filterIpAddress\" [(ngModel)]=\"filterIpAddress\" placeholder=\"Zadejte IP adresu\">\n                </div>\n                <div *ngIf=\"applyToAllIps\" class=\"mt-2 text-muted\">\n                  <small>Filtr bude aplikován na všechny IP adresy</small>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-check mt-3\">\n            <input class=\"form-check-input\" type=\"checkbox\" id=\"deleteExistingEvents\" [(ngModel)]=\"deleteExistingEvents\">\n            <label class=\"form-check-label\" for=\"deleteExistingEvents\">\n              Smazat také existující události odpovídající tomuto vzoru\n            </label>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zrušit</button>\n        <button type=\"button\" class=\"btn btn-warning\" (click)=\"confirmBlockEvent()\">\n          <i class=\"bi bi-slash-circle me-1\"></i>Blokovat událost\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro správu filtrů -->\n<div class=\"modal fade\" id=\"manageFiltersModal\" tabindex=\"-1\" aria-labelledby=\"manageFiltersModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"manageFiltersModalLabel\">Spravovat filtry bezpečnostních událostí</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"loadingFilters\" class=\"text-center\">\n          <div class=\"spinner-border\" role=\"status\">\n            <span class=\"visually-hidden\">Načítání...</span>\n          </div>\n        </div>\n\n        <div *ngIf=\"!loadingFilters && securityEventFilters.length === 0\" class=\"alert alert-info\">\n          Žádné filtry nejsou nastaveny.\n        </div>\n\n        <div *ngIf=\"!loadingFilters && securityEventFilters.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th class=\"text-nowrap\">Typ události</th>\n                <th class=\"text-nowrap\">Popis</th>\n                <th class=\"text-nowrap\">IP adresa</th>\n                <th class=\"text-nowrap\">Vytvořeno</th>\n                <th class=\"text-nowrap\">Vytvořil</th>\n                <th class=\"text-nowrap\">Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let filter of securityEventFilters\">\n                <td>{{ filter.eventTypeName }}</td>\n                <td>{{ filter.description || '-' }}</td>\n                <td>{{ filter.ipAddress || 'Všechny IP' }}</td>\n                <td>{{ filter.createdAt | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ filter.createdBy }}</td>\n                <td>\n                  <button type=\"button\" class=\"btn btn-outline-danger btn-sm\"\n                          (click)=\"confirmDeleteFilter(filter)\"\n                          title=\"Smazat filtr\">\n                    <i class=\"bi bi-trash\"></i>\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}