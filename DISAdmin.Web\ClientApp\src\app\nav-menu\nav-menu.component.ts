import { Component, OnInit, AfterViewInit, isDevMode, Renderer2 } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { ModalService } from '../services/modal.service';
import { User } from '../models/user.model';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-nav-menu',
  templateUrl: './nav-menu.component.html',
  styleUrls: ['./nav-menu.component.css']
})
export class NavMenuComponent implements OnInit, AfterViewInit {
  isExpanded = false;
  currentUser: User | null = null;
  isLoggedIn = false;
  isAdmin = false;
  isDarkMode = false;
  isDevMode = isDevMode();
  isLoginPage = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private modalService: ModalService,
    private renderer: Renderer2
  ) {
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.isLoggedIn = !!user;
      this.isAdmin = user?.isAdmin || false;

      // Reinicializace dropdown po změně uživatele
      setTimeout(() => {
        this.ensureBootstrapDropdowns();
      }, 100);
    });

    // Zavřít rozbalovací menu při navigaci
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.collapse();
    });
  }

  ngOnInit() {
    // Kontrola, zda je nastaven tmavý režim v localStorage
    this.isDarkMode = localStorage.getItem('darkMode') === 'true';
    this.applyTheme();

    // Kontrola, zda jsme na přihlašovací stránce - použijeme startsWith pro detekci i s parametry
    this.isLoginPage = this.router.url.startsWith('/login');
    console.log('Initial isLoginPage:', this.isLoginPage, 'URL:', this.router.url);

    // Sledování změn URL
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.isLoginPage = event.url.startsWith('/login');
      console.log('Navigation isLoginPage:', this.isLoginPage, 'URL:', event.url);
    });
  }

  ngAfterViewInit() {
    // Jednoduchá reinicializace Bootstrap dropdown po načtení view
    setTimeout(() => {
      this.ensureBootstrapDropdowns();
    }, 100);
  }

  private ensureBootstrapDropdowns() {
    // Zkontrolujeme, jestli Bootstrap existuje
    if (typeof (window as any).bootstrap !== 'undefined') {
      // Bootstrap je dostupný, inicializujeme dropdown
      console.log('Bootstrap is available, initializing dropdowns');

      // Najdeme všechny dropdown elementy a inicializujeme je
      const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
      dropdownElements.forEach((element) => {
        try {
          // Zkontrolujeme, jestli už není inicializován
          if (!(window as any).bootstrap.Dropdown.getInstance(element)) {
            new (window as any).bootstrap.Dropdown(element);
          }
        } catch (error) {
          console.warn('Error initializing dropdown:', error);
        }
      });
    } else {
      console.warn('Bootstrap is not available');
    }
  }

  collapse() {
    this.isExpanded = false;
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
  }

  logout() {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    localStorage.setItem('darkMode', this.isDarkMode.toString());
    this.applyTheme();
  }

  private applyTheme() {
    if (this.isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }


}
