{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/security.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../services/modal.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/common\";\nfunction SecurityComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction SecurityComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E1 aktivn\\u00ED upozorn\\u011Bn\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_8_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\", 48);\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 50)(6, \"span\", 49);\n    i0.ɵɵelement(7, \"i\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_8_tr_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const alert_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.openResolveAlertModal(alert_r15));\n    });\n    i0.ɵɵelement(20, \"i\", 52);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const alert_r15 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getSeverityClass(alert_r15.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getSeverityIcon(alert_r15.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getSeverityText(alert_r15.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getAlertTypeClass(alert_r15.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getAlertTypeIcon(alert_r15.alertType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.getAlertTypeText(alert_r15.alertType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, alert_r15.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(alert_r15.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r15.instanceName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r15.customerName || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_8_tr_19_Template, 21, 13, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.activeAlerts);\n  }\n}\nfunction SecurityComponent_div_9_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 ne\\u00FAsp\\u011B\\u0161n\\u00E9 pokusy o p\\u0159ipojen\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_16_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r19.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", stat_r19.failedCertificateValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r19.failedCertificateValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 8, stat_r19.lastFailedCertificateValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", stat_r19.failedApiKeyValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r19.failedApiKeyValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 11, stat_r19.lastFailedApiKeyValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r19.lastKnownIpAddress || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\", 50);\n    i0.ɵɵtext(5, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 50);\n    i0.ɵɵtext(7, \"Ne\\u00FAsp. validace cert.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 50);\n    i0.ɵɵtext(9, \"Posledn\\u00ED ne\\u00FAsp. validace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 50);\n    i0.ɵɵtext(11, \"Ne\\u00FAsp. validace API kl\\u00ED\\u010De\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"Posledn\\u00ED ne\\u00FAsp. validace API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 50);\n    i0.ɵɵtext(15, \"Posledn\\u00ED IP\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_16_tr_17_Template, 17, 14, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.failedConnectionStats);\n  }\n}\nfunction SecurityComponent_div_9_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_27_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 50)(2, \"span\", 49);\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 50)(6, \"span\", 49);\n    i0.ɵɵelement(7, \"i\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 50);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_27_tr_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const event_r21 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.openBlockEventModal(event_r21));\n    });\n    i0.ɵɵelement(20, \"i\", 54);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const event_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getSeverityClass(event_r21.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getSeverityIcon(event_r21.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getSeverityText(event_r21.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getEventTypeClass(event_r21.eventType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getEventTypeIcon(event_r21.eventType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r20.getEventTypeText(event_r21.eventType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, event_r21.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r21.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r21.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r21.username || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_27_tr_19_Template, 21, 13, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.securityEvents);\n  }\n}\nfunction SecurityComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 34)(2, \"div\", 35)(3, \"h5\", 36);\n    i0.ɵɵelement(4, \"i\", 37);\n    i0.ɵɵtext(5, \"Aktivn\\u00ED upozorn\\u011Bn\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 38);\n    i0.ɵɵtemplate(7, SecurityComponent_div_9_div_7_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(8, SecurityComponent_div_9_div_8_Template, 20, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 34)(10, \"div\", 35)(11, \"h5\", 36);\n    i0.ɵɵelement(12, \"i\", 39);\n    i0.ɵɵtext(13, \"Statistiky ne\\u00FAsp\\u011B\\u0161n\\u00FDch p\\u0159ipojen\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 38);\n    i0.ɵɵtemplate(15, SecurityComponent_div_9_div_15_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(16, SecurityComponent_div_9_div_16_Template, 18, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 34)(18, \"div\", 40)(19, \"h5\", 36);\n    i0.ɵɵelement(20, \"i\", 41);\n    i0.ɵɵtext(21, \"Ned\\u00E1vn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.openManageFiltersModal());\n    });\n    i0.ɵɵelement(23, \"i\", 43);\n    i0.ɵɵtext(24, \"Spravovat filtry \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 38);\n    i0.ɵɵtemplate(26, SecurityComponent_div_9_div_26_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(27, SecurityComponent_div_9_div_27_Template, 20, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length > 0);\n  }\n}\nfunction SecurityComponent_div_18_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" \\u0158e\\u0161en\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 49);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"form\", 56);\n    i0.ɵɵlistener(\"ngSubmit\", function SecurityComponent_div_18_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.resolveAlert());\n    });\n    i0.ɵɵelementStart(7, \"div\", 57)(8, \"label\", 58);\n    i0.ɵɵtext(9, \"\\u0158e\\u0161en\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"textarea\", 59);\n    i0.ɵɵtemplate(11, SecurityComponent_div_18_div_11_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_5_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeClass(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeIcon(ctx_r3.selectedAlert.alertType) + \" me-1\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getAlertTypeText(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\": \", ctx_r3.selectedAlert.description, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.resolveAlertForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.touched));\n  }\n}\nfunction SecurityComponent_div_33_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"label\", 74)(2, \"strong\");\n    i0.ɵɵtext(3, \"IP adresa:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"input\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_div_24_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.filterIpAddress = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.filterIpAddress);\n  }\n}\nfunction SecurityComponent_div_33_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"small\");\n    i0.ɵɵtext(2, \"Filtr bude aplikov\\u00E1n na v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozor!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Vytvo\\u0159\\u00EDte filtr, kter\\u00FD zablokuje podobn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti v budoucnu. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\");\n    i0.ɵɵtext(7, \"N\\u00E1hled filtru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 64)(9, \"div\", 38)(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Typ ud\\u00E1losti:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\")(15, \"strong\");\n    i0.ɵɵtext(16, \"Popis:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"div\", 65)(20, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.applyToAllIps = $event);\n    })(\"change\", function SecurityComponent_div_33_Template_input_change_20_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onApplyToAllIpsChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"label\", 67)(22, \"strong\");\n    i0.ɵɵtext(23, \"Pou\\u017E\\u00EDt pro v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, SecurityComponent_div_33_div_24_Template, 5, 1, \"div\", 68);\n    i0.ɵɵtemplate(25, SecurityComponent_div_33_div_25_Template, 3, 0, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 70)(27, \"input\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_33_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.deleteExistingEvents = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"label\", 72);\n    i0.ɵɵtext(29, \" Smazat tak\\u00E9 existuj\\u00EDc\\u00ED ud\\u00E1losti odpov\\u00EDdaj\\u00EDc\\u00ED tomuto vzoru \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getEventTypeText(ctx_r4.selectedEvent.eventType), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedEvent.description, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.applyToAllIps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.deleteExistingEvents);\n  }\n}\nfunction SecurityComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78)(2, \"span\", 32);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 filtry nejsou nastaveny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_50_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_50_tr_17_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const filter_r38 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.confirmDeleteFilter(filter_r38));\n    });\n    i0.ɵɵelement(14, \"i\", 80);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const filter_r38 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.eventTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.description || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r38.ipAddress || \"V\\u0161echny IP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, filter_r38.createdAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r38.createdBy);\n  }\n}\nfunction SecurityComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"table\", 46)(2, \"thead\")(3, \"tr\")(4, \"th\", 50);\n    i0.ɵɵtext(5, \"Typ ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 50);\n    i0.ɵɵtext(7, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 50);\n    i0.ɵɵtext(9, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 50);\n    i0.ɵɵtext(11, \"Vytvo\\u0159eno\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 50);\n    i0.ɵɵtext(13, \"Vytvo\\u0159il\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 50);\n    i0.ɵɵtext(15, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_50_tr_17_Template, 15, 8, \"tr\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.securityEventFilters);\n  }\n}\nexport let SecurityComponent = /*#__PURE__*/(() => {\n  class SecurityComponent {\n    constructor(securityService, fb, modalService, toastr) {\n      this.securityService = securityService;\n      this.fb = fb;\n      this.modalService = modalService;\n      this.toastr = toastr;\n      this.loading = false;\n      this.error = null;\n      this.securityEvents = [];\n      this.activeAlerts = [];\n      this.failedConnectionStats = [];\n      this.selectedAlert = null;\n      // Security Event Filters\n      this.securityEventFilters = [];\n      this.selectedEvent = null;\n      this.deleteExistingEvents = false;\n      this.loadingFilters = false;\n      this.applyToAllIps = true; // Výchozí stav - použít pro všechny IP\n      this.filterIpAddress = '';\n      this.resolveAlertForm = this.fb.group({\n        resolution: ['', Validators.required]\n      });\n    }\n    ngOnInit() {\n      this.loadSecurityDashboard();\n    }\n    loadSecurityDashboard() {\n      this.loading = true;\n      this.error = null;\n      this.securityService.getSecurityDashboard().subscribe({\n        next: response => {\n          // Převod časů na lokální čas\n          this.securityEvents = response.recentSecurityEvents.map(event => ({\n            ...event,\n            timestamp: new Date(event.timestamp)\n          }));\n          this.activeAlerts = response.activeAlerts.map(alert => ({\n            ...alert,\n            timestamp: new Date(alert.timestamp),\n            resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined\n          }));\n          this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n            ...stat,\n            lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n            lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n          }));\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading security dashboard', err);\n          this.error = 'Chyba při načítání bezpečnostního dashboardu';\n          this.loading = false;\n        }\n      });\n    }\n    openResolveAlertModal(alert) {\n      this.selectedAlert = alert;\n      this.resolveAlertForm.reset({\n        resolution: ''\n      });\n      this.modalService.open('resolveAlertModal');\n    }\n    resolveAlert() {\n      if (this.resolveAlertForm.invalid || !this.selectedAlert) {\n        return;\n      }\n      const formData = this.resolveAlertForm.value;\n      this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({\n        next: () => {\n          // Zavření modálu\n          this.modalService.close('resolveAlertModal');\n          // Aktualizace dat\n          this.loadSecurityDashboard();\n        },\n        error: err => {\n          console.error('Error resolving alert', err);\n          this.error = 'Chyba při řešení upozornění';\n        }\n      });\n    }\n    getSeverityClass(severity) {\n      switch (severity) {\n        case 5:\n          return 'text-danger fw-bold';\n        case 4:\n          return 'text-danger';\n        case 3:\n          return 'text-warning';\n        case 2:\n          return 'text-info';\n        case 1:\n          return 'text-success';\n        default:\n          return 'text-muted';\n      }\n    }\n    getSeverityIcon(severity) {\n      switch (severity) {\n        case 5:\n        case 4:\n          return 'bi-exclamation-triangle-fill';\n        case 3:\n          return 'bi-exclamation-circle-fill';\n        case 2:\n          return 'bi-info-circle-fill';\n        default:\n          return 'bi-check-circle-fill';\n      }\n    }\n    getSeverityText(severity) {\n      switch (severity) {\n        case 5:\n          return 'Kritická';\n        case 4:\n          return 'Vysoká';\n        case 3:\n          return 'Střední';\n        case 2:\n          return 'Nízká';\n        case 1:\n          return 'Informační';\n        default:\n          return 'Neznámá';\n      }\n    }\n    getEventTypeClass(eventType) {\n      switch (eventType) {\n        case 'SuspiciousActivity':\n          return 'text-danger';\n        case 'CertificateValidationFailure':\n          return 'text-warning';\n        case 'IpBlocked':\n          return 'text-danger';\n        case 'FailedAccessAttempt':\n          return 'text-warning';\n        default:\n          return 'text-info';\n      }\n    }\n    getEventTypeIcon(eventType) {\n      switch (eventType) {\n        case 'SuspiciousActivity':\n          return 'bi-exclamation-triangle-fill';\n        case 'CertificateValidationFailure':\n          return 'bi-shield-exclamation';\n        case 'IpBlocked':\n          return 'bi-slash-circle-fill';\n        case 'FailedAccessAttempt':\n          return 'bi-x-circle-fill';\n        case 'ApiKeyMisuse':\n          return 'bi-key-fill';\n        case 'UnauthorizedAccess':\n          return 'bi-shield-x';\n        case 'Other':\n          return 'bi-question-circle-fill';\n        default:\n          return 'bi-info-circle-fill';\n      }\n    }\n    getEventTypeText(eventType) {\n      switch (eventType) {\n        case 'FailedAccessAttempt':\n          return 'Neúspěšný pokus o přístup';\n        case 'SuspiciousActivity':\n          return 'Podezřelá aktivita';\n        case 'IpBlocked':\n          return 'Blokovaná IP adresa';\n        case 'CertificateValidationFailure':\n          return 'Selhání validace certifikátu';\n        case 'ApiKeyMisuse':\n          return 'Nesprávné použití API klíče';\n        case 'UnauthorizedAccess':\n          return 'Neautorizovaný přístup';\n        case 'Other':\n          return 'Ostatní';\n        default:\n          return eventType;\n      }\n    }\n    getAlertTypeClass(alertType) {\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'text-warning';\n        case 'FailedConnectionAttempts':\n          return 'text-danger';\n        case 'SuspiciousActivity':\n          return 'text-danger';\n        case 'ApiKeyMisuse':\n          return 'text-danger';\n        case 'SystemError':\n          return 'text-danger';\n        case 'Error':\n          return 'text-danger';\n        case 'Warning':\n          return 'text-warning';\n        case 'Information':\n          return 'text-info';\n        case 'Other':\n          return 'text-secondary';\n        default:\n          return 'text-info';\n      }\n    }\n    getAlertTypeIcon(alertType) {\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'bi-clock-history';\n        case 'FailedConnectionAttempts':\n          return 'bi-x-circle-fill';\n        case 'SuspiciousActivity':\n          return 'bi-exclamation-triangle-fill';\n        case 'ApiKeyMisuse':\n          return 'bi-key-fill';\n        case 'SystemError':\n          return 'bi-exclamation-octagon-fill';\n        case 'Other':\n          return 'bi-question-circle-fill';\n        case 'Information':\n          return 'bi-info-circle-fill';\n        case 'Warning':\n          return 'bi-exclamation-triangle-fill';\n        case 'Error':\n          return 'bi-x-octagon-fill';\n        default:\n          return 'bi-info-circle-fill';\n      }\n    }\n    getAlertTypeText(alertType) {\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'Expirující certifikát';\n        case 'FailedConnectionAttempts':\n          return 'Neúspěšné připojení';\n        case 'SuspiciousActivity':\n          return 'Podezřelá aktivita';\n        case 'ApiKeyMisuse':\n          return 'Chyba použití API klíče';\n        case 'SystemError':\n          return 'Systémová chyba';\n        case 'Other':\n          return 'Ostatní';\n        case 'Information':\n          return 'Informace';\n        case 'Warning':\n          return 'Varování';\n        case 'Error':\n          return 'Chyba';\n        default:\n          return alertType;\n      }\n    }\n    // Security Event Filter methods\n    openBlockEventModal(event) {\n      this.selectedEvent = event;\n      this.deleteExistingEvents = false;\n      this.applyToAllIps = true; // Výchozí stav\n      this.filterIpAddress = event.ipAddress || '';\n      const modal = new bootstrap.Modal(document.getElementById('blockEventModal'));\n      modal.show();\n    }\n    onApplyToAllIpsChange() {\n      if (!this.applyToAllIps && this.selectedEvent) {\n        // Pokud se vypne \"pro všechny IP\", nastav IP adresu z události\n        this.filterIpAddress = this.selectedEvent.ipAddress || '';\n      }\n    }\n    confirmBlockEvent() {\n      if (!this.selectedEvent) return;\n      const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n      const request = {\n        eventType: eventTypeNumber,\n        description: this.selectedEvent.description,\n        ipAddress: this.applyToAllIps ? undefined : this.filterIpAddress,\n        deleteExistingEvents: this.deleteExistingEvents\n      };\n      this.securityService.createSecurityEventFilter(request).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n            this.loadSecurityDashboard(); // Refresh data\n            const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal'));\n            modal?.hide();\n          } else {\n            this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n          }\n        },\n        error: error => {\n          console.error('Error creating filter:', error);\n          this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n        }\n      });\n    }\n    openManageFiltersModal() {\n      this.loadSecurityEventFilters();\n      const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal'));\n      modal.show();\n    }\n    loadSecurityEventFilters() {\n      this.loadingFilters = true;\n      this.securityService.getSecurityEventFilters().subscribe({\n        next: response => {\n          if (response.success && response.data) {\n            this.securityEventFilters = response.data.map(filter => ({\n              ...filter,\n              createdAt: new Date(filter.createdAt)\n            }));\n          } else {\n            this.securityEventFilters = [];\n          }\n          this.loadingFilters = false;\n        },\n        error: error => {\n          console.error('Error loading filters:', error);\n          this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n          this.loadingFilters = false;\n        }\n      });\n    }\n    confirmDeleteFilter(filter) {\n      if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n        this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n              this.loadSecurityEventFilters(); // Refresh filters\n            } else {\n              this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n            }\n          },\n          error: error => {\n            console.error('Error deleting filter:', error);\n            this.toastr.error('Chyba při mazání filtru', 'Chyba');\n          }\n        });\n      }\n    }\n    getEventTypeNumber(eventType) {\n      switch (eventType) {\n        case 'FailedAccessAttempt':\n          return 0;\n        case 'SuspiciousActivity':\n          return 1;\n        case 'IpBlocked':\n          return 2;\n        case 'CertificateValidationFailure':\n          return 3;\n        case 'ApiKeyMisuse':\n          return 4;\n        case 'UnauthorizedAccess':\n          return 5;\n        case 'Other':\n          return 6;\n        default:\n          return 6;\n        // Other\n      }\n    }\n\n    static {\n      this.ɵfac = function SecurityComponent_Factory(t) {\n        return new (t || SecurityComponent)(i0.ɵɵdirectiveInject(i1.SecurityService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ModalService), i0.ɵɵdirectiveInject(i4.ToastrService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityComponent,\n        selectors: [[\"app-security\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 54,\n        vars: 9,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"resolveAlertModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"resolveAlertModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"resolveAlertModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-1\"], [\"id\", \"blockEventModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"blockEventModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-header\", \"bg-warning\", \"text-dark\"], [\"id\", \"blockEventModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\", \"me-1\"], [\"id\", \"manageFiltersModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"manageFiltersModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [\"id\", \"manageFiltersModalLabel\", 1, \"modal-title\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"bi\", \"bi-bell-fill\", \"me-2\"], [1, \"card-body\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-funnel\", \"me-1\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-nowrap\", 3, \"ngClass\"], [3, \"ngClass\"], [1, \"text-nowrap\"], [\"title\", \"Vy\\u0159e\\u0161it\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-check-circle-fill\"], [\"type\", \"button\", \"title\", \"Blokovat ud\\u00E1lost\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\"], [1, \"alert\", 3, \"ngClass\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"resolution\", 1, \"form-label\"], [\"id\", \"resolution\", \"formControlName\", \"resolution\", \"rows\", \"3\", \"placeholder\", \"Popi\\u0161te, jak bylo upozorn\\u011Bn\\u00ED vy\\u0159e\\u0161eno\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"alert\", \"alert-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"card\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"applyToAllIps\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"applyToAllIps\", 1, \"form-check-label\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"mt-2 text-muted\", 4, \"ngIf\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", \"id\", \"deleteExistingEvents\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"deleteExistingEvents\", 1, \"form-check-label\"], [1, \"mt-2\"], [\"for\", \"filterIpAddress\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterIpAddress\", \"placeholder\", \"Zadejte IP adresu\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\", \"text-muted\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"spinner-border\"], [\"type\", \"button\", \"title\", \"Smazat filtr\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-trash\"]],\n        template: function SecurityComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_4_listener() {\n              return ctx.loadSecurityDashboard();\n            });\n            i0.ɵɵelement(5, \"i\", 3);\n            i0.ɵɵtext(6, \"Aktualizovat \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, SecurityComponent_div_7_Template, 4, 0, \"div\", 4);\n            i0.ɵɵtemplate(8, SecurityComponent_div_8_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(9, SecurityComponent_div_9_Template, 28, 6, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"h5\", 11);\n            i0.ɵɵtext(15, \"Vy\\u0159e\\u0161it upozorn\\u011Bn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"button\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵtemplate(18, SecurityComponent_div_18_Template, 12, 6, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 14)(20, \"button\", 15);\n            i0.ɵɵtext(21, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_22_listener() {\n              return ctx.resolveAlert();\n            });\n            i0.ɵɵelement(23, \"i\", 17);\n            i0.ɵɵtext(24, \"Vy\\u0159e\\u0161it \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 8)(27, \"div\", 9)(28, \"div\", 19)(29, \"h5\", 20);\n            i0.ɵɵtext(30, \"Blokovat bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"button\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 13);\n            i0.ɵɵtemplate(33, SecurityComponent_div_33_Template, 30, 6, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 14)(35, \"button\", 15);\n            i0.ɵɵtext(36, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_37_listener() {\n              return ctx.confirmBlockEvent();\n            });\n            i0.ɵɵelement(38, \"i\", 23);\n            i0.ɵɵtext(39, \"Blokovat ud\\u00E1lost \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(40, \"div\", 24)(41, \"div\", 25)(42, \"div\", 9)(43, \"div\", 10)(44, \"h5\", 26);\n            i0.ɵɵtext(45, \"Spravovat filtry bezpe\\u010Dnostn\\u00EDch ud\\u00E1lost\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"button\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 13);\n            i0.ɵɵtemplate(48, SecurityComponent_div_48_Template, 4, 0, \"div\", 27);\n            i0.ɵɵtemplate(49, SecurityComponent_div_49_Template, 2, 0, \"div\", 28);\n            i0.ɵɵtemplate(50, SecurityComponent_div_50_Template, 18, 1, \"div\", 29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 14)(52, \"button\", 15);\n            i0.ɵɵtext(53, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedAlert);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.resolveAlertForm.invalid);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEvent);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.loadingFilters);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length > 0);\n          }\n        },\n        dependencies: [LocalDatePipe, CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, ReactiveFormsModule, i2.FormGroupDirective, i2.FormControlName],\n        styles: [\".card[_ngcontent-%COMP%]{border-radius:.5rem;box-shadow:0 .125rem .25rem #00000013;overflow:hidden}.card-header[_ngcontent-%COMP%]{border-bottom:none}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none;font-weight:600}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:.75rem 1rem;vertical-align:middle}.btn-sm[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem}.alert[_ngcontent-%COMP%]{margin-bottom:1rem;border-radius:.25rem}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}.text-success[_ngcontent-%COMP%]{color:#198754!important}@media (prefers-color-scheme: dark){.card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}.table[_ngcontent-%COMP%]{color:#e9ecef}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-bottom-color:#373b3e}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top-color:#373b3e}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#ffffff13}.alert-info[_ngcontent-%COMP%]{background-color:#0d3b66;border-color:#0d3b66;color:#e9ecef}}\"]\n      });\n    }\n  }\n  return SecurityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}