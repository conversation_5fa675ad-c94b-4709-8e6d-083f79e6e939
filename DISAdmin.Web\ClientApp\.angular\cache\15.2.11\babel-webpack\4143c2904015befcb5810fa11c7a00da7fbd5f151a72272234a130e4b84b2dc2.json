{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/clipboard.service\";\nimport * as i2 from \"@angular/common\";\nfunction CertificateModalComponent_div_11_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2, \" Heslo bylo zkop\\u00EDrov\\u00E1no do schr\\u00E1nky. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CertificateModalComponent_div_11_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2, \" Pou\\u017E\\u00EDv\\u00E1 se v\\u00FDchoz\\u00ED heslo \\\"password\\\". Pro v\\u011Bt\\u0161\\u00ED bezpe\\u010Dnost doporu\\u010Dujeme po instalaci certifik\\u00E1tu zm\\u011Bnit heslo. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CertificateModalComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 12);\n    i0.ɵɵtext(2, \"Informace o certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13)(4, \"label\", 14);\n    i0.ɵɵtext(5, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 13)(9, \"label\", 14);\n    i0.ɵɵtext(10, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 13)(15, \"label\", 14);\n    i0.ɵɵtext(16, \"Heslo k certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function CertificateModalComponent_div_11_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.copyPasswordToClipboard());\n    });\n    i0.ɵɵelement(21, \"i\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, CertificateModalComponent_div_11_div_22_Template, 3, 0, \"div\", 20);\n    i0.ɵɵtemplate(23, CertificateModalComponent_div_11_div_23_Template, 3, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 22);\n    i0.ɵɵelement(25, \"i\", 23);\n    i0.ɵɵelementStart(26, \"strong\");\n    i0.ɵɵtext(27, \"D\\u016Fle\\u017Eit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Toto heslo si poznamenejte. Budete ho pot\\u0159ebovat p\\u0159i instalaci certifik\\u00E1tu. Z bezpe\\u010Dnostn\\u00EDch d\\u016Fvod\\u016F nen\\u00ED heslo nikde ulo\\u017Eeno a nebude mo\\u017En\\u00E9 ho pozd\\u011Bji zobrazit. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 12)(30, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CertificateModalComponent_div_11_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.downloadCertificate());\n    });\n    i0.ɵɵelement(31, \"i\", 25);\n    i0.ɵɵtext(32, \"St\\u00E1hnout certifik\\u00E1t \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.certificate.thumbprint);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 6, ctx_r0.certificate.expirationDate, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.certificate.password, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.passwordCopied ? \"bi-check-lg\" : \"bi-clipboard\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.passwordCopied);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.certificate.password === \"password\");\n  }\n}\nexport let CertificateModalComponent = /*#__PURE__*/(() => {\n  class CertificateModalComponent {\n    constructor(clipboardService) {\n      this.clipboardService = clipboardService;\n      this.certificate = null;\n      this.instanceName = '';\n      this.modalId = 'certificateModal';\n      this.close = new EventEmitter();\n      this.download = new EventEmitter();\n      // Příznak pro zobrazení potvrzení o zkopírování\n      this.passwordCopied = false;\n    }\n    /**\r\n     * Zavření modálního okna\r\n     */\n    closeModal() {\n      this.close.emit();\n    }\n    /**\r\n     * Stažení certifikátu\r\n     */\n    downloadCertificate() {\n      this.download.emit();\n    }\n    /**\r\n     * Zkopírování hesla certifikátu do schránky\r\n     */\n    copyPasswordToClipboard() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!_this.certificate || !_this.certificate.password) {\n          return;\n        }\n        const success = yield _this.clipboardService.copyToClipboard(_this.certificate.password, 'Heslo certifikátu bylo zkopírováno do schránky', 'Nepodařilo se zkopírovat heslo certifikátu');\n        if (success) {\n          // Nastavení příznaku pro zobrazení potvrzení\n          _this.passwordCopied = true;\n          // Po 3 sekundách skryjeme potvrzení\n          setTimeout(() => {\n            _this.passwordCopied = false;\n          }, 3000);\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function CertificateModalComponent_Factory(t) {\n        return new (t || CertificateModalComponent)(i0.ɵɵdirectiveInject(i1.ClipboardService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CertificateModalComponent,\n        selectors: [[\"app-certificate-modal\"]],\n        inputs: {\n          certificate: \"certificate\",\n          instanceName: \"instanceName\",\n          modalId: \"modalId\"\n        },\n        outputs: {\n          close: \"close\",\n          download: \"download\"\n        },\n        decls: 15,\n        vars: 5,\n        consts: [[\"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 3, \"id\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [1, \"modal-title\", 3, \"id\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"alert\", \"alert-success\", \"mb-4\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"mt-3\"], [1, \"mb-3\"], [1, \"form-label\"], [1, \"form-control\", \"bg-light\"], [1, \"input-group\"], [\"id\", \"certificatePassword\", 1, \"form-control\", \"bg-light\", \"font-monospace\", \"fw-bold\"], [\"type\", \"button\", \"title\", \"Zkop\\u00EDrovat heslo do schr\\u00E1nky\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"mt-1 small text-success\", 4, \"ngIf\"], [\"class\", \"mt-2 small text-info\", 4, \"ngIf\"], [1, \"alert\", \"alert-warning\", \"mt-3\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-download\", \"me-2\"], [1, \"mt-1\", \"small\", \"text-success\"], [1, \"bi\", \"bi-check-circle\", \"me-1\"], [1, \"mt-2\", \"small\", \"text-info\"], [1, \"bi\", \"bi-info-circle\", \"me-1\"]],\n        template: function CertificateModalComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\", 4);\n            i0.ɵɵtext(5, \"Certifik\\u00E1t byl vygenerov\\u00E1n\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function CertificateModalComponent_Template_button_click_6_listener() {\n              return ctx.closeModal();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n            i0.ɵɵelement(9, \"i\", 8);\n            i0.ɵɵtext(10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, CertificateModalComponent_div_11_Template, 33, 9, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 10)(13, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function CertificateModalComponent_Template_button_click_13_listener() {\n              return ctx.closeModal();\n            });\n            i0.ɵɵtext(14, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"id\", ctx.modalId);\n            i0.ɵɵattribute(\"aria-labelledby\", ctx.modalId + \"Label\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"id\", ctx.modalId + \"Label\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\" Certifik\\u00E1t byl \\u00FAsp\\u011B\\u0161n\\u011B vygenerov\\u00E1n pro instanci \", ctx.instanceName, \". \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.certificate);\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgIf, i2.DatePipe]\n      });\n    }\n  }\n  return CertificateModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}