<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Bezpečnostní u<PERSON></h2>
    <button class="btn btn-primary" (click)="loadSecurityDashboard()">
      <i class="bi bi-arrow-clockwise me-2"></i>Aktualizovat
    </button>
  </div>

  <div *ngIf="loading" class="d-flex justify-content-center my-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Načítání...</span>
    </div>
  </div>

  <div *ngIf="error" class="alert alert-danger mb-4">
    {{ error }}
  </div>

  <div *ngIf="!loading && !error">
    <!-- Aktivní upozornění -->
    <div class="card mb-4">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
          <i class="bi bi-bell-fill me-2"></i>Aktivní upozornění
        </h5>
      </div>
      <div class="card-body">
        <div *ngIf="activeAlerts.length === 0" class="alert alert-info">
          Žádná aktivní upozornění.
        </div>
        <div *ngIf="activeAlerts.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Závažnost</th>
                <th>Typ</th>
                <th>Čas</th>
                <th>Popis</th>
                <th>Instance</th>
                <th>Zákazník</th>
                <th>Akce</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let alert of activeAlerts">
                <td>
                  <span class="text-nowrap" [ngClass]="getSeverityClass(alert.severity)">
                    <i [ngClass]="getSeverityIcon(alert.severity) + ' me-1'"></i>
                    {{ getSeverityText(alert.severity) }}
                  </span>
                </td>
                <td class="text-nowrap">
                  <span [ngClass]="getAlertTypeClass(alert.alertType)">
                    <i [ngClass]="getAlertTypeIcon(alert.alertType) + ' me-1'"></i>{{ getAlertTypeText(alert.alertType) }}
                  </span>
                </td>
                <td class="text-nowrap">{{ alert.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>
                <td>{{ alert.description }}</td>
                <td>{{ alert.instanceName || '-' }}</td>
                <td>{{ alert.customerName || '-' }}</td>
                <td>
                  <button class="btn btn-sm btn-primary" (click)="openResolveAlertModal(alert)">
                    <i class="bi bi-check-circle-fill me-1"></i>Vyřešit
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Statistiky neúspěšných připojení -->
    <div class="card mb-4">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
          <i class="bi bi-shield-exclamation me-2"></i>Statistiky neúspěšných připojení
        </h5>
      </div>
      <div class="card-body">
        <div *ngIf="failedConnectionStats.length === 0" class="alert alert-info">
          Žádné neúspěšné pokusy o připojení.
        </div>
        <div *ngIf="failedConnectionStats.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th class="text-nowrap">Instance</th>
                <th class="text-nowrap">Neúsp. validace cert.</th>
                <th class="text-nowrap">Poslední neúsp. validace</th>
                <th class="text-nowrap">Neúsp. validace API klíče</th>
                <th class="text-nowrap">Poslední neúsp. validace API</th>
                <th class="text-nowrap">Poslední IP</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let stat of failedConnectionStats">
                <td>{{ stat.instanceName }}</td>
                <td>
                  <span [ngClass]="stat.failedCertificateValidationCount > 10 ? 'text-danger' : 'text-warning'">
                    {{ stat.failedCertificateValidationCount }}
                  </span>
                </td>
                <td>{{ stat.lastFailedCertificateValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>
                <td>
                  <span [ngClass]="stat.failedApiKeyValidationCount > 10 ? 'text-danger' : 'text-warning'">
                    {{ stat.failedApiKeyValidationCount }}
                  </span>
                </td>
                <td>{{ stat.lastFailedApiKeyValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>
                <td>{{ stat.lastKnownIpAddress || '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Nedávné bezpečnostní události -->
    <div class="card mb-4">
      <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="bi bi-journal-text me-2"></i>Nedávné bezpečnostní události
        </h5>
        <button type="button" class="btn btn-outline-light btn-sm" (click)="openManageFiltersModal()">
          <i class="bi bi-funnel me-1"></i>Spravovat filtry
        </button>
      </div>
      <div class="card-body">
        <div *ngIf="securityEvents.length === 0" class="alert alert-info">
          Žádné bezpečnostní události.
        </div>
        <div *ngIf="securityEvents.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Závažnost</th>
                <th>Typ</th>
                <th>Čas</th>
                <th>Popis</th>
                <th class="text-nowrap">IP adresa</th>
                <th>Uživatel</th>
                <th>Akce</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let event of securityEvents">
                <td>
                  <span [ngClass]="getSeverityClass(event.severity)">
                    <i [ngClass]="getSeverityIcon(event.severity) + ' me-1'"></i>
                    {{ getSeverityText(event.severity) }}
                  </span>
                </td>
                <td class="text-nowrap">
                  <span [ngClass]="getEventTypeClass(event.eventType)">
                    <i [ngClass]="getEventTypeIcon(event.eventType) + ' me-1'"></i>{{ getEventTypeText(event.eventType) }}
                  </span>
                </td>
                <td class="text-nowrap">{{ event.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>
                <td>{{ event.description }}</td>
                <td>{{ event.ipAddress }}</td>
                <td>{{ event.username || '-' }}</td>
                <td>
                  <button type="button" class="btn btn-outline-danger btn-sm"
                          (click)="openBlockEventModal(event)"
                          title="Blokovat událost">
                    <i class="bi bi-slash-circle"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro řešení upozornění -->
<div class="modal fade" id="resolveAlertModal" tabindex="-1" aria-labelledby="resolveAlertModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="resolveAlertModalLabel">Vyřešit upozornění</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="selectedAlert">
          <div class="alert" [ngClass]="getAlertTypeClass(selectedAlert.alertType)">
            <i [ngClass]="getAlertTypeIcon(selectedAlert.alertType) + ' me-1'"></i>
            <strong>{{ getAlertTypeText(selectedAlert.alertType) }}</strong>: {{ selectedAlert.description }}
          </div>

          <form [formGroup]="resolveAlertForm" (ngSubmit)="resolveAlert()">
            <div class="mb-3">
              <label for="resolution" class="form-label">Řešení</label>
              <textarea
                id="resolution"
                formControlName="resolution"
                class="form-control"
                rows="3"
                placeholder="Popište, jak bylo upozornění vyřešeno"
              ></textarea>
              <div *ngIf="resolveAlertForm.get('resolution')?.invalid && resolveAlertForm.get('resolution')?.touched" class="text-danger">
                Řešení je povinné
              </div>
            </div>
          </form>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zavřít</button>
        <button type="button" class="btn btn-primary" [disabled]="resolveAlertForm.invalid" (click)="resolveAlert()">
          <i class="bi bi-check-circle-fill me-1"></i>Vyřešit
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro blokování události -->
<div class="modal fade" id="blockEventModal" tabindex="-1" aria-labelledby="blockEventModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-warning text-dark">
        <h5 class="modal-title" id="blockEventModalLabel">Blokovat bezpečnostní událost</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="selectedEvent">
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-1"></i>
            <strong>Pozor!</strong> Vytvoříte filtr, který zablokuje podobné bezpečnostní události v budoucnu.
          </div>

          <h6>Náhled filtru:</h6>
          <div class="card">
            <div class="card-body">
              <p><strong>Typ události:</strong> {{ getEventTypeText(selectedEvent.eventType) }}</p>
              <p><strong>Popis:</strong> {{ selectedEvent.description }}</p>
              <p><strong>IP adresa:</strong> {{ selectedEvent.ipAddress }}</p>
            </div>
          </div>

          <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" id="deleteExistingEvents" [(ngModel)]="deleteExistingEvents">
            <label class="form-check-label" for="deleteExistingEvents">
              Smazat také existující události odpovídající tomuto vzoru
            </label>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zrušit</button>
        <button type="button" class="btn btn-warning" (click)="confirmBlockEvent()">
          <i class="bi bi-slash-circle me-1"></i>Blokovat událost
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro správu filtrů -->
<div class="modal fade" id="manageFiltersModal" tabindex="-1" aria-labelledby="manageFiltersModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="manageFiltersModalLabel">Spravovat filtry bezpečnostních událostí</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="loadingFilters" class="text-center">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Načítání...</span>
          </div>
        </div>

        <div *ngIf="!loadingFilters && securityEventFilters.length === 0" class="alert alert-info">
          Žádné filtry nejsou nastaveny.
        </div>

        <div *ngIf="!loadingFilters && securityEventFilters.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Typ události</th>
                <th>Popis</th>
                <th>IP adresa</th>
                <th>Vytvořeno</th>
                <th>Vytvořil</th>
                <th>Akce</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let filter of securityEventFilters">
                <td>{{ filter.eventTypeName }}</td>
                <td>{{ filter.description || '-' }}</td>
                <td>{{ filter.ipAddress || '-' }}</td>
                <td>{{ filter.createdAt | localDate:'dd.MM.yyyy HH:mm' }}</td>
                <td>{{ filter.createdBy }}</td>
                <td>
                  <button type="button" class="btn btn-outline-danger btn-sm"
                          (click)="confirmDeleteFilter(filter)"
                          title="Smazat filtr">
                    <i class="bi bi-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zavřít</button>
      </div>
    </div>
  </div>
</div>
