{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SecurityService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/dashboard`;\n  }\n  getSecurityDashboard() {\n    return this.http.get(`${this.apiUrl}/security`);\n  }\n  getAlerts(includeResolved = false) {\n    return this.http.get(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);\n  }\n  resolveAlert(alertId, resolution) {\n    const request = {\n      resolution\n    };\n    return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);\n  }\n  // Security Event Filters\n  getSecurityEventFilters() {\n    return this.http.get(`${environment.apiUrl}/security-event-filters`);\n  }\n  createSecurityEventFilter(request) {\n    return this.http.post(`${environment.apiUrl}/security-event-filters`, request);\n  }\n  deleteSecurityEventFilter(filterId) {\n    return this.http.delete(`${environment.apiUrl}/security-event-filters/${filterId}`);\n  }\n  static {\n    this.ɵfac = function SecurityService_Factory(t) {\n      return new (t || SecurityService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SecurityService,\n      factory: SecurityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,WAAW,QAAQ,gCAAgC;;;AAa5D,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFhB,WAAM,GAAG,GAAGH,WAAW,CAACI,MAAM,YAAY;EAEV;EAExCC,oBAAoB;IAClB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAA4B,GAAG,IAAI,CAACF,MAAM,WAAW,CAAC;EAC5E;EAEAG,SAAS,CAACC,kBAA2B,KAAK;IACxC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAkB,GAAG,IAAI,CAACF,MAAM,2BAA2BI,eAAe,EAAE,CAAC;EACnG;EAEAC,YAAY,CAACC,OAAe,EAAEC,UAAkB;IAC9C,MAAMC,OAAO,GAAwB;MAAED;IAAU,CAAE;IACnD,OAAO,IAAI,CAACR,IAAI,CAACU,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,WAAWM,OAAO,UAAU,EAAEE,OAAO,CAAC;EAC5E;EAEA;EACAE,uBAAuB;IACrB,OAAO,IAAI,CAACX,IAAI,CAACG,GAAG,CAAgE,GAAGN,WAAW,CAACI,MAAM,yBAAyB,CAAC;EACrI;EAEAW,yBAAyB,CAACH,OAAyC;IACjE,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAA8D,GAAGb,WAAW,CAACI,MAAM,yBAAyB,EAAEQ,OAAO,CAAC;EAC7I;EAEAI,yBAAyB,CAACC,QAAgB;IACxC,OAAO,IAAI,CAACd,IAAI,CAACe,MAAM,CAAsC,GAAGlB,WAAW,CAACI,MAAM,2BAA2Ba,QAAQ,EAAE,CAAC;EAC1H;;;uBA7BWhB,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAkB,SAAflB,eAAe;MAAAmB,YAFd;IAAM;EAAA", "names": ["environment", "SecurityService", "constructor", "http", "apiUrl", "getSecurityDashboard", "get", "get<PERSON><PERSON><PERSON>", "includeResolved", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "resolution", "request", "post", "getSecurityEventFilters", "createSecurityEventFilter", "deleteSecurityEventFilter", "filterId", "delete", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\services\\security.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport {\n  SecurityDashboardResponse,\n  AlertResponse,\n  ResolveAlertRequest,\n  SecurityEventFilterResponse,\n  CreateSecurityEventFilterRequest,\n  SecurityEventFilterApiResponse\n} from '../models/security.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SecurityService {\n  private apiUrl = `${environment.apiUrl}/dashboard`;\n\n  constructor(private http: HttpClient) { }\n\n  getSecurityDashboard(): Observable<SecurityDashboardResponse> {\n    return this.http.get<SecurityDashboardResponse>(`${this.apiUrl}/security`);\n  }\n\n  getAlerts(includeResolved: boolean = false): Observable<AlertResponse[]> {\n    return this.http.get<AlertResponse[]>(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);\n  }\n\n  resolveAlert(alertId: number, resolution: string): Observable<any> {\n    const request: ResolveAlertRequest = { resolution };\n    return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);\n  }\n\n  // Security Event Filters\n  getSecurityEventFilters(): Observable<SecurityEventFilterApiResponse<SecurityEventFilterResponse[]>> {\n    return this.http.get<SecurityEventFilterApiResponse<SecurityEventFilterResponse[]>>(`${environment.apiUrl}/security-event-filters`);\n  }\n\n  createSecurityEventFilter(request: CreateSecurityEventFilterRequest): Observable<SecurityEventFilterApiResponse<SecurityEventFilterResponse>> {\n    return this.http.post<SecurityEventFilterApiResponse<SecurityEventFilterResponse>>(`${environment.apiUrl}/security-event-filters`, request);\n  }\n\n  deleteSecurityEventFilter(filterId: number): Observable<SecurityEventFilterApiResponse<any>> {\n    return this.http.delete<SecurityEventFilterApiResponse<any>>(`${environment.apiUrl}/security-event-filters/${filterId}`);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}