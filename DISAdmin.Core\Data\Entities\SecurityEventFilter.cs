using System.ComponentModel.DataAnnotations;

namespace DISAdmin.Core.Data.Entities;

public class SecurityEventFilter
{
    public int Id { get; set; }

    public SecurityEventType EventType { get; set; }

    [MaxLength(1000)]
    public string? Description { get; set; }

    [MaxLength(45)]
    public string? IpAddress { get; set; }

    public DateTime CreatedAt { get; set; }

    [MaxLength(100)]
    public string CreatedBy { get; set; } = string.Empty;
}
