<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2><PERSON><PERSON><PERSON><PERSON><PERSON>ík<PERSON></h2>
    <div class="d-flex flex-wrap gap-2">
      <a [routerLink]="['/instance-wizard']" class="btn btn-success text-white" *ngIf="isAdmin">
        <i class="bi bi-magic me-2"></i><span class="d-none d-md-inline">Průvodce vytvořením instance</span><span class="d-inline d-md-none">Průvodce</span>
      </a>
      <a [routerLink]="['/customers/add']" class="btn btn-primary" *ngIf="isAdmin">
        <i class="bi bi-building-fill-add me-2"></i><span class="d-none d-md-inline">Přidat zákazníka</span><span class="d-inline d-md-none">Přidat</span>
      </a>

    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <!-- Pokročilé filtrování a vyhledávání -->
      <app-advanced-filter
        [entityType]="'customers'"
        [fields]="filterFields"
        (filterChange)="onFilterChange($event)">
      </app-advanced-filter>

      <div *ngIf="loading" class="d-flex justify-content-center mt-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Načítání...</span>
        </div>
      </div>

      <div *ngIf="error" class="alert alert-danger mt-4">
        {{ error }}
      </div>

      <div *ngIf="!loading && !error && filteredCustomers.length === 0" class="alert alert-info mt-4">
        Žádní zákazníci nebyli nalezeni.
      </div>

      <div *ngIf="!loading && !error && filteredCustomers.length > 0" class="table-responsive mt-4">
        <table class="table table-striped table-hover">
          <thead class="dark-header table-header-override">
            <tr class="dark-header-row">
              <th (click)="onSort('name')" class="sortable-header">
                Název
                <i class="bi"
                  [class.bi-sort-up]="sortColumn === 'name' && sortDirection === 'asc'"
                  [class.bi-sort-down]="sortColumn === 'name' && sortDirection === 'desc'"
                  [class.bi-sort]="sortColumn !== 'name'"></i>
              </th>
              <th (click)="onSort('abbreviation')" class="sortable-header d-none d-md-table-cell">
                Zkratka
                <i class="bi"
                  [class.bi-sort-up]="sortColumn === 'abbreviation' && sortDirection === 'asc'"
                  [class.bi-sort-down]="sortColumn === 'abbreviation' && sortDirection === 'desc'"
                  [class.bi-sort]="sortColumn !== 'abbreviation'"></i>
              </th>
              <th (click)="onSort('companyId')" class="sortable-header d-none d-md-table-cell">
                IČ
                <i class="bi"
                  [class.bi-sort-up]="sortColumn === 'companyId' && sortDirection === 'asc'"
                  [class.bi-sort-down]="sortColumn === 'companyId' && sortDirection === 'desc'"
                  [class.bi-sort]="sortColumn !== 'companyId'"></i>
              </th>
              <th (click)="onSort('contactsCount')" class="sortable-header d-none d-md-table-cell">
                Kontakty
                <i class="bi"
                  [class.bi-sort-up]="sortColumn === 'contactsCount' && sortDirection === 'asc'"
                  [class.bi-sort-down]="sortColumn === 'contactsCount' && sortDirection === 'desc'"
                  [class.bi-sort]="sortColumn !== 'contactsCount'"></i>
              </th>
              <th (click)="onSort('instancesCount')" class="sortable-header d-none d-lg-table-cell">
                Instance
                <i class="bi"
                  [class.bi-sort-up]="sortColumn === 'instancesCount' && sortDirection === 'asc'"
                  [class.bi-sort-down]="sortColumn === 'instancesCount' && sortDirection === 'desc'"
                  [class.bi-sort]="sortColumn !== 'instancesCount'"></i>
              </th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let customer of paginatedCustomers">
              <td>
                <div>{{ customer.name }}</div>
                <div class="d-md-none small text-muted">Zkratka: {{ customer.abbreviation }}</div>
                <div class="d-md-none small text-muted">IČ: {{ customer.companyId }}</div>
                <div class="d-md-none small text-muted">Kontakty: {{ customer.contactsCount || 0 }}</div>
                <div class="d-lg-none small text-muted">Instance: {{ customer.instancesCount || 0 }}</div>
              </td>
              <td class="d-none d-md-table-cell">{{ customer.abbreviation }}</td>
              <td class="d-none d-md-table-cell">{{ customer.companyId }}</td>
              <td class="d-none d-md-table-cell">{{ customer.contactsCount || 0 }}</td>
              <td class="d-none d-lg-table-cell">{{ customer.instancesCount || 0 }}</td>
              <td>
                <div class="btn-group">
                  <button class="btn btn-sm btn-outline-info" (click)="viewCustomerDetail(customer)" title="Zobrazit detail">
                    <i class="bi bi-eye-fill"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-primary" (click)="editCustomer(customer)" *ngIf="isAdmin" title="Upravit">
                    <i class="bi bi-pencil-fill"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-danger" (click)="deleteCustomer(customer)" *ngIf="isAdmin" title="Smazat">
                    <i class="bi bi-trash-fill"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Stránkování -->
      <div *ngIf="!loading && !error && filteredCustomers.length > 0" class="d-flex justify-content-between align-items-center mt-3">
        <div class="pagination-info">
          Zobrazeno {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredCustomers.length) }} z {{ filteredCustomers.length }} záznamů
        </div>
        <nav aria-label="Stránkování">
          <ul class="pagination mb-0">
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(1)">
                <i class="bi bi-chevron-double-left"></i>
              </a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)">
                <i class="bi bi-chevron-left"></i>
              </a>
            </li>
            <li *ngFor="let page of pageRange" class="page-item" [class.active]="page === currentPage">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(page)">{{ page }}</a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)">
                <i class="bi bi-chevron-right"></i>
              </a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(totalPages)">
                <i class="bi bi-chevron-double-right"></i>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro přidání/úpravu zákazníka -->
<div class="modal fade" id="customerModal" tabindex="-1" aria-labelledby="customerModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="customerModalLabel">{{ isEditMode ? 'Upravit zákazníka' : 'Přidat zákazníka' }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="error" class="alert alert-danger mb-3">
          {{ error }}
        </div>
        <form [formGroup]="customerForm" (ngSubmit)="saveCustomer()">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="name" class="form-label" [ngClass]="{'required-field': customerForm.get('name')?.invalid, 'valid-field': customerForm.get('name')?.valid}">Název</label>
              <input type="text" class="form-control" id="name" formControlName="name" [class.is-invalid]="customerForm.get('name')?.invalid && customerForm.get('name')?.touched">
              <div *ngIf="customerForm.get('name')?.invalid && customerForm.get('name')?.touched" class="invalid-feedback">
                Název je povinný
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="abbreviation" class="form-label" [ngClass]="{'required-field': customerForm.get('abbreviation')?.invalid, 'valid-field': customerForm.get('abbreviation')?.valid}">Zkratka</label>
              <input type="text" class="form-control" id="abbreviation" formControlName="abbreviation" [class.is-invalid]="customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched">
              <div *ngIf="customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched" class="invalid-feedback">
                Zkratka je povinná a nesmí být delší než 50 znaků
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="taxId" class="form-label">DIČ</label>
              <input type="text" class="form-control" id="taxId" formControlName="taxId">
            </div>

            <div class="col-md-6 mb-3">
              <label for="email" class="form-label" [ngClass]="{'required-field': customerForm.get('email')?.invalid, 'valid-field': customerForm.get('email')?.valid}">Email</label>
              <input type="email" class="form-control" id="email" formControlName="email" [class.is-invalid]="customerForm.get('email')?.invalid && customerForm.get('email')?.touched">
              <div *ngIf="customerForm.get('email')?.invalid && customerForm.get('email')?.touched" class="invalid-feedback">
                <span *ngIf="customerForm.get('email')?.errors?.['email']">Neplatný formát emailu</span>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="phone" class="form-label">Telefon</label>
              <input type="text" class="form-control" id="phone" formControlName="phone">
            </div>

            <div class="col-md-6 mb-3">
              <label for="website" class="form-label">Webové stránky</label>
              <input type="text" class="form-control" id="website" formControlName="website">
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 mb-3">
              <label for="street" class="form-label" [ngClass]="{'required-field': customerForm.get('street')?.invalid, 'valid-field': customerForm.get('street')?.valid}">Ulice</label>
              <input type="text" class="form-control" id="street" formControlName="street" [class.is-invalid]="customerForm.get('street')?.invalid && customerForm.get('street')?.touched">
              <div *ngIf="customerForm.get('street')?.invalid && customerForm.get('street')?.touched" class="invalid-feedback">
                Ulice je povinná
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="city" class="form-label" [ngClass]="{'required-field': customerForm.get('city')?.invalid, 'valid-field': customerForm.get('city')?.valid}">Město</label>
              <input type="text" class="form-control" id="city" formControlName="city" [class.is-invalid]="customerForm.get('city')?.invalid && customerForm.get('city')?.touched">
              <div *ngIf="customerForm.get('city')?.invalid && customerForm.get('city')?.touched" class="invalid-feedback">
                Město je povinné
              </div>
            </div>

            <div class="col-md-4 mb-3">
              <label for="postalCode" class="form-label" [ngClass]="{'required-field': customerForm.get('postalCode')?.invalid, 'valid-field': customerForm.get('postalCode')?.valid}">PSČ</label>
              <input type="text" class="form-control" id="postalCode" formControlName="postalCode" [class.is-invalid]="customerForm.get('postalCode')?.invalid && customerForm.get('postalCode')?.touched">
              <div *ngIf="customerForm.get('postalCode')?.invalid && customerForm.get('postalCode')?.touched" class="invalid-feedback">
                PSČ je povinné
              </div>
            </div>

            <div class="col-md-4 mb-3">
              <label for="country" class="form-label" [ngClass]="{'required-field': customerForm.get('country')?.invalid, 'valid-field': customerForm.get('country')?.valid}">Země</label>
              <input type="text" class="form-control" id="country" formControlName="country" [class.is-invalid]="customerForm.get('country')?.invalid && customerForm.get('country')?.touched">
              <div *ngIf="customerForm.get('country')?.invalid && customerForm.get('country')?.touched" class="invalid-feedback">
                Země je povinná
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="notes" class="form-label">Poznámka</label>
            <textarea class="form-control" id="notes" formControlName="notes" rows="3"></textarea>
          </div>

          <!-- Sekce pro kontaktní osoby (pouze v režimu editace) -->
          <div *ngIf="isEditMode" class="mt-4 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">Kontaktní osoby</h5>
              <button type="button" class="btn btn-outline-primary btn-sm" (click)="openAddContactModal()">
                <i class="bi bi-person-plus-fill me-1"></i>Přidat kontakt
              </button>
            </div>

            <div *ngIf="loadingContacts" class="d-flex justify-content-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </div>

            <div *ngIf="!loadingContacts && contacts.length === 0" class="alert alert-info">
              Žádné kontaktní osoby nebyly nalezeny.
            </div>

            <div *ngIf="!loadingContacts && contacts.length > 0" class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="dark-header table-header-override">
                  <tr class="dark-header-row">
                    <th>Jméno</th>
                    <th>Pozice</th>
                    <th>Email</th>
                    <th>Telefon</th>
                    <th>Akce</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let contact of contacts">
                    <td>
                      <span [class.fw-bold]="contact.isPrimary">{{ contact.firstName }} {{ contact.lastName }}</span>
                      <span *ngIf="contact.isPrimary" class="badge bg-primary ms-2">Primární</span>
                    </td>
                    <td>{{ contact.position || '-' }}</td>
                    <td>
                      <a *ngIf="contact.email" href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                      <span *ngIf="!contact.email">-</span>
                    </td>
                    <td>
                      <a *ngIf="contact.phone" href="tel:{{ contact.phone }}">{{ contact.phone }}</a>
                      <span *ngIf="!contact.phone">-</span>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary" (click)="editContact(contact)" title="Upravit">
                          <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" (click)="deleteContact(contact)" title="Smazat">
                          <i class="bi bi-trash-fill"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Sekce pro instance DIS (pouze v režimu editace) -->
          <div *ngIf="isEditMode" class="mt-4 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">Instance DIS</h5>
              <button type="button" class="btn btn-outline-primary btn-sm" (click)="openAddInstanceModal()">
                <i class="bi bi-plus-circle-fill me-1"></i>Přidat instanci
              </button>
            </div>

            <div *ngIf="loadingInstances" class="d-flex justify-content-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </div>

            <div *ngIf="!loadingInstances && instances.length === 0" class="alert alert-info">
              Žádné instance DIS nebyly nalezeny.
            </div>

            <div *ngIf="!loadingInstances && instances.length > 0" class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="dark-header table-header-override">
                  <tr class="dark-header-row">
                    <th>Název</th>
                    <th>Server</th>
                    <th>Databáze</th>
                    <th>Status</th>
                    <th>Aktuální verze</th>
                    <th>Datum instalace</th>
                    <th>Datum expirace</th>
                    <th>Akce</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let instance of instances">
                    <td><a href="javascript:void(0)" (click)="viewInstanceDetail(instance)">{{ instance.name }}</a></td>
                    <td>{{ instance.serverUrl }}</td>
                    <td></td>
                    <td>
                      <span class="badge" [ngClass]="{
                        'bg-success': instance.status === InstanceStatus.Active,
                        'bg-danger': instance.status === InstanceStatus.Blocked,
                        'bg-warning': instance.status === InstanceStatus.Trial,
                        'bg-secondary': instance.status === InstanceStatus.Maintenance,
                        'bg-dark': instance.status === InstanceStatus.Expired
                      }">
                        {{ getInstanceStatusText(instance.status) }}
                      </span>
                    </td>
                    <td>
                      <span *ngIf="loadingInstanceVersions[instance.id]" class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Načítání...</span>
                      </span>
                      <span *ngIf="!loadingInstanceVersions[instance.id]">
                        {{ getLatestVersion(instance.id) }}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2"
                                (click)="openAddInstanceVersionModal(instance)"
                                title="Přidat verzi">
                          <i class="bi bi-plus-circle-fill"></i>
                        </button>
                      </span>
                    </td>
                    <td>{{ instance.installationDate | date:'dd.MM.yyyy' }}</td>
                    <td>{{ instance.expirationDate ? (instance.expirationDate | date:'dd.MM.yyyy') : '' }}</td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-primary" (click)="editInstance(instance)" title="Upravit">
                          <i class="bi bi-pencil-fill"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info" [routerLink]="['/instance-metrics', instance.id]" title="Zobrazit metriky">
                          <i class="bi bi-graph-up"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" (click)="deleteInstance(instance)" title="Smazat">
                          <i class="bi bi-trash-fill"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeCustomerModal()">
              <i class="bi bi-x-circle me-1"></i>Zavřít
            </button>
            <button type="button" class="btn btn-primary" [disabled]="customerForm.invalid || saving" (click)="saveCustomer()">
              <span *ngIf="saving" class="spinner-border spinner-border-sm me-1"></span>
              <i *ngIf="!saving" class="bi bi-save me-1"></i>{{ isEditMode ? 'Aktualizovat' : 'Uložit' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro detail zákazníka -->
<div class="modal fade" id="customerDetailModal" tabindex="-1" aria-labelledby="customerDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="customerDetailModalLabel">Detail zákazníka</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body" *ngIf="selectedCustomer">
        <div class="row mb-4">
          <div class="col-md-6">
            <h4>{{ selectedCustomer.name }}</h4>
            <p><strong>Zkratka:</strong> {{ selectedCustomer.abbreviation }}</p>
            <p *ngIf="selectedCustomer.companyId"><strong>IČ:</strong> {{ selectedCustomer.companyId }}</p>
            <p *ngIf="selectedCustomer.taxId"><strong>DIČ:</strong> {{ selectedCustomer.taxId }}</p>
            <p><strong>Ulice:</strong> {{ selectedCustomer.street }}</p>
            <p><strong>Město:</strong> {{ selectedCustomer.city }}</p>
            <p><strong>PSČ:</strong> {{ selectedCustomer.postalCode }}</p>
            <p><strong>Země:</strong> {{ selectedCustomer.country }}</p>
          </div>
          <div class="col-md-6">
            <p *ngIf="selectedCustomer.email"><strong>Email:</strong> {{ selectedCustomer.email }}</p>
            <p *ngIf="selectedCustomer.phone"><strong>Telefon:</strong> {{ selectedCustomer.phone }}</p>
            <p *ngIf="selectedCustomer.website"><strong>Web:</strong> {{ selectedCustomer.website }}</p>
            <p *ngIf="selectedCustomer.notes"><strong>Poznámka:</strong> {{ selectedCustomer.notes }}</p>
          </div>
        </div>

        <!-- Statistiky zákazníka -->
        <div class="card mb-4">
          <div class="card-header bg-light">
            <h5 class="mb-0">Statistiky zákazníka</h5>
          </div>
          <div class="card-body">
            <div *ngIf="loadingStatistics" class="d-flex justify-content-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </div>

            <div *ngIf="!loadingStatistics && customerStatistics" class="row">
              <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                  <div class="card-body text-center">
                    <h3 class="display-4">{{ customerStatistics.instancesCount }}</h3>
                    <p class="mb-0">Celkem instancí</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                  <div class="card-body text-center">
                    <h3 class="display-4">{{ customerStatistics.activeInstancesCount }}</h3>
                    <p class="mb-0">Aktivních instancí</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                  <div class="card-body text-center">
                    <h3 class="display-4">{{ customerStatistics.apiCallsLast24h }}</h3>
                    <p class="mb-0">API volání (24h)</p>
                  </div>
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark">
                  <div class="card-body text-center">
                    <h3 class="display-4">{{ customerStatistics.expiringCertificatesCount }}</h3>
                    <p class="mb-0">Expirující certifikáty</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="card">
                  <div class="card-body">
                    <h5 class="card-title">Výkon API</h5>
                    <p class="mb-0"><strong>Průměrná doba odezvy:</strong> {{ customerStatistics.avgApiResponseTime | number:'1.2-2' }} ms</p>
                    <p class="mb-0"><strong>Bezpečnostní události (24h):</strong> {{ customerStatistics.securityEventsCount }}</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <div class="card">
                  <div class="card-body">
                    <h5 class="card-title">Nejpoužívanější verze</h5>
                    <p class="mb-0"><strong>Verze:</strong> {{ customerStatistics.mostUsedVersion || 'Není k dispozici' }}</p>
                    <p class="mb-0"><strong>Počet instancí:</strong> {{ customerStatistics.mostUsedVersionCount || 0 }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="!loadingStatistics && statisticsError" class="alert alert-danger">
              {{ statisticsError }}
            </div>
          </div>
        </div>

        <div class="mb-3">
          <h5 class="mb-0">Kontaktní osoby</h5>
        </div>

        <div *ngIf="loadingContacts" class="d-flex justify-content-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Načítání...</span>
          </div>
        </div>

        <div *ngIf="!loadingContacts && contacts.length === 0" class="alert alert-info">
          Žádné kontaktní osoby nebyly nalezeny.
        </div>

        <div *ngIf="!loadingContacts && contacts.length > 0" class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="dark-header table-header-override">
              <tr class="dark-header-row">
                <th>Jméno</th>
                <th>Pozice</th>
                <th>Email</th>
                <th>Telefon</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let contact of contacts">
                <td>
                  <span [class.fw-bold]="contact.isPrimary">{{ contact.firstName }} {{ contact.lastName }}</span>
                  <span *ngIf="contact.isPrimary" class="badge bg-primary ms-2">Primární</span>
                </td>
                <td>{{ contact.position }}</td>
                <td>
                  <a *ngIf="contact.email" href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                </td>
                <td>
                  <a *ngIf="contact.phone" href="tel:{{ contact.phone }}">{{ contact.phone }}</a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="mb-3 mt-4">
          <h5 class="mb-0">Instance DIS</h5>
        </div>

        <div *ngIf="loadingInstances" class="d-flex justify-content-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Načítání...</span>
          </div>
        </div>

        <div *ngIf="!loadingInstances && instances.length === 0" class="alert alert-info">
          Žádné instance DIS nebyly nalezeny.
        </div>

        <div *ngIf="!loadingInstances && instances.length > 0" class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="dark-header table-header-override">
              <tr class="dark-header-row">
                <th>Název</th>
                <th>Server</th>
                <th>Databáze</th>
                <th>Status</th>
                <th>Aktuální verze</th>
                <th>Datum instalace</th>
                <th>Datum expirace</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let instance of instances">
                <td><a href="javascript:void(0)" (click)="viewInstanceDetail(instance)">{{ instance.name }}</a></td>
                <td>{{ instance.serverUrl }}</td>
                <td></td>
                <td>
                  <span class="badge" [ngClass]="{
                    'bg-success': instance.status === InstanceStatus.Active,
                    'bg-danger': instance.status === InstanceStatus.Blocked,
                    'bg-warning': instance.status === InstanceStatus.Trial,
                    'bg-secondary': instance.status === InstanceStatus.Maintenance,
                    'bg-dark': instance.status === InstanceStatus.Expired
                  }">
                    {{ getInstanceStatusText(instance.status) }}
                  </span>
                </td>
                <td>
                  <span *ngIf="loadingInstanceVersions[instance.id]" class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Načítání...</span>
                  </span>
                  <span *ngIf="!loadingInstanceVersions[instance.id]">{{ getLatestVersion(instance.id) }}</span>
                </td>
                <td>{{ instance.installationDate | date:'dd.MM.yyyy' }}</td>
                <td>{{ instance.expirationDate ? (instance.expirationDate | date:'dd.MM.yyyy') : '' }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="d-flex justify-content-end mt-3">
          <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal" (click)="closeCustomerDetailModal()">
            <i class="bi bi-x-circle me-1"></i>Zavřít
          </button>
          <button type="button" class="btn btn-primary" [disabled]="!selectedCustomer" (click)="editSelectedCustomer()">
            <i class="bi bi-pencil-fill me-1"></i>Upravit
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro přidání/úpravu kontaktu -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="contactModalLabel">{{ isEditContactMode ? 'Upravit kontakt' : 'Přidat kontakt' }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="contactError" class="alert alert-danger mb-3">
          {{ contactError }}
        </div>
        <form [formGroup]="contactForm" (ngSubmit)="saveContact()">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="firstName" class="form-label" [ngClass]="{'required-field': contactForm.get('firstName')?.invalid, 'valid-field': contactForm.get('firstName')?.valid}">Jméno</label>
              <input type="text" class="form-control" id="firstName" formControlName="firstName">
              <div *ngIf="contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched" class="text-danger">
                Jméno je povinné
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="lastName" class="form-label" [ngClass]="{'required-field': contactForm.get('lastName')?.invalid, 'valid-field': contactForm.get('lastName')?.valid}">Příjmení</label>
              <input type="text" class="form-control" id="lastName" formControlName="lastName">
              <div *ngIf="contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched" class="text-danger">
                Příjmení je povinné
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="position" class="form-label">Pozice</label>
            <input type="text" class="form-control" id="position" formControlName="position">
          </div>

          <div class="mb-3">
            <label for="contactEmail" class="form-label">Email</label>
            <input type="email" class="form-control" id="contactEmail" formControlName="email" [class.is-invalid]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched">
            <div *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched" class="invalid-feedback">
              <span *ngIf="contactForm.get('email')?.errors?.['email']">Neplatný formát emailu</span>
            </div>
          </div>

          <div class="mb-3">
            <label for="contactPhone" class="form-label">Telefon</label>
            <input type="text" class="form-control" id="contactPhone" formControlName="phone">
          </div>

          <div class="mb-3">
            <label for="contactNote" class="form-label">Poznámka</label>
            <textarea class="form-control" id="contactNote" formControlName="notes" rows="2"></textarea>
          </div>

          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="isPrimary" formControlName="isPrimary">
            <label class="form-check-label" for="isPrimary">Hlavní kontakt</label>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeContactModal()">
              <i class="bi bi-x-circle me-1"></i>Zavřít
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="contactForm.invalid || savingContact">
              <span *ngIf="savingContact" class="spinner-border spinner-border-sm me-1"></span>
              <i *ngIf="!savingContact" class="bi bi-save me-1"></i>{{ isEditContactMode ? 'Aktualizovat' : 'Uložit' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro přidání/úpravu instance DIS -->
<div class="modal fade" id="instanceModal" tabindex="-1" aria-labelledby="instanceModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="instanceModalLabel">{{ isEditInstanceMode ? 'Upravit instanci DIS' : 'Přidat instanci DIS' }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="instanceError" class="alert alert-danger mb-3">
          {{ instanceError }}
        </div>
        <form [formGroup]="instanceForm" (ngSubmit)="saveInstance()">
          <div class="mb-3">
            <label for="instanceName" class="form-label required-field">Název instance</label>
            <input type="text" class="form-control" id="instanceName" formControlName="name" [class.is-invalid]="instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched">
            <div *ngIf="instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched" class="invalid-feedback">
              Název instance je povinný
            </div>
          </div>

          <div class="mb-3">
            <label for="instanceServerUrl" class="form-label required-field">URL serveru</label>
            <input type="text" class="form-control" id="instanceServerUrl" formControlName="serverUrl" [class.is-invalid]="instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched">
            <div *ngIf="instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched" class="invalid-feedback">
              URL serveru je povinná
            </div>
          </div>

          <div class="mb-3">
            <label for="instanceExpirationDate" class="form-label">Datum expirace</label>
            <input type="date" class="form-control" id="instanceExpirationDate" formControlName="expirationDate">
          </div>

          <div class="mb-3">
            <label for="instanceStatus" class="form-label">Status</label>
            <select class="form-select" id="instanceStatus" formControlName="status">
              <option value="Active">Aktivní</option>
              <option value="Blocked">Blokovaná</option>
              <option value="Expired">Expirovaná</option>
              <option value="Trial">Zkušební</option>
              <option value="Maintenance">Údržba</option>
            </select>
          </div>

          <div class="mb-3" *ngIf="instanceForm.get('status')?.value === 'Blocked'">
            <label for="instanceBlockReason" class="form-label">Důvod blokace</label>
            <input type="text" class="form-control" id="instanceBlockReason" formControlName="blockReason">
          </div>

          <div class="mb-3">
            <label class="form-label">Povolené moduly</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="moduleReporting" formControlName="moduleReporting">
              <label class="form-check-label" for="moduleReporting">Reportování</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="moduleAdvancedSecurity" formControlName="moduleAdvancedSecurity">
              <label class="form-check-label" for="moduleAdvancedSecurity">Pokročilá bezpečnost</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="moduleApiIntegration" formControlName="moduleApiIntegration">
              <label class="form-check-label" for="moduleApiIntegration">API integrace</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="moduleDataExport" formControlName="moduleDataExport">
              <label class="form-check-label" for="moduleDataExport">Export dat</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="moduleCustomization" formControlName="moduleCustomization">
              <label class="form-check-label" for="moduleCustomization">Přizpůsobení</label>
            </div>
          </div>

          <div class="mb-3">
            <label for="instanceNotes" class="form-label">Poznámka</label>
            <textarea class="form-control" id="instanceNotes" formControlName="notes" rows="3"></textarea>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeInstanceModal()">
              <i class="bi bi-x-circle me-1"></i>Zavřít
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="instanceForm.invalid || savingInstance">
              <span *ngIf="savingInstance" class="spinner-border spinner-border-sm me-1"></span>
              <i *ngIf="!savingInstance" class="bi bi-save me-1"></i>{{ isEditInstanceMode ? 'Aktualizovat' : 'Uložit' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro přidání/úpravu verze instance -->
<div class="modal fade" id="instanceVersionModal" tabindex="-1" aria-labelledby="instanceVersionModalLabel" aria-hidden="true" data-bs-backdrop="false" style="z-index: 1070 !important;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="instanceVersionModalLabel">{{ isEditInstanceVersionMode ? 'Upravit verzi instance' : 'Přidat verzi instance' }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="instanceVersionError" class="alert alert-danger mb-3">
          {{ instanceVersionError }}
        </div>
        <form [formGroup]="instanceVersionForm" (ngSubmit)="saveInstanceVersion()">
          <div class="mb-3">
            <label for="instanceName" class="form-label">Instance:</label>
            <input type="text" class="form-control" id="instanceName" [value]="selectedInstanceForVersion?.name" disabled>
          </div>

          <div class="mb-3">
            <label for="versionId" class="form-label required-field">Verze:</label>
            <select class="form-select" id="versionId" formControlName="versionId" [class.is-invalid]="instanceVersionForm.get('versionId')?.invalid && instanceVersionForm.get('versionId')?.touched">
              <option value="">-- Vyberte verzi --</option>
              <option *ngFor="let version of availableVersions" [value]="version.id">{{ version.versionNumber }} ({{ version.releaseDate | date:'dd.MM.yyyy' }})</option>
            </select>
            <div *ngIf="instanceVersionForm.get('versionId')?.invalid && instanceVersionForm.get('versionId')?.touched" class="invalid-feedback">
              Verze je povinná
            </div>
          </div>

          <div class="mb-3">
            <label for="installedByUserId" class="form-label required-field">Instaloval:</label>
            <select class="form-select" id="installedByUserId" formControlName="installedByUserId" [class.is-invalid]="instanceVersionForm.get('installedByUserId')?.invalid && instanceVersionForm.get('installedByUserId')?.touched">
              <option value="">-- Vyberte uživatele --</option>
              <option *ngFor="let user of availableUsers" [value]="user.id">{{ user.firstName }} {{ user.lastName }}</option>
            </select>
            <div *ngIf="instanceVersionForm.get('installedByUserId')?.invalid && instanceVersionForm.get('installedByUserId')?.touched" class="invalid-feedback">
              Uživatel je povinný
            </div>
          </div>

          <div class="mb-3">
            <label for="instanceVersionNotes" class="form-label">Poznámka:</label>
            <textarea class="form-control" id="instanceVersionNotes" formControlName="notes" rows="3"></textarea>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeInstanceVersionModal()">
              <i class="bi bi-x-circle me-1"></i>Zavřít
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="instanceVersionForm.invalid || savingInstanceVersion">
              <span *ngIf="savingInstanceVersion" class="spinner-border spinner-border-sm me-1"></span>
              <i *ngIf="!savingInstanceVersion" class="bi bi-save me-1"></i>{{ isEditInstanceVersionMode ? 'Aktualizovat' : 'Uložit' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro detail instance -->
<div class="modal fade" id="instanceDetailModal" tabindex="-1" aria-labelledby="instanceDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="instanceDetailModalLabel">Detail instance {{ selectedCustomer?.abbreviation }} - {{ selectedInstanceForVersion?.name }}</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="selectedInstanceForVersion">
          <div class="row mb-3">
            <div class="col-md-6">
              <h6>Název:</h6>
              <p>{{ selectedInstanceForVersion.name }}</p>
            </div>
            <div class="col-md-6">
              <h6>Status:</h6>
              <p>
                <span class="badge" [ngClass]="{
                  'bg-success': selectedInstanceForVersion.status === InstanceStatus.Active,
                  'bg-danger': selectedInstanceForVersion.status === InstanceStatus.Blocked,
                  'bg-warning': selectedInstanceForVersion.status === InstanceStatus.Trial,
                  'bg-secondary': selectedInstanceForVersion.status === InstanceStatus.Maintenance,
                  'bg-dark': selectedInstanceForVersion.status === InstanceStatus.Expired
                }">
                  {{ getInstanceStatusText(selectedInstanceForVersion.status) }}
                </span>
              </p>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <h6>URL serveru:</h6>
              <p>{{ selectedInstanceForVersion.serverUrl || '-' }}</p>
            </div>
            <div class="col-md-6">
              <h6>Datum poslední komunikace:</h6>
              <p>{{ selectedInstanceForVersion.lastConnectionDate ? (selectedInstanceForVersion.lastConnectionDate | date:'dd.MM.yyyy HH:mm') : '-' }}</p>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-12">
              <h6>API klíč:</h6>
              <div class="input-group">
                <input type="text" class="form-control" [value]="selectedInstanceForVersion.apiKey || '-'" readonly #apiKeyInput>
                <button class="btn input-group-button" type="button" (click)="copyApiKey(apiKeyInput)">
                  <i class="bi bi-clipboard"></i>
                </button>
                <button class="btn input-group-button" type="button" (click)="regenerateApiKey(selectedInstanceForVersion.id)" [disabled]="regeneratingApiKey">
                  <span *ngIf="regeneratingApiKey" class="spinner-border spinner-border-sm" role="status"></span>
                  <i *ngIf="!regeneratingApiKey" class="bi bi-arrow-repeat"></i>
                </button>
              </div>
              <div class="d-flex justify-content-between align-items-center mt-2">
                <small class="text-muted">Tento klíč je potřeba zadat do aplikace DIS pro komunikaci s DIS Admin.</small>
                <a [routerLink]="['/ip-whitelisting', selectedInstanceForVersion.id]" class="btn btn-sm btn-outline-primary" (click)="closeInstanceDetailModal()">
                  <i class="bi bi-shield-lock me-1"></i>IP Whitelisting
                </a>
              </div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <h6>Datum instalace:</h6>
              <p>{{ selectedInstanceForVersion.installationDate | date:'dd.MM.yyyy' }}</p>
            </div>
            <div class="col-md-6">
              <h6>Datum expirace:</h6>
              <p>{{ selectedInstanceForVersion.expirationDate ? (selectedInstanceForVersion.expirationDate | date:'dd.MM.yyyy') : '-' }}</p>
            </div>
          </div>

          <div class="mb-3">
            <h6>Poznámka:</h6>
            <p>{{ selectedInstanceForVersion.notes || '-' }}</p>
          </div>

          <!-- Sekce s informacemi o certifikátu -->
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">Certifikát</h5>
              <button type="button" class="btn btn-sm btn-success" (click)="generateCertificate(selectedInstanceForVersion.id)" [disabled]="generatingCertificate">
                <span *ngIf="generatingCertificate" class="spinner-border spinner-border-sm me-2" role="status"></span>
                <i *ngIf="!generatingCertificate" class="bi bi-shield-plus me-2"></i>Vygenerovat nový certifikát
              </button>
            </div>

            <div *ngIf="loadingCertificateInfo" class="d-flex justify-content-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </div>

            <div *ngIf="!loadingCertificateInfo && !certificateInfo" class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              Instance nemá přiřazený certifikát.
            </div>

            <div *ngIf="!loadingCertificateInfo && certificateInfo" class="card">
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>Thumbprint:</strong> {{ certificateInfo.thumbprint }}</p>
                    <p><strong>Subject:</strong> {{ certificateInfo.subject }}</p>
                    <p><strong>Issuer:</strong> {{ certificateInfo.issuer }}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>Platnost do:</strong> {{ certificateInfo.expirationDate | date:'dd.MM.yyyy HH:mm' }}</p>
                    <p><strong>Poslední validace:</strong> {{ certificateInfo.lastValidation | date:'dd.MM.yyyy HH:mm' || '-' }}</p>
                    <p>
                      <a [routerLink]="['/certificate-rotation/instance', selectedInstanceForVersion.id]" class="btn btn-sm btn-outline-primary" (click)="closeInstanceDetailModal()">
                        <i class="bi bi-gear me-1"></i>Nastavení automatické rotace
                      </a>
                    </p>
                    <p>
                      <strong>Status:</strong>
                      <span *ngIf="certificateInfo.isValid" class="badge bg-success">Platný</span>
                      <span *ngIf="!certificateInfo.isValid" class="badge bg-danger">Neplatný</span>
                      <span *ngIf="certificateInfo.daysToExpiration > 0" class="ms-2" [ngClass]="getCertificateExpirationClass(certificateInfo.daysToExpiration)">
                        ({{ certificateInfo.daysToExpiration }} dní do expirace)
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="mb-0">Historie verzí</h5>
              <button type="button" class="btn btn-sm btn-primary" (click)="openAddInstanceVersionModal(selectedInstanceForVersion)">
                <i class="bi bi-plus-circle-fill me-2"></i>Přidat verzi
              </button>
            </div>

            <div *ngIf="loadingInstanceVersions[selectedInstanceForVersion.id]" class="d-flex justify-content-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </div>

            <div *ngIf="!loadingInstanceVersions[selectedInstanceForVersion.id] && (!instanceVersions[selectedInstanceForVersion.id] || instanceVersions[selectedInstanceForVersion.id].length === 0)" class="alert alert-info">
              Žádné verze nebyly nalezeny.
            </div>

            <div *ngIf="!loadingInstanceVersions[selectedInstanceForVersion.id] && instanceVersions[selectedInstanceForVersion.id] && instanceVersions[selectedInstanceForVersion.id].length > 0" class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="dark-header table-header-override">
                  <tr class="dark-header-row">
                    <th>Verze</th>
                    <th class="text-nowrap">Datum instalace</th>
                    <th>Instaloval</th>
                    <th>Poznámka</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let version of instanceVersions[selectedInstanceForVersion.id]">
                    <td>{{ version.versionNumber }}</td>
                    <td>{{ version.installedAt | date:'dd.MM.yyyy HH:mm' }}</td>
                    <td>{{ version.installedByUserName }}</td>
                    <td>{{ version.notes || '-' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-end mt-3">
          <button type="button" class="btn btn-info me-2" [disabled]="!selectedInstanceForVersion" [routerLink]="['/instance-metrics', selectedInstanceForVersion?.id]" (click)="closeInstanceDetailModal()">
            <i class="bi bi-graph-up me-1"></i>Metriky
          </button>
          <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal" (click)="closeInstanceDetailModal()">
            <i class="bi bi-x-circle me-1"></i>Zavřít
          </button>
          <button type="button" class="btn btn-primary" [disabled]="!selectedInstanceForVersion" (click)="editInstance(selectedInstanceForVersion!)">
            <i class="bi bi-pencil-fill me-1"></i>Upravit
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pro zobrazení informací o vygenerovaném certifikátu -->
<div class="modal fade" id="certificateGeneratedModal" tabindex="-1" aria-labelledby="certificateGeneratedModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title" id="certificateGeneratedModalLabel">Certifikát vygenerován</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-success">
          <i class="bi bi-check-circle-fill me-2"></i>
          Certifikát byl úspěšně vygenerován.
        </div>

        <div *ngIf="generatedCertificate">
          <h6 class="mt-3">Informace o certifikátu:</h6>
          <ul class="list-group">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Thumbprint:</span>
              <span class="text-muted">{{ generatedCertificate.thumbprint }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Platnost do:</span>
              <span class="text-muted">{{ generatedCertificate.expirationDate | date:'dd.MM.yyyy HH:mm' }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Heslo k certifikátu:</span>
              <span class="text-monospace font-weight-bold">{{ generatedCertificate.password }}</span>
            </li>
          </ul>

          <div class="alert alert-warning mt-3">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong>Důležité:</strong> Toto heslo si poznamenejte. Budete ho potřebovat při instalaci certifikátu.
            Z bezpečnostních důvodů není heslo nikde uloženo a nebude možné ho později zobrazit.
          </div>

          <div class="mt-3">
            <button class="btn btn-primary" (click)="downloadCertificate()">
              <i class="bi bi-download me-2"></i>Stáhnout certifikát
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zavřít</button>
      </div>
    </div>
  </div>
</div>
