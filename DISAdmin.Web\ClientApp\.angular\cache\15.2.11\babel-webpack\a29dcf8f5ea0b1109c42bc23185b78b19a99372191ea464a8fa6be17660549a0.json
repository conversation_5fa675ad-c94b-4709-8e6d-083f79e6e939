{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SecurityService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.apiUrl}/dashboard`;\n  }\n  getSecurityDashboard() {\n    return this.http.get(`${this.apiUrl}/security`);\n  }\n  getAlerts(includeResolved = false) {\n    return this.http.get(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);\n  }\n  resolveAlert(alertId, resolution) {\n    const request = {\n      resolution\n    };\n    return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);\n  }\n  static {\n    this.ɵfac = function SecurityService_Factory(t) {\n      return new (t || SecurityService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SecurityService,\n      factory: SecurityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAGA,SAASA,WAAW,QAAQ,gCAAgC;;;AAa5D,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,SAAI,GAAJA,IAAI;IAFhB,WAAM,GAAG,GAAGH,WAAW,CAACI,MAAM,YAAY;EAEV;EAExCC,oBAAoB;IAClB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAA4B,GAAG,IAAI,CAACF,MAAM,WAAW,CAAC;EAC5E;EAEAG,SAAS,CAACC,kBAA2B,KAAK;IACxC,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAkB,GAAG,IAAI,CAACF,MAAM,2BAA2BI,eAAe,EAAE,CAAC;EACnG;EAEAC,YAAY,CAACC,OAAe,EAAEC,UAAkB;IAC9C,MAAMC,OAAO,GAAwB;MAAED;IAAU,CAAE;IACnD,OAAO,IAAI,CAACR,IAAI,CAACU,IAAI,CAAC,GAAG,IAAI,CAACT,MAAM,WAAWM,OAAO,UAAU,EAAEE,OAAO,CAAC;EAC5E;;;uBAhBWX,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAa,SAAfb,eAAe;MAAAc,YAFd;IAAM;EAAA", "names": ["environment", "SecurityService", "constructor", "http", "apiUrl", "getSecurityDashboard", "get", "get<PERSON><PERSON><PERSON>", "includeResolved", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "resolution", "request", "post", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\services\\security.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport {\n  SecurityDashboardResponse,\n  AlertResponse,\n  ResolveAlertRequest,\n  SecurityEventFilterResponse,\n  CreateSecurityEventFilterRequest,\n  SecurityEventFilterApiResponse\n} from '../models/security.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SecurityService {\n  private apiUrl = `${environment.apiUrl}/dashboard`;\n\n  constructor(private http: HttpClient) { }\n\n  getSecurityDashboard(): Observable<SecurityDashboardResponse> {\n    return this.http.get<SecurityDashboardResponse>(`${this.apiUrl}/security`);\n  }\n\n  getAlerts(includeResolved: boolean = false): Observable<AlertResponse[]> {\n    return this.http.get<AlertResponse[]>(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);\n  }\n\n  resolveAlert(alertId: number, resolution: string): Observable<any> {\n    const request: ResolveAlertRequest = { resolution };\n    return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}