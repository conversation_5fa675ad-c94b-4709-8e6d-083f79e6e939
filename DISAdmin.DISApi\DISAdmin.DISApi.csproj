<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Certificate" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.5" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="9.0.5" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DISAdmin.Core\DISAdmin.Core.csproj" />
    <ProjectReference Include="..\DISAdmin.Migrations\DISAdmin.Migrations.csproj" />
    <ProjectReference Include="..\DISAdmin.Api\DISAdmin.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Certificates\" />
  </ItemGroup>

</Project>
