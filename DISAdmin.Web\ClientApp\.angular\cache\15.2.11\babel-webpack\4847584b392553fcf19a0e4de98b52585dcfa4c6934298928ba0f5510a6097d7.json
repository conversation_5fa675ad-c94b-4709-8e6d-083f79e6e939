{"ast": null, "code": "import * as __NgCli_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nexport function getBaseUrl() {\n  return document.getElementsByTagName('base')[0].href;\n}\nconst providers = [{\n  provide: 'BASE_URL',\n  useFactory: getBaseUrl,\n  deps: []\n}];\nif (environment.production) {\n  enableProdMode();\n}\n__NgCli_bootstrap_1.platformBrowser(providers).bootstrapModule(AppModule).then(() => {\n  // Inicializace Bootstrap dropdown po načtení aplikace\n  setTimeout(() => {\n    console.log('Initializing Bootstrap dropdowns...');\n    if (typeof window.bootstrap !== 'undefined') {\n      const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\n      console.log('Found dropdown elements:', dropdownElements.length);\n      dropdownElements.forEach(element => {\n        try {\n          if (!window.bootstrap.Dropdown.getInstance(element)) {\n            new window.bootstrap.Dropdown(element);\n            console.log('Initialized dropdown for element:', element);\n          }\n        } catch (error) {\n          console.warn('Error initializing dropdown:', error);\n        }\n      });\n    } else {\n      console.warn('Bootstrap is not available in main.ts');\n    }\n  }, 2000);\n}).catch(err => console.log(err));\n// Odstraněn MutationObserver, který může způsobovat problémy\n// const observer = new MutationObserver(() => {\n//   const overlays = document.querySelectorAll('.modal-backdrop');\n//   console.log('Počet overlayů:', overlays.length);\n//   overlays.forEach((overlay, index) => {\n//     console.log(`Overlay ${index + 1}:`, overlay.outerHTML);\n//   });\n// });\n// observer.observe(document.body, { childList: true, subtree: true });", "map": {"version": 3, "mappings": ";AAAA,SAASA,cAAc,QAAQ,eAAe;AAG9C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,OAAM,SAAUC,UAAU;EACxB,OAAOC,QAAQ,CAACC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI;AACtD;AAEA,MAAMC,SAAS,GAAG,CAChB;EAAEC,OAAO,EAAE,UAAU;EAAEC,UAAU,EAAEN,UAAU;EAAEO,IAAI,EAAE;AAAE,CAAE,CAC1D;AAED,IAAIR,WAAW,CAACS,UAAU,EAAE;EAC1BX,cAAc,EAAE;;AAGlBY,oCAAuBL,SAAS,CAAC,CAACM,eAAe,CAACZ,SAAS,CAAC,CACzDa,IAAI,CAAC,MAAK;EACT;EACAC,UAAU,CAAC,MAAK;IACdC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,OAAQC,MAAc,CAACC,SAAS,KAAK,WAAW,EAAE;MACpD,MAAMC,gBAAgB,GAAGhB,QAAQ,CAACiB,gBAAgB,CAAC,6BAA6B,CAAC;MACjFL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEG,gBAAgB,CAACE,MAAM,CAAC;MAChEF,gBAAgB,CAACG,OAAO,CAAEC,OAAO,IAAI;QACnC,IAAI;UACF,IAAI,CAAEN,MAAc,CAACC,SAAS,CAACM,QAAQ,CAACC,WAAW,CAACF,OAAO,CAAC,EAAE;YAC5D,IAAKN,MAAc,CAACC,SAAS,CAACM,QAAQ,CAACD,OAAO,CAAC;YAC/CR,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEO,OAAO,CAAC;;SAE5D,CAAC,OAAOG,KAAK,EAAE;UACdX,OAAO,CAACY,IAAI,CAAC,8BAA8B,EAAED,KAAK,CAAC;;MAEvD,CAAC,CAAC;KACH,MAAM;MACLX,OAAO,CAACY,IAAI,CAAC,uCAAuC,CAAC;;EAEzD,CAAC,EAAE,IAAI,CAAC;AACV,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAY,IAAKd,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC,CAAC;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "names": ["enableProdMode", "AppModule", "environment", "getBaseUrl", "document", "getElementsByTagName", "href", "providers", "provide", "useFactory", "deps", "production", "__Ng<PERSON>li_bootstrap_1", "bootstrapModule", "then", "setTimeout", "console", "log", "window", "bootstrap", "dropdownElements", "querySelectorAll", "length", "for<PERSON>ach", "element", "Dropdown", "getInstance", "error", "warn", "catch", "err"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\main.ts"], "sourcesContent": ["import { enableProdMode } from '@angular/core';\r\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\nimport { AppModule } from './app/app.module';\r\nimport { environment } from './environments/environment';\r\n\r\nexport function getBaseUrl() {\r\n  return document.getElementsByTagName('base')[0].href;\r\n}\r\n\r\nconst providers = [\r\n  { provide: 'BASE_URL', useFactory: getBaseUrl, deps: [] }\r\n];\r\n\r\nif (environment.production) {\r\n  enableProdMode();\r\n}\r\n\r\nplatformBrowserDynamic(providers).bootstrapModule(AppModule)\r\n  .then(() => {\r\n    // Inicializace Bootstrap dropdown po načtení aplikace\r\n    setTimeout(() => {\r\n      console.log('Initializing Bootstrap dropdowns...');\r\n      if (typeof (window as any).bootstrap !== 'undefined') {\r\n        const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\r\n        console.log('Found dropdown elements:', dropdownElements.length);\r\n        dropdownElements.forEach((element) => {\r\n          try {\r\n            if (!(window as any).bootstrap.Dropdown.getInstance(element)) {\r\n              new (window as any).bootstrap.Dropdown(element);\r\n              console.log('Initialized dropdown for element:', element);\r\n            }\r\n          } catch (error) {\r\n            console.warn('Error initializing dropdown:', error);\r\n          }\r\n        });\r\n      } else {\r\n        console.warn('Bootstrap is not available in main.ts');\r\n      }\r\n    }, 2000);\r\n  })\r\n  .catch((err: unknown) => console.log(err));\r\n\r\n// Odstraněn MutationObserver, který může způsobovat problémy\r\n// const observer = new MutationObserver(() => {\r\n//   const overlays = document.querySelectorAll('.modal-backdrop');\r\n//   console.log('Počet overlayů:', overlays.length);\r\n//   overlays.forEach((overlay, index) => {\r\n//     console.log(`Overlay ${index + 1}:`, overlay.outerHTML);\r\n//   });\r\n// });\r\n// observer.observe(document.body, { childList: true, subtree: true });\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}