{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-toastr\";\nexport class ClipboardService {\n  constructor(toastr) {\n    this.toastr = toastr;\n  }\n  /**\r\n   * Zkopíruje text do schránky s automatickým fallback a toast notifikací\r\n   * @param text Text k zkopírování\r\n   * @param successMessage Zpráva při úspěchu (volitelná)\r\n   * @param errorMessage Zpráva při chybě (volitelná)\r\n   * @returns Promise<boolean> - true při <PERSON>, false při chybě\r\n   */\n  copyToClipboard(text, successMessage = 'Text byl zkopírován do schránky', errorMessage = 'Nepodařilo se zkopírovat text do schr<PERSON><PERSON>') {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!text || text.trim() === '') {\n        _this.toastr.error('Není co kopírovat', 'Chyba');\n        return false;\n      }\n      try {\n        // Pokusíme se použít moderní Clipboard API\n        if (navigator.clipboard && window.isSecureContext) {\n          yield navigator.clipboard.writeText(text);\n          _this.toastr.success(successMessage, 'Úspěch');\n          return true;\n        } else {\n          // Fallback pro starší prohlížeče nebo nezabezpečené kontexty\n          return _this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n        }\n      } catch (err) {\n        console.error('Clipboard API selhalo:', err);\n        // Pokus o fallback\n        return _this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n      }\n    })();\n  }\n  /**\r\n   * Fallback metoda pro kopírování do schránky pomocí execCommand\r\n   * @param text Text k zkopírování\r\n   * @param successMessage Zpráva při úspěchu\r\n   * @param errorMessage Zpráva při chybě\r\n   * @returns boolean - true při úspěchu, false při chybě\r\n   */\n  fallbackCopyToClipboard(text, successMessage, errorMessage) {\n    try {\n      // Vytvoříme dočasný textarea element\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      // Nastavíme styly pro skrytí elementu\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      textArea.style.width = '2em';\n      textArea.style.height = '2em';\n      textArea.style.padding = '0';\n      textArea.style.border = 'none';\n      textArea.style.outline = 'none';\n      textArea.style.boxShadow = 'none';\n      textArea.style.background = 'transparent';\n      document.body.appendChild(textArea);\n      // Označíme text a zkopírujeme\n      textArea.focus();\n      textArea.select();\n      textArea.setSelectionRange(0, text.length); // Pro mobilní zařízení\n      const successful = document.execCommand('copy');\n      document.body.removeChild(textArea);\n      if (successful) {\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        throw new Error('execCommand returned false');\n      }\n    } catch (err) {\n      console.error('Fallback kopírování selhalo:', err);\n      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n      return false;\n    }\n  }\n  /**\r\n   * Zkopíruje text z input elementu do schránky\r\n   * @param inputElement HTML input element\r\n   * @param successMessage Zpráva při úspěchu (volitelná)\r\n   * @param errorMessage Zpráva při chybě (volitelná)\r\n   * @returns Promise<boolean> - true při úspěchu, false při chybě\r\n   */\n  copyFromInput(inputElement, successMessage = 'Text byl zkopírován do schránky', errorMessage = 'Nepodařilo se zkopírovat text do schránky') {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const text = inputElement.value;\n      if (!text || text.trim() === '') {\n        _this2.toastr.error('Input element neobsahuje žádný text', 'Chyba');\n        return false;\n      }\n      try {\n        // Pokusíme se použít moderní Clipboard API\n        if (navigator.clipboard && window.isSecureContext) {\n          yield navigator.clipboard.writeText(text);\n          _this2.toastr.success(successMessage, 'Úspěch');\n          return true;\n        } else {\n          // Fallback s použitím input elementu\n          return _this2.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n        }\n      } catch (err) {\n        console.error('Clipboard API selhalo:', err);\n        // Pokus o fallback\n        return _this2.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n      }\n    })();\n  }\n  /**\r\n   * Fallback metoda pro kopírování z input elementu\r\n   * @param inputElement HTML input element\r\n   * @param successMessage Zpráva při úspěchu\r\n   * @param errorMessage Zpráva při chybě\r\n   * @returns boolean - true při úspěchu, false při chybě\r\n   */\n  fallbackCopyFromInput(inputElement, successMessage, errorMessage) {\n    try {\n      // Označíme text v input elementu\n      inputElement.focus();\n      inputElement.select();\n      inputElement.setSelectionRange(0, inputElement.value.length); // Pro mobilní zařízení\n      const successful = document.execCommand('copy');\n      if (successful) {\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        throw new Error('execCommand returned false');\n      }\n    } catch (err) {\n      console.error('Fallback kopírování z input selhalo:', err);\n      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n      return false;\n    }\n  }\n  /**\r\n   * Kontrola, zda je Clipboard API dostupné\r\n   * @returns boolean - true pokud je Clipboard API dostupné\r\n   */\n  isClipboardApiAvailable() {\n    return !!(navigator.clipboard && window.isSecureContext);\n  }\n  static {\n    this.ɵfac = function ClipboardService_Factory(t) {\n      return new (t || ClipboardService)(i0.ɵɵinject(i1.ToastrService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ClipboardService,\n      factory: ClipboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;AAMA,OAAM,MAAOA,gBAAgB;EAE3BC,YAAoBC,MAAqB;IAArB,WAAM,GAANA,MAAM;EAAmB;EAE7C;;;;;;;EAOMC,eAAe,CACnBC,IAAY,EACZC,iBAAyB,iCAAiC,EAC1DC,eAAuB,2CAA2C;IAAA;IAAA;MAGlE,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACG,IAAI,EAAE,KAAK,EAAE,EAAE;QAC/B,KAAI,CAACL,MAAM,CAACM,KAAK,CAAC,mBAAmB,EAAE,OAAO,CAAC;QAC/C,OAAO,KAAK;;MAGd,IAAI;QACF;QACA,IAAIC,SAAS,CAACC,SAAS,IAAIC,MAAM,CAACC,eAAe,EAAE;UACjD,MAAMH,SAAS,CAACC,SAAS,CAACG,SAAS,CAACT,IAAI,CAAC;UACzC,KAAI,CAACF,MAAM,CAACY,OAAO,CAACT,cAAc,EAAE,QAAQ,CAAC;UAC7C,OAAO,IAAI;SACZ,MAAM;UACL;UACA,OAAO,KAAI,CAACU,uBAAuB,CAACX,IAAI,EAAEC,cAAc,EAAEC,YAAY,CAAC;;OAE1E,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEQ,GAAG,CAAC;QAC5C;QACA,OAAO,KAAI,CAACD,uBAAuB,CAACX,IAAI,EAAEC,cAAc,EAAEC,YAAY,CAAC;;IACxE;EACH;EAEA;;;;;;;EAOQS,uBAAuB,CAC7BX,IAAY,EACZC,cAAsB,EACtBC,YAAoB;IAEpB,IAAI;MACF;MACA,MAAMY,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAACG,KAAK,GAAGjB,IAAI;MAErB;MACAc,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,OAAO;MACjCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,WAAW;MACjCN,QAAQ,CAACI,KAAK,CAACG,GAAG,GAAG,WAAW;MAChCP,QAAQ,CAACI,KAAK,CAACI,KAAK,GAAG,KAAK;MAC5BR,QAAQ,CAACI,KAAK,CAACK,MAAM,GAAG,KAAK;MAC7BT,QAAQ,CAACI,KAAK,CAACM,OAAO,GAAG,GAAG;MAC5BV,QAAQ,CAACI,KAAK,CAACO,MAAM,GAAG,MAAM;MAC9BX,QAAQ,CAACI,KAAK,CAACQ,OAAO,GAAG,MAAM;MAC/BZ,QAAQ,CAACI,KAAK,CAACS,SAAS,GAAG,MAAM;MACjCb,QAAQ,CAACI,KAAK,CAACU,UAAU,GAAG,aAAa;MAEzCb,QAAQ,CAACc,IAAI,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEnC;MACAA,QAAQ,CAACiB,KAAK,EAAE;MAChBjB,QAAQ,CAACkB,MAAM,EAAE;MACjBlB,QAAQ,CAACmB,iBAAiB,CAAC,CAAC,EAAEjC,IAAI,CAACkC,MAAM,CAAC,CAAC,CAAC;MAE5C,MAAMC,UAAU,GAAGpB,QAAQ,CAACqB,WAAW,CAAC,MAAM,CAAC;MAC/CrB,QAAQ,CAACc,IAAI,CAACQ,WAAW,CAACvB,QAAQ,CAAC;MAEnC,IAAIqB,UAAU,EAAE;QACd,IAAI,CAACrC,MAAM,CAACY,OAAO,CAACT,cAAc,EAAE,QAAQ,CAAC;QAC7C,OAAO,IAAI;OACZ,MAAM;QACL,MAAM,IAAIqC,KAAK,CAAC,4BAA4B,CAAC;;KAEhD,CAAC,OAAO1B,GAAG,EAAE;MACZC,OAAO,CAACT,KAAK,CAAC,8BAA8B,EAAEQ,GAAG,CAAC;MAClD,IAAI,CAACd,MAAM,CAACM,KAAK,CAAC,GAAGF,YAAY,iCAAiC,EAAE,OAAO,CAAC;MAC5E,OAAO,KAAK;;EAEhB;EAEA;;;;;;;EAOMqC,aAAa,CACjBC,YAA8B,EAC9BvC,iBAAyB,iCAAiC,EAC1DC,eAAuB,2CAA2C;IAAA;IAAA;MAGlE,MAAMF,IAAI,GAAGwC,YAAY,CAACvB,KAAK;MAE/B,IAAI,CAACjB,IAAI,IAAIA,IAAI,CAACG,IAAI,EAAE,KAAK,EAAE,EAAE;QAC/B,MAAI,CAACL,MAAM,CAACM,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;QACjE,OAAO,KAAK;;MAGd,IAAI;QACF;QACA,IAAIC,SAAS,CAACC,SAAS,IAAIC,MAAM,CAACC,eAAe,EAAE;UACjD,MAAMH,SAAS,CAACC,SAAS,CAACG,SAAS,CAACT,IAAI,CAAC;UACzC,MAAI,CAACF,MAAM,CAACY,OAAO,CAACT,cAAc,EAAE,QAAQ,CAAC;UAC7C,OAAO,IAAI;SACZ,MAAM;UACL;UACA,OAAO,MAAI,CAACwC,qBAAqB,CAACD,YAAY,EAAEvC,cAAc,EAAEC,YAAY,CAAC;;OAEhF,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEQ,GAAG,CAAC;QAC5C;QACA,OAAO,MAAI,CAAC6B,qBAAqB,CAACD,YAAY,EAAEvC,cAAc,EAAEC,YAAY,CAAC;;IAC9E;EACH;EAEA;;;;;;;EAOQuC,qBAAqB,CAC3BD,YAA8B,EAC9BvC,cAAsB,EACtBC,YAAoB;IAEpB,IAAI;MACF;MACAsC,YAAY,CAACT,KAAK,EAAE;MACpBS,YAAY,CAACR,MAAM,EAAE;MACrBQ,YAAY,CAACP,iBAAiB,CAAC,CAAC,EAAEO,YAAY,CAACvB,KAAK,CAACiB,MAAM,CAAC,CAAC,CAAC;MAE9D,MAAMC,UAAU,GAAGpB,QAAQ,CAACqB,WAAW,CAAC,MAAM,CAAC;MAE/C,IAAID,UAAU,EAAE;QACd,IAAI,CAACrC,MAAM,CAACY,OAAO,CAACT,cAAc,EAAE,QAAQ,CAAC;QAC7C,OAAO,IAAI;OACZ,MAAM;QACL,MAAM,IAAIqC,KAAK,CAAC,4BAA4B,CAAC;;KAEhD,CAAC,OAAO1B,GAAG,EAAE;MACZC,OAAO,CAACT,KAAK,CAAC,sCAAsC,EAAEQ,GAAG,CAAC;MAC1D,IAAI,CAACd,MAAM,CAACM,KAAK,CAAC,GAAGF,YAAY,iCAAiC,EAAE,OAAO,CAAC;MAC5E,OAAO,KAAK;;EAEhB;EAEA;;;;EAIAwC,uBAAuB;IACrB,OAAO,CAAC,EAAErC,SAAS,CAACC,SAAS,IAAIC,MAAM,CAACC,eAAe,CAAC;EAC1D;;;uBAvKWZ,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAA+C,SAAhB/C,gBAAgB;MAAAgD,YAFf;IAAM;EAAA", "names": ["ClipboardService", "constructor", "toastr", "copyToClipboard", "text", "successMessage", "errorMessage", "trim", "error", "navigator", "clipboard", "window", "isSecureContext", "writeText", "success", "fallbackCopyToClipboard", "err", "console", "textArea", "document", "createElement", "value", "style", "position", "left", "top", "width", "height", "padding", "border", "outline", "boxShadow", "background", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "setSelectionRange", "length", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "Error", "copyFromInput", "inputElement", "fallbackCopyFromInput", "isClipboardApiAvailable", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\services\\clipboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClipboardService {\n\n  constructor(private toastr: ToastrService) { }\n\n  /**\n   * Zkopíruje text do schr<PERSON>ky s automatickým fallback a toast notifikací\n   * @param text Text k zkopírování\n   * @param successMessage Zpráva při <PERSON> (volitelná)\n   * @param errorMessage Zpráva při chybě (volitelná)\n   * @returns Promise<boolean> - true při <PERSON>, false při chybě\n   */\n  async copyToClipboard(\n    text: string, \n    successMessage: string = 'Text byl zkopírován do schr<PERSON>ky',\n    errorMessage: string = 'Nepodařilo se zkopírovat text do schránky'\n  ): Promise<boolean> {\n    \n    if (!text || text.trim() === '') {\n      this.toastr.error('Není co kopírovat', 'Chy<PERSON>');\n      return false;\n    }\n\n    try {\n      // Pokusíme se použít moderní Clipboard API\n      if (navigator.clipboard && window.isSecureContext) {\n        await navigator.clipboard.writeText(text);\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        // Fallback pro starší prohlížeče nebo nezabezpečené kontexty\n        return this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n      }\n    } catch (err) {\n      console.error('Clipboard API selhalo:', err);\n      // Pokus o fallback\n      return this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n    }\n  }\n\n  /**\n   * Fallback metoda pro kopírování do schránky pomocí execCommand\n   * @param text Text k zkopírování\n   * @param successMessage Zpráva při úspěchu\n   * @param errorMessage Zpráva při chybě\n   * @returns boolean - true při úspěchu, false při chybě\n   */\n  private fallbackCopyToClipboard(\n    text: string, \n    successMessage: string, \n    errorMessage: string\n  ): boolean {\n    try {\n      // Vytvoříme dočasný textarea element\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      \n      // Nastavíme styly pro skrytí elementu\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      textArea.style.width = '2em';\n      textArea.style.height = '2em';\n      textArea.style.padding = '0';\n      textArea.style.border = 'none';\n      textArea.style.outline = 'none';\n      textArea.style.boxShadow = 'none';\n      textArea.style.background = 'transparent';\n      \n      document.body.appendChild(textArea);\n      \n      // Označíme text a zkopírujeme\n      textArea.focus();\n      textArea.select();\n      textArea.setSelectionRange(0, text.length); // Pro mobilní zařízení\n      \n      const successful = document.execCommand('copy');\n      document.body.removeChild(textArea);\n      \n      if (successful) {\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        throw new Error('execCommand returned false');\n      }\n    } catch (err) {\n      console.error('Fallback kopírování selhalo:', err);\n      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n      return false;\n    }\n  }\n\n  /**\n   * Zkopíruje text z input elementu do schránky\n   * @param inputElement HTML input element\n   * @param successMessage Zpráva při úspěchu (volitelná)\n   * @param errorMessage Zpráva při chybě (volitelná)\n   * @returns Promise<boolean> - true při úspěchu, false při chybě\n   */\n  async copyFromInput(\n    inputElement: HTMLInputElement,\n    successMessage: string = 'Text byl zkopírován do schránky',\n    errorMessage: string = 'Nepodařilo se zkopírovat text do schránky'\n  ): Promise<boolean> {\n    \n    const text = inputElement.value;\n    \n    if (!text || text.trim() === '') {\n      this.toastr.error('Input element neobsahuje žádný text', 'Chyba');\n      return false;\n    }\n\n    try {\n      // Pokusíme se použít moderní Clipboard API\n      if (navigator.clipboard && window.isSecureContext) {\n        await navigator.clipboard.writeText(text);\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        // Fallback s použitím input elementu\n        return this.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n      }\n    } catch (err) {\n      console.error('Clipboard API selhalo:', err);\n      // Pokus o fallback\n      return this.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n    }\n  }\n\n  /**\n   * Fallback metoda pro kopírování z input elementu\n   * @param inputElement HTML input element\n   * @param successMessage Zpráva při úspěchu\n   * @param errorMessage Zpráva při chybě\n   * @returns boolean - true při úspěchu, false při chybě\n   */\n  private fallbackCopyFromInput(\n    inputElement: HTMLInputElement,\n    successMessage: string,\n    errorMessage: string\n  ): boolean {\n    try {\n      // Označíme text v input elementu\n      inputElement.focus();\n      inputElement.select();\n      inputElement.setSelectionRange(0, inputElement.value.length); // Pro mobilní zařízení\n      \n      const successful = document.execCommand('copy');\n      \n      if (successful) {\n        this.toastr.success(successMessage, 'Úspěch');\n        return true;\n      } else {\n        throw new Error('execCommand returned false');\n      }\n    } catch (err) {\n      console.error('Fallback kopírování z input selhalo:', err);\n      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n      return false;\n    }\n  }\n\n  /**\n   * Kontrola, zda je Clipboard API dostupné\n   * @returns boolean - true pokud je Clipboard API dostupné\n   */\n  isClipboardApiAvailable(): boolean {\n    return !!(navigator.clipboard && window.isSecureContext);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}