using Serilog;
using Serilog.Events;

namespace DISAdmin.DISApi.Extensions;

/// <summary>
/// Rozšíření pro konfiguraci Console loggingu pomocí Serilog
/// </summary>
public static class ConsoleLoggingExtensions
{
    /// <summary>
    /// Přidá Console logging do souboru pomocí Serilog
    /// </summary>
    public static IHostBuilder AddConsoleFileLogging(this IHostBuilder hostBuilder)
    {
        return hostBuilder.UseSerilog((context, services, configuration) =>
        {
            // Základní konfigurace Serilog
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "DISAdmin.DISApi")
                .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName);

            // Přidání Console file sink pouze v produkčním prostředí
            if (context.HostingEnvironment.IsProduction())
            {
                var consoleLoggingConfig = context.Configuration.GetSection("ConsoleLogging");
                var enabled = consoleLoggingConfig.GetValue<bool>("Enabled", true);

                if (enabled)
                {
                    var logDirectory = consoleLoggingConfig.GetValue<string>("LogDirectory", "Logs");
                    var fileNamePrefix = consoleLoggingConfig.GetValue<string>("FileNamePrefix", "console");
                    var includeTimestamp = consoleLoggingConfig.GetValue<bool>("IncludeTimestamp", true);

                    // Vytvoření úplné cesty k adresáři
                    var fullLogDirectory = Path.IsPathRooted(logDirectory)
                        ? logDirectory
                        : Path.Combine(AppDomain.CurrentDomain.BaseDirectory, logDirectory);

                    // Vytvoření názvu souboru
                    var logFileName = includeTimestamp
                        ? $"{fileNamePrefix}-.log"
                        : $"{fileNamePrefix}.log";

                    var logFilePath = Path.Combine(fullLogDirectory, logFileName);

                    // Přidání file sink pro Console výstup
                    configuration.WriteTo.File(
                        path: logFilePath,
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [Console] {Message:lj}{NewLine}{Exception}",
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 30,
                        fileSizeLimitBytes: 10 * 1024 * 1024, // 10 MB
                        levelSwitch: new Serilog.Core.LoggingLevelSwitch(LogEventLevel.Information),
                        shared: true
                    );
                }
            }

            // Přidání standardního Console výstupu pro development
            if (context.HostingEnvironment.IsDevelopment())
            {
                configuration.WriteTo.Console(
                    outputTemplate: "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
                );
            }
        });
    }

    /// <summary>
    /// Vytvoří wrapper pro Console.WriteLine, který zapisuje do Serilog
    /// </summary>
    public static void ConfigureSerilogConsoleWrapper()
    {
        // Vytvoření custom TextWriter, který přesměrovává do Serilog
        var serilogTextWriter = new SerilogTextWriter();
        Console.SetOut(serilogTextWriter);
        Console.SetError(serilogTextWriter);
    }
}

/// <summary>
/// Custom TextWriter, který přesměrovává Console výstup do Serilog
/// </summary>
public class SerilogTextWriter : TextWriter
{
    private readonly Serilog.ILogger _logger;

    public SerilogTextWriter()
    {
        _logger = Log.ForContext("SourceContext", "Console");
    }

    public override System.Text.Encoding Encoding => System.Text.Encoding.UTF8;

    public override void WriteLine(string? value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            _logger.Information("{Message}", value);
        }
    }

    public override void Write(string? value)
    {
        if (!string.IsNullOrEmpty(value))
        {
            _logger.Information("{Message}", value);
        }
    }

    public override void WriteLine(string format, params object?[] args)
    {
        _logger.Information(format, args);
    }

    public override void Write(char value)
    {
        _logger.Information("{Character}", value);
    }

    public override void Write(char[] buffer, int index, int count)
    {
        if (buffer != null && count > 0)
        {
            var text = new string(buffer, index, count);
            _logger.Information("{Message}", text);
        }
    }
}

/// <summary>
/// Jednoduchý wrapper pro Console.WriteLine s file loggingem
/// </summary>
public static class ConsoleFileLogger
{
    private static StreamWriter? _fileWriter;
    private static readonly object _lock = new object();

    /// <summary>
    /// Inicializace Console file loggingu
    /// </summary>
    public static void Initialize(string logDirectory = "Logs", string filePrefix = "console")
    {
        try
        {
            lock (_lock)
            {
                if (_fileWriter != null)
                    return; // Již inicializováno

                // Vytvoření adresáře
                var fullLogDirectory = Path.IsPathRooted(logDirectory)
                    ? logDirectory
                    : Path.Combine(AppDomain.CurrentDomain.BaseDirectory, logDirectory);

                if (!Directory.Exists(fullLogDirectory))
                {
                    Directory.CreateDirectory(fullLogDirectory);
                }

                // Vytvoření souboru
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd");
                var logFileName = $"{filePrefix}-{timestamp}.log";
                var logFilePath = Path.Combine(fullLogDirectory, logFileName);

                _fileWriter = new StreamWriter(logFilePath, append: true, System.Text.Encoding.UTF8)
                {
                    AutoFlush = true
                };

                // Zápis úvodní zprávy
                _fileWriter.WriteLine($"=== Console file logging initialized at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Chyba při inicializaci Console file loggingu: {ex.Message}");
        }
    }

    /// <summary>
    /// Zápis zprávy do souboru i na Console
    /// </summary>
    public static void WriteLine(string message)
    {
        var timestampedMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} {message}";
        
        // Zápis na standardní Console
        System.Console.WriteLine(timestampedMessage);

        // Zápis do souboru
        try
        {
            lock (_lock)
            {
                _fileWriter?.WriteLine(timestampedMessage);
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Chyba při zápisu do Console log souboru: {ex.Message}");
        }
    }

    /// <summary>
    /// Zápis zprávy s formátováním
    /// </summary>
    public static void WriteLine(string format, params object[] args)
    {
        WriteLine(string.Format(format, args));
    }

    /// <summary>
    /// Uzavření file writeru
    /// </summary>
    public static void Close()
    {
        lock (_lock)
        {
            _fileWriter?.Close();
            _fileWriter?.Dispose();
            _fileWriter = null;
        }
    }
}
