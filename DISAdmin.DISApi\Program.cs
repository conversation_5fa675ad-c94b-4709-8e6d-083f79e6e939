using DISAdmin.Core;
using DISAdmin.Core.Services;
using DISAdmin.Core.Logging;
using DISAdmin.DISApi.Middleware;
using DISAdmin.DISApi.Models;
using DISAdmin.Migrations;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography.X509Certificates;
using Microsoft.AspNetCore.Authentication.Certificate;
using Serilog;

// Vytvoření builder s file logging podporou a podporou Windows Service
var builder = WebApplication.CreateBuilder(args);

// Konfigurace Console výstupu pro produkční prostředí
if (builder.Environment.IsProduction())
{
    ConfigureConsoleLogging(builder.Environment, builder.Configuration);
}

// Konfigurace database loggingu
var databaseLoggingConfig = builder.Configuration.GetSection("Logging:Database");
var dbLogLevelString = databaseLoggingConfig["LogLevel"] ?? "Warning";

// Přidání file logging služeb (bez Serilog UseSerilog)
builder.Services.AddFileLoggingServices(builder.Configuration);



// Detekce, zda aplikace běží jako Windows Service
var isWindowsService = args.Contains("--service") ||
                       Environment.GetCommandLineArgs().Contains("--service") ||
                       (!Environment.UserInteractive && Environment.OSVersion.Platform == PlatformID.Win32NT);

// Přidání podpory pro Windows Service pouze pokud je potřeba
if (isWindowsService)
{
    Console.WriteLine("Konfigurace pro Windows Service...");
    try
    {
        // Pokus o použití Windows Service podpory
        builder.Host.UseWindowsService();
        Console.WriteLine("Windows Service podpora aktivována");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Chyba při konfiguraci Windows Service: {ex.Message}");
        Console.WriteLine("Pokračuji v konzolové aplikaci režimu...");

        // Konfigurace pro dlouhodobě běžící službu bez Windows Service API
        builder.Host.UseConsoleLifetime(options =>
        {
            options.SuppressStatusMessages = true;
        });
    }
}

// Registrace služeb pro správu nastavení aplikace a serverových certifikátů
builder.Services.AddScoped<EncryptionService>();
builder.Services.AddScoped<IAppSettingsService, AppSettingsService>();
builder.Services.AddScoped<ServerCertificateService>();

// Vylepšená detekce prostředí - IIS vs Kestrel
var isRunningInIIS = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_IIS_PHYSICAL_PATH")) ||
                     !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("IIS_SITE_NAME")) ||
                     !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_MODULE_NAME"));

// Dodatečné kontroly pro IIS
var iisIndicators = new[]
{
    Environment.GetEnvironmentVariable("ASPNETCORE_IIS_PHYSICAL_PATH"),
    Environment.GetEnvironmentVariable("IIS_SITE_NAME"),
    Environment.GetEnvironmentVariable("ASPNETCORE_MODULE_NAME"),
    Environment.GetEnvironmentVariable("ASPNETCORE_FORWARDEDHEADERS_ENABLED"),
    Environment.GetEnvironmentVariable("ASPNETCORE_CLIENTCERT_ENABLED")
};

Console.WriteLine($"Prostředí: {builder.Environment.EnvironmentName}");
Console.WriteLine($"Běží v IIS: {isRunningInIIS}");
Console.WriteLine("IIS indikátory:");
foreach (var indicator in iisIndicators.Where(i => !string.IsNullOrEmpty(i)))
{
    Console.WriteLine($"  - {indicator}");
}

// Konfigurace pouze pro Kestrel (ne pro IIS)
if (!isRunningInIIS)
{
    builder.WebHost.ConfigureKestrel(serverOptions =>
    {
        serverOptions.ConfigureHttpsDefaults(httpsOptions =>
        {
            httpsOptions.ClientCertificateMode = Microsoft.AspNetCore.Server.Kestrel.Https.ClientCertificateMode.AllowCertificate;
            httpsOptions.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13;

        // Získání serverového certifikátu z databáze
        try
        {
            // Vytvoření scope pro získání služeb
            using var scope = builder.Services.BuildServiceProvider().CreateScope();
            var certificateService = scope.ServiceProvider.GetRequiredService<ServerCertificateService>();

            // Získání certifikátu z databáze
            var certificate = certificateService.GetServerCertificate();

            if (certificate == null)
            {
                // Pokud certifikát není v databázi, zkusíme ho načíst z konfigurace
                Console.WriteLine("Certifikát nebyl nalezen v databázi, zkouším načíst z konfigurace");

                var useStore = builder.Configuration.GetValue<bool>("ServerCertificate:UseStoreInstead", false);

                if (useStore)
                {
                    // Načtení certifikátu z úložiště certifikátů
                    var storeName = builder.Configuration.GetValue<StoreName>("ServerCertificate:StoreName", StoreName.My);
                    var storeLocation = builder.Configuration.GetValue<StoreLocation>("ServerCertificate:StoreLocation", StoreLocation.LocalMachine);
                    var thumbprint = builder.Configuration["ServerCertificate:Thumbprint"];

                    if (string.IsNullOrEmpty(thumbprint))
                    {
                        throw new InvalidOperationException("Thumbprint certifikátu není nastaven v konfiguraci");
                    }

                    using var store = new X509Store(storeName, storeLocation);
                    store.Open(OpenFlags.ReadOnly);

                    var certificates = store.Certificates.Find(X509FindType.FindByThumbprint, thumbprint, false);
                    if (certificates.Count == 0)
                    {
                        throw new InvalidOperationException($"Certifikát s thumbprintem {thumbprint} nebyl nalezen v úložišti {storeName}/{storeLocation}");
                    }

                    certificate = certificates[0];
                    Console.WriteLine($"Serverový certifikát načten z úložiště: {certificate.Subject}, Thumbprint: {certificate.Thumbprint}");
                }
                else
                {
                    // Načtení certifikátu ze souboru
                    var path = builder.Configuration["ServerCertificate:Path"];
                    var password = builder.Configuration["ServerCertificate:Password"];

                    if (string.IsNullOrEmpty(path))
                    {
                        throw new InvalidOperationException("Cesta k certifikátu není nastavena v konfiguraci");
                    }

                    try
                    {
                        // Zkusíme najít soubor v různých umístěních
                        string fullPath = path;
                        if (!File.Exists(fullPath))
                        {
                            // Zkusíme najít soubor v adresáři aplikace
                            fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path);
                            if (!File.Exists(fullPath))
                            {
                                // Zkusíme najít soubor v adresáři DISAdmin.Api
                                var apiDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "DISAdmin.Api");
                                fullPath = Path.Combine(apiDirectory, path);

                                if (!File.Exists(fullPath))
                                {
                                    // Zkusíme najít soubor v bin adresáři DISAdmin.Api
                                    apiDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "DISAdmin.Api", "bin", "Debug", "net9.0");
                                    fullPath = Path.Combine(apiDirectory, path);

                                    if (!File.Exists(fullPath))
                                    {
                                        throw new FileNotFoundException($"Soubor certifikátu nebyl nalezen: {path}. Hledáno v: {AppDomain.CurrentDomain.BaseDirectory}, {apiDirectory}");
                                    }
                                }
                            }
                        }

                        Console.WriteLine($"Certifikát nalezen v: {fullPath}");
                        certificate = new X509Certificate2(fullPath, password);
                        Console.WriteLine($"Serverový certifikát načten ze souboru: {path}");
                    }
                    catch (Exception fileEx)
                    {
                        Console.WriteLine($"Chyba při načítání certifikátu ze souboru: {fileEx.Message}");
                        Console.WriteLine("Pokus o načtení certifikátu přes API...");

                        // Pokus o načtení certifikátu přes API
                        try
                        {
                            // Získání API klíče a URL z konfigurace
                            var apiKey = builder.Configuration["DISApi:ApiKey"];
                            var apiUrl = builder.Configuration["DISApi:ApiUrl"];

                            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(apiUrl))
                            {
                                throw new InvalidOperationException("DISApi:ApiKey nebo DISApi:ApiUrl není nastaveno v konfiguraci");
                            }

                            // Vytvoření HTTP klienta
                            using var httpClient = new HttpClient();

                            // Sestavení URL pro získání certifikátu
                            var requestUrl = $"{apiUrl}/api/servercertificate/data?apiKey={apiKey}";

                            // Odeslání požadavku
                            var response = httpClient.GetAsync(requestUrl).GetAwaiter().GetResult();

                            // Kontrola odpovědi
                            if (!response.IsSuccessStatusCode)
                            {
                                throw new InvalidOperationException($"Chyba při získávání certifikátu přes API: {response.StatusCode}");
                            }

                            // Deserializace odpovědi
                            var responseContent = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                            var certData = System.Text.Json.JsonSerializer.Deserialize<CertificateDataResponse>(responseContent);

                            if (certData == null || string.IsNullOrEmpty(certData.Data))
                            {
                                throw new InvalidOperationException("Odpověď API neobsahuje data certifikátu");
                            }

                            // Převod Base64 na byte[]
                            var certBytes = Convert.FromBase64String(certData.Data);

                            // Vytvoření certifikátu
                            certificate = new X509Certificate2(certBytes, certData.Password);
                            Console.WriteLine($"Serverový certifikát načten přes API: {certificate.Subject}, Thumbprint: {certificate.Thumbprint}");
                        }
                        catch (Exception apiEx)
                        {
                            Console.WriteLine($"Chyba při načítání certifikátu přes API: {apiEx.Message}");
                            throw new AggregateException("Nelze načíst certifikát ani ze souboru, ani přes API", fileEx, apiEx);
                        }
                    }
                }
            }
            else
            {
                Console.WriteLine($"Serverový certifikát načten z databáze: {certificate.Subject}, Thumbprint: {certificate.Thumbprint}");
            }

            // Nastavení certifikátu pro HTTPS
            httpsOptions.ServerCertificate = certificate;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Chyba při načítání serverového certifikátu: {ex.Message}");
            throw;
        }

        // Implementace validace klientských certifikátů
        httpsOptions.ClientCertificateValidation = (certificate, chain, errors) =>
        {
            // Získání nastavení pro přeskočení validace certifikátů
            var skipCertValidation = builder.Configuration.GetValue<bool>("Security:SkipCertValidation", false);

            if (certificate == null)
            {
                Console.WriteLine("Certifikát je null!");
                return false;
            }

            Console.WriteLine($"Validace klientského certifikátu: {certificate.Subject}, Platnost: {certificate.NotBefore} - {certificate.NotAfter}");
            Console.WriteLine($"Chyby validace: {errors}");

            // Pokud je nastavené přeskočení validace, akceptujeme všechny certifikáty
            if (skipCertValidation)
            {
                Console.WriteLine("Validace certifikátů je vypnuta, akceptuji všechny certifikáty.");
                return true;
            }

            // V produkčním prostředí provádíme validaci
            // Kontrola, zda certifikát není expirovaný - používáme lokální čas místo UTC
            if (certificate.NotAfter < DateTime.Now || certificate.NotBefore > DateTime.Now)
            {
                Console.WriteLine($"Certifikát je expirovaný nebo ještě není platný: {certificate.Subject}, Platnost: {certificate.NotBefore} - {certificate.NotAfter}, Aktuální čas: {DateTime.Now}");
                return false;
            }

            // Pro testovací účely akceptujeme self-signed certifikáty
            if (errors == System.Net.Security.SslPolicyErrors.RemoteCertificateChainErrors)
            {
                // Kontrola, zda jde o self-signed certifikát (subject = issuer)
                if (certificate.Subject == certificate.Issuer)
                {
                    Console.WriteLine("Akceptuji self-signed certifikát.");
                    return true;
                }
            }

            // Kontrola, zda certifikát není revokovaný a další validace
            // Poznámka: Pro úplnou validaci by zde měla být implementace kontroly CRL nebo OCSP
            bool isValid = errors == System.Net.Security.SslPolicyErrors.None;
            Console.WriteLine($"Výsledek standardní validace certifikátu: {(isValid ? "Platný" : "Neplatný")}. Chyby: {errors}");
            return isValid;
        };
    });
    });
}
else
{
    Console.WriteLine("Konfigurace pro IIS prostředí...");

    // Přidání IIS integrace s rozšířenou konfigurací pro klientské certifikáty
    builder.Services.Configure<IISOptions>(options =>
    {
        options.ForwardClientCertificate = true;
        options.AutomaticAuthentication = false;
        Console.WriteLine("IIS: ForwardClientCertificate = true, AutomaticAuthentication = false");
    });

    // Konfigurace ForwardedHeaders pro IIS
    builder.Services.Configure<ForwardedHeadersOptions>(options =>
    {
        options.ForwardedHeaders = Microsoft.AspNetCore.HttpOverrides.ForwardedHeaders.XForwardedFor |
                                   Microsoft.AspNetCore.HttpOverrides.ForwardedHeaders.XForwardedProto |
                                   Microsoft.AspNetCore.HttpOverrides.ForwardedHeaders.XForwardedHost;
        options.KnownNetworks.Clear();
        options.KnownProxies.Clear();

        // Přidání podpory pro klientské certifikáty v hlavičkách
        options.ForwardedHeaders |= Microsoft.AspNetCore.HttpOverrides.ForwardedHeaders.All;

        Console.WriteLine($"IIS: ForwardedHeaders = {options.ForwardedHeaders}");
    });

    // Přidání middleware pro zpracování klientských certifikátů v IIS
    builder.Services.AddAuthentication()
        .AddCertificate(options =>
        {
            options.AllowedCertificateTypes = CertificateTypes.All;
            options.ValidateCertificateUse = false;
            options.ValidateValidityPeriod = false;
            options.RevocationMode = System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck;

            Console.WriteLine("IIS: Konfigurace Certificate Authentication");
        });
}

// Konfigurovatelné vypnutí/zapnutí validace certifikátů
var skipCertValidation = builder.Configuration.GetValue<bool>("Security:SkipCertValidation",
    builder.Environment.IsDevelopment());

// Konfigurace HTTP klienta s podmíněnou validací certifikátů
builder.Services.AddHttpClient("DISClient").ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (skipCertValidation)
    {
        handler.ServerCertificateCustomValidationCallback =
            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
    }
    return handler;
});

// Konfigurace database loggingu
// Nejdříve nastavíme ASP.NET Core logging filtry na nejnižší úroveň ze všech providerů
var minLogLevel = LogLevel.Information; // Výchozí

// Zkontrolujeme database logging level
if (Enum.TryParse<LogLevel>(dbLogLevelString, out var dbLogLevel))
{
    if (dbLogLevel < minLogLevel)
        minLogLevel = dbLogLevel;
}

// Zkontrolujeme file logging level
var fileLoggingOptions = new DISAdmin.Core.Logging.FileLoggingOptions();
builder.Configuration.GetSection("FileLogging").Bind(fileLoggingOptions);
if (fileLoggingOptions.Enabled && fileLoggingOptions.LogLevel < minLogLevel)
{
    minLogLevel = fileLoggingOptions.LogLevel;
}

builder.Logging.SetMinimumLevel(minLogLevel);

// Přidáme ASP.NET Core filtry pro všechny kategorie z provider filtrů
var allFilters = new Dictionary<string, LogLevel>();

// Načteme database filtry
var databaseFilters = builder.Configuration.GetSection("Logging:Database:Filters");
if (databaseFilters.Exists())
{
    foreach (var filter in databaseFilters.GetChildren())
    {
        if (Enum.TryParse<LogLevel>(filter.Value, out var filterLevel))
        {
            if (!allFilters.ContainsKey(filter.Key) || filterLevel < allFilters[filter.Key])
            {
                allFilters[filter.Key] = filterLevel;
            }
        }
    }
}

// Načteme file filtry
if (fileLoggingOptions.Filters != null)
{
    foreach (var filter in fileLoggingOptions.Filters)
    {
        if (Enum.TryParse<LogLevel>(filter.Value, out var filterLevel))
        {
            if (!allFilters.ContainsKey(filter.Key) || filterLevel < allFilters[filter.Key])
            {
                allFilters[filter.Key] = filterLevel;
            }
        }
    }
}

// Aplikujeme ASP.NET Core filtry (nejnižší úroveň ze všech providerů)
foreach (var filter in allFilters)
{
    builder.Logging.AddFilter(filter.Key, filter.Value);
}

builder.Logging.AddDatabaseLogger(options => {
    // Načtení základní úrovně
    if (Enum.TryParse<LogLevel>(dbLogLevelString, out var logLevel))
    {
        options.LogLevel = logLevel;
    }
    else
    {
        options.LogLevel = LogLevel.Warning; // Výchozí hodnota
    }

    // Načtení filtrů z konfigurace
    if (databaseFilters.Exists())
    {
        foreach (var filter in databaseFilters.GetChildren())
        {
            options.Filters[filter.Key] = filter.Value ?? "Warning";
        }
    }
}, "DISApi");

// Přidání Serilog file loggingu s konfigurací z config souboru

if (fileLoggingOptions.Enabled)
{
    // Získání správného adresáře pro logy
    var logDirectory = Path.IsPathRooted(fileLoggingOptions.LogDirectory)
        ? fileLoggingOptions.LogDirectory
        : Path.Combine(AppDomain.CurrentDomain.BaseDirectory, fileLoggingOptions.LogDirectory);

    // Vytvoření adresáře pokud neexistuje
    if (!Directory.Exists(logDirectory))
    {
        Directory.CreateDirectory(logDirectory);
    }

    // Vytvoření úplné cesty k log souboru
    var fileName = fileLoggingOptions.UseJsonFormat
        ? $"{fileLoggingOptions.FileNamePrefix}-.json"
        : $"{fileLoggingOptions.FileNamePrefix}-.log";
    var logFilePath = Path.Combine(logDirectory, fileName);

    var loggerConfig = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithProperty("Application", "DISAdmin.DISApi")
        .Enrich.WithProperty("Environment", builder.Environment.EnvironmentName);

    // Přidání filtrů z konfigurace
    if (fileLoggingOptions.Filters != null && fileLoggingOptions.Filters.Any())
    {
        foreach (var filter in fileLoggingOptions.Filters)
        {
            var category = filter.Key;
            var levelString = filter.Value;

            if (Enum.TryParse<LogLevel>(levelString, out var logLevel))
            {
                var serilogLevel = ConvertToSerilogLevel(logLevel);
                loggerConfig = loggerConfig.Filter.ByExcluding(logEvent =>
                    logEvent.Properties.ContainsKey("SourceContext") &&
                    logEvent.Properties["SourceContext"].ToString().Trim('"').StartsWith(category) &&
                    logEvent.Level < serilogLevel);
            }
        }
    }

    builder.Logging.AddSerilog(loggerConfig
        .WriteTo.File(
            path: logFilePath,
            restrictedToMinimumLevel: fileLoggingOptions.GetSerilogLogLevel(),
            outputTemplate: fileLoggingOptions.OutputTemplate,
            rollingInterval: fileLoggingOptions.GetRollingInterval(),
            retainedFileCountLimit: fileLoggingOptions.RetainedFileCountLimit,
            fileSizeLimitBytes: fileLoggingOptions.FileSizeLimitBytes,
            rollOnFileSizeLimit: true,
            shared: true,
            flushToDiskInterval: TimeSpan.FromSeconds(1))
        .CreateLogger());
}



// Přidání global exception handler
builder.Services.AddExceptionHandler<DISAdmin.Api.Exceptions.GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

// Přidání služby pro zachytávání neošetřených výjimek
builder.Services.AddHostedService<DISAdmin.Api.Services.UnhandledExceptionService>();

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Přidání controllerů pouze z DISAdmin.DISApi assembly
builder.Services.AddControllers();

// Přidání HttpContextAccessor pro přístup k HTTP kontextu
builder.Services.AddHttpContextAccessor();

// Přidání autentizace
builder.Services.AddAuthentication("ApiKeyScheme")
    .AddScheme<Microsoft.AspNetCore.Authentication.AuthenticationSchemeOptions, DISAdmin.DISApi.Authentication.ApiKeyAuthenticationHandler>("ApiKeyScheme", options => { });

// Odstranění reference na DISAdmin.Api z MVC aplikace
var mvcBuilder = builder.Services.AddMvc();
var applicationPartFactory = mvcBuilder.PartManager.ApplicationParts
    .FirstOrDefault(part => part.Name == "DISAdmin.Api");

if (applicationPartFactory != null)
{
    mvcBuilder.PartManager.ApplicationParts.Remove(applicationPartFactory);
}

builder.Services.AddEndpointsApiExplorer();

// CORS nastavení odstraněno, protože DISApi komunikuje pouze s desktopovými klienty, ne s webovými prohlížeči

// Přidání služeb z Core projektu
builder.Services.AddCoreServices(builder.Configuration);

// Registrace služeb pro DIS API
builder.Services.AddScoped<DISAdmin.Core.Services.DISVersionService>();
builder.Services.AddScoped<DISAdmin.Core.Services.DISInstanceService>();
builder.Services.AddScoped<DISAdmin.Core.Services.IpWhitelistingService>();

// Registrace dalších služeb potřebných pro MonitoringService
builder.Services.AddScoped<DISAdmin.Core.Services.MetricsService>();
builder.Services.AddScoped<DISAdmin.Core.Services.MonitoringService>();
builder.Services.AddScoped<DISAdmin.Core.Services.EmailService>();
builder.Services.AddScoped<DISAdmin.Core.Services.AlertingService>();
builder.Services.AddScoped<DISAdmin.Core.Services.AnomalyDetectionService>();

// Přidání hostované služby pro migrace
builder.Services.AddHostedService<MigrationHostedService>();

// Registrace SecurityMonitoringService
builder.Services.AddScoped<SecurityMonitoringService>();

// Registrace služby pro zpracování pravidel alertů
builder.Services.AddScoped<AlertRuleProcessingService>(sp => {
    var context = sp.GetRequiredService<DISAdmin.Core.Data.DISAdminDbContext>();
    var logger = sp.GetRequiredService<ILogger<AlertRuleProcessingService>>();
    var alertingService = sp.GetRequiredService<AlertingService>();
    var cachingService = sp.GetRequiredService<CachingService>();
    var anomalyDetectionService = sp.GetRequiredService<AnomalyDetectionService>();

    var service = new AlertRuleProcessingService(context, logger, alertingService, cachingService);
    alertingService.SetAlertRuleProcessingService(service);
    anomalyDetectionService.SetAlertRuleProcessingService(service);

    return service;
});

// Přidání HTTP loggingu v development módu
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddHttpLogging(logging =>
    {
        logging.LoggingFields = Microsoft.AspNetCore.HttpLogging.HttpLoggingFields.All;
        logging.RequestHeaders.Add("Authorization");
        logging.ResponseHeaders.Add("X-Response-Time");
        logging.MediaTypeOptions.AddText("application/json");
    });
}

var app = builder.Build();





// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
    app.UseHttpLogging();
}

// Použití exception handler middleware - musí být jako první
app.UseExceptionHandler();

// Použití middleware pro zpracování chyb - jako backup
app.UseMiddleware<ErrorHandlingMiddleware>();

app.UseHttpsRedirection();

// Použití ForwardedHeaders middleware pro IIS
if (isRunningInIIS)
{
    app.UseForwardedHeaders();
}

// Přidání middleware
app.UseMiddleware<EnhancedLoggingMiddleware>();
app.UseMiddleware<CertificateLoggingMiddleware>();

// Registrace middleware pro setup endpoint
app.MapWhen(context => context.Request.Path.StartsWithSegments("/setup"), appBuilder => {
    appBuilder.UseRouting();
    appBuilder.UseEndpoints(endpoints => {
        endpoints.MapControllers();
    });
});

// Registrace middleware pro ostatní endpointy
app.MapWhen(context => !context.Request.Path.StartsWithSegments("/setup"), appBuilder => {
    // ApiKeyValidationMiddleware
    appBuilder.Use(async (context, next) => {
        using var scope = app.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<ApiKeyValidationMiddleware>>();
        var dbContext = scope.ServiceProvider.GetRequiredService<DISAdminDbContext>();
        var filterService = scope.ServiceProvider.GetRequiredService<SecurityEventFilterService>();
        var middleware = new ApiKeyValidationMiddleware(next, logger, dbContext, filterService);
        await middleware.InvokeAsync(context);
    });

    // CertificateValidationMiddleware
    appBuilder.Use(async (context, next) => {
        using var scope = app.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<CertificateValidationMiddleware>>();
        var dbContext = scope.ServiceProvider.GetRequiredService<DISAdminDbContext>();
        var filterService = scope.ServiceProvider.GetRequiredService<SecurityEventFilterService>();
        var middleware = new CertificateValidationMiddleware(next, logger, dbContext, filterService);
        await middleware.InvokeAsync(context);
    });

    // IpWhitelistingMiddleware
    appBuilder.Use(async (context, next) => {
        using var scope = app.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<IpWhitelistingMiddleware>>();
        var ipWhitelistingService = scope.ServiceProvider.GetRequiredService<IpWhitelistingService>();
        var middleware = new IpWhitelistingMiddleware(next, logger);
        await middleware.InvokeAsync(context, ipWhitelistingService);
    });

    // ActivityLoggingMiddleware
    appBuilder.Use(async (context, next) => {
        using var scope = app.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<ActivityLoggingMiddleware>>();
        var dbContext = scope.ServiceProvider.GetRequiredService<DISAdminDbContext>();
        var middleware = new ActivityLoggingMiddleware(next, logger, dbContext);
        await middleware.InvokeAsync(context);
    });

    appBuilder.UseRouting();
    appBuilder.UseAuthentication();
    appBuilder.UseAuthorization();
    appBuilder.UseEndpoints(endpoints => {
        endpoints.MapControllers();
    });
});

app.MapControllers();

try
{
    app.Run();
}
catch (Exception ex)
{
    // Pokus o zápis chyby do databáze
    try
    {
        using var scope = new ServiceCollection()
            .AddDbContext<DISAdminDbContext>(options =>
            {
                var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
                options.UseSqlServer(connectionString);
            })
            .BuildServiceProvider()
            .CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<DISAdminDbContext>();

        // Vytvoření záznamu o chybě
        var errorLog = new ActivityLog
        {
            ActivityType = ActivityType.Other, // Použijeme Other, protože Error není v ActivityType
            Description = $"Chyba při spuštění DISApi: {ex.Message}",
            AdditionalInfo = ex.StackTrace,
            EntityName = "DISApi startup",
            Source = LogSource.DISApi,
            Timestamp = DateTime.UtcNow,
            IpAddress = "localhost"
        };

        dbContext.ActivityLogs.Add(errorLog);
        dbContext.SaveChanges();

        Console.WriteLine("Chyba byla zapsána do databáze");
    }
    catch (Exception logEx)
    {
        Console.WriteLine($"Chyba při ukládání logu do databáze: {logEx.Message}");
    }

    // Přeposlání výjimky
    throw;
}

/// <summary>
/// Převod Microsoft LogLevel na Serilog LogEventLevel
/// </summary>
static Serilog.Events.LogEventLevel ConvertToSerilogLevel(LogLevel logLevel)
{
    return logLevel switch
    {
        LogLevel.Trace => Serilog.Events.LogEventLevel.Verbose,
        LogLevel.Debug => Serilog.Events.LogEventLevel.Debug,
        LogLevel.Information => Serilog.Events.LogEventLevel.Information,
        LogLevel.Warning => Serilog.Events.LogEventLevel.Warning,
        LogLevel.Error => Serilog.Events.LogEventLevel.Error,
        LogLevel.Critical => Serilog.Events.LogEventLevel.Fatal,
        _ => Serilog.Events.LogEventLevel.Information
    };
}

/// <summary>
/// Konfigurace Console výstupu pro produkční prostředí
/// </summary>
static void ConfigureConsoleLogging(IWebHostEnvironment environment, IConfiguration configuration)
{
    try
    {
        // Načtení konfigurace Console loggingu
        var consoleLoggingConfig = configuration.GetSection("ConsoleLogging");
        var enabled = consoleLoggingConfig.GetValue<bool>("Enabled", true);

        if (!enabled)
        {
            Console.WriteLine("Console logging je vypnut v konfiguraci");
            return;
        }

        // Načtení konfiguračních hodnot
        var logDirectory = consoleLoggingConfig.GetValue<string>("LogDirectory", "Logs");
        var fileNamePrefix = consoleLoggingConfig.GetValue<string>("FileNamePrefix", "console");
        var includeTimestamp = consoleLoggingConfig.GetValue<bool>("IncludeTimestamp", true);
        var redirectErrors = consoleLoggingConfig.GetValue<bool>("RedirectErrors", true);

        // Vytvoření úplné cesty k adresáři
        var fullLogDirectory = Path.IsPathRooted(logDirectory)
            ? logDirectory
            : Path.Combine(AppDomain.CurrentDomain.BaseDirectory, logDirectory);

        if (!Directory.Exists(fullLogDirectory))
        {
            Directory.CreateDirectory(fullLogDirectory);
        }

        // Vytvoření názvu souboru s časovým razítkem
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd");
        var logFileName = includeTimestamp
            ? $"{fileNamePrefix}-{timestamp}.log"
            : $"{fileNamePrefix}.log";
        var logFilePath = Path.Combine(fullLogDirectory, logFileName);

        // Vytvoření StreamWriter pro zápis do souboru
        var fileStream = new FileStream(logFilePath, FileMode.Append, FileAccess.Write, FileShare.Read);
        var streamWriter = new StreamWriter(fileStream, System.Text.Encoding.UTF8)
        {
            AutoFlush = true // Automatické vyprázdnění bufferu
        };

        // Přesměrování Console.Out do souboru
        Console.SetOut(streamWriter);

        // Přesměrování Console.Error do souboru (volitelně)
        if (redirectErrors)
        {
            Console.SetError(streamWriter);
        }

        // Zápis úvodní zprávy
        Console.WriteLine($"=== Console logging started at {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
        Console.WriteLine($"Environment: {environment.EnvironmentName}");
        Console.WriteLine($"Application: DISAdmin.DISApi");
        Console.WriteLine($"Log file: {logFilePath}");
        Console.WriteLine($"Configuration: Enabled={enabled}, IncludeTimestamp={includeTimestamp}, RedirectErrors={redirectErrors}");
        Console.WriteLine("=== Console output will be written to this file ===");
        Console.WriteLine();
    }
    catch (Exception ex)
    {
        // Pokud se nepodaří nastavit file logging, pokračujeme s normálním Console výstupem
        Console.WriteLine($"Chyba při konfiguraci Console logging: {ex.Message}");
        Console.WriteLine("Pokračuji s normálním Console výstupem...");
    }
}
