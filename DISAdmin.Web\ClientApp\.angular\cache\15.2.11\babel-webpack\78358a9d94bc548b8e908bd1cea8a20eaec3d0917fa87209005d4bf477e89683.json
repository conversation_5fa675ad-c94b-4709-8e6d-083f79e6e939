{"ast": null, "code": "import { isDevMode } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/modal.service\";\nimport * as i4 from \"@angular/common\";\nfunction NavMenuComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggle());\n    });\n    i0.ɵɵelement(1, \"span\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-expanded\", ctx_r0.isExpanded);\n  }\n}\nconst _c0 = function () {\n  return [\"link-active\"];\n};\nconst _c1 = function () {\n  return [\"/dashboard\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 30)(1, \"a\", 31);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵtext(3, \"Dashboard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nconst _c2 = function () {\n  return [\"/users\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3, \"U\\u017Eivatel\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nconst _c3 = function () {\n  return [\"/certificates\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_21_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵtext(3, \"Spr\\u00E1va certifik\\u00E1t\\u016F\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nconst _c4 = function () {\n  return [\"/performance\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_22_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \"V\\u00FDkon DIS\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nconst _c5 = function () {\n  return [\"/security\"];\n};\nconst _c6 = function () {\n  return [\"/monitoring\"];\n};\nconst _c7 = function () {\n  return [\"/alerts\"];\n};\nconst _c8 = function () {\n  return [\"/logs\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18)(1, \"a\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵtext(3, \"Bezpe\\u010Dnost \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 38)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 39);\n    i0.ɵɵtext(8, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\")(10, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.collapse());\n    });\n    i0.ɵɵelement(11, \"i\", 40);\n    i0.ɵɵtext(12, \"Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.collapse());\n    });\n    i0.ɵɵelement(15, \"i\", 41);\n    i0.ɵɵtext(16, \"Alerty\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\")(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.collapse());\n    });\n    i0.ɵɵelement(19, \"i\", 42);\n    i0.ɵɵtext(20, \"Logy syst\\u00E9mu\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(5, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c5));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c6));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c7));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(9, _c8));\n  }\n}\nconst _c9 = function () {\n  return [\"/admin/server-certificate\"];\n};\nconst _c10 = function () {\n  return [\"/admin/disapi-status\"];\n};\nconst _c11 = function () {\n  return [\"/certificate-rotation\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18)(1, \"a\", 43);\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵtext(3, \"Administrace \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 45)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 46);\n    i0.ɵɵtext(8, \"Serverov\\u00FD certifik\\u00E1t\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\")(10, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.collapse());\n    });\n    i0.ɵɵelement(11, \"i\", 47);\n    i0.ɵɵtext(12, \"Stav DIS API\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.collapse());\n    });\n    i0.ɵɵelement(15, \"i\", 48);\n    i0.ɵɵtext(16, \"Rotace DIS certifik\\u00E1t\\u016F\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(4, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c9));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c10));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c11));\n  }\n}\nconst _c12 = function () {\n  return [\"/customers\"];\n};\nconst _c13 = function () {\n  return [\"/versions\"];\n};\nfunction NavMenuComponent_div_9_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 16);\n    i0.ɵɵtemplate(1, NavMenuComponent_div_9_ul_1_li_1_Template, 4, 4, \"li\", 17);\n    i0.ɵɵelementStart(2, \"li\", 18)(3, \"a\", 19);\n    i0.ɵɵelement(4, \"i\", 20);\n    i0.ɵɵtext(5, \"Spr\\u00E1va \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 21);\n    i0.ɵɵtemplate(7, NavMenuComponent_div_9_ul_1_li_7_Template, 4, 2, \"li\", 22);\n    i0.ɵɵelementStart(8, \"li\")(9, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.collapse());\n    });\n    i0.ɵɵelement(10, \"i\", 24);\n    i0.ɵɵtext(11, \"Z\\u00E1kazn\\u00EDci\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"li\", 18)(13, \"a\", 25);\n    i0.ɵɵelement(14, \"i\", 26);\n    i0.ɵɵtext(15, \"DIS \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ul\", 27)(17, \"li\")(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.collapse());\n    });\n    i0.ɵɵelement(19, \"i\", 28);\n    i0.ɵɵtext(20, \"Verze DIS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, NavMenuComponent_div_9_ul_1_li_21_Template, 4, 2, \"li\", 22);\n    i0.ɵɵtemplate(22, NavMenuComponent_div_9_ul_1_li_22_Template, 4, 2, \"li\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, NavMenuComponent_div_9_ul_1_li_23_Template, 21, 10, \"li\", 29);\n    i0.ɵɵtemplate(24, NavMenuComponent_div_9_ul_1_li_24_Template, 17, 8, \"li\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(10, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c12));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c13));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n  }\n}\nfunction NavMenuComponent_div_9_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.toggleTheme());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r6.isDarkMode ? \"P\\u0159epnout na sv\\u011Btl\\u00FD re\\u017Eim\" : \"P\\u0159epnout na tmav\\u00FD re\\u017Eim\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.isDarkMode ? \"bi-sun-fill\" : \"bi-moon-stars-fill\");\n  }\n}\nconst _c14 = function () {\n  return [\"/profile\"];\n};\nfunction NavMenuComponent_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 53);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 55)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_4_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 56);\n    i0.ɵɵtext(8, \"M\\u016Fj profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\");\n    i0.ɵɵelement(10, \"hr\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"li\")(12, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_4_Template_a_click_12_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.logout());\n    });\n    i0.ɵɵelement(13, \"i\", 59);\n    i0.ɵɵtext(14, \"Odhl\\u00E1sit se\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.currentUser == null ? null : ctx_r7.currentUser.firstName, \" \", ctx_r7.currentUser == null ? null : ctx_r7.currentUser.lastName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c14));\n  }\n}\nconst _c15 = function () {\n  return [\"/login\"];\n};\nfunction NavMenuComponent_div_9_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 60)(1, \"a\", 31);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵtext(3, \"P\\u0159ihl\\u00E1sit se\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c15));\n  }\n}\nconst _c16 = function (a0) {\n  return {\n    show: a0\n  };\n};\nfunction NavMenuComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, NavMenuComponent_div_9_ul_1_Template, 25, 14, \"ul\", 11);\n    i0.ɵɵelementStart(2, \"ul\", 12);\n    i0.ɵɵtemplate(3, NavMenuComponent_div_9_li_3_Template, 3, 2, \"li\", 13);\n    i0.ɵɵtemplate(4, NavMenuComponent_div_9_li_4_Template, 15, 4, \"li\", 14);\n    i0.ɵɵtemplate(5, NavMenuComponent_div_9_li_5_Template, 4, 2, \"li\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c16, ctx_r1.isExpanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoggedIn);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoginPage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoggedIn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoggedIn && !ctx_r1.isLoginPage);\n  }\n}\nconst _c17 = function (a0) {\n  return {\n    \"login-right-aligned\": a0\n  };\n};\nfunction NavMenuComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.toggleTheme());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c17, ctx_r2.isLoginPage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r2.isDarkMode ? \"P\\u0159epnout na sv\\u011Btl\\u00FD re\\u017Eim\" : \"P\\u0159epnout na tmav\\u00FD re\\u017Eim\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.isDarkMode ? \"bi-sun-fill\" : \"bi-moon-stars-fill\");\n  }\n}\nconst _c18 = function (a0) {\n  return {\n    \"login-container\": a0\n  };\n};\nconst _c19 = function (a0) {\n  return {\n    \"login-left-aligned\": a0\n  };\n};\nconst _c20 = function () {\n  return [\"/\"];\n};\nexport class NavMenuComponent {\n  constructor(authService, router, modalService, renderer) {\n    this.authService = authService;\n    this.router = router;\n    this.modalService = modalService;\n    this.renderer = renderer;\n    this.isExpanded = false;\n    this.currentUser = null;\n    this.isLoggedIn = false;\n    this.isAdmin = false;\n    this.isDarkMode = false;\n    this.isDevMode = isDevMode();\n    this.isLoginPage = false;\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isLoggedIn = !!user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n    // Zavřít rozbalovací menu při navigaci\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      this.collapse();\n    });\n  }\n  ngOnInit() {\n    // Kontrola, zda je nastaven tmavý režim v localStorage\n    this.isDarkMode = localStorage.getItem('darkMode') === 'true';\n    this.applyTheme();\n    // Kontrola, zda jsme na přihlašovací stránce - použijeme startsWith pro detekci i s parametry\n    this.isLoginPage = this.router.url.startsWith('/login');\n    console.log('Initial isLoginPage:', this.isLoginPage, 'URL:', this.router.url);\n    // Sledování změn URL\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.isLoginPage = event.url.startsWith('/login');\n      console.log('Navigation isLoginPage:', this.isLoginPage, 'URL:', event.url);\n    });\n  }\n  collapse() {\n    this.isExpanded = false;\n  }\n  toggle() {\n    this.isExpanded = !this.isExpanded;\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  toggleTheme() {\n    this.isDarkMode = !this.isDarkMode;\n    localStorage.setItem('darkMode', this.isDarkMode.toString());\n    this.applyTheme();\n  }\n  applyTheme() {\n    if (this.isDarkMode) {\n      document.body.classList.add('dark-theme');\n    } else {\n      document.body.classList.remove('dark-theme');\n    }\n  }\n  static {\n    this.ɵfac = function NavMenuComponent_Factory(t) {\n      return new (t || NavMenuComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ModalService), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavMenuComponent,\n      selectors: [[\"app-nav-menu\"]],\n      decls: 11,\n      vars: 11,\n      consts: [[1, \"navbar\", \"navbar-expand-sm\", \"navbar-toggleable-sm\", \"navbar-dark\", \"bg-primary\", \"box-shadow\", \"mb-3\"], [1, \"container\", 3, \"ngClass\"], [1, \"login-left\", 3, \"ngClass\"], [1, \"navbar-brand\", 3, \"routerLink\"], [1, \"bi\", \"bi-database-gear\", \"me-2\"], [\"class\", \"navbar-toggler\", \"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \".navbar-collapse\", \"aria-label\", \"Toggle navigation\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"navbar-collapse collapse d-sm-inline-flex justify-content-between\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"login-right\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \".navbar-collapse\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"navbar-collapse\", \"collapse\", \"d-sm-inline-flex\", \"justify-content-between\", 3, \"ngClass\"], [\"class\", \"navbar-nav flex-grow-1\", 4, \"ngIf\"], [1, \"navbar-nav\", \"align-items-center\"], [\"class\", \"nav-item me-2\", 4, \"ngIf\"], [\"class\", \"nav-item dropdown\", 4, \"ngIf\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"navbar-nav\", \"flex-grow-1\"], [\"class\", \"nav-item\", 3, \"routerLinkActive\", 4, \"ngIf\"], [1, \"nav-item\", \"dropdown\", 3, \"routerLinkActive\"], [\"href\", \"#\", \"id\", \"usersDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-people\", \"me-1\"], [\"aria-labelledby\", \"usersDropdown\", 1, \"dropdown-menu\"], [4, \"ngIf\"], [1, \"dropdown-item\", 3, \"routerLink\", \"click\"], [1, \"bi\", \"bi-people\", \"me-2\"], [\"href\", \"#\", \"id\", \"disDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-code-square\", \"me-1\"], [\"aria-labelledby\", \"disDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-code-square\", \"me-2\"], [\"class\", \"nav-item dropdown\", 3, \"routerLinkActive\", 4, \"ngIf\"], [1, \"nav-item\", 3, \"routerLinkActive\"], [1, \"nav-link\", 3, \"routerLink\"], [1, \"bi\", \"bi-speedometer2\", \"me-1\"], [1, \"bi\", \"bi-person-lock\", \"me-2\"], [1, \"bi\", \"bi-card-checklist\", \"me-2\"], [1, \"bi\", \"bi-speedometer\", \"me-2\"], [\"href\", \"#\", \"id\", \"securityDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-shield-lock\", \"me-1\"], [\"aria-labelledby\", \"securityDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"bi\", \"bi-graph-up\", \"me-2\"], [1, \"bi\", \"bi-bell\", \"me-2\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"href\", \"#\", \"id\", \"adminDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-gear\", \"me-1\"], [\"aria-labelledby\", \"adminDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-shield-lock\", \"me-2\"], [1, \"bi\", \"bi-hdd-network\", \"me-2\"], [1, \"bi\", \"bi-arrow-repeat\", \"me-2\"], [1, \"nav-item\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"theme-toggle-btn\", 3, \"title\", \"click\"], [1, \"bi\", 3, \"ngClass\"], [1, \"nav-item\", \"dropdown\"], [\"href\", \"#\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-person-circle\", \"me-1\"], [\"aria-labelledby\", \"navbarDropdown\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"bi\", \"bi-person\", \"me-2\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-box-arrow-right\", \"me-2\"], [1, \"nav-item\"], [1, \"bi\", \"bi-box-arrow-in-right\", \"me-1\"], [1, \"login-right\", 3, \"ngClass\"]],\n      template: function NavMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\")(1, \"nav\", 0)(2, \"div\", 1)(3, \"div\", 2)(4, \"a\", 3);\n          i0.ɵɵelement(5, \"i\", 4);\n          i0.ɵɵelementStart(6, \"span\");\n          i0.ɵɵtext(7, \"DIS Admin\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, NavMenuComponent_button_8_Template, 2, 1, \"button\", 5);\n          i0.ɵɵtemplate(9, NavMenuComponent_div_9_Template, 6, 7, \"div\", 6);\n          i0.ɵɵtemplate(10, NavMenuComponent_div_10_Template, 3, 5, \"div\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c18, ctx.isLoginPage));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c19, ctx.isLoginPage));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(10, _c20));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoginPage);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i2.RouterLink, i2.RouterLinkActive],\n      styles: [\"a.navbar-brand[_ngcontent-%COMP%] {\\r\\n  white-space: normal;\\r\\n  text-align: center;\\r\\n  word-break: break-all;\\r\\n}\\r\\n\\r\\nhtml[_ngcontent-%COMP%] {\\r\\n  font-size: 14px;\\r\\n}\\r\\n@media (min-width: 768px) {\\r\\n  html[_ngcontent-%COMP%] {\\r\\n    font-size: 16px;\\r\\n  }\\r\\n}\\r\\n\\r\\n.box-shadow[_ngcontent-%COMP%] {\\r\\n  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);\\r\\n}\\r\\n\\r\\n\\r\\n.btn-link[_ngcontent-%COMP%] {\\r\\n  text-decoration: none;\\r\\n  padding: 0.5rem 1rem;\\r\\n  color: rgba(255, 255, 255, 0.85);\\r\\n  background: transparent;\\r\\n  border: none;\\r\\n  text-align: left;\\r\\n}\\r\\n\\r\\n.btn-link[_ngcontent-%COMP%]:hover, .btn-link[_ngcontent-%COMP%]:focus {\\r\\n  color: white;\\r\\n  background-color: rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n.nav-link.dropdown-toggle.btn-link[_ngcontent-%COMP%] {\\r\\n  padding: 0.5rem 1rem;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n\\r\\n.dropdown-menu[_ngcontent-%COMP%] {\\r\\n  border-radius: 0.25rem;\\r\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\r\\n  border: none;\\r\\n  padding: 0.5rem 0;\\r\\n  display: none;\\r\\n}\\r\\n\\r\\n.dropdown-menu.show[_ngcontent-%COMP%] {\\r\\n  display: block;\\r\\n}\\r\\n\\r\\n.dropdown-item[_ngcontent-%COMP%] {\\r\\n  padding: 0.5rem 1rem;\\r\\n  font-size: 0.9rem;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\r\\n  margin-right: 0.5rem;\\r\\n}\\r\\n\\r\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\\r\\n}\\r\\n\\r\\n.dropdown-toggle[_ngcontent-%COMP%]::after {\\r\\n  margin-left: 0.5rem;\\r\\n  vertical-align: middle;\\r\\n}\\r\\n\\r\\n\\r\\n@media (max-width: 767.98px) {\\r\\n  .dropdown-menu[_ngcontent-%COMP%] {\\r\\n    border: none;\\r\\n    box-shadow: none;\\r\\n    padding-left: 1rem;\\r\\n    background-color: transparent;\\r\\n  }\\r\\n\\r\\n  .dropdown-item[_ngcontent-%COMP%] {\\r\\n    color: rgba(255, 255, 255, 0.8);\\r\\n  }\\r\\n\\r\\n  .dropdown-item[_ngcontent-%COMP%]:hover {\\r\\n    color: #fff;\\r\\n    background-color: transparent;\\r\\n  }\\r\\n\\r\\n  .dropdown-divider[_ngcontent-%COMP%] {\\r\\n    border-color: rgba(255, 255, 255, 0.2);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\r\\n  background-color: #343a40;\\r\\n  color: #fff;\\r\\n}\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\r\\n  color: #f8f9fa;\\r\\n}\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.1);\\r\\n}\\r\\n\\r\\n.dark-theme[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\r\\n  border-color: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n\\r\\n.login-container[_ngcontent-%COMP%] {\\r\\n  max-width: 1140px;\\r\\n  margin: 0 auto;\\r\\n  padding: 0 15px;\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n.login-left[_ngcontent-%COMP%], .login-right[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.login-left-aligned[_ngcontent-%COMP%] {\\r\\n  margin-left: calc(50% - 360px); \\r\\n}\\r\\n\\r\\n.login-right-aligned[_ngcontent-%COMP%] {\\r\\n  margin-right: calc(50% - 360px); \\r\\n}\\r\\n\\r\\n.login-container[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%] {\\r\\n  margin-right: 0;\\r\\n  padding-left: 0;\\r\\n}\\r\\n\\r\\n@media (max-width: 767px) {\\r\\n  .login-left-aligned[_ngcontent-%COMP%], .login-right-aligned[_ngcontent-%COMP%] {\\r\\n    margin-left: 0;\\r\\n    margin-right: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 768px) and (max-width: 991px) {\\r\\n  .login-left-aligned[_ngcontent-%COMP%] {\\r\\n    margin-left: calc(50% - 270px); \\r\\n  }\\r\\n\\r\\n  .login-right-aligned[_ngcontent-%COMP%] {\\r\\n    margin-right: calc(50% - 270px); \\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 992px) and (max-width: 1199px) {\\r\\n  .login-left-aligned[_ngcontent-%COMP%] {\\r\\n    margin-left: calc(50% - 320px); \\r\\n  }\\r\\n\\r\\n  .login-right-aligned[_ngcontent-%COMP%] {\\r\\n    margin-right: calc(50% - 320px); \\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 1200px) {\\r\\n  .login-left-aligned[_ngcontent-%COMP%] {\\r\\n    margin-left: calc(50% - 360px); \\r\\n  }\\r\\n\\r\\n  .login-right-aligned[_ngcontent-%COMP%] {\\r\\n    margin-right: calc(50% - 360px); \\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAA2CA,SAAS,QAAmB,eAAe;AACtF,SAAiBC,aAAa,QAAQ,iBAAiB;AAIvD,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;ICCjCC,iCASC;IADCA;MAAAA;MAAA;MAAA,OAASA,8BAAQ;IAAA,EAAC;IAElBA,0BAAyC;IAC3CA,iBAAS;;;;IAJPA,kDAAiC;;;;;;;;;;;IAY/BA,8BAA0E;IACtBA,wBAAuC;IAAAA,yBAAS;IAAAA,iBAAI;;;IADnFA,6DAAoC;IACnCA,eAA6B;IAA7BA,uDAA6B;;;;;;;;;IAS/CA,0BAAoB;IAAmDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAsC;IAAAA,mCAAS;IAAAA,iBAAI;;;IAAlGA,eAAyB;IAAzBA,uDAAyB;;;;;;;;;IAYtEA,0BAAoB;IAA0DA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAyC;IAAAA,iDAAkB;IAAAA,iBAAI;;;IAArHA,eAAgC;IAAhCA,uDAAgC;;;;;;;;;IAC7EA,0BAAoB;IAAyDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAsC;IAAAA,8BAAS;IAAAA,iBAAI;;;IAAxGA,eAA+B;IAA/BA,uDAA+B;;;;;;;;;;;;;;;;;;IAKhFA,8BAAmF;IAE/EA,wBAAsC;IAAAA,gCACxC;IAAAA,iBAAI;IACJA,8BAA6D;IACDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAA6C;IAAAA,oDAAqB;IAAAA,iBAAI;IACrJA,0BAAI;IAAwDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAmC;IAAAA,2BAAU;IAAAA,iBAAI;IAClIA,2BAAI;IAAoDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAA+B;IAAAA,uBAAM;IAAAA,iBAAI;IACtHA,2BAAI;IAAkDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAuC;IAAAA,kCAAY;IAAAA,iBAAI;;;IARxGA,6DAAoC;IAKjCA,eAA4B;IAA5BA,uDAA4B;IAC5BA,eAA8B;IAA9BA,uDAA8B;IAC9BA,eAA0B;IAA1BA,uDAA0B;IAC1BA,eAAwB;IAAxBA,uDAAwB;;;;;;;;;;;;;;;IAKzDA,8BAAmF;IAE/EA,wBAA+B;IAAAA,6BACjC;IAAAA,iBAAI;IACJA,8BAA0D;IACkBA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAsC;IAAAA,8CAAoB;IAAAA,iBAAI;IAC7JA,0BAAI;IAAiEA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAsC;IAAAA,6BAAY;IAAAA,iBAAI;IAChJA,2BAAI;IAAkEA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAuC;IAAAA,iDAAsB;IAAAA,iBAAI;;;IAPlIA,6DAAoC;IAKjCA,eAA4C;IAA5CA,uDAA4C;IAC5CA,eAAuC;IAAvCA,wDAAuC;IACvCA,eAAwC;IAAxCA,wDAAwC;;;;;;;;;;;;IAlD3EA,8BAAsD;IAEpDA,2EAEK;IAGLA,8BAAmE;IAE/DA,wBAAiC;IAAAA,4BACnC;IAAAA,iBAAI;IACJA,8BAA0D;IACxDA,2EAAoJ;IACpJA,0BAAI;IAAuDA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAiC;IAAAA,oCAAS;IAAAA,iBAAI;IAKlIA,+BAAmE;IAE/DA,yBAAsC;IAAAA,qBACxC;IAAAA,iBAAI;IACJA,+BAAwD;IACIA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,yBAAsC;IAAAA,0BAAS;IAAAA,iBAAI;IAClIA,6EAAuK;IACvKA,6EAA0J;IAC5JA,iBAAK;IAIPA,+EAUK;IAGLA,8EASK;IAEPA,iBAAK;;;;IApDwDA,eAAa;IAAbA,qCAAa;IAK1CA,eAAoC;IAApCA,8DAAoC;IAKzDA,eAAa;IAAbA,qCAAa;IACWA,eAA6B;IAA7BA,yDAA6B;IAKhCA,eAAoC;IAApCA,8DAAoC;IAKjCA,eAA4B;IAA5BA,yDAA4B;IACpDA,eAAa;IAAbA,qCAAa;IACbA,eAAa;IAAbA,qCAAa;IAK8CA,eAAa;IAAbA,qCAAa;IAabA,eAAa;IAAbA,qCAAa;;;;;;IAcjFA,8BAA+C;IACDA;MAAAA;MAAA;MAAA,OAASA,oCAAa;IAAA,EAAC;IACjEA,wBAAgF;IAClFA,iBAAS;;;;IAF2DA,eAAiF;IAAjFA,gJAAiF;IACrIA,eAA6D;IAA7DA,kFAA6D;;;;;;;;;IAG/EA,8BAAiD;IAE7CA,wBAAwC;IACxCA,YACF;IAAAA,iBAAI;IACJA,8BAA6E;IAClBA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IAACA,wBAAiC;IAAAA,+BAAU;IAAAA,iBAAI;IAC7HA,0BAAI;IAAAA,0BAA6B;IAAAA,iBAAK;IACtCA,2BAAI;IAAyBA;MAAAA;MAAA;MAAA,OAASA,+BAAQ;IAAA,EAAC;IAA0BA,yBAA0C;IAAAA,iCAAW;IAAAA,iBAAI;;;;IALlIA,eACF;IADEA,uKACF;IAE+BA,eAA2B;IAA3BA,wDAA2B;;;;;;;;IAM5DA,8BAAyD;IACTA,wBAA6C;IAAAA,sCAAY;IAAAA,iBAAI;;;IAAvFA,eAAyB;IAAzBA,wDAAyB;;;;;;;;;;IAhFnDA,+BAIC;IACCA,wEAsDK;IACLA,8BAA0C;IAExCA,sEAIK;IACLA,uEAUK;IAELA,sEAEK;IACPA,iBAAK;;;;IA/ELA,wEAAgC;IAEIA,eAAgB;IAAhBA,wCAAgB;IAyDvBA,eAAkB;IAAlBA,0CAAkB;IAKdA,eAAgB;IAAhBA,wCAAgB;IAYzBA,eAAiC;IAAjCA,gEAAiC;;;;;;;;;;;IAM3DA,+BAA8F;IAChDA;MAAAA;MAAA;MAAA,OAASA,oCAAa;IAAA,EAAC;IACjEA,wBAAgF;IAClFA,iBAAS;;;;IAHcA,yEAAgD;IACHA,eAAiF;IAAjFA,gJAAiF;IACrIA,eAA6D;IAA7DA,kFAA6D;;;;;;;;;;;;;;;;AD7FrF,OAAM,MAAOC,gBAAgB;EAS3BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,YAA0B,EAC1BC,QAAmB;IAHnB,gBAAW,GAAXH,WAAW;IACX,WAAM,GAANC,MAAM;IACN,iBAAY,GAAZC,YAAY;IACZ,aAAQ,GAARC,QAAQ;IAZlB,eAAU,GAAG,KAAK;IAClB,gBAAW,GAAgB,IAAI;IAC/B,eAAU,GAAG,KAAK;IAClB,YAAO,GAAG,KAAK;IACf,eAAU,GAAG,KAAK;IAClB,cAAS,GAAGT,SAAS,EAAE;IACvB,gBAAW,GAAG,KAAK;IAQjB,IAAI,CAACM,WAAW,CAACI,WAAW,CAACC,SAAS,CAACC,IAAI,IAAG;MAC5C,IAAI,CAACF,WAAW,GAAGE,IAAI;MACvB,IAAI,CAACC,UAAU,GAAG,CAAC,CAACD,IAAI;MACxB,IAAI,CAACE,OAAO,GAAGF,IAAI,EAAEE,OAAO,IAAI,KAAK;IACvC,CAAC,CAAC;IAEF;IACA,IAAI,CAACP,MAAM,CAACQ,MAAM,CAACC,IAAI,CACrBd,MAAM,CAACe,KAAK,IAAIA,KAAK,YAAYhB,aAAa,CAAC,CAChD,CAACU,SAAS,CAAC,MAAK;MACf,IAAI,CAACO,QAAQ,EAAE;IACjB,CAAC,CAAC;EACJ;EAEAC,QAAQ;IACN;IACA,IAAI,CAACC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;IAC7D,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACjB,MAAM,CAACkB,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC;IACvDC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACJ,WAAW,EAAE,MAAM,EAAE,IAAI,CAACjB,MAAM,CAACkB,GAAG,CAAC;IAE9E;IACA,IAAI,CAAClB,MAAM,CAACQ,MAAM,CAACC,IAAI,CACrBd,MAAM,CAACe,KAAK,IAAIA,KAAK,YAAYhB,aAAa,CAAC,CAChD,CAACU,SAAS,CAAEM,KAAU,IAAI;MACzB,IAAI,CAACO,WAAW,GAAGP,KAAK,CAACQ,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC;MACjDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACJ,WAAW,EAAE,MAAM,EAAEP,KAAK,CAACQ,GAAG,CAAC;IAC7E,CAAC,CAAC;EACJ;EAEAP,QAAQ;IACN,IAAI,CAACW,UAAU,GAAG,KAAK;EACzB;EAEAC,MAAM;IACJ,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAE,MAAM;IACJ,IAAI,CAACzB,WAAW,CAACyB,MAAM,EAAE;IACzB,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,WAAW;IACT,IAAI,CAACb,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClCC,YAAY,CAACa,OAAO,CAAC,UAAU,EAAE,IAAI,CAACd,UAAU,CAACe,QAAQ,EAAE,CAAC;IAC5D,IAAI,CAACZ,UAAU,EAAE;EACnB;EAEQA,UAAU;IAChB,IAAI,IAAI,CAACH,UAAU,EAAE;MACnBgB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;KAC1C,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;;EAEhD;;;uBAxEWpC,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAqC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCZ7B1C,8BAAQ;UAI6CA,uBAAwC;UAAAA,4BAAM;UAAAA,yBAAS;UAAAA,iBAAO;UAE7GA,uEAWS;UACTA,iEAmFM;UAENA,mEAIM;UACRA,iBAAM;;;UA1GiBA,eAA4C;UAA5CA,sEAA4C;UACzCA,eAA+C;UAA/CA,sEAA+C;UAC7CA,eAAoB;UAApBA,yDAAoB;UAG3CA,eAAkB;UAAlBA,uCAAkB;UAYlBA,eAAkB;UAAlBA,uCAAkB;UAoFsDA,eAAiB;UAAjBA,sCAAiB", "names": ["isDevMode", "NavigationEnd", "filter", "i0", "NavMenuComponent", "constructor", "authService", "router", "modalService", "renderer", "currentUser", "subscribe", "user", "isLoggedIn", "isAdmin", "events", "pipe", "event", "collapse", "ngOnInit", "isDarkMode", "localStorage", "getItem", "applyTheme", "isLoginPage", "url", "startsWith", "console", "log", "isExpanded", "toggle", "logout", "navigate", "toggleTheme", "setItem", "toString", "document", "body", "classList", "add", "remove", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\nav-menu\\nav-menu.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\nav-menu\\nav-menu.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, isDevMode, Renderer2 } from '@angular/core';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { ModalService } from '../services/modal.service';\r\nimport { User } from '../models/user.model';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-nav-menu',\r\n  templateUrl: './nav-menu.component.html',\r\n  styleUrls: ['./nav-menu.component.css']\r\n})\r\nexport class NavMenuComponent implements OnInit {\r\n  isExpanded = false;\r\n  currentUser: User | null = null;\r\n  isLoggedIn = false;\r\n  isAdmin = false;\r\n  isDarkMode = false;\r\n  isDevMode = isDevMode();\r\n  isLoginPage = false;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private modalService: ModalService,\r\n    private renderer: Renderer2\r\n  ) {\r\n    this.authService.currentUser.subscribe(user => {\r\n      this.currentUser = user;\r\n      this.isLoggedIn = !!user;\r\n      this.isAdmin = user?.isAdmin || false;\r\n    });\r\n\r\n    // Zavřít rozbalovací menu při navigaci\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe(() => {\r\n      this.collapse();\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Kontrola, zda je nastaven tmavý režim v localStorage\r\n    this.isDarkMode = localStorage.getItem('darkMode') === 'true';\r\n    this.applyTheme();\r\n\r\n    // Kontrola, zda jsme na přihlašovací stránce - použijeme startsWith pro detekci i s parametry\r\n    this.isLoginPage = this.router.url.startsWith('/login');\r\n    console.log('Initial isLoginPage:', this.isLoginPage, 'URL:', this.router.url);\r\n\r\n    // Sledování změn URL\r\n    this.router.events.pipe(\r\n      filter(event => event instanceof NavigationEnd)\r\n    ).subscribe((event: any) => {\r\n      this.isLoginPage = event.url.startsWith('/login');\r\n      console.log('Navigation isLoginPage:', this.isLoginPage, 'URL:', event.url);\r\n    });\r\n  }\r\n\r\n  collapse() {\r\n    this.isExpanded = false;\r\n  }\r\n\r\n  toggle() {\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout();\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  toggleTheme() {\r\n    this.isDarkMode = !this.isDarkMode;\r\n    localStorage.setItem('darkMode', this.isDarkMode.toString());\r\n    this.applyTheme();\r\n  }\r\n\r\n  private applyTheme() {\r\n    if (this.isDarkMode) {\r\n      document.body.classList.add('dark-theme');\r\n    } else {\r\n      document.body.classList.remove('dark-theme');\r\n    }\r\n  }\r\n\r\n\r\n}\r\n", "<header>\r\n  <nav class=\"navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary box-shadow mb-3\">\r\n    <div class=\"container\" [ngClass]=\"{'login-container': isLoginPage}\">\r\n      <div class=\"login-left\" [ngClass]=\"{'login-left-aligned': isLoginPage}\">\r\n        <a class=\"navbar-brand\" [routerLink]=\"['/']\"><i class=\"bi bi-database-gear me-2\"></i><span>DIS Admin</span></a>\r\n      </div>\r\n      <button\r\n        *ngIf=\"!isLoginPage\"\r\n        class=\"navbar-toggler\"\r\n        type=\"button\"\r\n        data-bs-toggle=\"collapse\"\r\n        data-bs-target=\".navbar-collapse\"\r\n        aria-label=\"Toggle navigation\"\r\n        [attr.aria-expanded]=\"isExpanded\"\r\n        (click)=\"toggle()\"\r\n      >\r\n        <span class=\"navbar-toggler-icon\"></span>\r\n      </button>\r\n      <div\r\n        *ngIf=\"!isLoginPage\"\r\n        class=\"navbar-collapse collapse d-sm-inline-flex justify-content-between\"\r\n        [ngClass]=\"{ show: isExpanded }\"\r\n      >\r\n        <ul class=\"navbar-nav flex-grow-1\" *ngIf=\"isLoggedIn\">\r\n\r\n          <li class=\"nav-item\" [routerLinkActive]=\"['link-active']\" *ngIf=\"isAdmin\">\r\n            <a class=\"nav-link\" [routerLink]=\"['/dashboard']\"><i class=\"bi bi-speedometer2 me-1\"></i>Dashboard</a>\r\n          </li>\r\n\r\n          <!-- Správa uživatelů a zákazníků -->\r\n          <li class=\"nav-item dropdown\" [routerLinkActive]=\"['link-active']\">\r\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"usersDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n              <i class=\"bi bi-people me-1\"></i>Správa\r\n            </a>\r\n            <ul class=\"dropdown-menu\" aria-labelledby=\"usersDropdown\">\r\n              <li *ngIf=\"isAdmin\"><a class=\"dropdown-item\" [routerLink]=\"['/users']\" (click)=\"collapse()\"><i class=\"bi bi-person-lock me-2\"></i>Uživatelé</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/customers']\" (click)=\"collapse()\"><i class=\"bi bi-people me-2\"></i>Zákazníci</a></li>\r\n            </ul>\r\n          </li>\r\n\r\n          <!-- Správa DIS -->\r\n          <li class=\"nav-item dropdown\" [routerLinkActive]=\"['link-active']\">\r\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"disDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n              <i class=\"bi bi-code-square me-1\"></i>DIS\r\n            </a>\r\n            <ul class=\"dropdown-menu\" aria-labelledby=\"disDropdown\">\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/versions']\" (click)=\"collapse()\"><i class=\"bi bi-code-square me-2\"></i>Verze DIS</a></li>\r\n              <li *ngIf=\"isAdmin\"><a class=\"dropdown-item\" [routerLink]=\"['/certificates']\" (click)=\"collapse()\"><i class=\"bi bi-card-checklist me-2\"></i>Správa certifikátů</a></li>\r\n              <li *ngIf=\"isAdmin\"><a class=\"dropdown-item\" [routerLink]=\"['/performance']\" (click)=\"collapse()\"><i class=\"bi bi-speedometer me-2\"></i>Výkon DIS</a></li>\r\n            </ul>\r\n          </li>\r\n\r\n          <!-- Bezpečnost a monitoring - pouze pro administrátory -->\r\n          <li class=\"nav-item dropdown\" [routerLinkActive]=\"['link-active']\" *ngIf=\"isAdmin\">\r\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"securityDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n              <i class=\"bi bi-shield-lock me-1\"></i>Bezpečnost\r\n            </a>\r\n            <ul class=\"dropdown-menu\" aria-labelledby=\"securityDropdown\">\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/security']\" (click)=\"collapse()\"><i class=\"bi bi-shield-exclamation me-2\"></i>Bezpečnostní události</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/monitoring']\" (click)=\"collapse()\"><i class=\"bi bi-graph-up me-2\"></i>Monitoring</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/alerts']\" (click)=\"collapse()\"><i class=\"bi bi-bell me-2\"></i>Alerty</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/logs']\" (click)=\"collapse()\"><i class=\"bi bi-journal-text me-2\"></i>Logy systému</a></li>\r\n            </ul>\r\n          </li>\r\n\r\n          <!-- Administrace -->\r\n          <li class=\"nav-item dropdown\" [routerLinkActive]=\"['link-active']\" *ngIf=\"isAdmin\">\r\n            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"adminDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n              <i class=\"bi bi-gear me-1\"></i>Administrace\r\n            </a>\r\n            <ul class=\"dropdown-menu\" aria-labelledby=\"adminDropdown\">\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/admin/server-certificate']\" (click)=\"collapse()\"><i class=\"bi bi-shield-lock me-2\"></i>Serverový certifikát</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/admin/disapi-status']\" (click)=\"collapse()\"><i class=\"bi bi-hdd-network me-2\"></i>Stav DIS API</a></li>\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/certificate-rotation']\" (click)=\"collapse()\"><i class=\"bi bi-arrow-repeat me-2\"></i>Rotace DIS certifikátů</a></li>\r\n            </ul>\r\n          </li>\r\n\r\n        </ul>\r\n        <ul class=\"navbar-nav align-items-center\">\r\n          <!-- Tlačítko pro přepnutí tématu - vždy viditelné (kromě login page) -->\r\n          <li class=\"nav-item me-2\" *ngIf=\"!isLoginPage\">\r\n            <button class=\"btn btn-sm theme-toggle-btn\" (click)=\"toggleTheme()\" title=\"{{ isDarkMode ? 'Přepnout na světlý režim' : 'Přepnout na tmavý režim' }}\">\r\n              <i class=\"bi\" [ngClass]=\"isDarkMode ? 'bi-sun-fill' : 'bi-moon-stars-fill'\"></i>\r\n            </button>\r\n          </li>\r\n          <li class=\"nav-item dropdown\" *ngIf=\"isLoggedIn\">\r\n            <a class=\"nav-link dropdown-toggle d-flex align-items-center\" href=\"#\" id=\"navbarDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n              <i class=\"bi bi-person-circle me-1\"></i>\r\n              {{ currentUser?.firstName }} {{ currentUser?.lastName }}\r\n            </a>\r\n            <ul class=\"dropdown-menu dropdown-menu-end\" aria-labelledby=\"navbarDropdown\">\r\n              <li><a class=\"dropdown-item\" [routerLink]=\"['/profile']\" (click)=\"collapse()\"><i class=\"bi bi-person me-2\"></i>Můj profil</a></li>\r\n              <li><hr class=\"dropdown-divider\"></li>\r\n              <li><a class=\"dropdown-item\" (click)=\"logout()\" style=\"cursor: pointer;\"><i class=\"bi bi-box-arrow-right me-2\"></i>Odhlásit se</a></li>\r\n            </ul>\r\n          </li>\r\n          <!-- Tlačítko pro přihlášení - skryto na přihlašovací stránce -->\r\n          <li class=\"nav-item\" *ngIf=\"!isLoggedIn && !isLoginPage\">\r\n            <a class=\"nav-link\" [routerLink]=\"['/login']\"><i class=\"bi bi-box-arrow-in-right me-1\"></i>Přihlásit se</a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <!-- Tlačítko pro přepnutí tématu - pouze pro login page -->\r\n      <div class=\"login-right\" [ngClass]=\"{'login-right-aligned': isLoginPage}\" *ngIf=\"isLoginPage\">\r\n        <button class=\"btn btn-sm theme-toggle-btn\" (click)=\"toggleTheme()\" title=\"{{ isDarkMode ? 'Přepnout na světlý režim' : 'Přepnout na tmavý režim' }}\">\r\n          <i class=\"bi\" [ngClass]=\"isDarkMode ? 'bi-sun-fill' : 'bi-moon-stars-fill'\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nav>\r\n</header>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}