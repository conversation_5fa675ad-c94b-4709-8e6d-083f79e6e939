<header>
  <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-primary box-shadow mb-3">
    <div class="container" [ngClass]="{'login-container': isLoginPage}">
      <div class="login-left" [ngClass]="{'login-left-aligned': isLoginPage}">
        <a class="navbar-brand" [routerLink]="['/']"><i class="bi bi-database-gear me-2"></i><span>DIS Admin</span></a>
      </div>
      <button
        *ngIf="!isLoginPage"
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target=".navbar-collapse"
        aria-label="Toggle navigation"
        [attr.aria-expanded]="isExpanded"
        (click)="toggle()"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div
        *ngIf="!isLoginPage"
        class="navbar-collapse collapse d-sm-inline-flex justify-content-between"
        [ngClass]="{ show: isExpanded }"
      >
        <ul class="navbar-nav flex-grow-1" *ngIf="isLoggedIn">

          <li class="nav-item" [routerLinkActive]="['link-active']" *ngIf="isAdmin">
            <a class="nav-link" [routerLink]="['/dashboard']"><i class="bi bi-speedometer2 me-1"></i>Dashboard</a>
          </li>

          <!-- Správa uživatelů a zákazníků -->
          <li class="nav-item dropdown" [routerLinkActive]="['link-active']">
            <a class="nav-link dropdown-toggle" href="#" id="usersDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-people me-1"></i>Správa
            </a>
            <ul class="dropdown-menu" aria-labelledby="usersDropdown">
              <li *ngIf="isAdmin"><a class="dropdown-item" [routerLink]="['/users']" (click)="collapse()"><i class="bi bi-person-lock me-2"></i>Uživatelé</a></li>
              <li><a class="dropdown-item" [routerLink]="['/customers']" (click)="collapse()"><i class="bi bi-people me-2"></i>Zákazníci</a></li>
            </ul>
          </li>

          <!-- Správa DIS -->
          <li class="nav-item dropdown" [routerLinkActive]="['link-active']">
            <a class="nav-link dropdown-toggle" href="#" id="disDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-code-square me-1"></i>DIS
            </a>
            <ul class="dropdown-menu" aria-labelledby="disDropdown">
              <li><a class="dropdown-item" [routerLink]="['/versions']" (click)="collapse()"><i class="bi bi-code-square me-2"></i>Verze DIS</a></li>
              <li *ngIf="isAdmin"><a class="dropdown-item" [routerLink]="['/certificates']" (click)="collapse()"><i class="bi bi-card-checklist me-2"></i>Správa certifikátů</a></li>
              <li *ngIf="isAdmin"><a class="dropdown-item" [routerLink]="['/performance']" (click)="collapse()"><i class="bi bi-speedometer me-2"></i>Výkon DIS</a></li>
            </ul>
          </li>

          <!-- Bezpečnost a monitoring - pouze pro administrátory -->
          <li class="nav-item dropdown" [routerLinkActive]="['link-active']" *ngIf="isAdmin">
            <a class="nav-link dropdown-toggle" href="#" id="securityDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-shield-lock me-1"></i>Bezpečnost
            </a>
            <ul class="dropdown-menu" aria-labelledby="securityDropdown">
              <li><a class="dropdown-item" [routerLink]="['/security']" (click)="collapse()"><i class="bi bi-shield-exclamation me-2"></i>Bezpečnostní události</a></li>
              <li><a class="dropdown-item" [routerLink]="['/monitoring']" (click)="collapse()"><i class="bi bi-graph-up me-2"></i>Monitoring</a></li>
              <li><a class="dropdown-item" [routerLink]="['/alerts']" (click)="collapse()"><i class="bi bi-bell me-2"></i>Alerty</a></li>
              <li><a class="dropdown-item" [routerLink]="['/logs']" (click)="collapse()"><i class="bi bi-journal-text me-2"></i>Logy systému</a></li>
            </ul>
          </li>

          <!-- Administrace -->
          <li class="nav-item dropdown" [routerLinkActive]="['link-active']" *ngIf="isAdmin">
            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-gear me-1"></i>Administrace
            </a>
            <ul class="dropdown-menu" aria-labelledby="adminDropdown">
              <li><a class="dropdown-item" [routerLink]="['/admin/server-certificate']" (click)="collapse()"><i class="bi bi-shield-lock me-2"></i>Serverový certifikát</a></li>
              <li><a class="dropdown-item" [routerLink]="['/admin/disapi-status']" (click)="collapse()"><i class="bi bi-hdd-network me-2"></i>Stav DIS API</a></li>
              <li><a class="dropdown-item" [routerLink]="['/certificate-rotation']" (click)="collapse()"><i class="bi bi-arrow-repeat me-2"></i>Rotace DIS certifikátů</a></li>
            </ul>
          </li>

        </ul>
        <ul class="navbar-nav align-items-center">
          <!-- Tlačítko pro přepnutí tématu - vždy viditelné (kromě login page) -->
          <li class="nav-item me-2" *ngIf="!isLoginPage">
            <button class="btn btn-sm theme-toggle-btn" (click)="toggleTheme()" title="{{ isDarkMode ? 'Přepnout na světlý režim' : 'Přepnout na tmavý režim' }}">
              <i class="bi" [ngClass]="isDarkMode ? 'bi-sun-fill' : 'bi-moon-stars-fill'"></i>
            </button>
          </li>
          <li class="nav-item dropdown" *ngIf="isLoggedIn">
            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-person-circle me-1"></i>
              {{ currentUser?.firstName }} {{ currentUser?.lastName }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
              <li><a class="dropdown-item" [routerLink]="['/profile']" (click)="collapse()"><i class="bi bi-person me-2"></i>Můj profil</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" (click)="logout()" style="cursor: pointer;"><i class="bi bi-box-arrow-right me-2"></i>Odhlásit se</a></li>
            </ul>
          </li>
          <!-- Tlačítko pro přihlášení - skryto na přihlašovací stránce -->
          <li class="nav-item" *ngIf="!isLoggedIn && !isLoginPage">
            <a class="nav-link" [routerLink]="['/login']"><i class="bi bi-box-arrow-in-right me-1"></i>Přihlásit se</a>
          </li>
        </ul>
      </div>
      <!-- Tlačítko pro přepnutí tématu - pouze pro login page -->
      <div class="login-right" [ngClass]="{'login-right-aligned': isLoginPage}" *ngIf="isLoginPage">
        <button class="btn btn-sm theme-toggle-btn" (click)="toggleTheme()" title="{{ isDarkMode ? 'Přepnout na světlý režim' : 'Přepnout na tmavý režim' }}">
          <i class="bi" [ngClass]="isDarkMode ? 'bi-sun-fill' : 'bi-moon-stars-fill'"></i>
        </button>
      </div>
    </div>
  </nav>
</header>
