{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { InstanceStatus } from '../../models/instance.model';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/customer.service\";\nimport * as i3 from \"../../services/contact.service\";\nimport * as i4 from \"../../services/instance.service\";\nimport * as i5 from \"../../services/instance-version.service\";\nimport * as i6 from \"../../services/version.service\";\nimport * as i7 from \"../../services/user.service\";\nimport * as i8 from \"../../services/certificate.service\";\nimport * as i9 from \"../../services/breadcrumb.service\";\nimport * as i10 from \"../../services/modal.service\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"../../services/auth.service\";\nimport * as i13 from \"../../services/clipboard.service\";\nimport * as i14 from \"@angular/common\";\nimport * as i15 from \"../../shared/certificate-modal/certificate-modal.component\";\nimport * as i16 from \"../../shared/instance-detail/instance-detail.component\";\nimport * as i17 from \"../../shared/pipes/local-date.pipe\";\nfunction CustomerDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.deleteCustomer());\n    });\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵtext(3, \" Smazat z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.editCustomer());\n    });\n    i0.ɵɵelement(5, \"i\", 84);\n    i0.ɵɵtext(6, \" Upravit z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerDetailComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_11_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_11_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r30.customerForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r30.customerForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zkratka je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zkratka nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_16_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_16_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r31.customerForm.get(\"abbreviation\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r31.customerForm.get(\"abbreviation\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_22_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"I\\u010CO nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_22_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r32.customerForm.get(\"companyId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_27_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"DI\\u010C nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_27_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r33.customerForm.get(\"taxId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_33_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_33_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r34.customerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r34.customerForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_38_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Telefon nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_38_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r35.customerForm.get(\"phone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_44_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Web nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_44_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r36.customerForm.get(\"website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Ulice je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Ulice nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_51_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_51_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r37.customerForm.get(\"street\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r37.customerForm.get(\"street\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"M\\u011Bsto je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"M\\u011Bsto nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_57_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_57_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r38.customerForm.get(\"city\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r38.customerForm.get(\"city\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"PS\\u010C je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"PS\\u010C nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 20 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_62_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_62_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r39.customerForm.get(\"postalCode\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r39.customerForm.get(\"postalCode\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zem\\u011B je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Zem\\u011B nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_67_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_10_div_67_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r40.customerForm.get(\"country\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r40.customerForm.get(\"country\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_div_72_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_10_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_10_div_72_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r41.customerForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_10_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction CustomerDetailComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90)(2, \"h5\", 91);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"form\", 18);\n    i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_div_10_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.saveCustomer());\n    });\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"div\", 20)(8, \"label\", 46);\n    i0.ɵɵtext(9, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 93);\n    i0.ɵɵtemplate(11, CustomerDetailComponent_div_10_div_11_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"label\", 94);\n    i0.ɵɵtext(14, \"Zkratka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 95);\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_10_div_16_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"div\", 20)(19, \"label\", 96);\n    i0.ɵɵtext(20, \"I\\u010CO\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 97);\n    i0.ɵɵtemplate(22, CustomerDetailComponent_div_10_div_22_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 20)(24, \"label\", 98);\n    i0.ɵɵtext(25, \"DI\\u010C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 99);\n    i0.ɵɵtemplate(27, CustomerDetailComponent_div_10_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 19)(29, \"div\", 20)(30, \"label\", 100);\n    i0.ɵɵtext(31, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 101);\n    i0.ɵɵtemplate(33, CustomerDetailComponent_div_10_div_33_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 20)(35, \"label\", 102);\n    i0.ɵɵtext(36, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 103);\n    i0.ɵɵtemplate(38, CustomerDetailComponent_div_10_div_38_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 20)(41, \"label\", 104);\n    i0.ɵɵtext(42, \"Web\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 105);\n    i0.ɵɵtemplate(44, CustomerDetailComponent_div_10_div_44_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"div\", 106)(48, \"label\", 107);\n    i0.ɵɵtext(49, \"Ulice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(50, \"input\", 108);\n    i0.ɵɵtemplate(51, CustomerDetailComponent_div_10_div_51_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 19)(53, \"div\", 109)(54, \"label\", 110);\n    i0.ɵɵtext(55, \"M\\u011Bsto\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 111);\n    i0.ɵɵtemplate(57, CustomerDetailComponent_div_10_div_57_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 109)(59, \"label\", 112);\n    i0.ɵɵtext(60, \"PS\\u010C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"input\", 113);\n    i0.ɵɵtemplate(62, CustomerDetailComponent_div_10_div_62_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 109)(64, \"label\", 114);\n    i0.ɵɵtext(65, \"Zem\\u011B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(66, \"input\", 115);\n    i0.ɵɵtemplate(67, CustomerDetailComponent_div_10_div_67_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 26)(69, \"label\", 55);\n    i0.ɵɵtext(70, \"Pozn\\u00E1mky\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"textarea\", 56);\n    i0.ɵɵtemplate(72, CustomerDetailComponent_div_10_div_72_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 38)(74, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_10_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.cancelEdit());\n    });\n    i0.ɵɵtext(75, \"Zru\\u0161it\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_10_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.saveCustomer());\n    });\n    i0.ɵɵtemplate(77, CustomerDetailComponent_div_10_span_77_Template, 1, 0, \"span\", 41);\n    i0.ɵɵtext(78, \" Ulo\\u017Eit \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_16_0;\n    let tmp_17_0;\n    let tmp_18_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.mode === \"create\" ? \"P\\u0159idat z\\u00E1kazn\\u00EDka\" : \"Upravit z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.customerForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r3.customerForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r3.customerForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ((tmp_3_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r3.customerForm.get(\"abbreviation\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ((tmp_5_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_5_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r3.customerForm.get(\"companyId\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(25, _c0, ((tmp_7_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_7_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r3.customerForm.get(\"taxId\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r3.customerForm.get(\"email\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r3.customerForm.get(\"email\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r3.customerForm.get(\"phone\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r3.customerForm.get(\"phone\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c0, ((tmp_11_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_11_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r3.customerForm.get(\"website\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r3.customerForm.get(\"street\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r3.customerForm.get(\"street\")) == null ? null : tmp_13_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r3.customerForm.get(\"city\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx_r3.customerForm.get(\"city\")) == null ? null : tmp_14_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r3.customerForm.get(\"postalCode\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r3.customerForm.get(\"postalCode\")) == null ? null : tmp_15_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r3.customerForm.get(\"country\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r3.customerForm.get(\"country\")) == null ? null : tmp_16_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ((tmp_17_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_17_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx_r3.customerForm.get(\"notes\")) == null ? null : tmp_18_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.customerForm.invalid || ctx_r3.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.saving);\n  }\n}\nfunction CustomerDetailComponent_div_11_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"I\\u010CO:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r66.customer.companyId, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"DI\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r67.customer.taxId, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r68.customer.email, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Telefon:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.customer.phone, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_p_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Web:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r70 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r70.customer.website, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_11_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 2)(2, \"p\")(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozn\\u00E1mky:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r71.customer.notes);\n  }\n}\nfunction CustomerDetailComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Detail z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"div\", 119)(6, \"div\", 120)(7, \"h4\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Zkratka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CustomerDetailComponent_div_11_p_13_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(14, CustomerDetailComponent_div_11_p_14_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_11_p_15_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_11_p_16_Template, 4, 1, \"p\", 6);\n    i0.ɵɵtemplate(17, CustomerDetailComponent_div_11_p_17_Template, 4, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 120)(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"Ulice:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25, \"M\\u011Bsto:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n    i0.ɵɵtext(29, \"PS\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\")(32, \"strong\");\n    i0.ɵɵtext(33, \"Zem\\u011B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\")(36, \"strong\");\n    i0.ɵɵtext(37, \"Vytvo\\u0159eno:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\")(41, \"strong\");\n    i0.ɵɵtext(42, \"Aktualizov\\u00E1no:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"localDate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, CustomerDetailComponent_div_11_div_45_Template, 7, 1, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.customer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.abbreviation, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.companyId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.taxId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.website);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.street, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.postalCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.customer.country, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(39, 14, ctx_r4.customer.createdAt, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(44, 17, ctx_r4.customer.updatedAt, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.customer.notes);\n  }\n}\nfunction CustomerDetailComponent_div_12_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r76.openAddContactModal());\n    });\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵtext(2, \" P\\u0159idat kontakt \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" Tento z\\u00E1kazn\\u00EDk nem\\u00E1 \\u017E\\u00E1dn\\u00E9 kontakty. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1, \"Ano\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 141);\n    i0.ɵɵtext(1, \"Ne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r86);\n      const contact_r79 = i0.ɵɵnextContext().$implicit;\n      const ctx_r84 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r84.editContact(contact_r79));\n    });\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const contact_r79 = i0.ɵɵnextContext().$implicit;\n      const ctx_r87 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r87.deleteContact(contact_r79));\n    });\n    i0.ɵɵelement(1, \"i\", 145);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, CustomerDetailComponent_div_12_div_8_tr_17_span_10_Template, 2, 0, \"span\", 135);\n    i0.ɵɵtemplate(11, CustomerDetailComponent_div_12_div_8_tr_17_span_11_Template, 2, 0, \"span\", 136);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 137);\n    i0.ɵɵtemplate(14, CustomerDetailComponent_div_12_div_8_tr_17_button_14_Template, 2, 0, \"button\", 138);\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_12_div_8_tr_17_button_15_Template, 2, 0, \"button\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const contact_r79 = ctx.$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", contact_r79.firstName, \" \", contact_r79.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.position || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.email || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r79.phone || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r79.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r79.isPrimary);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.isAdmin);\n  }\n}\nfunction CustomerDetailComponent_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"table\", 133)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, CustomerDetailComponent_div_12_div_8_tr_17_Template, 16, 9, \"tr\", 134);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r75.contacts);\n  }\n}\nfunction CustomerDetailComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 123)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Kontakty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerDetailComponent_div_12_button_4_Template, 3, 0, \"button\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92);\n    i0.ɵɵtemplate(6, CustomerDetailComponent_div_12_div_6_Template, 4, 0, \"div\", 125);\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_12_div_7_Template, 2, 0, \"div\", 126);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_12_div_8_Template, 18, 1, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loadingContacts && ctx_r5.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.loadingContacts && ctx_r5.contacts.length > 0);\n  }\n}\nfunction CustomerDetailComponent_div_13_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const ctx_r94 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r94.openAddInstanceModal());\n    });\n    i0.ɵɵelement(1, \"i\", 129);\n    i0.ɵɵtext(2, \" P\\u0159idat instanci \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1, \" Tento z\\u00E1kazn\\u00EDk nem\\u00E1 \\u017E\\u00E1dn\\u00E9 instance DIS. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r97 = i0.ɵɵnextContext().$implicit;\n    const ctx_r98 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r98.instanceVersions[instance_r97.id][0].versionNumber || \"N/A\", \" \");\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"N/A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r104);\n      const instance_r97 = i0.ɵɵnextContext().$implicit;\n      const ctx_r102 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r102.editInstance(instance_r97));\n    });\n    i0.ɵɵelement(1, \"i\", 143);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r106 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 146);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_13_div_8_tr_15_span_7_Template, 2, 1, \"span\", 6);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_13_div_8_tr_15_span_8_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"div\", 137)(14, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_div_13_div_8_tr_15_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r106);\n      const instance_r97 = restoredCtx.$implicit;\n      const ctx_r105 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r105.viewInstanceDetail(instance_r97));\n    });\n    i0.ɵɵelement(15, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CustomerDetailComponent_div_13_div_8_tr_15_button_16_Template, 2, 0, \"button\", 138);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const instance_r97 = ctx.$implicit;\n    const ctx_r96 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r97.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r96.getInstanceStatusClass(instance_r97.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r96.getInstanceStatusName(instance_r97.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.instanceVersions[instance_r97.id] == null ? null : ctx_r96.instanceVersions[instance_r97.id].length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r96.instanceVersions[instance_r97.id] == null ? null : ctx_r96.instanceVersions[instance_r97.id].length));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", instance_r97.lastConnectionDate ? i0.ɵɵpipeBind2(11, 7, instance_r97.lastConnectionDate, \"dd.MM.yyyy HH:mm\") : \"Nikdy\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.isAdmin);\n  }\n}\nfunction CustomerDetailComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"table\", 133)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Posledn\\u00ED p\\u0159ipojen\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomerDetailComponent_div_13_div_8_tr_15_Template, 17, 10, \"tr\", 134);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r93 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r93.instances);\n  }\n}\nfunction CustomerDetailComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 123)(2, \"h5\", 91);\n    i0.ɵɵtext(3, \"Instance DIS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerDetailComponent_div_13_button_4_Template, 3, 0, \"button\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92);\n    i0.ɵɵtemplate(6, CustomerDetailComponent_div_13_div_6_Template, 4, 0, \"div\", 125);\n    i0.ɵɵtemplate(7, CustomerDetailComponent_div_13_div_7_Template, 2, 0, \"div\", 126);\n    i0.ɵɵtemplate(8, CustomerDetailComponent_div_13_div_8_Template, 16, 1, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.loadingInstances && ctx_r6.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.loadingInstances && ctx_r6.instances.length > 0);\n  }\n}\nfunction CustomerDetailComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.contactError, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_29_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Jm\\u00E9no je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_29_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Jm\\u00E9no nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_29_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_29_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.contactForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r8.contactForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_34_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"P\\u0159\\u00EDjmen\\u00ED je povinn\\u00E9\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_34_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"P\\u0159\\u00EDjmen\\u00ED nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_34_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_34_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.contactForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.contactForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_39_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozice nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_39_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.contactForm.get(\"position\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_45_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_45_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_45_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_45_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r11.contactForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r11.contactForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_50_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Telefon nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_50_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.contactForm.get(\"phone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_55_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 100 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_55_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r13.contactForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nfunction CustomerDetailComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.instanceError, \" \");\n  }\n}\nfunction CustomerDetailComponent_div_82_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_82_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"N\\u00E1zev nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_82_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_82_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r16.instanceForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r16.instanceForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_102_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"URL serveru je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_102_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"URL serveru nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_102_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_div_102_small_2_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r17.instanceForm.get(\"serverUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r17.instanceForm.get(\"serverUrl\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_div_111_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_111_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r18.instanceForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nfunction CustomerDetailComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.instanceVersionError, \" \");\n  }\n}\nfunction CustomerDetailComponent_option_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r121 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", version_r121.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(version_r121.versionNumber);\n  }\n}\nfunction CustomerDetailComponent_div_162_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Verze je povinn\\u00E1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_162_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_162_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r22.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction CustomerDetailComponent_option_169_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r123 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r123.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", user_r123.firstName, \" \", user_r123.lastName, \"\");\n  }\n}\nfunction CustomerDetailComponent_div_170_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"U\\u017Eivatel je povinn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_170_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_170_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r24.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction CustomerDetailComponent_div_175_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerDetailComponent_div_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtemplate(1, CustomerDetailComponent_div_175_small_1_Template, 2, 0, \"small\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r25.instanceVersionForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction CustomerDetailComponent_span_180_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 118);\n  }\n}\nconst _c1 = function () {\n  return [];\n};\nexport class CustomerDetailComponent {\n  constructor(route, router, customerService, contactService, instanceService, instanceVersionService, versionService, userService, certificateService, breadcrumbService, modalService, formBuilder, authService, clipboardService) {\n    this.route = route;\n    this.router = router;\n    this.customerService = customerService;\n    this.contactService = contactService;\n    this.instanceService = instanceService;\n    this.instanceVersionService = instanceVersionService;\n    this.versionService = versionService;\n    this.userService = userService;\n    this.certificateService = certificateService;\n    this.breadcrumbService = breadcrumbService;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.clipboardService = clipboardService;\n    // Zpřístupnění enumu InstanceStatus pro šablonu\n    this.InstanceStatus = InstanceStatus;\n    this.customerId = 0;\n    this.customer = null;\n    this.contacts = [];\n    this.instances = [];\n    this.instanceVersions = {};\n    this.displayedInstanceVersions = {};\n    this.showAllVersions = {};\n    this.certificateInfo = {};\n    this.pendingInstanceId = null; // ID instance, kterou chceme otevřít po načtení dat\n    this.users = [];\n    this.versions = [];\n    // Režim komponenty (vytvoření, editace, zobrazení)\n    this.mode = 'view';\n    this.loading = false;\n    this.loadingContacts = false;\n    this.loadingInstances = false;\n    this.loadingVersions = false;\n    this.loadingUsers = false;\n    this.saving = false;\n    this.savingContact = false;\n    this.savingInstance = false;\n    this.savingInstanceVersion = false;\n    this.isEditMode = false;\n    this.isEditContactMode = false;\n    this.isEditInstanceMode = false;\n    this.isEditInstanceVersionMode = false;\n    this.error = null;\n    this.contactError = null;\n    this.instanceError = null;\n    this.instanceVersionError = null;\n    // Callback pro akce po načtení detailu\n    this.loadCustomerDetailCallback = null;\n    // Příznak, zda je přihlášený uživatel administrátor\n    this.isAdmin = false;\n    this.selectedContact = null;\n    this.selectedInstance = null;\n    this.selectedInstanceForVersion = null;\n    // Vygenerovaný certifikát\n    this.generatedCertificate = null;\n    // Inicializace formulářů\n    this.customerForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n      companyId: ['', Validators.maxLength(20)],\n      taxId: ['', Validators.maxLength(20)],\n      street: ['', [Validators.required, Validators.maxLength(255)]],\n      city: ['', [Validators.required, Validators.maxLength(255)]],\n      postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n      country: ['Česká republika', [Validators.required, Validators.maxLength(100)]],\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      website: ['', Validators.maxLength(255)],\n      notes: ['', Validators.maxLength(500)]\n    });\n    this.contactForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.maxLength(100)]],\n      lastName: ['', [Validators.required, Validators.maxLength(100)]],\n      position: ['', Validators.maxLength(100)],\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      notes: ['', Validators.maxLength(100)],\n      isPrimary: [false]\n    });\n    this.instanceForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      notes: ['', Validators.maxLength(500)],\n      status: ['Active'],\n      blockReason: ['', Validators.maxLength(500)],\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false]\n    });\n    this.instanceVersionForm = this.formBuilder.group({\n      versionId: ['', Validators.required],\n      installedByUserId: ['', Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n  }\n  ngOnInit() {\n    // Kontrola, zda je uživatel administrátor\n    this.isAdmin = this.authService.isAdmin();\n    // Načtení verzí a uživatelů pro formuláře\n    this.loadVersions();\n    this.loadUsers();\n    // Zjištění, zda jsme na cestě /customers/add\n    if (this.router.url === '/customers/add') {\n      // Jsme v režimu vytváření nového zákazníka\n      this.mode = 'create';\n      this.isEditMode = true;\n      // Nastavení breadcrumbs pro vytvoření\n      this.breadcrumbService.setBreadcrumbs([{\n        label: 'Zákazníci',\n        url: '/customers',\n        icon: 'building-fill'\n      }, {\n        label: 'Přidat zákazníka',\n        url: '/customers/add',\n        icon: 'plus-circle-fill'\n      }]);\n      // Reset formuláře\n      this.customerForm.reset({\n        country: 'Česká republika'\n      });\n    } else {\n      // Získání ID zákazníka z URL pro režim zobrazení nebo editace\n      this.route.params.subscribe(params => {\n        if (params['id']) {\n          this.customerId = +params['id'];\n          this.loadCustomerDetail(this.customerId);\n          // Kontrola, zda máme otevřít detail v režimu editace\n          this.route.queryParams.subscribe(queryParams => {\n            console.log('Query params:', queryParams);\n            if (queryParams['edit'] === 'true') {\n              console.log('Edit mode detected from query params');\n              this.mode = 'edit';\n              // Po načtení detailu přepneme do režimu editace\n              this.loadCustomerDetailCallback = () => {\n                console.log('loadCustomerDetailCallback executing, customer:', this.customer);\n                if (this.customer) {\n                  console.log('Calling editCustomer() from callback');\n                  this.editCustomer();\n                } else {\n                  console.log('Customer data not available in callback');\n                }\n              };\n            } else {\n              console.log('View mode set');\n              this.mode = 'view';\n            }\n          });\n          // Kontrola, zda máme otevřít detail instance (z localStorage)\n          try {\n            console.log('Kontroluji localStorage pro otevření detailu instance');\n            const openInstanceDetailJson = localStorage.getItem('openInstanceDetail');\n            console.log('Data z localStorage:', openInstanceDetailJson);\n            if (openInstanceDetailJson) {\n              const openInstanceDetail = JSON.parse(openInstanceDetailJson);\n              console.log('Parsovaná data:', openInstanceDetail);\n              // Kontrola, zda data v localStorage odpovídají aktuálnímu zákazníkovi\n              // a zda nejsou starší než 10 sekund\n              const currentTime = new Date().getTime();\n              const dataTime = openInstanceDetail.timestamp || 0;\n              const isRecent = currentTime - dataTime < 10000; // 10 sekund\n              console.log('Aktuální customerId:', this.customerId);\n              console.log('CustomerId z localStorage:', openInstanceDetail.customerId);\n              console.log('Je časově aktuální:', isRecent, '(rozdíl:', currentTime - dataTime, 'ms)');\n              if (openInstanceDetail.customerId === this.customerId && isRecent) {\n                const instanceId = openInstanceDetail.instanceId;\n                console.log('Nastavuji otevření instance ID:', instanceId);\n                // Uložíme ID instance, kterou chceme otevřít\n                this.pendingInstanceId = instanceId;\n                // Odstraníme data z localStorage, aby se modální okno neotevíralo opakovaně\n                localStorage.removeItem('openInstanceDetail');\n                console.log('Data z localStorage byla odstraněna');\n              } else {\n                console.log('Data v localStorage neodpovídají aktuálnímu zákazníkovi nebo nejsou aktuální');\n              }\n            } else {\n              console.log('Žádná data pro otevření instance v localStorage');\n            }\n          } catch (error) {\n            console.error('Chyba při zpracování dat z localStorage:', error);\n          }\n        }\n      });\n    }\n  }\n  /**\r\n   * Načtení detailu zákazníka\r\n   */\n  loadCustomerDetail(customerId) {\n    console.log('loadCustomerDetail() called with ID:', customerId);\n    this.loading = true;\n    this.loadingContacts = true;\n    this.loadingInstances = true;\n    this.customerService.getCustomer(customerId).pipe(first()).subscribe({\n      next: customer => {\n        console.log('Customer data loaded:', customer);\n        this.customer = customer;\n        // Nastavení breadcrumbs\n        this.breadcrumbService.setBreadcrumbs([{\n          label: 'Zákazníci',\n          url: '/customers',\n          icon: 'building-fill'\n        }, {\n          label: customer.name,\n          url: `/customers/${customerId}`,\n          icon: 'info-circle-fill'\n        }]);\n        this.loading = false;\n        // Načtení kontaktů zákazníka\n        this.loadContacts(customerId);\n        // Načtení instancí DIS zákazníka\n        this.loadInstances(customerId);\n        // Volat callback po načtení detailu, pokud existuje\n        if (this.loadCustomerDetailCallback) {\n          console.log('Executing loadCustomerDetailCallback');\n          this.loadCustomerDetailCallback();\n          this.loadCustomerDetailCallback = null; // Použít jen jednou\n        }\n      },\n\n      error: err => {\n        this.error = `Chyba při načítání zákazníka: ${err.message}`;\n        this.loading = false;\n        this.loadingContacts = false;\n        this.loadingInstances = false;\n      }\n    });\n  }\n  /**\r\n   * Načtení kontaktů zákazníka\r\n   */\n  loadContacts(customerId) {\n    this.contactService.getContactsByCustomerId(customerId).pipe(first()).subscribe({\n      next: contacts => {\n        this.contacts = contacts;\n        this.loadingContacts = false;\n      },\n      error: err => {\n        this.error = `Chyba při načítání kontaktů: ${err.message}`;\n        this.loadingContacts = false;\n      }\n    });\n  }\n  /**\r\n   * Načtení instancí DIS zákazníka\r\n   */\n  loadInstances(customerId) {\n    this.instanceService.getInstancesByCustomerId(customerId).pipe(first()).subscribe({\n      next: instances => {\n        this.instances = instances;\n        this.loadingInstances = false;\n        // Načtení verzí pro každou instanci\n        instances.forEach(instance => {\n          this.loadInstanceVersions(instance.id);\n        });\n        // Pokud máme čekající ID instance, otevřeme její detail\n        if (this.pendingInstanceId) {\n          console.log('Máme čekající ID instance:', this.pendingInstanceId);\n          // Najdeme instanci podle ID\n          const instance = instances.find(i => i.id === this.pendingInstanceId);\n          console.log('Nalezená instance:', instance);\n          if (instance) {\n            console.log('Otevírám modální okno s detailem instance:', instance.id);\n            // Počkáme, až se stránka načte\n            setTimeout(() => {\n              // Nastavíme vybranou instanci\n              this.selectedInstanceForVersion = instance;\n              // Načtení informací o certifikátu\n              this.loadCertificateInfo(instance.id);\n              // Otevřít modal přímo pomocí Bootstrap API\n              console.log('Otevírám modální okno přímo pomocí Bootstrap API');\n              const modalElement = document.getElementById('instanceDetailModal');\n              if (modalElement) {\n                console.log('Modal element nalezen, otevírám...');\n                const modal = new window.bootstrap.Modal(modalElement);\n                modal.show();\n              } else {\n                console.error('Modal element s ID instanceDetailModal nebyl nalezen');\n              }\n            }, 500);\n          } else {\n            console.log('Instance nebyla nalezena v seznamu načtených instancí');\n          }\n          // Resetujeme čekající ID instance\n          this.pendingInstanceId = null;\n        }\n      },\n      error: err => {\n        this.error = `Chyba při načítání instancí DIS: ${err.message}`;\n        this.loadingInstances = false;\n      }\n    });\n  }\n  /**\r\n   * Načtení verzí instance\r\n   */\n  loadInstanceVersions(instanceId) {\n    this.instanceVersionService.getInstanceVersions(instanceId).pipe(first()).subscribe({\n      next: versions => {\n        // Uložení všech verzí\n        this.instanceVersions[instanceId] = versions;\n        // Výchozí zobrazení pouze posledních 5 verzí\n        this.displayedInstanceVersions[instanceId] = versions.slice(0, 5);\n        // Výchozí stav zobrazení všech verzí\n        this.showAllVersions[instanceId] = false;\n      },\n      error: err => {\n        console.error(`Chyba při načítání verzí instance ${instanceId}:`, err);\n      }\n    });\n  }\n  /**\r\n   * Přepnutí zobrazení všech verzí instance\r\n   */\n  toggleAllVersions(instanceId) {\n    this.showAllVersions[instanceId] = !this.showAllVersions[instanceId];\n    if (this.showAllVersions[instanceId]) {\n      // Zobrazit všechny verze\n      this.displayedInstanceVersions[instanceId] = [...this.instanceVersions[instanceId]];\n    } else {\n      // Zobrazit pouze posledních 5 verzí\n      this.displayedInstanceVersions[instanceId] = this.instanceVersions[instanceId].slice(0, 5);\n    }\n  }\n  /**\r\n   * Načtení informací o certifikátu instance\r\n   */\n  loadCertificateInfo(instanceId) {\n    this.certificateService.getInstanceCertificateInfo(instanceId).pipe(first()).subscribe({\n      next: info => {\n        this.certificateInfo[instanceId] = info;\n      },\n      error: err => {\n        console.error(`Chyba při načítání informací o certifikátu instance ${instanceId}:`, err);\n      }\n    });\n  }\n  /**\r\n   * Načtení verzí pro formuláře\r\n   */\n  loadVersions() {\n    this.loadingVersions = true;\n    this.versionService.getVersions().pipe(first()).subscribe({\n      next: versions => {\n        this.versions = versions;\n        this.loadingVersions = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání verzí:', err);\n        this.loadingVersions = false;\n      }\n    });\n  }\n  /**\r\n   * Načtení uživatelů pro formuláře\r\n   */\n  loadUsers() {\n    this.loadingUsers = true;\n    this.userService.getUsers().pipe(first()).subscribe({\n      next: users => {\n        this.users = users;\n        this.loadingUsers = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání uživatelů:', err);\n        this.loadingUsers = false;\n      }\n    });\n  }\n  /**\r\n   * Přepnutí do režimu editace zákazníka\r\n   */\n  editCustomer() {\n    console.log('editCustomer() called');\n    if (!this.customer) {\n      console.log('No customer data available');\n      return;\n    }\n    console.log('Switching to edit mode for customer:', this.customer);\n    this.isEditMode = true;\n    this.mode = 'edit'; // Explicitně nastavíme režim na 'edit'\n    // Naplnění formuláře daty zákazníka\n    console.log('Patching form with customer data');\n    this.customerForm.patchValue({\n      name: this.customer.name,\n      abbreviation: this.customer.abbreviation,\n      companyId: this.customer.companyId,\n      taxId: this.customer.taxId,\n      street: this.customer.street,\n      city: this.customer.city,\n      postalCode: this.customer.postalCode,\n      country: this.customer.country,\n      email: this.customer.email,\n      phone: this.customer.phone,\n      website: this.customer.website,\n      notes: this.customer.notes\n    });\n    console.log('Form after patching:', this.customerForm.value);\n  }\n  /**\r\n   * Uložení změn zákazníka nebo vytvoření nového\r\n   */\n  saveCustomer() {\n    console.log('saveCustomer() called in customer-detail.component.ts');\n    console.log('Form valid:', this.customerForm.valid);\n    console.log('Form values:', this.customerForm.value);\n    console.log('Mode:', this.mode);\n    console.log('isEditMode:', this.isEditMode);\n    console.log('customer:', this.customer);\n    if (this.customerForm.invalid) {\n      console.log('Form is invalid, returning');\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.customerForm.controls).forEach(key => {\n        const control = this.customerForm.get(key);\n        control?.markAsTouched();\n        if (control?.invalid) {\n          console.log(`Field ${key} is invalid:`, control.errors);\n        }\n      });\n      return;\n    }\n    console.log('Form is valid, proceeding with save');\n    this.saving = true;\n    const formData = this.customerForm.value;\n    // Vytvoření objektu zákazníka z formuláře\n    const customerData = {\n      name: formData.name,\n      abbreviation: formData.abbreviation,\n      companyId: formData.companyId,\n      taxId: formData.taxId,\n      street: formData.street,\n      city: formData.city,\n      postalCode: formData.postalCode,\n      country: formData.country,\n      email: formData.email,\n      phone: formData.phone,\n      website: formData.website,\n      notes: formData.notes\n    };\n    if (this.mode === 'create') {\n      // Vytvoření nového zákazníka\n      this.customerService.createCustomer(customerData).pipe(first()).subscribe({\n        next: createdCustomer => {\n          this.saving = false;\n          // Přesměrování na detail nově vytvořeného zákazníka\n          this.router.navigate(['/customers', createdCustomer.id]);\n        },\n        error: err => {\n          this.error = `Chyba při vytváření zákazníka: ${err.message}`;\n          this.saving = false;\n        }\n      });\n    } else if (this.mode === 'edit' && this.customer) {\n      // Aktualizace existujícího zákazníka\n      console.log('Updating customer with ID:', this.customer.id);\n      console.log('Customer data to send:', customerData);\n      this.customerService.updateCustomer(this.customer.id, customerData).pipe(first()).subscribe({\n        next: response => {\n          console.log('Customer updated successfully:', response);\n          this.saving = false;\n          this.isEditMode = false;\n          this.loadCustomerDetail(this.customerId);\n        },\n        error: err => {\n          console.error('Error updating customer:', err);\n          this.error = `Chyba při ukládání zákazníka: ${err.message || err.statusText || 'Neznámá chyba'}`;\n          this.saving = false;\n        }\n      });\n    }\n  }\n  /**\r\n   * Zrušení editace zákazníka\r\n   */\n  cancelEdit() {\n    if (this.mode === 'create') {\n      // Při vytváření nového zákazníka se vrátíme na seznam zákazníků\n      this.router.navigate(['/customers']);\n    } else {\n      // Při editaci existujícího zákazníka se vrátíme do režimu zobrazení\n      this.isEditMode = false;\n    }\n  }\n  /**\r\n   * Otevření modálu pro přidání kontaktu\r\n   */\n  openAddContactModal() {\n    this.isEditContactMode = false;\n    this.selectedContact = null;\n    this.contactError = null;\n    // Reset formuláře\n    this.contactForm.reset({\n      isPrimary: false\n    });\n    // Otevření modálu\n    this.modalService.open('contactModal');\n  }\n  /**\r\n   * Otevření modálu pro editaci kontaktu\r\n   */\n  editContact(contact) {\n    this.isEditContactMode = true;\n    this.selectedContact = contact;\n    this.contactError = null;\n    // Naplnění formuláře daty kontaktu\n    this.contactForm.patchValue({\n      firstName: contact.firstName,\n      lastName: contact.lastName,\n      position: contact.position || '',\n      email: contact.email || '',\n      phone: contact.phone || '',\n      notes: contact.notes || '',\n      isPrimary: contact.isPrimary\n    });\n    // Otevření modálu\n    this.modalService.open('contactModal');\n  }\n  /**\r\n   * Uložení kontaktu\r\n   */\n  saveContact() {\n    if (this.contactForm.invalid) {\n      return;\n    }\n    this.savingContact = true;\n    const formData = this.contactForm.value;\n    if (this.isEditContactMode && this.selectedContact) {\n      // Editace existujícího kontaktu\n      const updatedContact = {\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        position: formData.position,\n        email: formData.email,\n        phone: formData.phone,\n        notes: formData.notes,\n        isPrimary: formData.isPrimary\n      };\n      this.contactService.updateContact(this.selectedContact.id, updatedContact).pipe(first()).subscribe({\n        next: () => {\n          this.savingContact = false;\n          this.closeContactModal();\n          this.loadContacts(this.customerId);\n        },\n        error: err => {\n          this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n          this.savingContact = false;\n        }\n      });\n    } else {\n      // Přidání nového kontaktu\n      const newContact = {\n        customerId: this.customerId,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        position: formData.position,\n        email: formData.email,\n        phone: formData.phone,\n        notes: formData.notes,\n        isPrimary: formData.isPrimary\n      };\n      this.contactService.createContact(newContact).pipe(first()).subscribe({\n        next: () => {\n          this.savingContact = false;\n          this.closeContactModal();\n          this.loadContacts(this.customerId);\n        },\n        error: err => {\n          this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n          this.savingContact = false;\n        }\n      });\n    }\n  }\n  /**\r\n   * Zavření modálu pro kontakty\r\n   */\n  closeContactModal() {\n    this.modalService.close('contactModal');\n  }\n  /**\r\n   * Smazání kontaktu\r\n   */\n  deleteContact(contact) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`, 'Smazání kontaktu', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this.contactService.deleteContact(contact.id).pipe(first()).subscribe({\n          next: () => {\n            _this.loadContacts(_this.customerId);\n          },\n          error: err => {\n            _this.error = `Chyba při mazání kontaktu: ${err.message}`;\n            _this.modalService.alert(`Chyba při mazání kontaktu: ${err.message}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      }\n    })();\n  }\n  /**\r\n   * Otevření modálu pro přidání instance\r\n   */\n  openAddInstanceModal() {\n    this.isEditInstanceMode = false;\n    this.selectedInstance = null;\n    this.instanceError = null;\n    // Reset formuláře\n    this.instanceForm.reset({\n      status: InstanceStatus.Active,\n      moduleReporting: true,\n      moduleAdvancedSecurity: false,\n      moduleApiIntegration: false,\n      moduleDataExport: false,\n      moduleCustomization: false\n    });\n    // Otevření modálu\n    this.modalService.open('instanceModal');\n  }\n  /**\r\n   * Otevření modálu pro editaci instance\r\n   */\n  editInstance(instance) {\n    this.isEditInstanceMode = true;\n    this.selectedInstance = instance;\n    this.instanceError = null;\n    // Naplnění formuláře daty instance\n    this.instanceForm.patchValue({\n      name: instance.name,\n      serverUrl: instance.serverUrl,\n      expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n      notes: instance.notes,\n      // Převod řetězcového statusu na číselnou hodnotu enumu\n      status: this.getStatusEnumValue(instance.status),\n      moduleReporting: instance.moduleReporting,\n      moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n      moduleApiIntegration: instance.moduleApiIntegration,\n      moduleDataExport: instance.moduleDataExport,\n      moduleCustomization: instance.moduleCustomization\n    });\n    // Otevření modálu\n    this.modalService.open('instanceModal');\n  }\n  /**\r\n   * Zobrazení detailu instance\r\n   */\n  viewInstanceDetail(instance) {\n    this.selectedInstanceForVersion = instance;\n    // Načtení verzí instance\n    this.loadInstanceVersions(instance.id);\n    // Načtení informací o certifikátu\n    this.loadCertificateInfo(instance.id);\n    // Otevřít modal\n    this.modalService.open('instanceDetailModal');\n  }\n  /**\r\n   * Zavření modálu pro detail instance\r\n   */\n  closeInstanceDetailModal() {\n    this.modalService.close('instanceDetailModal');\n  }\n  /**\r\n   * Editace instance z detailu instance\r\n   */\n  editInstanceFromDetail(instance) {\n    // Nejprve zavřeme modál s detailem instance\n    this.closeInstanceDetailModal();\n    // Poté otevřeme modál pro editaci instance\n    setTimeout(() => {\n      this.editInstance(instance);\n    }, 500); // Počkáme 500ms, aby se první modál stihl zavřít\n  }\n  /**\r\n   * Zavření modálu pro přidání/úpravu instance\r\n   */\n  closeInstanceModal() {\n    this.modalService.close('instanceModal');\n  }\n  /**\r\n   * Převod řetězcové hodnoty statusu na enum InstanceStatus\r\n   */\n  convertStatusToEnum(status) {\n    // Pokud je status číselná hodnota jako řetězec (např. \"3\"), převedeme ji na číslo\n    if (!isNaN(Number(status))) {\n      const numericStatus = Number(status);\n      return numericStatus;\n    }\n    // Jinak zpracujeme řetězcové hodnoty\n    switch (status) {\n      case 'Active':\n        return InstanceStatus.Active;\n      case 'Blocked':\n        return InstanceStatus.Blocked;\n      case 'Expired':\n        return InstanceStatus.Expired;\n      case 'Trial':\n        return InstanceStatus.Trial;\n      case 'Maintenance':\n        return InstanceStatus.Maintenance;\n      default:\n        return InstanceStatus.Active;\n      // Výchozí hodnota je Active\n    }\n  }\n  /**\r\n   * Převod řetězcového statusu na číselnou hodnotu enumu\r\n   */\n  getStatusEnumValue(status) {\n    if (typeof status === 'number') {\n      return status;\n    }\n    switch (status) {\n      case 'Active':\n        return InstanceStatus.Active;\n      case 'Blocked':\n        return InstanceStatus.Blocked;\n      case 'Expired':\n        return InstanceStatus.Expired;\n      case 'Trial':\n        return InstanceStatus.Trial;\n      case 'Maintenance':\n        return InstanceStatus.Maintenance;\n      default:\n        return InstanceStatus.Active;\n      // Výchozí hodnota je Active\n    }\n  }\n  /**\r\n   * Uložení instance\r\n   */\n  saveInstance() {\n    if (this.instanceForm.invalid) {\n      return;\n    }\n    this.savingInstance = true;\n    const formData = this.instanceForm.value;\n    console.log('Hodnoty formuláře před úpravou:', formData);\n    if (this.isEditInstanceMode && this.selectedInstance) {\n      // Editace existující instance\n      const updatedInstance = {\n        name: formData.name,\n        serverUrl: formData.serverUrl,\n        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n        notes: formData.notes,\n        status: this.convertStatusToEnum(formData.status),\n        moduleReporting: formData.moduleReporting,\n        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n        moduleApiIntegration: formData.moduleApiIntegration,\n        moduleDataExport: formData.moduleDataExport,\n        moduleCustomization: formData.moduleCustomization\n      };\n      console.log('Odesílaná data instance:', updatedInstance);\n      this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance).pipe(first()).subscribe({\n        next: response => {\n          console.log('Instance úspěšně aktualizována:', response);\n          this.savingInstance = false;\n          this.closeInstanceModal();\n          this.loadInstances(this.customerId);\n        },\n        error: err => {\n          console.error('Chyba při ukládání instance:', err);\n          this.instanceError = `Chyba při ukládání instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n          this.savingInstance = false;\n        }\n      });\n    } else {\n      // Přidání nové instance\n      const newInstance = {\n        customerId: this.customerId,\n        name: formData.name,\n        serverUrl: formData.serverUrl,\n        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n        notes: formData.notes,\n        status: this.convertStatusToEnum(formData.status),\n        moduleReporting: formData.moduleReporting,\n        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n        moduleApiIntegration: formData.moduleApiIntegration,\n        moduleDataExport: formData.moduleDataExport,\n        moduleCustomization: formData.moduleCustomization\n      };\n      console.log('Odesílaná data nové instance:', newInstance);\n      this.instanceService.createInstance(newInstance).pipe(first()).subscribe({\n        next: response => {\n          console.log('Instance úspěšně vytvořena:', response);\n          this.savingInstance = false;\n          this.closeInstanceModal();\n          this.loadInstances(this.customerId);\n        },\n        error: err => {\n          console.error('Chyba při vytváření instance:', err);\n          this.instanceError = `Chyba při vytváření instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n          this.savingInstance = false;\n        }\n      });\n    }\n  }\n  /**\r\n   * Smazání zákazníka\r\n   */\n  deleteCustomer() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.customer) return;\n      const confirmed = yield _this2.modalService.confirm(`Opravdu chcete smazat zákazníka ${_this2.customer.name}?`, 'Smazání zákazníka', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this2.loading = true;\n        _this2.customerService.deleteCustomer(_this2.customer.id).pipe(first()).subscribe({\n          next: () => {\n            _this2.router.navigate(['/customers']);\n          },\n          error: err => {\n            _this2.error = `Chyba při mazání zákazníka: ${err.message}`;\n            _this2.loading = false;\n            _this2.modalService.alert(`Chyba při mazání zákazníka: ${err.message}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      }\n    })();\n  }\n  /**\r\n   * Návrat na seznam zákazníků\r\n   */\n  goBack() {\n    this.router.navigate(['/customers']);\n  }\n  /**\r\n   * Kopírování API klíče do schránky\r\n   */\n  copyApiKey(inputElement) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3.clipboardService.copyFromInput(inputElement, 'API klíč byl zkopírován do schránky', 'Nepodařilo se zkopírovat API klíč');\n    })();\n  }\n  /**\r\n   * Generování nového certifikátu pro instanci\r\n   */\n  generateCertificate(instanceId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const confirmed = yield _this4.modalService.confirm('Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.', 'Generování certifikátu', 'Generovat', 'Zrušit', 'btn-success', 'btn-secondary');\n      if (!confirmed) {\n        return;\n      }\n      _this4.certificateService.generateCertificate(instanceId).subscribe({\n        next: response => {\n          // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n          // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n          const certificatePassword = response.password || 'password';\n          _this4.generatedCertificate = {\n            certificate: response.certificate,\n            privateKey: response.privateKey,\n            thumbprint: response.thumbprint,\n            expirationDate: response.expirationDate,\n            password: certificatePassword,\n            certificatePassword: certificatePassword\n          };\n          // Aktualizace informací o certifikátu\n          _this4.loadCertificateInfo(instanceId);\n          // Explicitní nastavení hesla pro zobrazení v modálním okně\n          const passwordElement = document.getElementById('certificatePassword');\n          if (passwordElement && _this4.generatedCertificate) {\n            passwordElement.textContent = _this4.generatedCertificate.password || 'Heslo není k dispozici';\n          }\n          // Zobrazení modálního okna s informacemi o certifikátu\n          _this4.modalService.open('certificateGeneratedModal');\n        },\n        error: err => {\n          console.error('Chyba při generování certifikátu', err);\n          _this4.modalService.alert(`Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`, 'Chyba', 'Zavřít', 'btn-danger');\n        }\n      });\n    })();\n  }\n  /**\r\n   * Stažení vygenerovaného certifikátu\r\n   */\n  downloadCertificate() {\n    if (!this.generatedCertificate) {\n      return;\n    }\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n  /**\r\n   * Pomocná metoda pro konverzi Base64 na Blob\r\n   */\n  base64ToBlob(base64, contentType) {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  /**\r\n   * Zavření modálního okna s certifikátem\r\n   */\n  closeCertificateModal() {\n    this.modalService.close('certificateGeneratedModal');\n  }\n  /**\r\n   * Otevření modálního okna pro přidání verze instance\r\n   */\n  openAddInstanceVersionModal() {\n    if (!this.selectedInstanceForVersion) {\n      console.error('Není vybrána žádná instance');\n      return;\n    }\n    this.isEditInstanceVersionMode = false;\n    this.instanceVersionError = null;\n    // Získání ID aktuálně přihlášeného uživatele\n    const currentUserId = this.authService.getCurrentUserId();\n    // Reset formuláře s předvyplněným aktuálním uživatelem\n    this.instanceVersionForm.reset({\n      versionId: '',\n      installedByUserId: currentUserId,\n      notes: ''\n    });\n    // Otevření modálu\n    this.modalService.open('instanceVersionModal');\n  }\n  /**\r\n   * Zavření modálního okna pro přidání/úpravu verze instance\r\n   */\n  closeInstanceVersionModal() {\n    this.modalService.close('instanceVersionModal');\n  }\n  /**\r\n   * Uložení verze instance\r\n   */\n  saveInstanceVersion() {\n    if (this.instanceVersionForm.invalid) {\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceVersionForm.controls).forEach(key => {\n        const control = this.instanceVersionForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n    if (!this.selectedInstanceForVersion) {\n      this.instanceVersionError = 'Není vybrána žádná instance.';\n      return;\n    }\n    this.savingInstanceVersion = true;\n    const formData = this.instanceVersionForm.value;\n    // Vytvoření nové verze instance\n    const newInstanceVersion = {\n      versionId: formData.versionId,\n      installedByUserId: formData.installedByUserId,\n      notes: formData.notes || ''\n    };\n    this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion).pipe(first()).subscribe({\n      next: response => {\n        console.log('Verze instance úspěšně vytvořena:', response);\n        this.savingInstanceVersion = false;\n        this.closeInstanceVersionModal();\n        // Aktualizace seznamu verzí instance\n        this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n      },\n      error: err => {\n        console.error('Chyba při vytváření verze instance:', err);\n        this.instanceVersionError = `Chyba při vytváření verze instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n        this.savingInstanceVersion = false;\n      }\n    });\n  }\n  /**\r\n   * Testovací metoda pro zobrazení hesla\r\n   */\n  testPassword() {\n    if (this.generatedCertificate) {\n      this.modalService.alert(`Heslo k certifikátu: <strong>${this.generatedCertificate.password}</strong>`, 'Heslo k certifikátu', 'Zavřít', 'btn-primary');\n    } else {\n      this.modalService.alert('Certifikát není k dispozici', 'Informace', 'Zavřít', 'btn-secondary');\n    }\n  }\n  /**\r\n   * Pomocná metoda pro získání jména statusu instance\r\n   */\n  getInstanceStatusName(status) {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active':\n          return 'Aktivní';\n        case 'Blocked':\n          return 'Blokovaná';\n        case 'Expired':\n          return 'Expirovaná';\n        case 'Trial':\n          return 'Zkušební';\n        case 'Maintenance':\n          return 'Údržba';\n        default:\n          return status;\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active:\n          return 'Aktivní';\n        case InstanceStatus.Blocked:\n          return 'Blokovaná';\n        case InstanceStatus.Expired:\n          return 'Expirovaná';\n        case InstanceStatus.Trial:\n          return 'Zkušební';\n        case InstanceStatus.Maintenance:\n          return 'Údržba';\n        default:\n          return String(status);\n      }\n    }\n  }\n  /**\r\n   * Pomocná metoda pro získání třídy pro status instance\r\n   */\n  getInstanceStatusClass(status) {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active':\n          return 'bg-success';\n        case 'Blocked':\n          return 'bg-danger';\n        case 'Expired':\n          return 'bg-warning text-dark';\n        case 'Trial':\n          return 'bg-info text-dark';\n        case 'Maintenance':\n          return 'bg-secondary';\n        default:\n          return 'bg-secondary';\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active:\n          return 'bg-success';\n        case InstanceStatus.Blocked:\n          return 'bg-danger';\n        case InstanceStatus.Expired:\n          return 'bg-warning text-dark';\n        case InstanceStatus.Trial:\n          return 'bg-info text-dark';\n        case InstanceStatus.Maintenance:\n          return 'bg-secondary';\n        default:\n          return 'bg-secondary';\n      }\n    }\n  }\n  static {\n    this.ɵfac = function CustomerDetailComponent_Factory(t) {\n      return new (t || CustomerDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContactService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.InstanceVersionService), i0.ɵɵdirectiveInject(i6.VersionService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.CertificateService), i0.ɵɵdirectiveInject(i9.BreadcrumbService), i0.ɵɵdirectiveInject(i10.ModalService), i0.ɵɵdirectiveInject(i11.FormBuilder), i0.ɵɵdirectiveInject(i12.AuthService), i0.ɵɵdirectiveInject(i13.ClipboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerDetailComponent,\n      selectors: [[\"app-customer-detail\"]],\n      decls: 183,\n      vars: 65,\n      consts: [[1, \"container-fluid\"], [1, \"row\", \"mb-3\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-1\"], [4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [\"id\", \"contactModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"contactModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"contactModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"firstName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", \"placeholder\", \"Jm\\u00E9no\", 1, \"form-control\"], [\"class\", \"text-danger mt-1\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", \"placeholder\", \"P\\u0159\\u00EDjmen\\u00ED\", 1, \"form-control\"], [1, \"mb-3\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", \"placeholder\", \"Pozice\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"contactEmail\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"contactEmail\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"contactPhone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"contactPhone\", \"formControlName\", \"phone\", \"placeholder\", \"Telefon\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"contactNotes\", 1, \"form-label\"], [\"id\", \"contactNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isPrimary\", \"formControlName\", \"isPrimary\", 1, \"form-check-input\"], [\"for\", \"isPrimary\", 1, \"form-check-label\"], [1, \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"modalId\", \"instanceDetailModal\", 3, \"instance\", \"certificateInfo\", \"instanceVersions\", \"showAddVersionButton\", \"close\", \"edit\", \"generateCertificate\", \"addVersion\"], [\"id\", \"instanceModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [\"id\", \"instanceModalLabel\", 1, \"modal-title\"], [\"for\", \"name\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"N\\u00E1zev instance\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\", \"required-field\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [3, \"value\"], [\"for\", \"serverUrl\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"serverUrl\", \"formControlName\", \"serverUrl\", \"placeholder\", \"URL serveru\", 1, \"form-control\"], [\"for\", \"expirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"expirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky\", 1, \"form-control\", 3, \"ngClass\"], [1, \"form-label\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"id\", \"instanceVersionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceVersionModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceVersionModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceName\", \"disabled\", \"\", 1, \"form-control\", 3, \"value\"], [\"for\", \"versionId\", 1, \"form-label\", \"required-field\"], [\"id\", \"versionId\", \"formControlName\", \"versionId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"installedByUserId\", 1, \"form-label\", \"required-field\"], [\"id\", \"installedByUserId\", \"formControlName\", \"installedByUserId\", 1, \"form-select\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Pozn\\u00E1mky k instalaci verze\", 1, \"form-control\", 3, \"ngClass\"], [\"modalId\", \"certificateGeneratedModal\", 3, \"certificate\", \"instanceName\", \"close\", \"download\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-1\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"N\\u00E1zev spole\\u010Dnosti\", 1, \"form-control\"], [\"for\", \"abbreviation\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"abbreviation\", \"formControlName\", \"abbreviation\", \"placeholder\", \"Zkratka\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"companyId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"companyId\", \"formControlName\", \"companyId\", \"placeholder\", \"I\\u010CO\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"taxId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taxId\", \"formControlName\", \"taxId\", \"placeholder\", \"DI\\u010C\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"Telefon\", 1, \"form-control\"], [\"for\", \"website\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"website\", \"formControlName\", \"website\", \"placeholder\", \"Web\", 1, \"form-control\", 3, \"ngClass\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"street\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"street\", \"formControlName\", \"street\", \"placeholder\", \"Ulice\", 1, \"form-control\"], [1, \"col-md-4\", \"mb-3\"], [\"for\", \"city\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"city\", \"formControlName\", \"city\", \"placeholder\", \"M\\u011Bsto\", 1, \"form-control\"], [\"for\", \"postalCode\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"postalCode\", \"formControlName\", \"postalCode\", \"placeholder\", \"PS\\u010C\", 1, \"form-control\"], [\"for\", \"country\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"country\", \"formControlName\", \"country\", \"placeholder\", \"Zem\\u011B\", 1, \"form-control\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"text-danger\", \"mt-1\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [1, \"row\", \"mb-0\"], [1, \"col-md-6\"], [\"class\", \"row mt-0\", 4, \"ngIf\"], [1, \"row\", \"mt-0\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"btn btn-sm btn-light\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center my-3\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-light\", 3, \"click\"], [1, \"bi\", \"bi-plus-lg\", \"me-1\"], [1, \"d-flex\", \"justify-content-center\", \"my-3\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-secondary\", 4, \"ngIf\"], [1, \"btn-group\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Upravit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", \"title\", \"Smazat\", 3, \"click\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-secondary\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"badge\", 3, \"ngClass\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-eye-fill\"], [1, \"alert\", \"alert-danger\", \"mb-3\"]],\n      template: function CustomerDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵtext(6, \" Zp\\u011Bt na seznam z\\u00E1kazn\\u00EDk\\u016F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, CustomerDetailComponent_div_7_Template, 7, 0, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, CustomerDetailComponent_div_8_Template, 4, 0, \"div\", 7);\n          i0.ɵɵtemplate(9, CustomerDetailComponent_div_9_Template, 2, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, CustomerDetailComponent_div_10_Template, 79, 31, \"div\", 9);\n          i0.ɵɵtemplate(11, CustomerDetailComponent_div_11_Template, 46, 20, \"div\", 9);\n          i0.ɵɵtemplate(12, CustomerDetailComponent_div_12_Template, 9, 4, \"div\", 9);\n          i0.ɵɵtemplate(13, CustomerDetailComponent_div_13_Template, 9, 4, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11)(16, \"div\", 12)(17, \"div\", 13)(18, \"h5\", 14);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_20_listener() {\n            return ctx.closeContactModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtemplate(22, CustomerDetailComponent_div_22_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementStart(23, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_23_listener() {\n            return ctx.saveContact();\n          });\n          i0.ɵɵelementStart(24, \"div\", 19)(25, \"div\", 20)(26, \"label\", 21);\n          i0.ɵɵtext(27, \"Jm\\u00E9no\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 22);\n          i0.ɵɵtemplate(29, CustomerDetailComponent_div_29_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"label\", 24);\n          i0.ɵɵtext(32, \"P\\u0159\\u00EDjmen\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 25);\n          i0.ɵɵtemplate(34, CustomerDetailComponent_div_34_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 26)(36, \"label\", 27);\n          i0.ɵɵtext(37, \"Pozice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 28);\n          i0.ɵɵtemplate(39, CustomerDetailComponent_div_39_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 19)(41, \"div\", 20)(42, \"label\", 29);\n          i0.ɵɵtext(43, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 30);\n          i0.ɵɵtemplate(45, CustomerDetailComponent_div_45_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 20)(47, \"label\", 31);\n          i0.ɵɵtext(48, \"Telefon\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"input\", 32);\n          i0.ɵɵtemplate(50, CustomerDetailComponent_div_50_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 26)(52, \"label\", 33);\n          i0.ɵɵtext(53, \"Pozn\\u00E1mky\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"textarea\", 34);\n          i0.ɵɵtemplate(55, CustomerDetailComponent_div_55_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 35);\n          i0.ɵɵelement(57, \"input\", 36);\n          i0.ɵɵelementStart(58, \"label\", 37);\n          i0.ɵɵtext(59, \"Prim\\u00E1rn\\u00ED kontakt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 38)(61, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_61_listener() {\n            return ctx.closeContactModal();\n          });\n          i0.ɵɵtext(62, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 40);\n          i0.ɵɵtemplate(64, CustomerDetailComponent_span_64_Template, 1, 0, \"span\", 41);\n          i0.ɵɵtext(65, \" Ulo\\u017Eit \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(66, \"app-instance-detail\", 42);\n          i0.ɵɵlistener(\"close\", function CustomerDetailComponent_Template_app_instance_detail_close_66_listener() {\n            return ctx.closeInstanceDetailModal();\n          })(\"edit\", function CustomerDetailComponent_Template_app_instance_detail_edit_66_listener($event) {\n            return ctx.editInstanceFromDetail($event);\n          })(\"generateCertificate\", function CustomerDetailComponent_Template_app_instance_detail_generateCertificate_66_listener($event) {\n            return ctx.generateCertificate($event);\n          })(\"addVersion\", function CustomerDetailComponent_Template_app_instance_detail_addVersion_66_listener() {\n            return ctx.openAddInstanceVersionModal();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 43)(68, \"div\", 44)(69, \"div\", 12)(70, \"div\", 13)(71, \"h5\", 45);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_73_listener() {\n            return ctx.closeInstanceModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 16);\n          i0.ɵɵtemplate(75, CustomerDetailComponent_div_75_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementStart(76, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_76_listener() {\n            return ctx.saveInstance();\n          });\n          i0.ɵɵelementStart(77, \"div\", 19)(78, \"div\", 20)(79, \"label\", 46);\n          i0.ɵɵtext(80, \"N\\u00E1zev\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"input\", 47);\n          i0.ɵɵtemplate(82, CustomerDetailComponent_div_82_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 20)(84, \"label\", 48);\n          i0.ɵɵtext(85, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"select\", 49)(87, \"option\", 50);\n          i0.ɵɵtext(88, \"Aktivn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"option\", 50);\n          i0.ɵɵtext(90, \"Blokovan\\u00E1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"option\", 50);\n          i0.ɵɵtext(92, \"Expirovan\\u00E1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"option\", 50);\n          i0.ɵɵtext(94, \"Zku\\u0161ebn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"option\", 50);\n          i0.ɵɵtext(96, \"\\u00DAdr\\u017Eba\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"div\", 19)(98, \"div\", 20)(99, \"label\", 51);\n          i0.ɵɵtext(100, \"URL serveru\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(101, \"input\", 52);\n          i0.ɵɵtemplate(102, CustomerDetailComponent_div_102_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"div\", 20)(104, \"label\", 53);\n          i0.ɵɵtext(105, \"Datum expirace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(106, \"input\", 54);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 26)(108, \"label\", 55);\n          i0.ɵɵtext(109, \"Pozn\\u00E1mky\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(110, \"textarea\", 56);\n          i0.ɵɵtemplate(111, CustomerDetailComponent_div_111_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 26)(113, \"label\", 57);\n          i0.ɵɵtext(114, \"Moduly\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 58);\n          i0.ɵɵelement(116, \"input\", 59);\n          i0.ɵɵelementStart(117, \"label\", 60);\n          i0.ɵɵtext(118, \"Reporting\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(119, \"div\", 58);\n          i0.ɵɵelement(120, \"input\", 61);\n          i0.ɵɵelementStart(121, \"label\", 62);\n          i0.ɵɵtext(122, \"Pokro\\u010Dil\\u00E9 zabezpe\\u010Den\\u00ED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(123, \"div\", 58);\n          i0.ɵɵelement(124, \"input\", 63);\n          i0.ɵɵelementStart(125, \"label\", 64);\n          i0.ɵɵtext(126, \"API integrace\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(127, \"div\", 58);\n          i0.ɵɵelement(128, \"input\", 65);\n          i0.ɵɵelementStart(129, \"label\", 66);\n          i0.ɵɵtext(130, \"Export dat\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(131, \"div\", 58);\n          i0.ɵɵelement(132, \"input\", 67);\n          i0.ɵɵelementStart(133, \"label\", 68);\n          i0.ɵɵtext(134, \"Customizace\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(135, \"div\", 38)(136, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_136_listener() {\n            return ctx.closeInstanceModal();\n          });\n          i0.ɵɵtext(137, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"button\", 40);\n          i0.ɵɵtemplate(139, CustomerDetailComponent_span_139_Template, 1, 0, \"span\", 41);\n          i0.ɵɵtext(140, \" Ulo\\u017Eit \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(141, \"div\", 69)(142, \"div\", 11)(143, \"div\", 12)(144, \"div\", 13)(145, \"h5\", 70);\n          i0.ɵɵtext(146);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_147_listener() {\n            return ctx.closeInstanceVersionModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(148, \"div\", 16);\n          i0.ɵɵtemplate(149, CustomerDetailComponent_div_149_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementStart(150, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomerDetailComponent_Template_form_ngSubmit_150_listener() {\n            return ctx.saveInstanceVersion();\n          });\n          i0.ɵɵelementStart(151, \"div\", 26)(152, \"label\", 71);\n          i0.ɵɵtext(153, \"Instance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(154, \"input\", 72);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"div\", 26)(156, \"label\", 73);\n          i0.ɵɵtext(157, \"Verze:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"select\", 74)(159, \"option\", 75);\n          i0.ɵɵtext(160, \"-- Vyberte verzi --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(161, CustomerDetailComponent_option_161_Template, 2, 2, \"option\", 76);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(162, CustomerDetailComponent_div_162_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"div\", 26)(164, \"label\", 77);\n          i0.ɵɵtext(165, \"Instaloval:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(166, \"select\", 78)(167, \"option\", 75);\n          i0.ɵɵtext(168, \"-- Vyberte u\\u017Eivatele --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(169, CustomerDetailComponent_option_169_Template, 2, 3, \"option\", 76);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(170, CustomerDetailComponent_div_170_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"div\", 26)(172, \"label\", 55);\n          i0.ɵɵtext(173, \"Pozn\\u00E1mky:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(174, \"textarea\", 79);\n          i0.ɵɵtemplate(175, CustomerDetailComponent_div_175_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"div\", 38)(177, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function CustomerDetailComponent_Template_button_click_177_listener() {\n            return ctx.closeInstanceVersionModal();\n          });\n          i0.ɵɵtext(178, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(179, \"button\", 40);\n          i0.ɵɵtemplate(180, CustomerDetailComponent_span_180_Template, 1, 0, \"span\", 41);\n          i0.ɵɵtext(181, \" Ulo\\u017Eit \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(182, \"app-certificate-modal\", 80);\n          i0.ɵɵlistener(\"close\", function CustomerDetailComponent_Template_app_certificate_modal_close_182_listener() {\n            return ctx.closeCertificateModal();\n          })(\"download\", function CustomerDetailComponent_Template_app_certificate_modal_download_182_listener() {\n            return ctx.downloadCertificate();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          let tmp_14_0;\n          let tmp_15_0;\n          let tmp_16_0;\n          let tmp_17_0;\n          let tmp_18_0;\n          let tmp_28_0;\n          let tmp_34_0;\n          let tmp_35_0;\n          let tmp_36_0;\n          let tmp_44_0;\n          let tmp_46_0;\n          let tmp_47_0;\n          let tmp_48_0;\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer && ctx.isAdmin);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && ctx.customer);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditContactMode ? \"Upravit kontakt\" : \"P\\u0159idat kontakt\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.contactError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.contactForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c0, ((tmp_12_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_12_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.contactForm.get(\"position\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_14_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c0, ((tmp_15_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_15_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.contactForm.get(\"phone\")) == null ? null : tmp_16_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c0, ((tmp_17_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_17_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.contactForm.get(\"notes\")) == null ? null : tmp_18_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.contactForm.invalid || ctx.savingContact);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingContact);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"instance\", ctx.selectedInstanceForVersion)(\"certificateInfo\", ctx.selectedInstanceForVersion ? ctx.certificateInfo[ctx.selectedInstanceForVersion.id] : null)(\"instanceVersions\", ctx.selectedInstanceForVersion ? ctx.instanceVersions[ctx.selectedInstanceForVersion.id] || i0.ɵɵpureFunction0(59, _c1) : i0.ɵɵpureFunction0(60, _c1))(\"showAddVersionButton\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditInstanceMode ? \"Upravit instanci DIS\" : \"P\\u0159idat instanci DIS\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.instanceError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.instanceForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_28_0.invalid) && ((tmp_28_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_28_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Active);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Blocked);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Expired);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Trial);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.InstanceStatus.Maintenance);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_34_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_34_0.invalid) && ((tmp_34_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_34_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c0, ((tmp_35_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_35_0.invalid) && ((tmp_35_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_35_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_36_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_36_0.invalid) && ((tmp_36_0 = ctx.instanceForm.get(\"notes\")) == null ? null : tmp_36_0.touched));\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"disabled\", ctx.instanceForm.invalid || ctx.savingInstance);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingInstance);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.isEditInstanceVersionMode ? \"Upravit verzi instance\" : \"P\\u0159idat verzi instance\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.instanceVersionError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.instanceVersionForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.versions);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_44_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_44_0.invalid) && ((tmp_44_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_44_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.users);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_46_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_46_0.invalid) && ((tmp_46_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_46_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c0, ((tmp_47_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_47_0.invalid) && ((tmp_47_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_47_0.touched)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_48_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_48_0.invalid) && ((tmp_48_0 = ctx.instanceVersionForm.get(\"notes\")) == null ? null : tmp_48_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.instanceVersionForm.invalid || ctx.savingInstanceVersion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingInstanceVersion);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"certificate\", ctx.generatedCertificate)(\"instanceName\", (ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name) || \"\");\n        }\n      },\n      dependencies: [i14.NgClass, i14.NgForOf, i14.NgIf, i11.ɵNgNoValidate, i11.NgSelectOption, i11.ɵNgSelectMultipleOption, i11.DefaultValueAccessor, i11.CheckboxControlValueAccessor, i11.SelectControlValueAccessor, i11.NgControlStatus, i11.NgControlStatusGroup, i11.FormGroupDirective, i11.FormControlName, i15.CertificateModalComponent, i16.InstanceDetailComponent, i17.LocalDatePipe],\n      styles: [\".required-field[_ngcontent-%COMP%]::after {\\n  content: ' *';\\n  color: #dc3545;\\n  font-weight: bold;\\n}\\n\\n\\n.valid-field[_ngcontent-%COMP%]::after {\\n  content: '';\\n}\\n\\n\\n.card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  margin-bottom: 1.5rem;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\n\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--bs-light);\\n  font-weight: 600;\\n}\\n\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  background-color: #2b3035;\\n  border-color: #373b3e;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--bs-dark);\\n  color: var(--bs-light);\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  background-color: #212529;\\n  border-color: #495057;\\n  color: #e9ecef;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus, body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\\n  background-color: #2b3035;\\n  color: #e9ecef;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #adb5bd !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AAcnE,SAAuCC,cAAc,QAAQ,6BAA6B;AAI1F,SAASC,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;ICb9BC,2BAAgD;IACFA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IACpEA,wBAAgC;IAACA,4CACnC;IAAAA,iBAAS;IACTA,kCAAyD;IAAzBA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IACtDA,wBAAiC;IAACA,6CACpC;IAAAA,iBAAS;;;;;IAOjBA,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAKpDA,+BAA8C;IAC5CA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAcYA,6BAA8D;IAAAA,0CAAgB;IAAAA,iBAAQ;;;;;IACtFA,6BAA+D;IAAAA,sFAAmC;IAAAA,iBAAQ;;;;;IAF5GA,gCAA6G;IAC3GA,0FAAsF;IACtFA,0FAA0G;IAC5GA,iBAAM;;;;;;IAFIA,eAAoD;IAApDA,+IAAoD;IACpDA,eAAqD;IAArDA,gJAAqD;;;;;IAO7DA,6BAAsE;IAAAA,uCAAkB;IAAAA,iBAAQ;;;;;IAChGA,6BAAuE;IAAAA,kFAAoC;IAAAA,iBAAQ;;;;;IAFrHA,gCAA6H;IAC3HA,0FAAgG;IAChGA,0FAAmH;IACrHA,iBAAM;;;;;;IAFIA,eAA4D;IAA5DA,uJAA4D;IAC5DA,eAA6D;IAA7DA,wJAA6D;;;;;IASrEA,6BAAoE;IAAAA,mFAAgC;IAAAA,iBAAQ;;;;;IAD9GA,gCAAuH;IACrHA,0FAA4G;IAC9GA,iBAAM;;;;;IADIA,eAA0D;IAA1DA,qJAA0D;;;;;IAOlEA,6BAAgE;IAAAA,mFAAgC;IAAAA,iBAAQ;;;;;IAD1GA,gCAA+G;IAC7GA,0FAAwG;IAC1GA,iBAAM;;;;;IADIA,eAAsD;IAAtDA,iJAAsD;;;;;IAS9DA,6BAA4D;IAAAA,gDAAsB;IAAAA,iBAAQ;;;;;IAC1FA,6BAAgE;IAAAA,iFAAmC;IAAAA,iBAAQ;;;;;IAF7GA,gCAA+G;IAC7GA,0FAA0F;IAC1FA,0FAA2G;IAC7GA,iBAAM;;;;;;IAFIA,eAAkD;IAAlDA,6IAAkD;IAClDA,eAAsD;IAAtDA,iJAAsD;;;;;IAO9DA,6BAAgE;IAAAA,kFAAoC;IAAAA,iBAAQ;;;;;IAD9GA,gCAA+G;IAC7GA,0FAA4G;IAC9GA,iBAAM;;;;;IADIA,eAAsD;IAAtDA,iJAAsD;;;;;IAS9DA,6BAAkE;IAAAA,+EAAiC;IAAAA,iBAAQ;;;;;IAD7GA,gCAAmH;IACjHA,0FAA2G;IAC7GA,iBAAM;;;;;IADIA,eAAwD;IAAxDA,mJAAwD;;;;;IAWhEA,6BAAgE;IAAAA,qCAAgB;IAAAA,iBAAQ;;;;;IACxFA,6BAAiE;IAAAA,iFAAmC;IAAAA,iBAAQ;;;;;IAF9GA,gCAAiH;IAC/GA,0FAAwF;IACxFA,0FAA4G;IAC9GA,iBAAM;;;;;;IAFIA,eAAsD;IAAtDA,iJAAsD;IACtDA,eAAuD;IAAvDA,kJAAuD;;;;;IAS/DA,6BAA8D;IAAAA,0CAAgB;IAAAA,iBAAQ;;;;;IACtFA,6BAA+D;IAAAA,sFAAmC;IAAAA,iBAAQ;;;;;IAF5GA,gCAA6G;IAC3GA,0FAAsF;IACtFA,0FAA0G;IAC5GA,iBAAM;;;;;;IAFIA,eAAoD;IAApDA,+IAAoD;IACpDA,eAAqD;IAArDA,gJAAqD;;;;;IAO7DA,6BAAoE;IAAAA,wCAAc;IAAAA,iBAAQ;;;;;IAC1FA,6BAAqE;IAAAA,mFAAgC;IAAAA,iBAAQ;;;;;IAF/GA,gCAAyH;IACvHA,0FAA0F;IAC1FA,0FAA6G;IAC/GA,iBAAM;;;;;;IAFIA,eAA0D;IAA1DA,qJAA0D;IAC1DA,eAA2D;IAA3DA,sJAA2D;;;;;IAOnEA,6BAAiE;IAAAA,yCAAe;IAAAA,iBAAQ;;;;;IACxFA,6BAAkE;IAAAA,qFAAkC;IAAAA,iBAAQ;;;;;IAF9GA,gCAAmH;IACjHA,0FAAwF;IACxFA,0FAA4G;IAC9GA,iBAAM;;;;;;IAFIA,eAAuD;IAAvDA,kJAAuD;IACvDA,eAAwD;IAAxDA,mJAAwD;;;;;IAQlEA,6BAAgE;IAAAA,yFAAsC;IAAAA,iBAAQ;;;;;IADhHA,gCAA+G;IAC7GA,0FAA8G;IAChHA,iBAAM;;;;;IADIA,eAAsD;IAAtDA,iJAAsD;;;;;IAM9DA,4BAA2G;;;;;;;;;;;IAlHrHA,+BAA0C;IAErBA,YAAkE;IAAAA,iBAAK;IAE1FA,+BAAuB;IACYA;MAAAA;MAAA;MAAA,OAAYA,qCAAc;IAAA,EAAC;IAC1DA,+BAAiB;IAEuCA,0BAAK;IAAAA,iBAAQ;IACjEA,6BAAyG;IACzGA,kFAGM;IACRA,iBAAM;IACNA,gCAA2B;IACmCA,wBAAO;IAAAA,iBAAQ;IAC3EA,6BAAkO;IAClOA,kFAGM;IACRA,iBAAM;IAERA,gCAAiB;IAE6BA,yBAAG;IAAAA,iBAAQ;IACrDA,6BAAkN;IAClNA,kFAEM;IACRA,iBAAM;IACNA,gCAA2B;IACaA,yBAAG;IAAAA,iBAAQ;IACjDA,6BAAkM;IAClMA,kFAEM;IACRA,iBAAM;IAERA,gCAAiB;IAEyBA,sBAAK;IAAAA,iBAAQ;IACnDA,8BAAgG;IAChGA,kFAGM;IACRA,iBAAM;IACNA,gCAA2B;IACaA,wBAAO;IAAAA,iBAAQ;IACrDA,8BAAgG;IAChGA,kFAEM;IACRA,iBAAM;IAERA,gCAAiB;IAE2BA,oBAAG;IAAAA,iBAAQ;IACnDA,8BAA0M;IAC1MA,kFAEM;IACRA,iBAAM;IACNA,2BACM;IACRA,iBAAM;IACNA,gCAAiB;IAEyCA,sBAAK;IAAAA,iBAAQ;IACnEA,8BAAiG;IACjGA,kFAGM;IACRA,iBAAM;IAERA,gCAAiB;IAEuCA,2BAAK;IAAAA,iBAAQ;IACjEA,8BAA6F;IAC7FA,kFAGM;IACRA,iBAAM;IACNA,iCAA2B;IACiCA,yBAAG;IAAAA,iBAAQ;IACrEA,8BAAuG;IACvGA,kFAGM;IACRA,iBAAM;IACNA,iCAA2B;IAC8BA,0BAAI;IAAAA,iBAAQ;IACnEA,8BAAkG;IAClGA,kFAGM;IACRA,iBAAM;IAERA,gCAAkB;IACsBA,8BAAQ;IAAAA,iBAAQ;IACtDA,gCAAkN;IAClNA,kFAEM;IACRA,iBAAM;IACNA,gCAAwC;IACuBA;MAAAA;MAAA;MAAA,OAASA,mCAAY;IAAA,EAAC;IAACA,4BAAM;IAAAA,iBAAS;IACnGA,oCAAmH;IAAzBA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAChHA,oFAA2G;IAC3GA,8BACF;IAAAA,iBAAS;;;;;;;;;;;;;;;;;;;;;IAlHIA,eAAkE;IAAlEA,kHAAkE;IAG7EA,eAA0B;IAA1BA,+CAA0B;IAKpBA,eAA4E;IAA5EA,uLAA4E;IAO6BA,eAAkH;IAAlHA,uOAAkH;IAC3NA,eAA4F;IAA5FA,uMAA4F;IASGA,eAA4G;IAA5GA,iOAA4G;IAC3MA,eAAsF;IAAtFA,iMAAsF;IAMCA,eAAoG;IAApGA,yNAAoG;IAC3LA,eAA8E;IAA9EA,yLAA8E;IAS9EA,eAA8E;IAA9EA,yLAA8E;IAQ9EA,eAA8E;IAA9EA,6LAA8E;IAQaA,eAAwG;IAAxGA,iOAAwG;IACnMA,eAAkF;IAAlFA,iMAAkF;IAWlFA,eAAgF;IAAhFA,+LAAgF;IAUhFA,eAA4E;IAA5EA,2LAA4E;IAQ5EA,eAAwF;IAAxFA,uMAAwF;IAQxFA,eAAkF;IAAlFA,iMAAkF;IAQQA,eAAoG;IAApGA,6NAAoG;IAChMA,eAA8E;IAA9EA,6LAA8E;IAMtCA,eAA2C;IAA3CA,uEAA2C;IAChFA,eAAY;IAAZA,oCAAY;;;;;IAkBrBA,yBAA8B;IAAQA,yBAAI;IAAAA,iBAAS;IAACA,YAAwB;IAAAA,iBAAI;;;;IAA5BA,eAAwB;IAAxBA,0DAAwB;;;;;IAC5EA,yBAA0B;IAAQA,yBAAI;IAAAA,iBAAS;IAACA,YAAoB;IAAAA,iBAAI;;;;IAAxBA,eAAoB;IAApBA,sDAAoB;;;;;IACpEA,yBAA0B;IAAQA,sBAAM;IAAAA,iBAAS;IAACA,YAAoB;IAAAA,iBAAI;;;;IAAxBA,eAAoB;IAApBA,sDAAoB;;;;;IACtEA,yBAA0B;IAAQA,wBAAQ;IAAAA,iBAAS;IAACA,YAAoB;IAAAA,iBAAI;;;;IAAxBA,eAAoB;IAApBA,sDAAoB;;;;;IACxEA,yBAA4B;IAAQA,oBAAI;IAAAA,iBAAS;IAACA,YAAsB;IAAAA,iBAAI;;;;IAA1BA,eAAsB;IAAtBA,wDAAsB;;;;;IAW5EA,gCAA6C;IAE9BA,8BAAS;IAAAA,iBAAS;IAC7BA,yBAAG;IAAAA,YAAoB;IAAAA,iBAAI;;;;IAAxBA,eAAoB;IAApBA,4CAAoB;;;;;IA3B/BA,+BAAuD;IAElCA,0CAAgB;IAAAA,iBAAK;IAExCA,+BAAuB;IAGbA,YAAmB;IAAAA,iBAAK;IAC5BA,yBAAG;IAAQA,yBAAQ;IAAAA,iBAAS;IAACA,aAA2B;IAAAA,iBAAI;IAC5DA,6EAAgF;IAChFA,6EAAwE;IACxEA,6EAA0E;IAC1EA,6EAA4E;IAC5EA,6EAA4E;IAC9EA,iBAAM;IACNA,iCAAsB;IACTA,uBAAM;IAAAA,iBAAS;IAACA,aAAqB;IAAAA,iBAAI;IACpDA,0BAAG;IAAQA,4BAAM;IAAAA,iBAAS;IAACA,aAAmB;IAAAA,iBAAI;IAClDA,0BAAG;IAAQA,0BAAI;IAAAA,iBAAS;IAACA,aAAyB;IAAAA,iBAAI;IACtDA,0BAAG;IAAQA,2BAAK;IAAAA,iBAAS;IAACA,aAAsB;IAAAA,iBAAI;IACpDA,0BAAG;IAAQA,gCAAU;IAAAA,iBAAS;IAACA,aAAuD;;IAAAA,iBAAI;IAC1FA,0BAAG;IAAQA,oCAAc;IAAAA,iBAAS;IAACA,aAAuD;;IAAAA,iBAAI;IAGlGA,mFAKM;IACRA,iBAAM;;;;IAvBIA,eAAmB;IAAnBA,0CAAmB;IACMA,eAA2B;IAA3BA,4DAA2B;IACpDA,eAAwB;IAAxBA,gDAAwB;IACxBA,eAAoB;IAApBA,4CAAoB;IACpBA,eAAoB;IAApBA,4CAAoB;IACpBA,eAAoB;IAApBA,4CAAoB;IACpBA,eAAsB;IAAtBA,8CAAsB;IAGCA,eAAqB;IAArBA,sDAAqB;IACrBA,eAAmB;IAAnBA,oDAAmB;IACrBA,eAAyB;IAAzBA,0DAAyB;IACxBA,eAAsB;IAAtBA,uDAAsB;IACjBA,eAAuD;IAAvDA,qGAAuD;IACnDA,eAAuD;IAAvDA,qGAAuD;IAGxFA,eAAoB;IAApBA,4CAAoB;;;;;;IAa1BA,mCAAqF;IAAhCA;MAAAA;MAAA;MAAA,OAASA,4CAAqB;IAAA,EAAC;IAClFA,yBAAkC;IAACA,qCACrC;IAAAA,iBAAS;;;;;IAGTA,gCAAwE;IAEtCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAgF;IAC9EA,mFACF;IAAAA,iBAAM;;;;;IAqBIA,iCAAyD;IAAAA,mBAAG;IAAAA,iBAAO;;;;;IACnEA,iCAA4D;IAAAA,kBAAE;IAAAA,iBAAO;;;;;;IAInEA,mCAA8G;IAA/CA;MAAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IAC3FA,yBAAiC;IACnCA,iBAAS;;;;;;IACTA,mCAA8G;IAAhDA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAsB;IAAA,EAAC;IAC5FA,yBAAgC;IAClCA,iBAAS;;;;;IAhBfA,0BAAqC;IAC/BA,YAA8C;IAAAA,iBAAK;IACvDA,0BAAI;IAAAA,YAA6B;IAAAA,iBAAK;IACtCA,0BAAI;IAAAA,YAA0B;IAAAA,iBAAK;IACnCA,0BAAI;IAAAA,YAA0B;IAAAA,iBAAK;IACnCA,0BAAI;IACFA,iGAAmE;IACnEA,iGAAqE;IACvEA,iBAAK;IACLA,2BAAI;IAEAA,qGAES;IACTA,qGAES;IACXA,iBAAM;;;;;IAhBJA,eAA8C;IAA9CA,+EAA8C;IAC9CA,eAA6B;IAA7BA,iDAA6B;IAC7BA,eAA0B;IAA1BA,8CAA0B;IAC1BA,eAA0B;IAA1BA,8CAA0B;IAEIA,eAAuB;IAAvBA,4CAAuB;IACrBA,eAAwB;IAAxBA,6CAAwB;IAI/CA,eAAa;IAAbA,sCAAa;IAGbA,eAAa;IAAbA,sCAAa;;;;;IA3BlCA,gCAA8E;IAIlEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,qBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,wBAAO;IAAAA,iBAAK;IAChBA,2BAAI;IAAAA,mCAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,wFAmBK;IACPA,iBAAQ;;;;IApBkBA,gBAAW;IAAXA,0CAAW;;;;;IA/B7CA,+BAAuD;IAElCA,wBAAQ;IAAAA,iBAAK;IAC9BA,uFAES;IACXA,iBAAM;IACNA,+BAAuB;IACrBA,iFAIM;IAENA,iFAEM;IAENA,kFAmCM;IACRA,iBAAM;;;;IAnDKA,eAAa;IAAbA,qCAAa;IAKhBA,eAAqB;IAArBA,6CAAqB;IAMrBA,eAA+C;IAA/CA,8EAA+C;IAI/CA,eAA6C;IAA7CA,4EAA6C;;;;;;IA2CnDA,mCAAsF;IAAjCA;MAAAA;MAAA;MAAA,OAASA,6CAAsB;IAAA,EAAC;IACnFA,yBAAkC;IAACA,sCACrC;IAAAA,iBAAS;;;;;IAGTA,gCAAyE;IAEvCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAkF;IAChFA,uFACF;IAAAA,iBAAM;;;;;IAsBIA,4BAAoD;IAClDA,YACF;IAAAA,iBAAO;;;;;IADLA,eACF;IADEA,oGACF;;;;;IACAA,4BAAqD;IAAAA,mBAAG;IAAAA,iBAAO;;;;;;IAU7DA,mCAAgH;IAAjDA;MAAAA;MAAA;MAAA;MAAA,OAASA,kDAAsB;IAAA,EAAC;IAC7FA,yBAAiC;IACnCA,iBAAS;;;;;;IAvBfA,0BAAuC;IACjCA,YAAmB;IAAAA,iBAAK;IAC5BA,0BAAI;IAEAA,YACF;IAAAA,iBAAO;IAETA,0BAAI;IACFA,6FAEO;IACPA,6FAA+D;IACjEA,iBAAK;IACLA,0BAAI;IACFA,aACF;;IAAAA,iBAAK;IACLA,2BAAI;IAE4CA;MAAA;MAAA;MAAA;MAAA,OAASA,wDAA4B;IAAA,EAAC;IAChFA,0BAA8B;IAChCA,iBAAS;IACTA,qGAES;IACXA,iBAAM;;;;;IAvBJA,eAAmB;IAAnBA,uCAAmB;IAEDA,eAAmD;IAAnDA,6EAAmD;IACrEA,eACF;IADEA,mFACF;IAGOA,eAA2C;IAA3CA,kIAA2C;IAG3CA,eAA4C;IAA5CA,qIAA4C;IAGnDA,eACF;IADEA,uJACF;IAMaA,eAAa;IAAbA,sCAAa;;;;;IAjClCA,gCAAgF;IAIpEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,wCAAc;IAAAA,iBAAK;IACvBA,2BAAI;IAAAA,kDAAkB;IAAAA,iBAAK;IAC3BA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,yFA0BK;IACPA,iBAAQ;;;;IA3BmBA,gBAAY;IAAZA,2CAAY;;;;;IA9B/CA,+BAAuD;IAElCA,4BAAY;IAAAA,iBAAK;IAClCA,uFAES;IACXA,iBAAM;IACNA,+BAAuB;IACrBA,iFAIM;IAENA,iFAEM;IAENA,kFAyCM;IACRA,iBAAM;;;;IAzDKA,eAAa;IAAbA,qCAAa;IAKhBA,eAAsB;IAAtBA,8CAAsB;IAMtBA,eAAiD;IAAjDA,gFAAiD;IAIjDA,eAA+C;IAA/CA,8EAA+C;;;;;IAuDnDA,gCAA0D;IACxDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,oDACF;;;;;IAOQA,6BAAkE;IAAAA,0CAAgB;IAAAA,iBAAQ;;;;;IAC1FA,6BAAmE;IAAAA,sFAAmC;IAAAA,iBAAQ;;;;;IAFhHA,gCAAqH;IACnHA,mFAA0F;IAC1FA,mFAA8G;IAChHA,iBAAM;;;;;;IAFIA,eAAwD;IAAxDA,kJAAwD;IACxDA,eAAyD;IAAzDA,mJAAyD;;;;;IAOjEA,6BAAiE;IAAAA,uDAAmB;IAAAA,iBAAQ;;;;;IAC5FA,6BAAkE;IAAAA,mGAAsC;IAAAA,iBAAQ;;;;;IAFlHA,gCAAmH;IACjHA,mFAA4F;IAC5FA,mFAAgH;IAClHA,iBAAM;;;;;;IAFIA,eAAuD;IAAvDA,iJAAuD;IACvDA,eAAwD;IAAxDA,kJAAwD;;;;;IAQlEA,6BAAkE;IAAAA,kFAAoC;IAAAA,iBAAQ;;;;;IADhHA,gCAAmH;IACjHA,mFAA8G;IAChHA,iBAAM;;;;;IADIA,eAAwD;IAAxDA,mJAAwD;;;;;IAQ9DA,6BAA2D;IAAAA,gDAAsB;IAAAA,iBAAQ;;;;;IACzFA,6BAA+D;IAAAA,iFAAmC;IAAAA,iBAAQ;;;;;IAF5GA,gCAA6G;IAC3GA,mFAAyF;IACzFA,mFAA0G;IAC5GA,iBAAM;;;;;;IAFIA,eAAiD;IAAjDA,4IAAiD;IACjDA,eAAqD;IAArDA,gJAAqD;;;;;IAO7DA,6BAA+D;IAAAA,kFAAoC;IAAAA,iBAAQ;;;;;IAD7GA,gCAA6G;IAC3GA,mFAA2G;IAC7GA,iBAAM;;;;;IADIA,eAAqD;IAArDA,gJAAqD;;;;;IAQ/DA,6BAA+D;IAAAA,yFAAsC;IAAAA,iBAAQ;;;;;IAD/GA,gCAA6G;IAC3GA,mFAA6G;IAC/GA,iBAAM;;;;;IADIA,eAAqD;IAArDA,gJAAqD;;;;;IAU7DA,4BAAkH;;;;;IAgCxHA,gCAA2D;IACzDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,sDACF;;;;;IAOQA,6BAA8D;IAAAA,0CAAgB;IAAAA,iBAAQ;;;;;IACtFA,6BAA+D;IAAAA,sFAAmC;IAAAA,iBAAQ;;;;;IAF5GA,gCAA6G;IAC3GA,mFAAsF;IACtFA,mFAA0G;IAC5GA,iBAAM;;;;;;IAFIA,eAAoD;IAApDA,+IAAoD;IACpDA,eAAqD;IAArDA,gJAAqD;;;;;IAmB7DA,6BAAmE;IAAAA,2CAAsB;IAAAA,iBAAQ;;;;;IACjGA,6BAAoE;IAAAA,uFAAyC;IAAAA,iBAAQ;;;;;IAFvHA,gCAAuH;IACrHA,oFAAiG;IACjGA,oFAAqH;IACvHA,iBAAM;;;;;;IAFIA,eAAyD;IAAzDA,oJAAyD;IACzDA,eAA0D;IAA1DA,qJAA0D;;;;;IAYpEA,6BAAgE;IAAAA,yFAAsC;IAAAA,iBAAQ;;;;;IADhHA,gCAA+G;IAC7GA,oFAA8G;IAChHA,iBAAM;;;;;IADIA,eAAsD;IAAtDA,iJAAsD;;;;;IA6B9DA,4BAAmH;;;;;IAmBzHA,gCAAkE;IAChEA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6DACF;;;;;IAUMA,kCAA8D;IAAAA,YAA2B;IAAAA,iBAAS;;;;IAAzDA,uCAAoB;IAACA,eAA2B;IAA3BA,gDAA2B;;;;;IAGzFA,6BAA0E;IAAAA,qCAAgB;IAAAA,iBAAQ;;;;;IADpGA,gCAAqI;IACnIA,oFAAkG;IACpGA,iBAAM;;;;;IADIA,eAAgE;IAAhEA,2JAAgE;;;;;IAOxEA,kCAAqD;IAAAA,YAAwC;IAAAA,iBAAS;;;;IAAnEA,oCAAiB;IAACA,eAAwC;IAAxCA,2EAAwC;;;;;IAG7FA,6BAAkF;IAAAA,6CAAmB;IAAAA,iBAAQ;;;;;IAD/GA,gCAAqJ;IACnJA,oFAA6G;IAC/GA,iBAAM;;;;;IADIA,eAAwE;IAAxEA,mKAAwE;;;;;IAOhFA,6BAAuE;IAAAA,yFAAsC;IAAAA,iBAAQ;;;;;IADvHA,gCAA6H;IAC3HA,oFAAqH;IACvHA,iBAAM;;;;;IADIA,eAA6D;IAA7DA,wJAA6D;;;;;IAMrEA,4BAA0H;;;;;;ADjgBxI,OAAM,MAAOC,uBAAuB;EAyDlCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,cAA8B,EAC9BC,eAAgC,EAChCC,sBAA8C,EAC9CC,cAA8B,EAC9BC,WAAwB,EACxBC,kBAAsC,EACtCC,iBAAoC,EACpCC,YAA0B,EAC1BC,WAAwB,EACxBC,WAAwB,EACxBC,gBAAkC;IAblC,UAAK,GAALb,KAAK;IACL,WAAM,GAANC,MAAM;IACN,oBAAe,GAAfC,eAAe;IACf,mBAAc,GAAdC,cAAc;IACd,oBAAe,GAAfC,eAAe;IACf,2BAAsB,GAAtBC,sBAAsB;IACtB,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,uBAAkB,GAAlBC,kBAAkB;IAClB,sBAAiB,GAAjBC,iBAAiB;IACjB,iBAAY,GAAZC,YAAY;IACZ,gBAAW,GAAXC,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,qBAAgB,GAAhBC,gBAAgB;IAtE1B;IACA,mBAAc,GAAGlB,cAAc;IAC/B,eAAU,GAAW,CAAC;IACtB,aAAQ,GAA0B,IAAI;IACtC,aAAQ,GAAc,EAAE;IACxB,cAAS,GAAkB,EAAE;IAC7B,qBAAgB,GAAyC,EAAE;IAC3D,8BAAyB,GAAyC,EAAE;IACpE,oBAAe,GAA+B,EAAE;IAChD,oBAAe,GAAQ,EAAE;IACzB,sBAAiB,GAAkB,IAAI,CAAC,CAAC;IACzC,UAAK,GAAW,EAAE;IAClB,aAAQ,GAAiB,EAAE;IAE3B;IACA,SAAI,GAA+B,MAAM;IAOzC,YAAO,GAAY,KAAK;IACxB,oBAAe,GAAY,KAAK;IAChC,qBAAgB,GAAY,KAAK;IACjC,oBAAe,GAAY,KAAK;IAChC,iBAAY,GAAY,KAAK;IAE7B,WAAM,GAAY,KAAK;IACvB,kBAAa,GAAY,KAAK;IAC9B,mBAAc,GAAY,KAAK;IAC/B,0BAAqB,GAAY,KAAK;IAEtC,eAAU,GAAY,KAAK;IAC3B,sBAAiB,GAAY,KAAK;IAClC,uBAAkB,GAAY,KAAK;IACnC,8BAAyB,GAAY,KAAK;IAE1C,UAAK,GAAkB,IAAI;IAC3B,iBAAY,GAAkB,IAAI;IAClC,kBAAa,GAAkB,IAAI;IACnC,yBAAoB,GAAkB,IAAI;IAE1C;IACA,+BAA0B,GAAwB,IAAI;IAEtD;IACA,YAAO,GAAY,KAAK;IAExB,oBAAe,GAAmB,IAAI;IACtC,qBAAgB,GAAuB,IAAI;IAC3C,+BAA0B,GAAuB,IAAI;IAErD;IACA,yBAAoB,GAAyC,IAAI;IAkB/D;IACA,IAAI,CAACmB,YAAY,GAAG,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC;MACzCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DC,YAAY,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACnEE,SAAS,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC;MACzCG,KAAK,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC;MACrCI,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9DK,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DM,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACjEO,OAAO,EAAE,CAAC,iBAAiB,EAAE,CAAC/B,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9EQ,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAACgC,KAAK,EAAEhC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1DS,KAAK,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC;MACrCU,OAAO,EAAE,CAAC,EAAE,EAAElC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC;MACxCW,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC;KACtC,CAAC;IAEF,IAAI,CAACY,WAAW,GAAG,IAAI,CAACnB,WAAW,CAACI,KAAK,CAAC;MACxCgB,SAAS,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjEc,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAChEe,QAAQ,EAAE,CAAC,EAAE,EAAEvC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC;MACzCQ,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAACgC,KAAK,EAAEhC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1DS,KAAK,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAACwB,SAAS,CAAC,EAAE,CAAC,CAAC;MACrCW,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC;MACtCgB,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IAEF,IAAI,CAACC,YAAY,GAAG,IAAI,CAACxB,WAAW,CAACI,KAAK,CAAC;MACzCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DkB,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjEmB,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBR,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC;MACtCoB,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClBC,WAAW,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC,CAAC;MAC5CsB,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,oBAAoB,EAAE,CAAC,KAAK,CAAC;MAC7BC,gBAAgB,EAAE,CAAC,KAAK,CAAC;MACzBC,mBAAmB,EAAE,CAAC,KAAK;KAC5B,CAAC;IAEF,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAClC,WAAW,CAACI,KAAK,CAAC;MAChD+B,SAAS,EAAE,CAAC,EAAE,EAAEpD,UAAU,CAACuB,QAAQ,CAAC;MACpC8B,iBAAiB,EAAE,CAAC,EAAE,EAAErD,UAAU,CAACuB,QAAQ,CAAC;MAC5CY,KAAK,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACwB,SAAS,CAAC,GAAG,CAAC;KACtC,CAAC;EACJ;EAEA8B,QAAQ;IACN;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACrC,WAAW,CAACqC,OAAO,EAAE;IAEzC;IACA,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,IAAI,CAAClD,MAAM,CAACmD,GAAG,KAAK,gBAAgB,EAAE;MACxC;MACA,IAAI,CAACC,IAAI,GAAG,QAAQ;MACpB,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB;MACA,IAAI,CAAC7C,iBAAiB,CAAC8C,cAAc,CAAC,CACpC;QAAEC,KAAK,EAAE,WAAW;QAAEJ,GAAG,EAAE,YAAY;QAAEK,IAAI,EAAE;MAAe,CAAE,EAChE;QAAED,KAAK,EAAE,kBAAkB;QAAEJ,GAAG,EAAE,gBAAgB;QAAEK,IAAI,EAAE;MAAkB,CAAE,CAC/E,CAAC;MAEF;MACA,IAAI,CAAC3C,YAAY,CAAC4C,KAAK,CAAC;QACtBjC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACzB,KAAK,CAAC2D,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;QACnC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACE,UAAU,GAAG,CAACF,MAAM,CAAC,IAAI,CAAC;UAC/B,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAACD,UAAU,CAAC;UAExC;UACA,IAAI,CAAC7D,KAAK,CAAC+D,WAAW,CAACH,SAAS,CAACG,WAAW,IAAG;YAC7CC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,WAAW,CAAC;YACzC,IAAIA,WAAW,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;cAClCC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;cACnD,IAAI,CAACZ,IAAI,GAAG,MAAM;cAClB;cACA,IAAI,CAACa,0BAA0B,GAAG,MAAK;gBACrCF,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE,IAAI,CAACE,QAAQ,CAAC;gBAC7E,IAAI,IAAI,CAACA,QAAQ,EAAE;kBACjBH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;kBACnD,IAAI,CAACG,YAAY,EAAE;iBACpB,MAAM;kBACLJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;cAE1D,CAAC;aACF,MAAM;cACLD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;cAC5B,IAAI,CAACZ,IAAI,GAAG,MAAM;;UAEtB,CAAC,CAAC;UAEF;UACA,IAAI;YACFW,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;YACpE,MAAMI,sBAAsB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;YACzEP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,sBAAsB,CAAC;YAE3D,IAAIA,sBAAsB,EAAE;cAC1B,MAAMG,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACL,sBAAsB,CAAC;cAC7DL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,kBAAkB,CAAC;cAElD;cACA;cACA,MAAMG,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;cACxC,MAAMC,QAAQ,GAAGN,kBAAkB,CAACO,SAAS,IAAI,CAAC;cAClD,MAAMC,QAAQ,GAAIL,WAAW,GAAGG,QAAQ,GAAI,KAAK,CAAC,CAAC;cAEnDd,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACJ,UAAU,CAAC;cACpDG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,kBAAkB,CAACX,UAAU,CAAC;cACxEG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,QAAQ,EAAE,UAAU,EAAEL,WAAW,GAAGG,QAAQ,EAAE,KAAK,CAAC;cAEvF,IAAIN,kBAAkB,CAACX,UAAU,KAAK,IAAI,CAACA,UAAU,IAAImB,QAAQ,EAAE;gBACjE,MAAMC,UAAU,GAAGT,kBAAkB,CAACS,UAAU;gBAChDjB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEgB,UAAU,CAAC;gBAE1D;gBACA,IAAI,CAACC,iBAAiB,GAAGD,UAAU;gBAEnC;gBACAX,YAAY,CAACa,UAAU,CAAC,oBAAoB,CAAC;gBAC7CnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;eACnD,MAAM;gBACLD,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;;aAE9F,MAAM;cACLD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;WAEjE,CAAC,OAAOmB,KAAK,EAAE;YACdpB,OAAO,CAACoB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;;;MAGtE,CAAC,CAAC;;EAEN;EAEA;;;EAGAtB,kBAAkB,CAACD,UAAkB;IACnCG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEJ,UAAU,CAAC;IAC/D,IAAI,CAACwB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAACrF,eAAe,CAACsF,WAAW,CAAC3B,UAAU,CAAC,CACzC4B,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAGvB,QAAQ,IAAI;QACjBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,QAAQ,CAAC;QAC9C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QAExB;QACA,IAAI,CAAC1D,iBAAiB,CAAC8C,cAAc,CAAC,CACpC;UAAEC,KAAK,EAAE,WAAW;UAAEJ,GAAG,EAAE,YAAY;UAAEK,IAAI,EAAE;QAAe,CAAE,EAChE;UAAED,KAAK,EAAEW,QAAQ,CAACnD,IAAI;UAAEoC,GAAG,EAAE,cAAcS,UAAU,EAAE;UAAEJ,IAAI,EAAE;QAAkB,CAAE,CACpF,CAAC;QAEF,IAAI,CAAC4B,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACM,YAAY,CAAC9B,UAAU,CAAC;QAE7B;QACA,IAAI,CAAC+B,aAAa,CAAC/B,UAAU,CAAC;QAE9B;QACA,IAAI,IAAI,CAACK,0BAA0B,EAAE;UACnCF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnD,IAAI,CAACC,0BAA0B,EAAE;UACjC,IAAI,CAACA,0BAA0B,GAAG,IAAI,CAAC,CAAC;;MAE5C,CAAC;;MACDkB,KAAK,EAAGS,GAAG,IAAI;QACb,IAAI,CAACT,KAAK,GAAG,iCAAiCS,GAAG,CAACC,OAAO,EAAE;QAC3D,IAAI,CAACT,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACN;EAEA;;;EAGAI,YAAY,CAAC9B,UAAkB;IAC7B,IAAI,CAAC1D,cAAc,CAAC4F,uBAAuB,CAAClC,UAAU,CAAC,CACpD4B,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAGM,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACV,eAAe,GAAG,KAAK;MAC9B,CAAC;MACDF,KAAK,EAAGS,GAAG,IAAI;QACb,IAAI,CAACT,KAAK,GAAG,gCAAgCS,GAAG,CAACC,OAAO,EAAE;QAC1D,IAAI,CAACR,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACN;EAEA;;;EAGAM,aAAa,CAAC/B,UAAkB;IAC9B,IAAI,CAACzD,eAAe,CAAC6F,wBAAwB,CAACpC,UAAU,CAAC,CACtD4B,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAGQ,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACX,gBAAgB,GAAG,KAAK;QAE7B;QACAW,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAG;UAC3B,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAACE,EAAE,CAAC;QACxC,CAAC,CAAC;QAEF;QACA,IAAI,IAAI,CAACpB,iBAAiB,EAAE;UAC1BlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACiB,iBAAiB,CAAC;UAEjE;UACA,MAAMkB,QAAQ,GAAGF,SAAS,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAK,IAAI,CAACpB,iBAAiB,CAAC;UACrElB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmC,QAAQ,CAAC;UAE3C,IAAIA,QAAQ,EAAE;YACZpC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEmC,QAAQ,CAACE,EAAE,CAAC;YAEtE;YACAG,UAAU,CAAC,MAAK;cACd;cACA,IAAI,CAACC,0BAA0B,GAAGN,QAAQ;cAE1C;cACA,IAAI,CAACO,mBAAmB,CAACP,QAAQ,CAACE,EAAE,CAAC;cAErC;cACAtC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;cAC/D,MAAM2C,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;cACnE,IAAIF,YAAY,EAAE;gBAChB5C,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;gBACjD,MAAM8C,KAAK,GAAG,IAAIC,MAAM,CAACC,SAAS,CAACC,KAAK,CAACN,YAAY,CAAC;gBACtDG,KAAK,CAACI,IAAI,EAAE;eACb,MAAM;gBACLnD,OAAO,CAACoB,KAAK,CAAC,sDAAsD,CAAC;;YAEzE,CAAC,EAAE,GAAG,CAAC;WACR,MAAM;YACLpB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;UAGtE;UACA,IAAI,CAACiB,iBAAiB,GAAG,IAAI;;MAEjC,CAAC;MACDE,KAAK,EAAGS,GAAG,IAAI;QACb,IAAI,CAACT,KAAK,GAAG,oCAAoCS,GAAG,CAACC,OAAO,EAAE;QAC9D,IAAI,CAACP,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACN;EAEA;;;EAGAc,oBAAoB,CAACpB,UAAkB;IACrC,IAAI,CAAC5E,sBAAsB,CAAC+G,mBAAmB,CAACnC,UAAU,CAAC,CACxDQ,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAG2B,QAAQ,IAAI;QACjB;QACA,IAAI,CAACC,gBAAgB,CAACrC,UAAU,CAAC,GAAGoC,QAAQ;QAE5C;QACA,IAAI,CAACE,yBAAyB,CAACtC,UAAU,CAAC,GAAGoC,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEjE;QACA,IAAI,CAACC,eAAe,CAACxC,UAAU,CAAC,GAAG,KAAK;MAC1C,CAAC;MACDG,KAAK,EAAGS,GAAG,IAAI;QACb7B,OAAO,CAACoB,KAAK,CAAC,qCAAqCH,UAAU,GAAG,EAAEY,GAAG,CAAC;MACxE;KACD,CAAC;EACN;EAEA;;;EAGA6B,iBAAiB,CAACzC,UAAkB;IAClC,IAAI,CAACwC,eAAe,CAACxC,UAAU,CAAC,GAAG,CAAC,IAAI,CAACwC,eAAe,CAACxC,UAAU,CAAC;IAEpE,IAAI,IAAI,CAACwC,eAAe,CAACxC,UAAU,CAAC,EAAE;MACpC;MACA,IAAI,CAACsC,yBAAyB,CAACtC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAACqC,gBAAgB,CAACrC,UAAU,CAAC,CAAC;KACpF,MAAM;MACL;MACA,IAAI,CAACsC,yBAAyB,CAACtC,UAAU,CAAC,GAAG,IAAI,CAACqC,gBAAgB,CAACrC,UAAU,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE9F;EAEA;;;EAGAb,mBAAmB,CAAC1B,UAAkB;IACpC,IAAI,CAACzE,kBAAkB,CAACmH,0BAA0B,CAAC1C,UAAU,CAAC,CAC3DQ,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAGkC,IAAI,IAAI;QACb,IAAI,CAACC,eAAe,CAAC5C,UAAU,CAAC,GAAG2C,IAAI;MACzC,CAAC;MACDxC,KAAK,EAAGS,GAAG,IAAI;QACb7B,OAAO,CAACoB,KAAK,CAAC,uDAAuDH,UAAU,GAAG,EAAEY,GAAG,CAAC;MAC1F;KACD,CAAC;EACN;EAEA;;;EAGA3C,YAAY;IACV,IAAI,CAAC4E,eAAe,GAAG,IAAI;IAE3B,IAAI,CAACxH,cAAc,CAACyH,WAAW,EAAE,CAC9BtC,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAG2B,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACS,eAAe,GAAG,KAAK;MAC9B,CAAC;MACD1C,KAAK,EAAGS,GAAG,IAAI;QACb7B,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAES,GAAG,CAAC;QAC/C,IAAI,CAACiC,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACN;EAEA;;;EAGA3E,SAAS;IACP,IAAI,CAAC6E,YAAY,GAAG,IAAI;IAExB,IAAI,CAACzH,WAAW,CAAC0H,QAAQ,EAAE,CACxBxC,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAGwC,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACF,YAAY,GAAG,KAAK;MAC3B,CAAC;MACD5C,KAAK,EAAGS,GAAG,IAAI;QACb7B,OAAO,CAACoB,KAAK,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACnD,IAAI,CAACmC,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACN;EAEA;;;EAGA5D,YAAY;IACVJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;MAClBH,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC;;IAGFD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACE,QAAQ,CAAC;IAClE,IAAI,CAACb,UAAU,GAAG,IAAI;IACtB,IAAI,CAACD,IAAI,GAAG,MAAM,CAAC,CAAC;IAEpB;IACAW,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,IAAI,CAACnD,YAAY,CAACqH,UAAU,CAAC;MAC3BnH,IAAI,EAAE,IAAI,CAACmD,QAAQ,CAACnD,IAAI;MACxBG,YAAY,EAAE,IAAI,CAACgD,QAAQ,CAAChD,YAAY;MACxCC,SAAS,EAAE,IAAI,CAAC+C,QAAQ,CAAC/C,SAAS;MAClCC,KAAK,EAAE,IAAI,CAAC8C,QAAQ,CAAC9C,KAAK;MAC1BC,MAAM,EAAE,IAAI,CAAC6C,QAAQ,CAAC7C,MAAM;MAC5BC,IAAI,EAAE,IAAI,CAAC4C,QAAQ,CAAC5C,IAAI;MACxBC,UAAU,EAAE,IAAI,CAAC2C,QAAQ,CAAC3C,UAAU;MACpCC,OAAO,EAAE,IAAI,CAAC0C,QAAQ,CAAC1C,OAAO;MAC9BC,KAAK,EAAE,IAAI,CAACyC,QAAQ,CAACzC,KAAK;MAC1BC,KAAK,EAAE,IAAI,CAACwC,QAAQ,CAACxC,KAAK;MAC1BC,OAAO,EAAE,IAAI,CAACuC,QAAQ,CAACvC,OAAO;MAC9BC,KAAK,EAAE,IAAI,CAACsC,QAAQ,CAACtC;KACtB,CAAC;IACFmC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnD,YAAY,CAACsH,KAAK,CAAC;EAC9D;EAEA;;;EAGAC,YAAY;IACVrE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpED,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACnD,YAAY,CAACwH,KAAK,CAAC;IACnDtE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACnD,YAAY,CAACsH,KAAK,CAAC;IACpDpE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACZ,IAAI,CAAC;IAC/BW,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACX,UAAU,CAAC;IAC3CU,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACE,QAAQ,CAAC;IAEvC,IAAI,IAAI,CAACrD,YAAY,CAACyH,OAAO,EAAE;MAC7BvE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC;MACAuE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3H,YAAY,CAAC4H,QAAQ,CAAC,CAACvC,OAAO,CAACwC,GAAG,IAAG;QACpD,MAAMC,OAAO,GAAG,IAAI,CAAC9H,YAAY,CAAC+H,GAAG,CAACF,GAAG,CAAC;QAC1CC,OAAO,EAAEE,aAAa,EAAE;QACxB,IAAIF,OAAO,EAAEL,OAAO,EAAE;UACpBvE,OAAO,CAACC,GAAG,CAAC,SAAS0E,GAAG,cAAc,EAAEC,OAAO,CAACG,MAAM,CAAC;;MAE3D,CAAC,CAAC;MACF;;IAGF/E,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAAC+E,MAAM,GAAG,IAAI;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACnI,YAAY,CAACsH,KAAK;IAExC;IACA,MAAMc,YAAY,GAAG;MACnBlI,IAAI,EAAEiI,QAAQ,CAACjI,IAAI;MACnBG,YAAY,EAAE8H,QAAQ,CAAC9H,YAAY;MACnCC,SAAS,EAAE6H,QAAQ,CAAC7H,SAAS;MAC7BC,KAAK,EAAE4H,QAAQ,CAAC5H,KAAK;MACrBC,MAAM,EAAE2H,QAAQ,CAAC3H,MAAM;MACvBC,IAAI,EAAE0H,QAAQ,CAAC1H,IAAI;MACnBC,UAAU,EAAEyH,QAAQ,CAACzH,UAAU;MAC/BC,OAAO,EAAEwH,QAAQ,CAACxH,OAAO;MACzBC,KAAK,EAAEuH,QAAQ,CAACvH,KAAK;MACrBC,KAAK,EAAEsH,QAAQ,CAACtH,KAAK;MACrBC,OAAO,EAAEqH,QAAQ,CAACrH,OAAO;MACzBC,KAAK,EAAEoH,QAAQ,CAACpH;KACjB;IAED,IAAI,IAAI,CAACwB,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACA,IAAI,CAACnD,eAAe,CAACiJ,cAAc,CAACD,YAAY,CAAC,CAC9CzD,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAG0D,eAAe,IAAI;UACxB,IAAI,CAACJ,MAAM,GAAG,KAAK;UACnB;UACA,IAAI,CAAC/I,MAAM,CAACoJ,QAAQ,CAAC,CAAC,YAAY,EAAED,eAAe,CAAC9C,EAAE,CAAC,CAAC;QAC1D,CAAC;QACDlB,KAAK,EAAGS,GAAG,IAAI;UACb,IAAI,CAACT,KAAK,GAAG,kCAAkCS,GAAG,CAACC,OAAO,EAAE;UAC5D,IAAI,CAACkD,MAAM,GAAG,KAAK;QACrB;OACD,CAAC;KACL,MAAM,IAAI,IAAI,CAAC3F,IAAI,KAAK,MAAM,IAAI,IAAI,CAACc,QAAQ,EAAE;MAChD;MACAH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACE,QAAQ,CAACmC,EAAE,CAAC;MAC3DtC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiF,YAAY,CAAC;MAEnD,IAAI,CAAChJ,eAAe,CAACoJ,cAAc,CAAC,IAAI,CAACnF,QAAQ,CAACmC,EAAE,EAAE4C,YAAY,CAAC,CAChEzD,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAG6D,QAAQ,IAAI;UACjBvF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsF,QAAQ,CAAC;UACvD,IAAI,CAACP,MAAM,GAAG,KAAK;UACnB,IAAI,CAAC1F,UAAU,GAAG,KAAK;UACvB,IAAI,CAACQ,kBAAkB,CAAC,IAAI,CAACD,UAAU,CAAC;QAC1C,CAAC;QACDuB,KAAK,EAAGS,GAAG,IAAI;UACb7B,OAAO,CAACoB,KAAK,CAAC,0BAA0B,EAAES,GAAG,CAAC;UAC9C,IAAI,CAACT,KAAK,GAAG,iCAAiCS,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC2D,UAAU,IAAI,eAAe,EAAE;UAChG,IAAI,CAACR,MAAM,GAAG,KAAK;QACrB;OACD,CAAC;;EAER;EAEA;;;EAGAS,UAAU;IACR,IAAI,IAAI,CAACpG,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACA,IAAI,CAACpD,MAAM,CAACoJ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;KACrC,MAAM;MACL;MACA,IAAI,CAAC/F,UAAU,GAAG,KAAK;;EAE3B;EAEA;;;EAGAoG,mBAAmB;IACjB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAAC/H,WAAW,CAAC4B,KAAK,CAAC;MACrBxB,SAAS,EAAE;KACZ,CAAC;IAEF;IACA,IAAI,CAACxB,YAAY,CAACoJ,IAAI,CAAC,cAAc,CAAC;EACxC;EAEA;;;EAGAC,WAAW,CAACC,OAAgB;IAC1B,IAAI,CAACL,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,eAAe,GAAGI,OAAO;IAC9B,IAAI,CAACH,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAAC/H,WAAW,CAACqG,UAAU,CAAC;MAC1BpG,SAAS,EAAEiI,OAAO,CAACjI,SAAS;MAC5BC,QAAQ,EAAEgI,OAAO,CAAChI,QAAQ;MAC1BC,QAAQ,EAAE+H,OAAO,CAAC/H,QAAQ,IAAI,EAAE;MAChCP,KAAK,EAAEsI,OAAO,CAACtI,KAAK,IAAI,EAAE;MAC1BC,KAAK,EAAEqI,OAAO,CAACrI,KAAK,IAAI,EAAE;MAC1BE,KAAK,EAAEmI,OAAO,CAACnI,KAAK,IAAI,EAAE;MAC1BK,SAAS,EAAE8H,OAAO,CAAC9H;KACpB,CAAC;IAEF;IACA,IAAI,CAACxB,YAAY,CAACoJ,IAAI,CAAC,cAAc,CAAC;EACxC;EAEA;;;EAGAG,WAAW;IACT,IAAI,IAAI,CAACnI,WAAW,CAACyG,OAAO,EAAE;MAC5B;;IAGF,IAAI,CAAC2B,aAAa,GAAG,IAAI;IACzB,MAAMjB,QAAQ,GAAG,IAAI,CAACnH,WAAW,CAACsG,KAAK;IAEvC,IAAI,IAAI,CAACuB,iBAAiB,IAAI,IAAI,CAACC,eAAe,EAAE;MAClD;MACA,MAAMO,cAAc,GAAyB;QAC3CpI,SAAS,EAAEkH,QAAQ,CAAClH,SAAS;QAC7BC,QAAQ,EAAEiH,QAAQ,CAACjH,QAAQ;QAC3BC,QAAQ,EAAEgH,QAAQ,CAAChH,QAAQ;QAC3BP,KAAK,EAAEuH,QAAQ,CAACvH,KAAK;QACrBC,KAAK,EAAEsH,QAAQ,CAACtH,KAAK;QACrBE,KAAK,EAAEoH,QAAQ,CAACpH,KAAK;QACrBK,SAAS,EAAE+G,QAAQ,CAAC/G;OACrB;MAED,IAAI,CAAC/B,cAAc,CAACiK,aAAa,CAAC,IAAI,CAACR,eAAe,CAACtD,EAAE,EAAE6D,cAAc,CAAC,CACvE1E,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAE,MAAK;UACT,IAAI,CAACwE,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACG,iBAAiB,EAAE;UACxB,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAAC9B,UAAU,CAAC;QACpC,CAAC;QACDuB,KAAK,EAAGS,GAAG,IAAI;UACb,IAAI,CAACgE,YAAY,GAAG,gCAAgChE,GAAG,CAACC,OAAO,EAAE;UACjE,IAAI,CAACoE,aAAa,GAAG,KAAK;QAC5B;OACD,CAAC;KACL,MAAM;MACL;MACA,MAAMI,UAAU,GAAyB;QACvCzG,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B9B,SAAS,EAAEkH,QAAQ,CAAClH,SAAS;QAC7BC,QAAQ,EAAEiH,QAAQ,CAACjH,QAAQ;QAC3BC,QAAQ,EAAEgH,QAAQ,CAAChH,QAAQ;QAC3BP,KAAK,EAAEuH,QAAQ,CAACvH,KAAK;QACrBC,KAAK,EAAEsH,QAAQ,CAACtH,KAAK;QACrBE,KAAK,EAAEoH,QAAQ,CAACpH,KAAK;QACrBK,SAAS,EAAE+G,QAAQ,CAAC/G;OACrB;MAED,IAAI,CAAC/B,cAAc,CAACoK,aAAa,CAACD,UAAU,CAAC,CAC1C7E,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAE,MAAK;UACT,IAAI,CAACwE,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACG,iBAAiB,EAAE;UACxB,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAAC9B,UAAU,CAAC;QACpC,CAAC;QACDuB,KAAK,EAAGS,GAAG,IAAI;UACb,IAAI,CAACgE,YAAY,GAAG,gCAAgChE,GAAG,CAACC,OAAO,EAAE;UACjE,IAAI,CAACoE,aAAa,GAAG,KAAK;QAC5B;OACD,CAAC;;EAER;EAEA;;;EAGAG,iBAAiB;IACf,IAAI,CAAC3J,YAAY,CAAC8J,KAAK,CAAC,cAAc,CAAC;EACzC;EAEA;;;EAGMC,aAAa,CAACT,OAAgB;IAAA;IAAA;MAClC,MAAMU,SAAS,SAAS,KAAI,CAAChK,YAAY,CAACiK,OAAO,CAC/C,iCAAiCX,OAAO,CAACjI,SAAS,IAAIiI,OAAO,CAAChI,QAAQ,GAAG,EACzE,kBAAkB,EAClB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAI0I,SAAS,EAAE;QACb,KAAI,CAACvK,cAAc,CAACsK,aAAa,CAACT,OAAO,CAAC1D,EAAE,CAAC,CAC1Cb,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;UACT8B,IAAI,EAAE,MAAK;YACT,KAAI,CAACC,YAAY,CAAC,KAAI,CAAC9B,UAAU,CAAC;UACpC,CAAC;UACDuB,KAAK,EAAGS,GAAG,IAAI;YACb,KAAI,CAACT,KAAK,GAAG,8BAA8BS,GAAG,CAACC,OAAO,EAAE;YACxD,KAAI,CAACpF,YAAY,CAACkK,KAAK,CACrB,8BAA8B/E,GAAG,CAACC,OAAO,EAAE,EAC3C,OAAO,EACP,QAAQ,EACR,YAAY,CACb;UACH;SACD,CAAC;;IACL;EACH;EAEA;;;EAGA+E,oBAAoB;IAClB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAAC7I,YAAY,CAACuB,KAAK,CAAC;MACtBpB,MAAM,EAAE3C,cAAc,CAACsL,MAAM;MAC7BzI,eAAe,EAAE,IAAI;MACrBC,sBAAsB,EAAE,KAAK;MAC7BC,oBAAoB,EAAE,KAAK;MAC3BC,gBAAgB,EAAE,KAAK;MACvBC,mBAAmB,EAAE;KACtB,CAAC;IAEF;IACA,IAAI,CAAClC,YAAY,CAACoJ,IAAI,CAAC,eAAe,CAAC;EACzC;EAEA;;;EAGAoB,YAAY,CAAC9E,QAAqB;IAChC,IAAI,CAAC0E,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,gBAAgB,GAAG3E,QAAQ;IAChC,IAAI,CAAC4E,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAAC7I,YAAY,CAACgG,UAAU,CAAC;MAC3BnH,IAAI,EAAEoF,QAAQ,CAACpF,IAAI;MACnBoB,SAAS,EAAEgE,QAAQ,CAAChE,SAAS;MAC7BC,cAAc,EAAE+D,QAAQ,CAAC/D,cAAc,GAAG,IAAIuC,IAAI,CAACwB,QAAQ,CAAC/D,cAAc,CAAC,CAAC8I,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MAC5GvJ,KAAK,EAAEuE,QAAQ,CAACvE,KAAK;MACrB;MACAS,MAAM,EAAE,IAAI,CAAC+I,kBAAkB,CAACjF,QAAQ,CAAC9D,MAAM,CAAC;MAChDE,eAAe,EAAE4D,QAAQ,CAAC5D,eAAe;MACzCC,sBAAsB,EAAE2D,QAAQ,CAAC3D,sBAAsB;MACvDC,oBAAoB,EAAE0D,QAAQ,CAAC1D,oBAAoB;MACnDC,gBAAgB,EAAEyD,QAAQ,CAACzD,gBAAgB;MAC3CC,mBAAmB,EAAEwD,QAAQ,CAACxD;KAC/B,CAAC;IAEF;IACA,IAAI,CAAClC,YAAY,CAACoJ,IAAI,CAAC,eAAe,CAAC;EACzC;EAEA;;;EAGAwB,kBAAkB,CAAClF,QAAqB;IACtC,IAAI,CAACM,0BAA0B,GAAGN,QAAQ;IAE1C;IACA,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAACE,EAAE,CAAC;IAEtC;IACA,IAAI,CAACK,mBAAmB,CAACP,QAAQ,CAACE,EAAE,CAAC;IAErC;IACA,IAAI,CAAC5F,YAAY,CAACoJ,IAAI,CAAC,qBAAqB,CAAC;EAC/C;EAEA;;;EAGAyB,wBAAwB;IACtB,IAAI,CAAC7K,YAAY,CAAC8J,KAAK,CAAC,qBAAqB,CAAC;EAChD;EAEA;;;EAGAgB,sBAAsB,CAACpF,QAAqB;IAC1C;IACA,IAAI,CAACmF,wBAAwB,EAAE;IAE/B;IACA9E,UAAU,CAAC,MAAK;MACd,IAAI,CAACyE,YAAY,CAAC9E,QAAQ,CAAC;IAC7B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEA;;;EAGAqF,kBAAkB;IAChB,IAAI,CAAC/K,YAAY,CAAC8J,KAAK,CAAC,eAAe,CAAC;EAC1C;EAEA;;;EAGQkB,mBAAmB,CAACpJ,MAAc;IACxC;IACA,IAAI,CAACqJ,KAAK,CAACC,MAAM,CAACtJ,MAAM,CAAC,CAAC,EAAE;MAC1B,MAAMuJ,aAAa,GAAGD,MAAM,CAACtJ,MAAM,CAAC;MACpC,OAAOuJ,aAAa;;IAGtB;IACA,QAAQvJ,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO3C,cAAc,CAACsL,MAAM;MAC3C,KAAK,SAAS;QAAE,OAAOtL,cAAc,CAACmM,OAAO;MAC7C,KAAK,SAAS;QAAE,OAAOnM,cAAc,CAACoM,OAAO;MAC7C,KAAK,OAAO;QAAE,OAAOpM,cAAc,CAACqM,KAAK;MACzC,KAAK,aAAa;QAAE,OAAOrM,cAAc,CAACsM,WAAW;MACrD;QAAS,OAAOtM,cAAc,CAACsL,MAAM;MAAE;IAAA;EAE3C;EAEA;;;EAGQI,kBAAkB,CAAC/I,MAA+B;IACxD,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOA,MAAM;;IAGf,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO3C,cAAc,CAACsL,MAAM;MAC3C,KAAK,SAAS;QAAE,OAAOtL,cAAc,CAACmM,OAAO;MAC7C,KAAK,SAAS;QAAE,OAAOnM,cAAc,CAACoM,OAAO;MAC7C,KAAK,OAAO;QAAE,OAAOpM,cAAc,CAACqM,KAAK;MACzC,KAAK,aAAa;QAAE,OAAOrM,cAAc,CAACsM,WAAW;MACrD;QAAS,OAAOtM,cAAc,CAACsL,MAAM;MAAE;IAAA;EAE3C;EAEA;;;EAGAiB,YAAY;IACV,IAAI,IAAI,CAAC/J,YAAY,CAACoG,OAAO,EAAE;MAC7B;;IAGF,IAAI,CAAC4D,cAAc,GAAG,IAAI;IAC1B,MAAMlD,QAAQ,GAAG,IAAI,CAAC9G,YAAY,CAACiG,KAAK;IAExCpE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEgF,QAAQ,CAAC;IAExD,IAAI,IAAI,CAAC6B,kBAAkB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACpD;MACA,MAAMqB,eAAe,GAAG;QACtBpL,IAAI,EAAEiI,QAAQ,CAACjI,IAAI;QACnBoB,SAAS,EAAE6G,QAAQ,CAAC7G,SAAS;QAC7BC,cAAc,EAAE4G,QAAQ,CAAC5G,cAAc,GAAG,IAAIuC,IAAI,CAACqE,QAAQ,CAAC5G,cAAc,CAAC,GAAGgK,SAAS;QACvFxK,KAAK,EAAEoH,QAAQ,CAACpH,KAAK;QACrBS,MAAM,EAAE,IAAI,CAACoJ,mBAAmB,CAACzC,QAAQ,CAAC3G,MAAM,CAAC;QACjDE,eAAe,EAAEyG,QAAQ,CAACzG,eAAe;QACzCC,sBAAsB,EAAEwG,QAAQ,CAACxG,sBAAsB;QACvDC,oBAAoB,EAAEuG,QAAQ,CAACvG,oBAAoB;QACnDC,gBAAgB,EAAEsG,QAAQ,CAACtG,gBAAgB;QAC3CC,mBAAmB,EAAEqG,QAAQ,CAACrG;OAC/B;MAEDoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmI,eAAe,CAAC;MAExD,IAAI,CAAChM,eAAe,CAACkM,cAAc,CAAC,IAAI,CAACvB,gBAAgB,CAACzE,EAAE,EAAE8F,eAAe,CAAC,CAC3E3G,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAG6D,QAAQ,IAAI;UACjBvF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEsF,QAAQ,CAAC;UACxD,IAAI,CAAC4C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACV,kBAAkB,EAAE;UACzB,IAAI,CAAC7F,aAAa,CAAC,IAAI,CAAC/B,UAAU,CAAC;QACrC,CAAC;QACDuB,KAAK,EAAGS,GAAG,IAAI;UACb7B,OAAO,CAACoB,KAAK,CAAC,8BAA8B,EAAES,GAAG,CAAC;UAClD,IAAI,CAACmF,aAAa,GAAG,gCAAgCnF,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC2D,UAAU,IAAI,eAAe,EAAE;UACvG,IAAI,CAAC2C,cAAc,GAAG,KAAK;QAC7B;OACD,CAAC;KACL,MAAM;MACL;MACA,MAAMI,WAAW,GAAG;QAClB1I,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B7C,IAAI,EAAEiI,QAAQ,CAACjI,IAAI;QACnBoB,SAAS,EAAE6G,QAAQ,CAAC7G,SAAS;QAC7BC,cAAc,EAAE4G,QAAQ,CAAC5G,cAAc,GAAG,IAAIuC,IAAI,CAACqE,QAAQ,CAAC5G,cAAc,CAAC,GAAGgK,SAAS;QACvFxK,KAAK,EAAEoH,QAAQ,CAACpH,KAAK;QACrBS,MAAM,EAAE,IAAI,CAACoJ,mBAAmB,CAACzC,QAAQ,CAAC3G,MAAM,CAAC;QACjDE,eAAe,EAAEyG,QAAQ,CAACzG,eAAe;QACzCC,sBAAsB,EAAEwG,QAAQ,CAACxG,sBAAsB;QACvDC,oBAAoB,EAAEuG,QAAQ,CAACvG,oBAAoB;QACnDC,gBAAgB,EAAEsG,QAAQ,CAACtG,gBAAgB;QAC3CC,mBAAmB,EAAEqG,QAAQ,CAACrG;OAC/B;MAEDoB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsI,WAAW,CAAC;MAEzD,IAAI,CAACnM,eAAe,CAACoM,cAAc,CAACD,WAAW,CAAC,CAC7C9G,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;QACT8B,IAAI,EAAG6D,QAAQ,IAAI;UACjBvF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEsF,QAAQ,CAAC;UACpD,IAAI,CAAC4C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACV,kBAAkB,EAAE;UACzB,IAAI,CAAC7F,aAAa,CAAC,IAAI,CAAC/B,UAAU,CAAC;QACrC,CAAC;QACDuB,KAAK,EAAGS,GAAG,IAAI;UACb7B,OAAO,CAACoB,KAAK,CAAC,+BAA+B,EAAES,GAAG,CAAC;UACnD,IAAI,CAACmF,aAAa,GAAG,iCAAiCnF,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC2D,UAAU,IAAI,eAAe,EAAE;UACxG,IAAI,CAAC2C,cAAc,GAAG,KAAK;QAC7B;OACD,CAAC;;EAER;EAEA;;;EAGMM,cAAc;IAAA;IAAA;MAClB,IAAI,CAAC,MAAI,CAACtI,QAAQ,EAAE;MAEpB,MAAMuG,SAAS,SAAS,MAAI,CAAChK,YAAY,CAACiK,OAAO,CAC/C,mCAAmC,MAAI,CAACxG,QAAQ,CAACnD,IAAI,GAAG,EACxD,mBAAmB,EACnB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAI0J,SAAS,EAAE;QACb,MAAI,CAACrF,OAAO,GAAG,IAAI;QAEnB,MAAI,CAACnF,eAAe,CAACuM,cAAc,CAAC,MAAI,CAACtI,QAAQ,CAACmC,EAAE,CAAC,CAClDb,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;UACT8B,IAAI,EAAE,MAAK;YACT,MAAI,CAACzF,MAAM,CAACoJ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;UACtC,CAAC;UACDjE,KAAK,EAAGS,GAAG,IAAI;YACb,MAAI,CAACT,KAAK,GAAG,+BAA+BS,GAAG,CAACC,OAAO,EAAE;YACzD,MAAI,CAACT,OAAO,GAAG,KAAK;YACpB,MAAI,CAAC3E,YAAY,CAACkK,KAAK,CACrB,+BAA+B/E,GAAG,CAACC,OAAO,EAAE,EAC5C,OAAO,EACP,QAAQ,EACR,YAAY,CACb;UACH;SACD,CAAC;;IACL;EACH;EAEA;;;EAGA4G,MAAM;IACJ,IAAI,CAACzM,MAAM,CAACoJ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA;;;EAGMsD,UAAU,CAACC,YAA8B;IAAA;IAAA;MAC7C,MAAM,MAAI,CAAC/L,gBAAgB,CAACgM,aAAa,CACvCD,YAAY,EACZ,qCAAqC,EACrC,mCAAmC,CACpC;IAAC;EACJ;EAEA;;;EAGME,mBAAmB,CAAC7H,UAAkB;IAAA;IAAA;MAC1C,MAAMyF,SAAS,SAAS,MAAI,CAAChK,YAAY,CAACiK,OAAO,CAC/C,kHAAkH,EAClH,wBAAwB,EACxB,WAAW,EACX,QAAQ,EACR,aAAa,EACb,eAAe,CAChB;MAED,IAAI,CAACD,SAAS,EAAE;QACd;;MAGF,MAAI,CAAClK,kBAAkB,CAACsM,mBAAmB,CAAC7H,UAAU,CAAC,CAACrB,SAAS,CAAC;QAChE8B,IAAI,EAAG6D,QAAQ,IAAI;UACjB;UACA;UACA,MAAMwD,mBAAmB,GAAGxD,QAAQ,CAACyD,QAAQ,IAAI,UAAU;UAE3D,MAAI,CAACC,oBAAoB,GAAG;YAC1BC,WAAW,EAAE3D,QAAQ,CAAC2D,WAAW;YACjCC,UAAU,EAAE5D,QAAQ,CAAC4D,UAAU;YAC/BC,UAAU,EAAE7D,QAAQ,CAAC6D,UAAU;YAC/B/K,cAAc,EAAEkH,QAAQ,CAAClH,cAAc;YACvC2K,QAAQ,EAAED,mBAAmB;YAC7BA,mBAAmB,EAAEA;WACtB;UAED;UACA,MAAI,CAACpG,mBAAmB,CAAC1B,UAAU,CAAC;UAEpC;UACA,MAAMoI,eAAe,GAAGxG,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;UACtE,IAAIuG,eAAe,IAAI,MAAI,CAACJ,oBAAoB,EAAE;YAChDI,eAAe,CAACC,WAAW,GAAG,MAAI,CAACL,oBAAoB,CAACD,QAAQ,IAAI,wBAAwB;;UAG9F;UACA,MAAI,CAACtM,YAAY,CAACoJ,IAAI,CAAC,2BAA2B,CAAC;QACrD,CAAC;QACD1E,KAAK,EAAGS,GAAG,IAAI;UACb7B,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAES,GAAG,CAAC;UACtD,MAAI,CAACnF,YAAY,CAACkK,KAAK,CACrB,qCAAqC/E,GAAG,CAACT,KAAK,EAAEU,OAAO,IAAID,GAAG,CAACC,OAAO,IAAI,eAAe,EAAE,EAC3F,OAAO,EACP,QAAQ,EACR,YAAY,CACb;QACH;OACD,CAAC;IAAC;EACL;EAEA;;;EAGAyH,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAACN,oBAAoB,EAAE;MAC9B;;IAGF;IACA,MAAMO,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAACR,oBAAoB,CAACE,UAAU,EAAE,sBAAsB,CAAC;IAC5F,MAAM/J,GAAG,GAAG4D,MAAM,CAAC0G,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IAC5C,MAAMI,CAAC,GAAG/G,QAAQ,CAACgH,aAAa,CAAC,GAAG,CAAC;IACrCD,CAAC,CAACE,IAAI,GAAG1K,GAAG;IACZwK,CAAC,CAACG,QAAQ,GAAG,eAAe,IAAI,CAACrH,0BAA0B,EAAE1F,IAAI,IAAI,UAAU,MAAM;IACrF6F,QAAQ,CAACmH,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,EAAE;IACTlH,MAAM,CAAC0G,GAAG,CAACS,eAAe,CAAC/K,GAAG,CAAC;IAC/ByD,QAAQ,CAACmH,IAAI,CAACI,WAAW,CAACR,CAAC,CAAC;EAC9B;EAEA;;;EAGQH,YAAY,CAACY,MAAc,EAAEC,WAAmB;IACtD,MAAMC,cAAc,GAAGC,IAAI,CAACH,MAAM,CAAC;IACnC,MAAMI,UAAU,GAAG,EAAE;IAErB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,cAAc,CAACI,MAAM,EAAED,MAAM,IAAI,GAAG,EAAE;MAClE,MAAMlH,KAAK,GAAG+G,cAAc,CAAC/G,KAAK,CAACkH,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MACxD,MAAME,WAAW,GAAG,IAAIC,KAAK,CAACrH,KAAK,CAACmH,MAAM,CAAC;MAE3C,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAACmH,MAAM,EAAEnI,CAAC,EAAE,EAAE;QACrCoI,WAAW,CAACpI,CAAC,CAAC,GAAGgB,KAAK,CAACsH,UAAU,CAACtI,CAAC,CAAC;;MAGtC,MAAMuI,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7CH,UAAU,CAACQ,IAAI,CAACF,SAAS,CAAC;;IAG5B,OAAO,IAAIG,IAAI,CAACT,UAAU,EAAE;MAAEU,IAAI,EAAEb;IAAW,CAAE,CAAC;EACpD;EAEA;;;EAGAc,qBAAqB;IACnB,IAAI,CAAC1O,YAAY,CAAC8J,KAAK,CAAC,2BAA2B,CAAC;EACtD;EAEA;;;EAGA6E,2BAA2B;IACzB,IAAI,CAAC,IAAI,CAAC3I,0BAA0B,EAAE;MACpC1C,OAAO,CAACoB,KAAK,CAAC,6BAA6B,CAAC;MAC5C;;IAGF,IAAI,CAACkK,yBAAyB,GAAG,KAAK;IACtC,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC5O,WAAW,CAAC6O,gBAAgB,EAAE;IAEzD;IACA,IAAI,CAAC5M,mBAAmB,CAACa,KAAK,CAAC;MAC7BZ,SAAS,EAAE,EAAE;MACbC,iBAAiB,EAAEyM,aAAa;MAChC3N,KAAK,EAAE;KACR,CAAC;IAEF;IACA,IAAI,CAACnB,YAAY,CAACoJ,IAAI,CAAC,sBAAsB,CAAC;EAChD;EAEA;;;EAGA4F,yBAAyB;IACvB,IAAI,CAAChP,YAAY,CAAC8J,KAAK,CAAC,sBAAsB,CAAC;EACjD;EAEA;;;EAGAmF,mBAAmB;IACjB,IAAI,IAAI,CAAC9M,mBAAmB,CAAC0F,OAAO,EAAE;MACpC;MACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5F,mBAAmB,CAAC6F,QAAQ,CAAC,CAACvC,OAAO,CAACwC,GAAG,IAAG;QAC3D,MAAMC,OAAO,GAAG,IAAI,CAAC/F,mBAAmB,CAACgG,GAAG,CAACF,GAAG,CAAC;QACjDC,OAAO,EAAEE,aAAa,EAAE;MAC1B,CAAC,CAAC;MACF;;IAGF,IAAI,CAAC,IAAI,CAACpC,0BAA0B,EAAE;MACpC,IAAI,CAAC6I,oBAAoB,GAAG,8BAA8B;MAC1D;;IAGF,IAAI,CAACK,qBAAqB,GAAG,IAAI;IACjC,MAAM3G,QAAQ,GAAG,IAAI,CAACpG,mBAAmB,CAACuF,KAAK;IAE/C;IACA,MAAMyH,kBAAkB,GAAG;MACzB/M,SAAS,EAAEmG,QAAQ,CAACnG,SAAS;MAC7BC,iBAAiB,EAAEkG,QAAQ,CAAClG,iBAAiB;MAC7ClB,KAAK,EAAEoH,QAAQ,CAACpH,KAAK,IAAI;KAC1B;IAED,IAAI,CAACxB,sBAAsB,CAACyP,kBAAkB,CAAC,IAAI,CAACpJ,0BAA0B,CAACJ,EAAE,EAAEuJ,kBAAkB,CAAC,CACnGpK,IAAI,CAAC7F,KAAK,EAAE,CAAC,CACbgE,SAAS,CAAC;MACT8B,IAAI,EAAG6D,QAAyB,IAAI;QAClCvF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsF,QAAQ,CAAC;QAC1D,IAAI,CAACqG,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACF,yBAAyB,EAAE;QAEhC;QACA,IAAI,CAACrJ,oBAAoB,CAAC,IAAI,CAACK,0BAA2B,CAACJ,EAAE,CAAC;MAChE,CAAC;MACDlB,KAAK,EAAGS,GAAQ,IAAI;QAClB7B,OAAO,CAACoB,KAAK,CAAC,qCAAqC,EAAES,GAAG,CAAC;QACzD,IAAI,CAAC0J,oBAAoB,GAAG,uCAAuC1J,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC2D,UAAU,IAAI,eAAe,EAAE;QACrH,IAAI,CAACoG,qBAAqB,GAAG,KAAK;MACpC;KACD,CAAC;EACN;EAEA;;;EAGAG,YAAY;IACV,IAAI,IAAI,CAAC9C,oBAAoB,EAAE;MAC7B,IAAI,CAACvM,YAAY,CAACkK,KAAK,CACrB,gCAAgC,IAAI,CAACqC,oBAAoB,CAACD,QAAQ,WAAW,EAC7E,qBAAqB,EACrB,QAAQ,EACR,aAAa,CACd;KACF,MAAM;MACL,IAAI,CAACtM,YAAY,CAACkK,KAAK,CACrB,6BAA6B,EAC7B,WAAW,EACX,QAAQ,EACR,eAAe,CAChB;;EAEL;EAEA;;;EAGAoF,qBAAqB,CAAC1N,MAA+B;IACnD,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,SAAS;QAC/B,KAAK,SAAS;UAAE,OAAO,WAAW;QAClC,KAAK,SAAS;UAAE,OAAO,YAAY;QACnC,KAAK,OAAO;UAAE,OAAO,UAAU;QAC/B,KAAK,aAAa;UAAE,OAAO,QAAQ;QACnC;UAAS,OAAOA,MAAM;MAAC;KAE1B,MAAM;MACL,QAAQA,MAAM;QACZ,KAAK3C,cAAc,CAACsL,MAAM;UAAE,OAAO,SAAS;QAC5C,KAAKtL,cAAc,CAACmM,OAAO;UAAE,OAAO,WAAW;QAC/C,KAAKnM,cAAc,CAACoM,OAAO;UAAE,OAAO,YAAY;QAChD,KAAKpM,cAAc,CAACqM,KAAK;UAAE,OAAO,UAAU;QAC5C,KAAKrM,cAAc,CAACsM,WAAW;UAAE,OAAO,QAAQ;QAChD;UAAS,OAAOgE,MAAM,CAAC3N,MAAM,CAAC;MAAC;;EAGrC;EAEA;;;EAGA4N,sBAAsB,CAAC5N,MAA+B;IACpD,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,YAAY;QAClC,KAAK,SAAS;UAAE,OAAO,WAAW;QAClC,KAAK,SAAS;UAAE,OAAO,sBAAsB;QAC7C,KAAK,OAAO;UAAE,OAAO,mBAAmB;QACxC,KAAK,aAAa;UAAE,OAAO,cAAc;QACzC;UAAS,OAAO,cAAc;MAAC;KAElC,MAAM;MACL,QAAQA,MAAM;QACZ,KAAK3C,cAAc,CAACsL,MAAM;UAAE,OAAO,YAAY;QAC/C,KAAKtL,cAAc,CAACmM,OAAO;UAAE,OAAO,WAAW;QAC/C,KAAKnM,cAAc,CAACoM,OAAO;UAAE,OAAO,sBAAsB;QAC1D,KAAKpM,cAAc,CAACqM,KAAK;UAAE,OAAO,mBAAmB;QACrD,KAAKrM,cAAc,CAACsM,WAAW;UAAE,OAAO,cAAc;QACtD;UAAS,OAAO,cAAc;MAAC;;EAGrC;;;uBA/sCWnM,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAqQ;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UC3BpC1Q,8BAA6B;UAIqBA;YAAA,OAAS2Q,YAAQ;UAAA,EAAC;UAC1D3Q,uBAAqC;UAACA,8DACxC;UAAAA,iBAAS;UACTA,wEAOM;UACRA,iBAAM;UAKVA,wEAIM;UAGNA,wEAEM;UAGNA,4EAwHM;UAGNA,4EA+BM;UAGNA,0EAuDM;UAGNA,0EA6DM;UACRA,iBAAM;UAGNA,gCAA+G;UAIxDA,aAA8D;UAAAA,iBAAK;UAClHA,mCAA0G;UAAlDA;YAAA,OAAS2Q,uBAAmB;UAAA,EAAC;UAAqB3Q,iBAAS;UAErHA,gCAAwB;UACtBA,2EAEM;UACNA,iCAA2D;UAA3BA;YAAA,OAAY2Q,iBAAa;UAAA,EAAC;UACxD3Q,gCAAiB;UAE4CA,2BAAK;UAAAA,iBAAQ;UACtEA,6BAAuG;UACvGA,2EAGM;UACRA,iBAAM;UACNA,gCAA2B;UAC+BA,wCAAQ;UAAAA,iBAAQ;UACxEA,6BAAwG;UACxGA,2EAGM;UACRA,iBAAM;UAERA,gCAAkB;UACyBA,uBAAM;UAAAA,iBAAQ;UACvDA,6BAA+M;UAC/MA,2EAEM;UACRA,iBAAM;UACNA,gCAAiB;UAEgCA,sBAAK;UAAAA,iBAAQ;UAC1DA,6BAAuG;UACvGA,2EAGM;UACRA,iBAAM;UACNA,gCAA2B;UACoBA,wBAAO;UAAAA,iBAAQ;UAC5DA,6BAA2M;UAC3MA,2EAEM;UACRA,iBAAM;UAERA,gCAAkB;UAC6BA,8BAAQ;UAAAA,iBAAQ;UAC7DA,gCAAuN;UACvNA,2EAEM;UACRA,iBAAM;UACNA,gCAA6B;UAC3BA,6BAA2F;UAC3FA,kCAAgD;UAAAA,2CAAgB;UAAAA,iBAAQ;UAE1EA,gCAAwC;UACuBA;YAAA,OAAS2Q,uBAAmB;UAAA,EAAC;UAAC3Q,4BAAM;UAAAA,iBAAS;UAC1GA,mCAAgG;UAC9FA,6EAAkH;UAClHA,8BACF;UAAAA,iBAAS;UASrBA,gDAS+C;UAH7CA;YAAA,OAAS2Q,8BAA0B;UAAA,EAAC;YAAA,OAC5BA,kCAA8B;UAAA,EADF;YAAA,OAEbA,+BAA2B;UAAA,EAFd;YAAA,OAGtBA,iCAA6B;UAAA,EAHP;UAItC3Q,iBAAsB;UAGtBA,gCAAiH;UAIzDA,aAAyE;UAAAA,iBAAK;UAC9HA,mCAA2G;UAAnDA;YAAA,OAAS2Q,wBAAoB;UAAA,EAAC;UAAqB3Q,iBAAS;UAEtHA,gCAAwB;UACtBA,2EAEM;UACNA,iCAA6D;UAA5BA;YAAA,OAAY2Q,kBAAc;UAAA,EAAC;UAC1D3Q,gCAAiB;UAEuCA,2BAAK;UAAAA,iBAAQ;UACjEA,6BAAsG;UACtGA,2EAGM;UACRA,iBAAM;UACNA,gCAA2B;UAC6BA,uBAAM;UAAAA,iBAAQ;UACpEA,mCAAiE;UACvBA,6BAAO;UAAAA,iBAAS;UACxDA,mCAAyC;UAAAA,+BAAS;UAAAA,iBAAS;UAC3DA,mCAAyC;UAAAA,gCAAU;UAAAA,iBAAS;UAC5DA,mCAAuC;UAAAA,mCAAQ;UAAAA,iBAAS;UACxDA,mCAA6C;UAAAA,iCAAM;UAAAA,iBAAS;UAIlEA,gCAAiB;UAE4CA,6BAAW;UAAAA,iBAAQ;UAC5EA,8BAA6G;UAC7GA,6EAGM;UACRA,iBAAM;UACNA,iCAA2B;UACsBA,gCAAc;UAAAA,iBAAQ;UACrEA,8BAA6F;UAC/FA,iBAAM;UAERA,iCAAkB;UACsBA,+BAAQ;UAAAA,iBAAQ;UACtDA,iCAAkN;UAClNA,6EAEM;UACRA,iBAAM;UACNA,iCAAkB;UACUA,wBAAM;UAAAA,iBAAQ;UACxCA,iCAA6B;UAC3BA,8BAAuG;UACvGA,mCAAsD;UAAAA,2BAAS;UAAAA,iBAAQ;UAEzEA,iCAA6B;UAC3BA,8BAAqH;UACrHA,mCAA6D;UAAAA,2DAAqB;UAAAA,iBAAQ;UAE5FA,iCAA6B;UAC3BA,8BAAiH;UACjHA,mCAA2D;UAAAA,+BAAa;UAAAA,iBAAQ;UAElFA,iCAA6B;UAC3BA,8BAAyG;UACzGA,mCAAuD;UAAAA,4BAAU;UAAAA,iBAAQ;UAE3EA,iCAA6B;UAC3BA,8BAA+G;UAC/GA,mCAA0D;UAAAA,6BAAW;UAAAA,iBAAQ;UAGjFA,iCAAwC;UACuBA;YAAA,OAAS2Q,wBAAoB;UAAA,EAAC;UAAC3Q,6BAAM;UAAAA,iBAAS;UAC3GA,oCAAkG;UAChGA,+EAAmH;UACnHA,+BACF;UAAAA,iBAAS;UASrBA,iCAA+H;UAIhEA,cAAoF;UAAAA,iBAAK;UAChJA,oCAAkH;UAA1DA;YAAA,OAAS2Q,+BAA2B;UAAA,EAAC;UAAqB3Q,iBAAS;UAE7HA,iCAAwB;UACtBA,6EAEM;UACNA,kCAA2E;UAAnCA;YAAA,OAAY2Q,yBAAqB;UAAA,EAAC;UACxE3Q,iCAAkB;UAC6BA,2BAAS;UAAAA,iBAAQ;UAC9DA,8BAA8G;UAChHA,iBAAM;UACNA,iCAAkB;UACyCA,wBAAM;UAAAA,iBAAQ;UACvEA,oCAAuE;UACpDA,qCAAmB;UAAAA,iBAAS;UAC7CA,mFAAkG;UACpGA,iBAAS;UACTA,6EAEM;UACRA,iBAAM;UACNA,iCAAkB;UACiDA,6BAAW;UAAAA,iBAAQ;UACpFA,oCAAuF;UACpEA,8CAAuB;UAAAA,iBAAS;UACjDA,mFAAsG;UACxGA,iBAAS;UACTA,6EAEM;UACRA,iBAAM;UACNA,iCAAkB;UACsBA,gCAAS;UAAAA,iBAAQ;UACvDA,iCAAkP;UAClPA,6EAEM;UACRA,iBAAM;UACNA,iCAAwC;UACuBA;YAAA,OAAS2Q,+BAA2B;UAAA,EAAC;UAAC3Q,6BAAM;UAAAA,iBAAS;UAClHA,oCAAgH;UAC9GA,+EAA0H;UAC1HA,+BACF;UAAAA,iBAAS;UASrBA,mDAKqC;UADnCA;YAAA,OAAS2Q,2BAAuB;UAAA,EAAC;YAAA,OACrBA,yBAAqB;UAAA,EADA;UAEnC3Q,iBAAwB;;;;;;;;;;;;;;;;;;;;UAtiBVA,eAAwC;UAAxCA,qEAAwC;UAa9CA,eAAa;UAAbA,kCAAa;UAObA,eAAW;UAAXA,gCAAW;UAKXA,eAAgB;UAAhBA,qCAAgB;UA2HhBA,eAA6B;UAA7BA,sDAA6B;UAkC7BA,eAA6B;UAA7BA,sDAA6B;UA0D7BA,eAA6B;UAA7BA,sDAA6B;UAqEkBA,eAA8D;UAA9DA,uFAA8D;UAIvGA,eAAkB;UAAlBA,uCAAkB;UAGlBA,eAAyB;UAAzBA,2CAAyB;UAKnBA,eAAoF;UAApFA,6LAAoF;UAQpFA,eAAkF;UAAlFA,2LAAkF;UAQYA,eAAwG;UAAxGA,2NAAwG;UACxMA,eAAkF;UAAlFA,2LAAkF;UAQhFA,eAA4E;UAA5EA,qLAA4E;UAOsBA,eAAkG;UAAlGA,qNAAkG;UACpMA,eAA4E;UAA5EA,qLAA4E;UAOqBA,eAAkG;UAAlGA,qNAAkG;UACrMA,eAA4E;UAA5EA,qLAA4E;UAUpCA,eAAiD;UAAjDA,uEAAiD;UACtFA,eAAmB;UAAnBA,wCAAmB;UAYtCA,eAAuC;UAAvCA,yDAAuC;UAgBeA,eAAyE;UAAzEA,kGAAyE;UAInHA,eAAmB;UAAnBA,wCAAmB;UAGnBA,eAA0B;UAA1BA,4CAA0B;UAKpBA,eAA4E;UAA5EA,qLAA4E;UAQxEA,eAA+B;UAA/BA,iDAA+B;UAC/BA,eAAgC;UAAhCA,kDAAgC;UAChCA,eAAgC;UAAhCA,kDAAgC;UAChCA,eAA8B;UAA9BA,gDAA8B;UAC9BA,eAAoC;UAApCA,sDAAoC;UAQxCA,eAAsF;UAAtFA,+LAAsF;UAYIA,eAAoG;UAApGA,uNAAoG;UAChMA,eAA8E;UAA9EA,uLAA8E;UA6BtCA,gBAAmD;UAAnDA,yEAAmD;UACxFA,eAAoB;UAApBA,yCAAoB;UAesBA,eAAoF;UAApFA,6GAAoF;UAIrIA,eAA0B;UAA1BA,+CAA0B;UAG1BA,eAAiC;UAAjCA,mDAAiC;UAGuBA,eAA0C;UAA1CA,2GAA0C;UAMtEA,eAAW;UAAXA,sCAAW;UAEnCA,eAAoG;UAApGA,6MAAoG;UAQ/EA,eAAQ;UAARA,mCAAQ;UAE7BA,eAAoH;UAApHA,6NAAoH;UAMNA,eAAkH;UAAlHA,qOAAkH;UAChOA,eAA4F;UAA5FA,qMAA4F;UAMpDA,eAAiE;UAAjEA,uFAAiE;UACtGA,eAA2B;UAA3BA,gDAA2B;UAY9CA,eAAoC;UAApCA,sDAAoC", "names": ["Validators", "InstanceStatus", "first", "i0", "CustomerDetailComponent", "constructor", "route", "router", "customerService", "contactService", "instanceService", "instanceVersionService", "versionService", "userService", "certificateService", "breadcrumbService", "modalService", "formBuilder", "authService", "clipboardService", "customerForm", "group", "name", "required", "max<PERSON><PERSON><PERSON>", "abbreviation", "companyId", "taxId", "street", "city", "postalCode", "country", "email", "phone", "website", "notes", "contactForm", "firstName", "lastName", "position", "isPrimary", "instanceForm", "serverUrl", "expirationDate", "status", "blockReason", "moduleReporting", "moduleAdvancedSecurity", "moduleApiIntegration", "moduleDataExport", "moduleCustomization", "instanceVersionForm", "versionId", "installedByUserId", "ngOnInit", "isAdmin", "loadVersions", "loadUsers", "url", "mode", "isEditMode", "setBreadcrumbs", "label", "icon", "reset", "params", "subscribe", "customerId", "loadCustomerDetail", "queryParams", "console", "log", "loadCustomerDetailCallback", "customer", "editCustomer", "openInstanceDetailJson", "localStorage", "getItem", "openInstanceDetail", "JSON", "parse", "currentTime", "Date", "getTime", "dataTime", "timestamp", "isRecent", "instanceId", "pendingInstanceId", "removeItem", "error", "loading", "loadingContacts", "loadingInstances", "getCustomer", "pipe", "next", "loadContacts", "loadInstances", "err", "message", "getContactsByCustomerId", "contacts", "getInstancesByCustomerId", "instances", "for<PERSON>ach", "instance", "loadInstanceVersions", "id", "find", "i", "setTimeout", "selectedInstanceForVersion", "loadCertificateInfo", "modalElement", "document", "getElementById", "modal", "window", "bootstrap", "Modal", "show", "getInstanceVersions", "versions", "instanceVersions", "displayedInstanceVersions", "slice", "showAllVersions", "toggleAllVersions", "getInstanceCertificateInfo", "info", "certificateInfo", "loadingVersions", "getVersions", "loadingUsers", "getUsers", "users", "patchValue", "value", "saveCustomer", "valid", "invalid", "Object", "keys", "controls", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "saving", "formData", "customerData", "createCustomer", "createdCustomer", "navigate", "updateCustomer", "response", "statusText", "cancelEdit", "openAddContactModal", "isEditContactMode", "selectedContact", "contactError", "open", "editContact", "contact", "saveContact", "savingContact", "updatedContact", "updateContact", "closeContactModal", "newContact", "createContact", "close", "deleteContact", "confirmed", "confirm", "alert", "openAddInstanceModal", "isEditInstanceMode", "selectedInstance", "instanceError", "Active", "editInstance", "toISOString", "split", "getStatusEnumValue", "viewInstanceDetail", "closeInstanceDetailModal", "editInstanceFromDetail", "closeInstanceModal", "convertStatusToEnum", "isNaN", "Number", "numericStatus", "Blocked", "Expired", "Trial", "Maintenance", "saveInstance", "savingInstance", "updatedInstance", "undefined", "updateInstance", "newInstance", "createInstance", "deleteCustomer", "goBack", "copyApiKey", "inputElement", "copyFromInput", "generateCertificate", "certificatePassword", "password", "generatedCertificate", "certificate", "privateKey", "thumbprint", "passwordElement", "textContent", "downloadCertificate", "blob", "base64ToBlob", "URL", "createObjectURL", "a", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "base64", "contentType", "byteCharacters", "atob", "byteArrays", "offset", "length", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "push", "Blob", "type", "closeCertificateModal", "openAddInstanceVersionModal", "isEditInstanceVersionMode", "instanceVersionError", "currentUserId", "getCurrentUserId", "closeInstanceVersionModal", "saveInstanceVersion", "savingInstanceVersion", "newInstanceVersion", "addInstanceVersion", "testPassword", "getInstanceStatusName", "String", "getInstanceStatusClass", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\customers\\customer-detail\\customer-detail.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\customers\\customer-detail\\customer-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { CustomerService } from '../../services/customer.service';\nimport { ContactService } from '../../services/contact.service';\nimport { InstanceService } from '../../services/instance.service';\nimport { InstanceVersionService } from '../../services/instance-version.service';\nimport { VersionService } from '../../services/version.service';\nimport { UserService } from '../../services/user.service';\nimport { CertificateService } from '../../services/certificate.service';\nimport { BreadcrumbService } from '../../services/breadcrumb.service';\nimport { ModalService } from '../../services/modal.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ClipboardService } from '../../services/clipboard.service';\nimport { Customer, CustomerDetail, CustomerContact } from '../../models/customer.model';\nimport { Contact, CreateContactRequest, UpdateContactRequest } from '../../models/contact.model';\nimport { DISInstance, InstanceVersion, InstanceStatus } from '../../models/instance.model';\nimport { DISVersion } from '../../models/version.model';\nimport { User } from '../../models/user.model';\nimport { CertificateGenerationResponse } from '../../models/certificate.model';\nimport { first } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-customer-detail',\n  templateUrl: './customer-detail.component.html',\n  styleUrls: ['./customer-detail.component.css']\n})\nexport class CustomerDetailComponent implements OnInit {\n  // Zpřístupnění enumu InstanceStatus pro šablonu\n  InstanceStatus = InstanceStatus;\n  customerId: number = 0;\n  customer: CustomerDetail | null = null;\n  contacts: Contact[] = [];\n  instances: DISInstance[] = [];\n  instanceVersions: { [key: number]: InstanceVersion[] } = {};\n  displayedInstanceVersions: { [key: number]: InstanceVersion[] } = {};\n  showAllVersions: { [key: number]: boolean } = {};\n  certificateInfo: any = {};\n  pendingInstanceId: number | null = null; // ID instance, kterou chceme otevřít po načtení dat\n  users: User[] = [];\n  versions: DISVersion[] = [];\n\n  // Režim komponenty (vytvoření, editace, zobrazení)\n  mode: 'create' | 'edit' | 'view' = 'view';\n\n  customerForm: FormGroup;\n  contactForm: FormGroup;\n  instanceForm: FormGroup;\n  instanceVersionForm: FormGroup;\n\n  loading: boolean = false;\n  loadingContacts: boolean = false;\n  loadingInstances: boolean = false;\n  loadingVersions: boolean = false;\n  loadingUsers: boolean = false;\n\n  saving: boolean = false;\n  savingContact: boolean = false;\n  savingInstance: boolean = false;\n  savingInstanceVersion: boolean = false;\n\n  isEditMode: boolean = false;\n  isEditContactMode: boolean = false;\n  isEditInstanceMode: boolean = false;\n  isEditInstanceVersionMode: boolean = false;\n\n  error: string | null = null;\n  contactError: string | null = null;\n  instanceError: string | null = null;\n  instanceVersionError: string | null = null;\n\n  // Callback pro akce po načtení detailu\n  loadCustomerDetailCallback: (() => void) | null = null;\n\n  // Příznak, zda je přihlášený uživatel administrátor\n  isAdmin: boolean = false;\n\n  selectedContact: Contact | null = null;\n  selectedInstance: DISInstance | null = null;\n  selectedInstanceForVersion: DISInstance | null = null;\n\n  // Vygenerovaný certifikát\n  generatedCertificate: CertificateGenerationResponse | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private customerService: CustomerService,\n    private contactService: ContactService,\n    private instanceService: InstanceService,\n    private instanceVersionService: InstanceVersionService,\n    private versionService: VersionService,\n    private userService: UserService,\n    private certificateService: CertificateService,\n    private breadcrumbService: BreadcrumbService,\n    private modalService: ModalService,\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private clipboardService: ClipboardService\n  ) {\n    // Inicializace formulářů\n    this.customerForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n      companyId: ['', Validators.maxLength(20)],\n      taxId: ['', Validators.maxLength(20)],\n      street: ['', [Validators.required, Validators.maxLength(255)]],\n      city: ['', [Validators.required, Validators.maxLength(255)]],\n      postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n      country: ['Česká republika', [Validators.required, Validators.maxLength(100)]],\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      website: ['', Validators.maxLength(255)],\n      notes: ['', Validators.maxLength(500)]\n    });\n\n    this.contactForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.maxLength(100)]],\n      lastName: ['', [Validators.required, Validators.maxLength(100)]],\n      position: ['', Validators.maxLength(100)],\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      notes: ['', Validators.maxLength(100)],\n      isPrimary: [false]\n    });\n\n    this.instanceForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      notes: ['', Validators.maxLength(500)],\n      status: ['Active'],\n      blockReason: ['', Validators.maxLength(500)],\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false]\n    });\n\n    this.instanceVersionForm = this.formBuilder.group({\n      versionId: ['', Validators.required],\n      installedByUserId: ['', Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n  }\n\n  ngOnInit(): void {\n    // Kontrola, zda je uživatel administrátor\n    this.isAdmin = this.authService.isAdmin();\n\n    // Načtení verzí a uživatelů pro formuláře\n    this.loadVersions();\n    this.loadUsers();\n\n    // Zjištění, zda jsme na cestě /customers/add\n    if (this.router.url === '/customers/add') {\n      // Jsme v režimu vytváření nového zákazníka\n      this.mode = 'create';\n      this.isEditMode = true;\n\n      // Nastavení breadcrumbs pro vytvoření\n      this.breadcrumbService.setBreadcrumbs([\n        { label: 'Zákazníci', url: '/customers', icon: 'building-fill' },\n        { label: 'Přidat zákazníka', url: '/customers/add', icon: 'plus-circle-fill' }\n      ]);\n\n      // Reset formuláře\n      this.customerForm.reset({\n        country: 'Česká republika'\n      });\n    } else {\n      // Získání ID zákazníka z URL pro režim zobrazení nebo editace\n      this.route.params.subscribe(params => {\n        if (params['id']) {\n          this.customerId = +params['id'];\n          this.loadCustomerDetail(this.customerId);\n\n          // Kontrola, zda máme otevřít detail v režimu editace\n          this.route.queryParams.subscribe(queryParams => {\n            console.log('Query params:', queryParams);\n            if (queryParams['edit'] === 'true') {\n              console.log('Edit mode detected from query params');\n              this.mode = 'edit';\n              // Po načtení detailu přepneme do režimu editace\n              this.loadCustomerDetailCallback = () => {\n                console.log('loadCustomerDetailCallback executing, customer:', this.customer);\n                if (this.customer) {\n                  console.log('Calling editCustomer() from callback');\n                  this.editCustomer();\n                } else {\n                  console.log('Customer data not available in callback');\n                }\n              };\n            } else {\n              console.log('View mode set');\n              this.mode = 'view';\n            }\n          });\n\n          // Kontrola, zda máme otevřít detail instance (z localStorage)\n          try {\n            console.log('Kontroluji localStorage pro otevření detailu instance');\n            const openInstanceDetailJson = localStorage.getItem('openInstanceDetail');\n            console.log('Data z localStorage:', openInstanceDetailJson);\n\n            if (openInstanceDetailJson) {\n              const openInstanceDetail = JSON.parse(openInstanceDetailJson);\n              console.log('Parsovaná data:', openInstanceDetail);\n\n              // Kontrola, zda data v localStorage odpovídají aktuálnímu zákazníkovi\n              // a zda nejsou starší než 10 sekund\n              const currentTime = new Date().getTime();\n              const dataTime = openInstanceDetail.timestamp || 0;\n              const isRecent = (currentTime - dataTime) < 10000; // 10 sekund\n\n              console.log('Aktuální customerId:', this.customerId);\n              console.log('CustomerId z localStorage:', openInstanceDetail.customerId);\n              console.log('Je časově aktuální:', isRecent, '(rozdíl:', currentTime - dataTime, 'ms)');\n\n              if (openInstanceDetail.customerId === this.customerId && isRecent) {\n                const instanceId = openInstanceDetail.instanceId;\n                console.log('Nastavuji otevření instance ID:', instanceId);\n\n                // Uložíme ID instance, kterou chceme otevřít\n                this.pendingInstanceId = instanceId;\n\n                // Odstraníme data z localStorage, aby se modální okno neotevíralo opakovaně\n                localStorage.removeItem('openInstanceDetail');\n                console.log('Data z localStorage byla odstraněna');\n              } else {\n                console.log('Data v localStorage neodpovídají aktuálnímu zákazníkovi nebo nejsou aktuální');\n              }\n            } else {\n              console.log('Žádná data pro otevření instance v localStorage');\n            }\n          } catch (error) {\n            console.error('Chyba při zpracování dat z localStorage:', error);\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * Načtení detailu zákazníka\n   */\n  loadCustomerDetail(customerId: number): void {\n    console.log('loadCustomerDetail() called with ID:', customerId);\n    this.loading = true;\n    this.loadingContacts = true;\n    this.loadingInstances = true;\n\n    this.customerService.getCustomer(customerId)\n      .pipe(first())\n      .subscribe({\n        next: (customer) => {\n          console.log('Customer data loaded:', customer);\n          this.customer = customer;\n\n          // Nastavení breadcrumbs\n          this.breadcrumbService.setBreadcrumbs([\n            { label: 'Zákazníci', url: '/customers', icon: 'building-fill' },\n            { label: customer.name, url: `/customers/${customerId}`, icon: 'info-circle-fill' }\n          ]);\n\n          this.loading = false;\n\n          // Načtení kontaktů zákazníka\n          this.loadContacts(customerId);\n\n          // Načtení instancí DIS zákazníka\n          this.loadInstances(customerId);\n\n          // Volat callback po načtení detailu, pokud existuje\n          if (this.loadCustomerDetailCallback) {\n            console.log('Executing loadCustomerDetailCallback');\n            this.loadCustomerDetailCallback();\n            this.loadCustomerDetailCallback = null; // Použít jen jednou\n          }\n        },\n        error: (err) => {\n          this.error = `Chyba při načítání zákazníka: ${err.message}`;\n          this.loading = false;\n          this.loadingContacts = false;\n          this.loadingInstances = false;\n        }\n      });\n  }\n\n  /**\n   * Načtení kontaktů zákazníka\n   */\n  loadContacts(customerId: number): void {\n    this.contactService.getContactsByCustomerId(customerId)\n      .pipe(first())\n      .subscribe({\n        next: (contacts) => {\n          this.contacts = contacts;\n          this.loadingContacts = false;\n        },\n        error: (err) => {\n          this.error = `Chyba při načítání kontaktů: ${err.message}`;\n          this.loadingContacts = false;\n        }\n      });\n  }\n\n  /**\n   * Načtení instancí DIS zákazníka\n   */\n  loadInstances(customerId: number): void {\n    this.instanceService.getInstancesByCustomerId(customerId)\n      .pipe(first())\n      .subscribe({\n        next: (instances) => {\n          this.instances = instances;\n          this.loadingInstances = false;\n\n          // Načtení verzí pro každou instanci\n          instances.forEach(instance => {\n            this.loadInstanceVersions(instance.id);\n          });\n\n          // Pokud máme čekající ID instance, otevřeme její detail\n          if (this.pendingInstanceId) {\n            console.log('Máme čekající ID instance:', this.pendingInstanceId);\n\n            // Najdeme instanci podle ID\n            const instance = instances.find(i => i.id === this.pendingInstanceId);\n            console.log('Nalezená instance:', instance);\n\n            if (instance) {\n              console.log('Otevírám modální okno s detailem instance:', instance.id);\n\n              // Počkáme, až se stránka načte\n              setTimeout(() => {\n                // Nastavíme vybranou instanci\n                this.selectedInstanceForVersion = instance;\n\n                // Načtení informací o certifikátu\n                this.loadCertificateInfo(instance.id);\n\n                // Otevřít modal přímo pomocí Bootstrap API\n                console.log('Otevírám modální okno přímo pomocí Bootstrap API');\n                const modalElement = document.getElementById('instanceDetailModal');\n                if (modalElement) {\n                  console.log('Modal element nalezen, otevírám...');\n                  const modal = new window.bootstrap.Modal(modalElement);\n                  modal.show();\n                } else {\n                  console.error('Modal element s ID instanceDetailModal nebyl nalezen');\n                }\n              }, 500);\n            } else {\n              console.log('Instance nebyla nalezena v seznamu načtených instancí');\n            }\n\n            // Resetujeme čekající ID instance\n            this.pendingInstanceId = null;\n          }\n        },\n        error: (err) => {\n          this.error = `Chyba při načítání instancí DIS: ${err.message}`;\n          this.loadingInstances = false;\n        }\n      });\n  }\n\n  /**\n   * Načtení verzí instance\n   */\n  loadInstanceVersions(instanceId: number): void {\n    this.instanceVersionService.getInstanceVersions(instanceId)\n      .pipe(first())\n      .subscribe({\n        next: (versions) => {\n          // Uložení všech verzí\n          this.instanceVersions[instanceId] = versions;\n\n          // Výchozí zobrazení pouze posledních 5 verzí\n          this.displayedInstanceVersions[instanceId] = versions.slice(0, 5);\n\n          // Výchozí stav zobrazení všech verzí\n          this.showAllVersions[instanceId] = false;\n        },\n        error: (err) => {\n          console.error(`Chyba při načítání verzí instance ${instanceId}:`, err);\n        }\n      });\n  }\n\n  /**\n   * Přepnutí zobrazení všech verzí instance\n   */\n  toggleAllVersions(instanceId: number): void {\n    this.showAllVersions[instanceId] = !this.showAllVersions[instanceId];\n\n    if (this.showAllVersions[instanceId]) {\n      // Zobrazit všechny verze\n      this.displayedInstanceVersions[instanceId] = [...this.instanceVersions[instanceId]];\n    } else {\n      // Zobrazit pouze posledních 5 verzí\n      this.displayedInstanceVersions[instanceId] = this.instanceVersions[instanceId].slice(0, 5);\n    }\n  }\n\n  /**\n   * Načtení informací o certifikátu instance\n   */\n  loadCertificateInfo(instanceId: number): void {\n    this.certificateService.getInstanceCertificateInfo(instanceId)\n      .pipe(first())\n      .subscribe({\n        next: (info) => {\n          this.certificateInfo[instanceId] = info;\n        },\n        error: (err) => {\n          console.error(`Chyba při načítání informací o certifikátu instance ${instanceId}:`, err);\n        }\n      });\n  }\n\n  /**\n   * Načtení verzí pro formuláře\n   */\n  loadVersions(): void {\n    this.loadingVersions = true;\n\n    this.versionService.getVersions()\n      .pipe(first())\n      .subscribe({\n        next: (versions) => {\n          this.versions = versions;\n          this.loadingVersions = false;\n        },\n        error: (err) => {\n          console.error('Chyba při načítání verzí:', err);\n          this.loadingVersions = false;\n        }\n      });\n  }\n\n  /**\n   * Načtení uživatelů pro formuláře\n   */\n  loadUsers(): void {\n    this.loadingUsers = true;\n\n    this.userService.getUsers()\n      .pipe(first())\n      .subscribe({\n        next: (users) => {\n          this.users = users;\n          this.loadingUsers = false;\n        },\n        error: (err) => {\n          console.error('Chyba při načítání uživatelů:', err);\n          this.loadingUsers = false;\n        }\n      });\n  }\n\n  /**\n   * Přepnutí do režimu editace zákazníka\n   */\n  editCustomer(): void {\n    console.log('editCustomer() called');\n    if (!this.customer) {\n      console.log('No customer data available');\n      return;\n    }\n\n    console.log('Switching to edit mode for customer:', this.customer);\n    this.isEditMode = true;\n    this.mode = 'edit'; // Explicitně nastavíme režim na 'edit'\n\n    // Naplnění formuláře daty zákazníka\n    console.log('Patching form with customer data');\n    this.customerForm.patchValue({\n      name: this.customer.name,\n      abbreviation: this.customer.abbreviation,\n      companyId: this.customer.companyId,\n      taxId: this.customer.taxId,\n      street: this.customer.street,\n      city: this.customer.city,\n      postalCode: this.customer.postalCode,\n      country: this.customer.country,\n      email: this.customer.email,\n      phone: this.customer.phone,\n      website: this.customer.website,\n      notes: this.customer.notes\n    });\n    console.log('Form after patching:', this.customerForm.value);\n  }\n\n  /**\n   * Uložení změn zákazníka nebo vytvoření nového\n   */\n  saveCustomer(): void {\n    console.log('saveCustomer() called in customer-detail.component.ts');\n    console.log('Form valid:', this.customerForm.valid);\n    console.log('Form values:', this.customerForm.value);\n    console.log('Mode:', this.mode);\n    console.log('isEditMode:', this.isEditMode);\n    console.log('customer:', this.customer);\n\n    if (this.customerForm.invalid) {\n      console.log('Form is invalid, returning');\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.customerForm.controls).forEach(key => {\n        const control = this.customerForm.get(key);\n        control?.markAsTouched();\n        if (control?.invalid) {\n          console.log(`Field ${key} is invalid:`, control.errors);\n        }\n      });\n      return;\n    }\n\n    console.log('Form is valid, proceeding with save');\n    this.saving = true;\n    const formData = this.customerForm.value;\n\n    // Vytvoření objektu zákazníka z formuláře\n    const customerData = {\n      name: formData.name,\n      abbreviation: formData.abbreviation,\n      companyId: formData.companyId,\n      taxId: formData.taxId,\n      street: formData.street,\n      city: formData.city,\n      postalCode: formData.postalCode,\n      country: formData.country,\n      email: formData.email,\n      phone: formData.phone,\n      website: formData.website,\n      notes: formData.notes\n    };\n\n    if (this.mode === 'create') {\n      // Vytvoření nového zákazníka\n      this.customerService.createCustomer(customerData)\n        .pipe(first())\n        .subscribe({\n          next: (createdCustomer) => {\n            this.saving = false;\n            // Přesměrování na detail nově vytvořeného zákazníka\n            this.router.navigate(['/customers', createdCustomer.id]);\n          },\n          error: (err) => {\n            this.error = `Chyba při vytváření zákazníka: ${err.message}`;\n            this.saving = false;\n          }\n        });\n    } else if (this.mode === 'edit' && this.customer) {\n      // Aktualizace existujícího zákazníka\n      console.log('Updating customer with ID:', this.customer.id);\n      console.log('Customer data to send:', customerData);\n\n      this.customerService.updateCustomer(this.customer.id, customerData)\n        .pipe(first())\n        .subscribe({\n          next: (response) => {\n            console.log('Customer updated successfully:', response);\n            this.saving = false;\n            this.isEditMode = false;\n            this.loadCustomerDetail(this.customerId);\n          },\n          error: (err) => {\n            console.error('Error updating customer:', err);\n            this.error = `Chyba při ukládání zákazníka: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.saving = false;\n          }\n        });\n    }\n  }\n\n  /**\n   * Zrušení editace zákazníka\n   */\n  cancelEdit(): void {\n    if (this.mode === 'create') {\n      // Při vytváření nového zákazníka se vrátíme na seznam zákazníků\n      this.router.navigate(['/customers']);\n    } else {\n      // Při editaci existujícího zákazníka se vrátíme do režimu zobrazení\n      this.isEditMode = false;\n    }\n  }\n\n  /**\n   * Otevření modálu pro přidání kontaktu\n   */\n  openAddContactModal(): void {\n    this.isEditContactMode = false;\n    this.selectedContact = null;\n    this.contactError = null;\n\n    // Reset formuláře\n    this.contactForm.reset({\n      isPrimary: false\n    });\n\n    // Otevření modálu\n    this.modalService.open('contactModal');\n  }\n\n  /**\n   * Otevření modálu pro editaci kontaktu\n   */\n  editContact(contact: Contact): void {\n    this.isEditContactMode = true;\n    this.selectedContact = contact;\n    this.contactError = null;\n\n    // Naplnění formuláře daty kontaktu\n    this.contactForm.patchValue({\n      firstName: contact.firstName,\n      lastName: contact.lastName,\n      position: contact.position || '',\n      email: contact.email || '',\n      phone: contact.phone || '',\n      notes: contact.notes || '',\n      isPrimary: contact.isPrimary\n    });\n\n    // Otevření modálu\n    this.modalService.open('contactModal');\n  }\n\n  /**\n   * Uložení kontaktu\n   */\n  saveContact(): void {\n    if (this.contactForm.invalid) {\n      return;\n    }\n\n    this.savingContact = true;\n    const formData = this.contactForm.value;\n\n    if (this.isEditContactMode && this.selectedContact) {\n      // Editace existujícího kontaktu\n      const updatedContact: UpdateContactRequest = {\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        position: formData.position,\n        email: formData.email,\n        phone: formData.phone,\n        notes: formData.notes,\n        isPrimary: formData.isPrimary\n      };\n\n      this.contactService.updateContact(this.selectedContact.id, updatedContact)\n        .pipe(first())\n        .subscribe({\n          next: () => {\n            this.savingContact = false;\n            this.closeContactModal();\n            this.loadContacts(this.customerId);\n          },\n          error: (err) => {\n            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n            this.savingContact = false;\n          }\n        });\n    } else {\n      // Přidání nového kontaktu\n      const newContact: CreateContactRequest = {\n        customerId: this.customerId,\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        position: formData.position,\n        email: formData.email,\n        phone: formData.phone,\n        notes: formData.notes,\n        isPrimary: formData.isPrimary\n      };\n\n      this.contactService.createContact(newContact)\n        .pipe(first())\n        .subscribe({\n          next: () => {\n            this.savingContact = false;\n            this.closeContactModal();\n            this.loadContacts(this.customerId);\n          },\n          error: (err) => {\n            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;\n            this.savingContact = false;\n          }\n        });\n    }\n  }\n\n  /**\n   * Zavření modálu pro kontakty\n   */\n  closeContactModal(): void {\n    this.modalService.close('contactModal');\n  }\n\n  /**\n   * Smazání kontaktu\n   */\n  async deleteContact(contact: Contact): Promise<void> {\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`,\n      'Smazání kontaktu',\n      'Smazat',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.contactService.deleteContact(contact.id)\n        .pipe(first())\n        .subscribe({\n          next: () => {\n            this.loadContacts(this.customerId);\n          },\n          error: (err) => {\n            this.error = `Chyba při mazání kontaktu: ${err.message}`;\n            this.modalService.alert(\n              `Chyba při mazání kontaktu: ${err.message}`,\n              'Chyba',\n              'Zavřít',\n              'btn-danger'\n            );\n          }\n        });\n    }\n  }\n\n  /**\n   * Otevření modálu pro přidání instance\n   */\n  openAddInstanceModal(): void {\n    this.isEditInstanceMode = false;\n    this.selectedInstance = null;\n    this.instanceError = null;\n\n    // Reset formuláře\n    this.instanceForm.reset({\n      status: InstanceStatus.Active, // Použití číselné hodnoty enumu\n      moduleReporting: true,\n      moduleAdvancedSecurity: false,\n      moduleApiIntegration: false,\n      moduleDataExport: false,\n      moduleCustomization: false\n    });\n\n    // Otevření modálu\n    this.modalService.open('instanceModal');\n  }\n\n  /**\n   * Otevření modálu pro editaci instance\n   */\n  editInstance(instance: DISInstance): void {\n    this.isEditInstanceMode = true;\n    this.selectedInstance = instance;\n    this.instanceError = null;\n\n    // Naplnění formuláře daty instance\n    this.instanceForm.patchValue({\n      name: instance.name,\n      serverUrl: instance.serverUrl,\n      expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n      notes: instance.notes,\n      // Převod řetězcového statusu na číselnou hodnotu enumu\n      status: this.getStatusEnumValue(instance.status),\n      moduleReporting: instance.moduleReporting,\n      moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n      moduleApiIntegration: instance.moduleApiIntegration,\n      moduleDataExport: instance.moduleDataExport,\n      moduleCustomization: instance.moduleCustomization\n    });\n\n    // Otevření modálu\n    this.modalService.open('instanceModal');\n  }\n\n  /**\n   * Zobrazení detailu instance\n   */\n  viewInstanceDetail(instance: DISInstance): void {\n    this.selectedInstanceForVersion = instance;\n\n    // Načtení verzí instance\n    this.loadInstanceVersions(instance.id);\n\n    // Načtení informací o certifikátu\n    this.loadCertificateInfo(instance.id);\n\n    // Otevřít modal\n    this.modalService.open('instanceDetailModal');\n  }\n\n  /**\n   * Zavření modálu pro detail instance\n   */\n  closeInstanceDetailModal(): void {\n    this.modalService.close('instanceDetailModal');\n  }\n\n  /**\n   * Editace instance z detailu instance\n   */\n  editInstanceFromDetail(instance: DISInstance): void {\n    // Nejprve zavřeme modál s detailem instance\n    this.closeInstanceDetailModal();\n\n    // Poté otevřeme modál pro editaci instance\n    setTimeout(() => {\n      this.editInstance(instance);\n    }, 500); // Počkáme 500ms, aby se první modál stihl zavřít\n  }\n\n  /**\n   * Zavření modálu pro přidání/úpravu instance\n   */\n  closeInstanceModal(): void {\n    this.modalService.close('instanceModal');\n  }\n\n  /**\n   * Převod řetězcové hodnoty statusu na enum InstanceStatus\n   */\n  private convertStatusToEnum(status: string): InstanceStatus {\n    // Pokud je status číselná hodnota jako řetězec (např. \"3\"), převedeme ji na číslo\n    if (!isNaN(Number(status))) {\n      const numericStatus = Number(status);\n      return numericStatus;\n    }\n\n    // Jinak zpracujeme řetězcové hodnoty\n    switch (status) {\n      case 'Active': return InstanceStatus.Active;\n      case 'Blocked': return InstanceStatus.Blocked;\n      case 'Expired': return InstanceStatus.Expired;\n      case 'Trial': return InstanceStatus.Trial;\n      case 'Maintenance': return InstanceStatus.Maintenance;\n      default: return InstanceStatus.Active; // Výchozí hodnota je Active\n    }\n  }\n\n  /**\n   * Převod řetězcového statusu na číselnou hodnotu enumu\n   */\n  private getStatusEnumValue(status: string | InstanceStatus): InstanceStatus {\n    if (typeof status === 'number') {\n      return status;\n    }\n\n    switch (status) {\n      case 'Active': return InstanceStatus.Active;\n      case 'Blocked': return InstanceStatus.Blocked;\n      case 'Expired': return InstanceStatus.Expired;\n      case 'Trial': return InstanceStatus.Trial;\n      case 'Maintenance': return InstanceStatus.Maintenance;\n      default: return InstanceStatus.Active; // Výchozí hodnota je Active\n    }\n  }\n\n  /**\n   * Uložení instance\n   */\n  saveInstance(): void {\n    if (this.instanceForm.invalid) {\n      return;\n    }\n\n    this.savingInstance = true;\n    const formData = this.instanceForm.value;\n\n    console.log('Hodnoty formuláře před úpravou:', formData);\n\n    if (this.isEditInstanceMode && this.selectedInstance) {\n      // Editace existující instance\n      const updatedInstance = {\n        name: formData.name,\n        serverUrl: formData.serverUrl,\n        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n        notes: formData.notes,\n        status: this.convertStatusToEnum(formData.status),\n        moduleReporting: formData.moduleReporting,\n        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n        moduleApiIntegration: formData.moduleApiIntegration,\n        moduleDataExport: formData.moduleDataExport,\n        moduleCustomization: formData.moduleCustomization\n      };\n\n      console.log('Odesílaná data instance:', updatedInstance);\n\n      this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance)\n        .pipe(first())\n        .subscribe({\n          next: (response) => {\n            console.log('Instance úspěšně aktualizována:', response);\n            this.savingInstance = false;\n            this.closeInstanceModal();\n            this.loadInstances(this.customerId);\n          },\n          error: (err) => {\n            console.error('Chyba při ukládání instance:', err);\n            this.instanceError = `Chyba při ukládání instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.savingInstance = false;\n          }\n        });\n    } else {\n      // Přidání nové instance\n      const newInstance = {\n        customerId: this.customerId,\n        name: formData.name,\n        serverUrl: formData.serverUrl,\n        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,\n        notes: formData.notes,\n        status: this.convertStatusToEnum(formData.status),\n        moduleReporting: formData.moduleReporting,\n        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,\n        moduleApiIntegration: formData.moduleApiIntegration,\n        moduleDataExport: formData.moduleDataExport,\n        moduleCustomization: formData.moduleCustomization\n      };\n\n      console.log('Odesílaná data nové instance:', newInstance);\n\n      this.instanceService.createInstance(newInstance)\n        .pipe(first())\n        .subscribe({\n          next: (response) => {\n            console.log('Instance úspěšně vytvořena:', response);\n            this.savingInstance = false;\n            this.closeInstanceModal();\n            this.loadInstances(this.customerId);\n          },\n          error: (err) => {\n            console.error('Chyba při vytváření instance:', err);\n            this.instanceError = `Chyba při vytváření instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n            this.savingInstance = false;\n          }\n        });\n    }\n  }\n\n  /**\n   * Smazání zákazníka\n   */\n  async deleteCustomer(): Promise<void> {\n    if (!this.customer) return;\n\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat zákazníka ${this.customer.name}?`,\n      'Smazání zákazníka',\n      'Smazat',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.loading = true;\n\n      this.customerService.deleteCustomer(this.customer.id)\n        .pipe(first())\n        .subscribe({\n          next: () => {\n            this.router.navigate(['/customers']);\n          },\n          error: (err) => {\n            this.error = `Chyba při mazání zákazníka: ${err.message}`;\n            this.loading = false;\n            this.modalService.alert(\n              `Chyba při mazání zákazníka: ${err.message}`,\n              'Chyba',\n              'Zavřít',\n              'btn-danger'\n            );\n          }\n        });\n    }\n  }\n\n  /**\n   * Návrat na seznam zákazníků\n   */\n  goBack(): void {\n    this.router.navigate(['/customers']);\n  }\n\n  /**\n   * Kopírování API klíče do schránky\n   */\n  async copyApiKey(inputElement: HTMLInputElement): Promise<void> {\n    await this.clipboardService.copyFromInput(\n      inputElement,\n      'API klíč byl zkopírován do schránky',\n      'Nepodařilo se zkopírovat API klíč'\n    );\n  }\n\n  /**\n   * Generování nového certifikátu pro instanci\n   */\n  async generateCertificate(instanceId: number): Promise<void> {\n    const confirmed = await this.modalService.confirm(\n      'Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.',\n      'Generování certifikátu',\n      'Generovat',\n      'Zrušit',\n      'btn-success',\n      'btn-secondary'\n    );\n\n    if (!confirmed) {\n      return;\n    }\n\n    this.certificateService.generateCertificate(instanceId).subscribe({\n      next: (response) => {\n        // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n        // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n        const certificatePassword = response.password || 'password';\n\n        this.generatedCertificate = {\n          certificate: response.certificate,\n          privateKey: response.privateKey,\n          thumbprint: response.thumbprint,\n          expirationDate: response.expirationDate,\n          password: certificatePassword,\n          certificatePassword: certificatePassword\n        };\n\n        // Aktualizace informací o certifikátu\n        this.loadCertificateInfo(instanceId);\n\n        // Explicitní nastavení hesla pro zobrazení v modálním okně\n        const passwordElement = document.getElementById('certificatePassword');\n        if (passwordElement && this.generatedCertificate) {\n          passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';\n        }\n\n        // Zobrazení modálního okna s informacemi o certifikátu\n        this.modalService.open('certificateGeneratedModal');\n      },\n      error: (err) => {\n        console.error('Chyba při generování certifikátu', err);\n        this.modalService.alert(\n          `Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`,\n          'Chyba',\n          'Zavřít',\n          'btn-danger'\n        );\n      }\n    });\n  }\n\n  /**\n   * Stažení vygenerovaného certifikátu\n   */\n  downloadCertificate(): void {\n    if (!this.generatedCertificate) {\n      return;\n    }\n\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n\n  /**\n   * Pomocná metoda pro konverzi Base64 na Blob\n   */\n  private base64ToBlob(base64: string, contentType: string): Blob {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n\n    return new Blob(byteArrays, { type: contentType });\n  }\n\n  /**\n   * Zavření modálního okna s certifikátem\n   */\n  closeCertificateModal(): void {\n    this.modalService.close('certificateGeneratedModal');\n  }\n\n  /**\n   * Otevření modálního okna pro přidání verze instance\n   */\n  openAddInstanceVersionModal(): void {\n    if (!this.selectedInstanceForVersion) {\n      console.error('Není vybrána žádná instance');\n      return;\n    }\n\n    this.isEditInstanceVersionMode = false;\n    this.instanceVersionError = null;\n\n    // Získání ID aktuálně přihlášeného uživatele\n    const currentUserId = this.authService.getCurrentUserId();\n\n    // Reset formuláře s předvyplněným aktuálním uživatelem\n    this.instanceVersionForm.reset({\n      versionId: '',\n      installedByUserId: currentUserId, // Předvyplnění aktuálního uživatele\n      notes: ''\n    });\n\n    // Otevření modálu\n    this.modalService.open('instanceVersionModal');\n  }\n\n  /**\n   * Zavření modálního okna pro přidání/úpravu verze instance\n   */\n  closeInstanceVersionModal(): void {\n    this.modalService.close('instanceVersionModal');\n  }\n\n  /**\n   * Uložení verze instance\n   */\n  saveInstanceVersion(): void {\n    if (this.instanceVersionForm.invalid) {\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceVersionForm.controls).forEach(key => {\n        const control = this.instanceVersionForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n\n    if (!this.selectedInstanceForVersion) {\n      this.instanceVersionError = 'Není vybrána žádná instance.';\n      return;\n    }\n\n    this.savingInstanceVersion = true;\n    const formData = this.instanceVersionForm.value;\n\n    // Vytvoření nové verze instance\n    const newInstanceVersion = {\n      versionId: formData.versionId,\n      installedByUserId: formData.installedByUserId,\n      notes: formData.notes || ''\n    };\n\n    this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion)\n      .pipe(first())\n      .subscribe({\n        next: (response: InstanceVersion) => {\n          console.log('Verze instance úspěšně vytvořena:', response);\n          this.savingInstanceVersion = false;\n          this.closeInstanceVersionModal();\n\n          // Aktualizace seznamu verzí instance\n          this.loadInstanceVersions(this.selectedInstanceForVersion!.id);\n        },\n        error: (err: any) => {\n          console.error('Chyba při vytváření verze instance:', err);\n          this.instanceVersionError = `Chyba při vytváření verze instance: ${err.message || err.statusText || 'Neznámá chyba'}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n  }\n\n  /**\n   * Testovací metoda pro zobrazení hesla\n   */\n  testPassword(): void {\n    if (this.generatedCertificate) {\n      this.modalService.alert(\n        `Heslo k certifikátu: <strong>${this.generatedCertificate.password}</strong>`,\n        'Heslo k certifikátu',\n        'Zavřít',\n        'btn-primary'\n      );\n    } else {\n      this.modalService.alert(\n        'Certifikát není k dispozici',\n        'Informace',\n        'Zavřít',\n        'btn-secondary'\n      );\n    }\n  }\n\n  /**\n   * Pomocná metoda pro získání jména statusu instance\n   */\n  getInstanceStatusName(status: InstanceStatus | string): string {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active': return 'Aktivní';\n        case 'Blocked': return 'Blokovaná';\n        case 'Expired': return 'Expirovaná';\n        case 'Trial': return 'Zkušební';\n        case 'Maintenance': return 'Údržba';\n        default: return status;\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active: return 'Aktivní';\n        case InstanceStatus.Blocked: return 'Blokovaná';\n        case InstanceStatus.Expired: return 'Expirovaná';\n        case InstanceStatus.Trial: return 'Zkušební';\n        case InstanceStatus.Maintenance: return 'Údržba';\n        default: return String(status);\n      }\n    }\n  }\n\n  /**\n   * Pomocná metoda pro získání třídy pro status instance\n   */\n  getInstanceStatusClass(status: InstanceStatus | string): string {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active': return 'bg-success';\n        case 'Blocked': return 'bg-danger';\n        case 'Expired': return 'bg-warning text-dark';\n        case 'Trial': return 'bg-info text-dark';\n        case 'Maintenance': return 'bg-secondary';\n        default: return 'bg-secondary';\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active: return 'bg-success';\n        case InstanceStatus.Blocked: return 'bg-danger';\n        case InstanceStatus.Expired: return 'bg-warning text-dark';\n        case InstanceStatus.Trial: return 'bg-info text-dark';\n        case InstanceStatus.Maintenance: return 'bg-secondary';\n        default: return 'bg-secondary';\n      }\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"row mb-3\">\n    <div class=\"col-12\">\n      <div class=\"d-flex justify-content-between align-items-center\">\n        <button class=\"btn btn-outline-secondary\" (click)=\"goBack()\">\n          <i class=\"bi bi-arrow-left me-1\"></i> Zpět na seznam zákazníků\n        </button>\n        <div *ngIf=\"!isEditMode && customer && isAdmin\">\n          <button class=\"btn btn-outline-danger me-2\" (click)=\"deleteCustomer()\">\n            <i class=\"bi bi-trash me-1\"></i> Smazat zákazníka\n          </button>\n          <button class=\"btn btn-primary\" (click)=\"editCustomer()\">\n            <i class=\"bi bi-pencil me-1\"></i> Upravit zákazníka\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Nač<PERSON>tání -->\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <!-- Chybová hláška -->\n  <div *ngIf=\"error\" class=\"alert alert-danger\">\n    {{ error }}\n  </div>\n\n  <!-- Formulář pro vytvoření/editaci zákazníka -->\n  <div *ngIf=\"isEditMode\" class=\"card mb-4\">\n    <div class=\"card-header bg-primary text-white\">\n      <h5 class=\"mb-0\">{{ mode === 'create' ? 'Přidat zákazníka' : 'Upravit zákazníka' }}</h5>\n    </div>\n    <div class=\"card-body\">\n      <form [formGroup]=\"customerForm\" (ngSubmit)=\"saveCustomer()\">\n        <div class=\"row\">\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"name\" class=\"form-label required-field\">Název</label>\n            <input type=\"text\" class=\"form-control\" id=\"name\" formControlName=\"name\" placeholder=\"Název společnosti\">\n            <div *ngIf=\"customerForm.get('name')?.invalid && customerForm.get('name')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('name')?.errors?.['required']\">Název je povinný</small>\n              <small *ngIf=\"customerForm.get('name')?.errors?.['maxlength']\">Název nesmí být delší než 200 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"abbreviation\" class=\"form-label required-field\">Zkratka</label>\n            <input type=\"text\" class=\"form-control\" id=\"abbreviation\" formControlName=\"abbreviation\" placeholder=\"Zkratka\" [ngClass]=\"{'is-invalid': customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched}\">\n            <div *ngIf=\"customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('abbreviation')?.errors?.['required']\">Zkratka je povinná</small>\n              <small *ngIf=\"customerForm.get('abbreviation')?.errors?.['maxlength']\">Zkratka nesmí být delší než 50 znaků</small>\n            </div>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"companyId\" class=\"form-label\">IČO</label>\n            <input type=\"text\" class=\"form-control\" id=\"companyId\" formControlName=\"companyId\" placeholder=\"IČO\" [ngClass]=\"{'is-invalid': customerForm.get('companyId')?.invalid && customerForm.get('companyId')?.touched}\">\n            <div *ngIf=\"customerForm.get('companyId')?.invalid && customerForm.get('companyId')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('companyId')?.errors?.['maxlength']\">IČO nesmí být delší než 20 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"taxId\" class=\"form-label\">DIČ</label>\n            <input type=\"text\" class=\"form-control\" id=\"taxId\" formControlName=\"taxId\" placeholder=\"DIČ\" [ngClass]=\"{'is-invalid': customerForm.get('taxId')?.invalid && customerForm.get('taxId')?.touched}\">\n            <div *ngIf=\"customerForm.get('taxId')?.invalid && customerForm.get('taxId')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('taxId')?.errors?.['maxlength']\">DIČ nesmí být delší než 20 znaků</small>\n            </div>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"email\" class=\"form-label\">Email</label>\n            <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\" placeholder=\"Email\">\n            <div *ngIf=\"customerForm.get('email')?.invalid && customerForm.get('email')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('email')?.errors?.['email']\">Neplatný formát emailu</small>\n              <small *ngIf=\"customerForm.get('email')?.errors?.['maxlength']\">Email nesmí být delší než 255 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"phone\" class=\"form-label\">Telefon</label>\n            <input type=\"tel\" class=\"form-control\" id=\"phone\" formControlName=\"phone\" placeholder=\"Telefon\">\n            <div *ngIf=\"customerForm.get('phone')?.invalid && customerForm.get('phone')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('phone')?.errors?.['maxlength']\">Telefon nesmí být delší než 50 znaků</small>\n            </div>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-6 mb-3\">\n            <label for=\"website\" class=\"form-label\">Web</label>\n            <input type=\"text\" class=\"form-control\" id=\"website\" formControlName=\"website\" placeholder=\"Web\" [ngClass]=\"{'is-invalid': customerForm.get('website')?.invalid && customerForm.get('website')?.touched}\">\n            <div *ngIf=\"customerForm.get('website')?.invalid && customerForm.get('website')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('website')?.errors?.['maxlength']\">Web nesmí být delší než 255 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-6 mb-3\">\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-12 mb-3\">\n            <label for=\"street\" class=\"form-label required-field\">Ulice</label>\n            <input type=\"text\" class=\"form-control\" id=\"street\" formControlName=\"street\" placeholder=\"Ulice\">\n            <div *ngIf=\"customerForm.get('street')?.invalid && customerForm.get('street')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('street')?.errors?.['required']\">Ulice je povinná</small>\n              <small *ngIf=\"customerForm.get('street')?.errors?.['maxlength']\">Ulice nesmí být delší než 255 znaků</small>\n            </div>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-4 mb-3\">\n            <label for=\"city\" class=\"form-label required-field\">Město</label>\n            <input type=\"text\" class=\"form-control\" id=\"city\" formControlName=\"city\" placeholder=\"Město\">\n            <div *ngIf=\"customerForm.get('city')?.invalid && customerForm.get('city')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('city')?.errors?.['required']\">Město je povinné</small>\n              <small *ngIf=\"customerForm.get('city')?.errors?.['maxlength']\">Město nesmí být delší než 255 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-4 mb-3\">\n            <label for=\"postalCode\" class=\"form-label required-field\">PSČ</label>\n            <input type=\"text\" class=\"form-control\" id=\"postalCode\" formControlName=\"postalCode\" placeholder=\"PSČ\">\n            <div *ngIf=\"customerForm.get('postalCode')?.invalid && customerForm.get('postalCode')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('postalCode')?.errors?.['required']\">PSČ je povinné</small>\n              <small *ngIf=\"customerForm.get('postalCode')?.errors?.['maxlength']\">PSČ nesmí být delší než 20 znaků</small>\n            </div>\n          </div>\n          <div class=\"col-md-4 mb-3\">\n            <label for=\"country\" class=\"form-label required-field\">Země</label>\n            <input type=\"text\" class=\"form-control\" id=\"country\" formControlName=\"country\" placeholder=\"Země\">\n            <div *ngIf=\"customerForm.get('country')?.invalid && customerForm.get('country')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"customerForm.get('country')?.errors?.['required']\">Země je povinná</small>\n              <small *ngIf=\"customerForm.get('country')?.errors?.['maxlength']\">Země nesmí být delší než 100 znaků</small>\n            </div>\n          </div>\n        </div>\n        <div class=\"mb-3\">\n          <label for=\"notes\" class=\"form-label\">Poznámky</label>\n          <textarea class=\"form-control\" id=\"notes\" formControlName=\"notes\" rows=\"3\" placeholder=\"Poznámky\" [ngClass]=\"{'is-invalid': customerForm.get('notes')?.invalid && customerForm.get('notes')?.touched}\"></textarea>\n          <div *ngIf=\"customerForm.get('notes')?.invalid && customerForm.get('notes')?.touched\" class=\"text-danger mt-1\">\n            <small *ngIf=\"customerForm.get('notes')?.errors?.['maxlength']\">Poznámky nesmí být delší než 500 znaků</small>\n          </div>\n        </div>\n        <div class=\"d-flex justify-content-end\">\n          <button type=\"button\" class=\"btn btn-outline-secondary me-2\" (click)=\"cancelEdit()\">Zrušit</button>\n          <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"customerForm.invalid || saving\" (click)=\"saveCustomer()\">\n            <span *ngIf=\"saving\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n            Uložit\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n\n  <!-- Detail zákazníka -->\n  <div *ngIf=\"!isEditMode && customer\" class=\"card mb-4\">\n    <div class=\"card-header bg-primary text-white\">\n      <h5 class=\"mb-0\">Detail zákazníka</h5>\n    </div>\n    <div class=\"card-body\">\n      <div class=\"row mb-0\">\n        <div class=\"col-md-6\">\n          <h4>{{ customer.name }}</h4>\n          <p><strong>Zkratka:</strong> {{ customer.abbreviation }}</p>\n          <p *ngIf=\"customer.companyId\"><strong>IČO:</strong> {{ customer.companyId }}</p>\n          <p *ngIf=\"customer.taxId\"><strong>DIČ:</strong> {{ customer.taxId }}</p>\n          <p *ngIf=\"customer.email\"><strong>Email:</strong> {{ customer.email }}</p>\n          <p *ngIf=\"customer.phone\"><strong>Telefon:</strong> {{ customer.phone }}</p>\n          <p *ngIf=\"customer.website\"><strong>Web:</strong> {{ customer.website }}</p>\n        </div>\n        <div class=\"col-md-6\">\n          <p><strong>Ulice:</strong> {{ customer.street }}</p>\n          <p><strong>Město:</strong> {{ customer.city }}</p>\n          <p><strong>PSČ:</strong> {{ customer.postalCode }}</p>\n          <p><strong>Země:</strong> {{ customer.country }}</p>\n          <p><strong>Vytvořeno:</strong> {{ customer.createdAt | localDate:'dd.MM.yyyy HH:mm' }}</p>\n          <p><strong>Aktualizováno:</strong> {{ customer.updatedAt | localDate:'dd.MM.yyyy HH:mm' }}</p>\n        </div>\n      </div>\n      <div *ngIf=\"customer.notes\" class=\"row mt-0\">\n        <div class=\"col-12\">\n          <p><strong>Poznámky:</strong></p>\n          <p>{{ customer.notes }}</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Kontakty zákazníka -->\n  <div *ngIf=\"!isEditMode && customer\" class=\"card mb-4\">\n    <div class=\"card-header bg-primary text-white d-flex justify-content-between align-items-center\">\n      <h5 class=\"mb-0\">Kontakty</h5>\n      <button *ngIf=\"isAdmin\" class=\"btn btn-sm btn-light\" (click)=\"openAddContactModal()\">\n        <i class=\"bi bi-plus-lg me-1\"></i> Přidat kontakt\n      </button>\n    </div>\n    <div class=\"card-body\">\n      <div *ngIf=\"loadingContacts\" class=\"d-flex justify-content-center my-3\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Načítání...</span>\n        </div>\n      </div>\n\n      <div *ngIf=\"!loadingContacts && contacts.length === 0\" class=\"alert alert-info\">\n        Tento zákazník nemá žádné kontakty.\n      </div>\n\n      <div *ngIf=\"!loadingContacts && contacts.length > 0\" class=\"table-responsive\">\n        <table class=\"table table-striped table-hover\">\n          <thead>\n            <tr>\n              <th>Jméno</th>\n              <th>Pozice</th>\n              <th>Email</th>\n              <th>Telefon</th>\n              <th>Primární</th>\n              <th>Akce</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let contact of contacts\">\n              <td>{{ contact.firstName }} {{ contact.lastName }}</td>\n              <td>{{ contact.position || '-' }}</td>\n              <td>{{ contact.email || '-' }}</td>\n              <td>{{ contact.phone || '-' }}</td>\n              <td>\n                <span class=\"badge bg-success\" *ngIf=\"contact.isPrimary\">Ano</span>\n                <span class=\"badge bg-secondary\" *ngIf=\"!contact.isPrimary\">Ne</span>\n              </td>\n              <td>\n                <div class=\"btn-group\">\n                  <button *ngIf=\"isAdmin\" class=\"btn btn-sm btn-outline-primary\" (click)=\"editContact(contact)\" title=\"Upravit\">\n                    <i class=\"bi bi-pencil-fill\"></i>\n                  </button>\n                  <button *ngIf=\"isAdmin\" class=\"btn btn-sm btn-outline-danger\" (click)=\"deleteContact(contact)\" title=\"Smazat\">\n                    <i class=\"bi bi-trash-fill\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n\n  <!-- Instance DIS zákazníka -->\n  <div *ngIf=\"!isEditMode && customer\" class=\"card mb-4\">\n    <div class=\"card-header bg-primary text-white d-flex justify-content-between align-items-center\">\n      <h5 class=\"mb-0\">Instance DIS</h5>\n      <button *ngIf=\"isAdmin\" class=\"btn btn-sm btn-light\" (click)=\"openAddInstanceModal()\">\n        <i class=\"bi bi-plus-lg me-1\"></i> Přidat instanci\n      </button>\n    </div>\n    <div class=\"card-body\">\n      <div *ngIf=\"loadingInstances\" class=\"d-flex justify-content-center my-3\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Načítání...</span>\n        </div>\n      </div>\n\n      <div *ngIf=\"!loadingInstances && instances.length === 0\" class=\"alert alert-info\">\n        Tento zákazník nemá žádné instance DIS.\n      </div>\n\n      <div *ngIf=\"!loadingInstances && instances.length > 0\" class=\"table-responsive\">\n        <table class=\"table table-striped table-hover\">\n          <thead>\n            <tr>\n              <th>Název</th>\n              <th>Status</th>\n              <th>Aktuální verze</th>\n              <th>Poslední připojení</th>\n              <th>Akce</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let instance of instances\">\n              <td>{{ instance.name }}</td>\n              <td>\n                <span class=\"badge\" [ngClass]=\"getInstanceStatusClass(instance.status)\">\n                  {{ getInstanceStatusName(instance.status) }}\n                </span>\n              </td>\n              <td>\n                <span *ngIf=\"instanceVersions[instance.id]?.length\">\n                  {{ instanceVersions[instance.id][0].versionNumber || 'N/A' }}\n                </span>\n                <span *ngIf=\"!instanceVersions[instance.id]?.length\">N/A</span>\n              </td>\n              <td>\n                {{ instance.lastConnectionDate ? (instance.lastConnectionDate | localDate:'dd.MM.yyyy HH:mm') : 'Nikdy' }}\n              </td>\n              <td>\n                <div class=\"btn-group\">\n                  <button class=\"btn btn-sm btn-outline-info\" (click)=\"viewInstanceDetail(instance)\" title=\"Zobrazit detail\">\n                    <i class=\"bi bi-eye-fill\"></i>\n                  </button>\n                  <button *ngIf=\"isAdmin\" class=\"btn btn-sm btn-outline-primary\" (click)=\"editInstance(instance)\" title=\"Upravit\">\n                    <i class=\"bi bi-pencil-fill\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu kontaktu -->\n<div class=\"modal fade\" id=\"contactModal\" tabindex=\"-1\" aria-labelledby=\"contactModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"contactModalLabel\">{{ isEditContactMode ? 'Upravit kontakt' : 'Přidat kontakt' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closeContactModal()\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"contactError\" class=\"alert alert-danger mb-3\">\n          {{ contactError }}\n        </div>\n        <form [formGroup]=\"contactForm\" (ngSubmit)=\"saveContact()\">\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"firstName\" class=\"form-label required-field\">Jméno</label>\n              <input type=\"text\" class=\"form-control\" id=\"firstName\" formControlName=\"firstName\" placeholder=\"Jméno\">\n              <div *ngIf=\"contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"contactForm.get('firstName')?.errors?.['required']\">Jméno je povinné</small>\n                <small *ngIf=\"contactForm.get('firstName')?.errors?.['maxlength']\">Jméno nesmí být delší než 100 znaků</small>\n              </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"lastName\" class=\"form-label required-field\">Příjmení</label>\n              <input type=\"text\" class=\"form-control\" id=\"lastName\" formControlName=\"lastName\" placeholder=\"Příjmení\">\n              <div *ngIf=\"contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"contactForm.get('lastName')?.errors?.['required']\">Příjmení je povinné</small>\n                <small *ngIf=\"contactForm.get('lastName')?.errors?.['maxlength']\">Příjmení nesmí být delší než 100 znaků</small>\n              </div>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"position\" class=\"form-label\">Pozice</label>\n            <input type=\"text\" class=\"form-control\" id=\"position\" formControlName=\"position\" placeholder=\"Pozice\" [ngClass]=\"{'is-invalid': contactForm.get('position')?.invalid && contactForm.get('position')?.touched}\">\n            <div *ngIf=\"contactForm.get('position')?.invalid && contactForm.get('position')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"contactForm.get('position')?.errors?.['maxlength']\">Pozice nesmí být delší než 100 znaků</small>\n            </div>\n          </div>\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"contactEmail\" class=\"form-label\">Email</label>\n              <input type=\"email\" class=\"form-control\" id=\"contactEmail\" formControlName=\"email\" placeholder=\"Email\">\n              <div *ngIf=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"contactForm.get('email')?.errors?.['email']\">Neplatný formát emailu</small>\n                <small *ngIf=\"contactForm.get('email')?.errors?.['maxlength']\">Email nesmí být delší než 255 znaků</small>\n              </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"contactPhone\" class=\"form-label\">Telefon</label>\n              <input type=\"text\" class=\"form-control\" id=\"contactPhone\" formControlName=\"phone\" placeholder=\"Telefon\" [ngClass]=\"{'is-invalid': contactForm.get('phone')?.invalid && contactForm.get('phone')?.touched}\">\n              <div *ngIf=\"contactForm.get('phone')?.invalid && contactForm.get('phone')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"contactForm.get('phone')?.errors?.['maxlength']\">Telefon nesmí být delší než 50 znaků</small>\n              </div>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"contactNotes\" class=\"form-label\">Poznámky</label>\n            <textarea class=\"form-control\" id=\"contactNotes\" formControlName=\"notes\" rows=\"3\" placeholder=\"Poznámky\" [ngClass]=\"{'is-invalid': contactForm.get('notes')?.invalid && contactForm.get('notes')?.touched}\"></textarea>\n            <div *ngIf=\"contactForm.get('notes')?.invalid && contactForm.get('notes')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"contactForm.get('notes')?.errors?.['maxlength']\">Poznámky nesmí být delší než 100 znaků</small>\n            </div>\n          </div>\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" class=\"form-check-input\" id=\"isPrimary\" formControlName=\"isPrimary\">\n            <label class=\"form-check-label\" for=\"isPrimary\">Primární kontakt</label>\n          </div>\n          <div class=\"d-flex justify-content-end\">\n            <button type=\"button\" class=\"btn btn-outline-secondary me-2\" (click)=\"closeContactModal()\">Zrušit</button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"contactForm.invalid || savingContact\">\n              <span *ngIf=\"savingContact\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n              Uložit\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Komponenta pro detail instance -->\n<app-instance-detail\n  [instance]=\"selectedInstanceForVersion\"\n  [certificateInfo]=\"selectedInstanceForVersion ? certificateInfo[selectedInstanceForVersion.id] : null\"\n  [instanceVersions]=\"selectedInstanceForVersion ? (instanceVersions[selectedInstanceForVersion.id] || []) : []\"\n  [showAddVersionButton]=\"true\"\n  modalId=\"instanceDetailModal\"\n  (close)=\"closeInstanceDetailModal()\"\n  (edit)=\"editInstanceFromDetail($event)\"\n  (generateCertificate)=\"generateCertificate($event)\"\n  (addVersion)=\"openAddInstanceVersionModal()\">\n</app-instance-detail>\n\n<!-- Modal pro přidání/úpravu instance DIS -->\n<div class=\"modal fade\" id=\"instanceModal\" tabindex=\"-1\" aria-labelledby=\"instanceModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"instanceModalLabel\">{{ isEditInstanceMode ? 'Upravit instanci DIS' : 'Přidat instanci DIS' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closeInstanceModal()\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"instanceError\" class=\"alert alert-danger mb-3\">\n          {{ instanceError }}\n        </div>\n        <form [formGroup]=\"instanceForm\" (ngSubmit)=\"saveInstance()\">\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"name\" class=\"form-label required-field\">Název</label>\n              <input type=\"text\" class=\"form-control\" id=\"name\" formControlName=\"name\" placeholder=\"Název instance\">\n              <div *ngIf=\"instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"instanceForm.get('name')?.errors?.['required']\">Název je povinný</small>\n                <small *ngIf=\"instanceForm.get('name')?.errors?.['maxlength']\">Název nesmí být delší než 200 znaků</small>\n              </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"status\" class=\"form-label required-field\">Status</label>\n              <select class=\"form-select\" id=\"status\" formControlName=\"status\">\n                <option [value]=\"InstanceStatus.Active\">Aktivní</option>\n                <option [value]=\"InstanceStatus.Blocked\">Blokovaná</option>\n                <option [value]=\"InstanceStatus.Expired\">Expirovaná</option>\n                <option [value]=\"InstanceStatus.Trial\">Zkušební</option>\n                <option [value]=\"InstanceStatus.Maintenance\">Údržba</option>\n              </select>\n            </div>\n          </div>\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"serverUrl\" class=\"form-label required-field\">URL serveru</label>\n              <input type=\"text\" class=\"form-control\" id=\"serverUrl\" formControlName=\"serverUrl\" placeholder=\"URL serveru\">\n              <div *ngIf=\"instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched\" class=\"text-danger mt-1\">\n                <small *ngIf=\"instanceForm.get('serverUrl')?.errors?.['required']\">URL serveru je povinná</small>\n                <small *ngIf=\"instanceForm.get('serverUrl')?.errors?.['maxlength']\">URL serveru nesmí být delší než 255 znaků</small>\n              </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"expirationDate\" class=\"form-label\">Datum expirace</label>\n              <input type=\"date\" class=\"form-control\" id=\"expirationDate\" formControlName=\"expirationDate\">\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"notes\" class=\"form-label\">Poznámky</label>\n            <textarea class=\"form-control\" id=\"notes\" formControlName=\"notes\" rows=\"3\" placeholder=\"Poznámky\" [ngClass]=\"{'is-invalid': instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched}\"></textarea>\n            <div *ngIf=\"instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"instanceForm.get('notes')?.errors?.['maxlength']\">Poznámky nesmí být delší než 500 znaků</small>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label class=\"form-label\">Moduly</label>\n            <div class=\"form-check mb-2\">\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"moduleReporting\" formControlName=\"moduleReporting\">\n              <label class=\"form-check-label\" for=\"moduleReporting\">Reporting</label>\n            </div>\n            <div class=\"form-check mb-2\">\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"moduleAdvancedSecurity\" formControlName=\"moduleAdvancedSecurity\">\n              <label class=\"form-check-label\" for=\"moduleAdvancedSecurity\">Pokročilé zabezpečení</label>\n            </div>\n            <div class=\"form-check mb-2\">\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"moduleApiIntegration\" formControlName=\"moduleApiIntegration\">\n              <label class=\"form-check-label\" for=\"moduleApiIntegration\">API integrace</label>\n            </div>\n            <div class=\"form-check mb-2\">\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"moduleDataExport\" formControlName=\"moduleDataExport\">\n              <label class=\"form-check-label\" for=\"moduleDataExport\">Export dat</label>\n            </div>\n            <div class=\"form-check mb-2\">\n              <input type=\"checkbox\" class=\"form-check-input\" id=\"moduleCustomization\" formControlName=\"moduleCustomization\">\n              <label class=\"form-check-label\" for=\"moduleCustomization\">Customizace</label>\n            </div>\n          </div>\n          <div class=\"d-flex justify-content-end\">\n            <button type=\"button\" class=\"btn btn-outline-secondary me-2\" (click)=\"closeInstanceModal()\">Zrušit</button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"instanceForm.invalid || savingInstance\">\n              <span *ngIf=\"savingInstance\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n              Uložit\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu verze instance -->\n<div class=\"modal fade\" id=\"instanceVersionModal\" tabindex=\"-1\" aria-labelledby=\"instanceVersionModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"instanceVersionModalLabel\">{{ isEditInstanceVersionMode ? 'Upravit verzi instance' : 'Přidat verzi instance' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closeInstanceVersionModal()\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"instanceVersionError\" class=\"alert alert-danger mb-3\">\n          {{ instanceVersionError }}\n        </div>\n        <form [formGroup]=\"instanceVersionForm\" (ngSubmit)=\"saveInstanceVersion()\">\n          <div class=\"mb-3\">\n            <label for=\"instanceName\" class=\"form-label\">Instance:</label>\n            <input type=\"text\" class=\"form-control\" id=\"instanceName\" [value]=\"selectedInstanceForVersion?.name\" disabled>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"versionId\" class=\"form-label required-field\">Verze:</label>\n            <select class=\"form-select\" id=\"versionId\" formControlName=\"versionId\">\n              <option value=\"\">-- Vyberte verzi --</option>\n              <option *ngFor=\"let version of versions\" [value]=\"version.id\">{{ version.versionNumber }}</option>\n            </select>\n            <div *ngIf=\"instanceVersionForm.get('versionId')?.invalid && instanceVersionForm.get('versionId')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"instanceVersionForm.get('versionId')?.errors?.['required']\">Verze je povinná</small>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"installedByUserId\" class=\"form-label required-field\">Instaloval:</label>\n            <select class=\"form-select\" id=\"installedByUserId\" formControlName=\"installedByUserId\">\n              <option value=\"\">-- Vyberte uživatele --</option>\n              <option *ngFor=\"let user of users\" [value]=\"user.id\">{{ user.firstName }} {{ user.lastName }}</option>\n            </select>\n            <div *ngIf=\"instanceVersionForm.get('installedByUserId')?.invalid && instanceVersionForm.get('installedByUserId')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"instanceVersionForm.get('installedByUserId')?.errors?.['required']\">Uživatel je povinný</small>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"notes\" class=\"form-label\">Poznámky:</label>\n            <textarea class=\"form-control\" id=\"notes\" formControlName=\"notes\" rows=\"3\" placeholder=\"Poznámky k instalaci verze\" [ngClass]=\"{'is-invalid': instanceVersionForm.get('notes')?.invalid && instanceVersionForm.get('notes')?.touched}\"></textarea>\n            <div *ngIf=\"instanceVersionForm.get('notes')?.invalid && instanceVersionForm.get('notes')?.touched\" class=\"text-danger mt-1\">\n              <small *ngIf=\"instanceVersionForm.get('notes')?.errors?.['maxlength']\">Poznámky nesmí být delší než 500 znaků</small>\n            </div>\n          </div>\n          <div class=\"d-flex justify-content-end\">\n            <button type=\"button\" class=\"btn btn-outline-secondary me-2\" (click)=\"closeInstanceVersionModal()\">Zrušit</button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"instanceVersionForm.invalid || savingInstanceVersion\">\n              <span *ngIf=\"savingInstanceVersion\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n              Uložit\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Sdílená komponenta pro zobrazení vygenerovaného certifikátu -->\n<app-certificate-modal\n  [certificate]=\"generatedCertificate\"\n  [instanceName]=\"selectedInstanceForVersion?.name || ''\"\n  modalId=\"certificateGeneratedModal\"\n  (close)=\"closeCertificateModal()\"\n  (download)=\"downloadCertificate()\">\n</app-certificate-modal>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}