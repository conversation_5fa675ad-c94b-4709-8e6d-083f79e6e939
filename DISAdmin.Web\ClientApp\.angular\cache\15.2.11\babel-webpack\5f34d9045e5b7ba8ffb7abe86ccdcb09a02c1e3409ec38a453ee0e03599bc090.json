{"ast": null, "code": "import { HomeComponent } from './home/<USER>';\nimport { LoginComponent } from './login/login.component';\nimport { UsersComponent } from './users/users.component';\nimport { UserDetailComponent } from './users/user-detail/user-detail.component';\nimport { CustomersComponent } from './customers/customers.component';\nimport { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';\nimport { VersionsComponent } from './versions/versions.component';\nimport { VersionFormComponent } from './versions/version-form/version-form.component';\nimport { ProfileComponent } from './profile/profile.component';\nimport { SecurityComponent } from './security/security.component';\nimport { CertificatesComponent } from './certificates/certificates.component';\nimport { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { AlertsComponent } from './alerts/alerts.component';\nimport { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';\nimport { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';\nimport { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';\nimport { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';\nimport { AuthGuard } from './services/auth.guard';\nexport const routes = [{\n  path: '',\n  component: HomeComponent,\n  pathMatch: 'full',\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Domů',\n    icon: 'house-fill'\n  }\n}, {\n  path: 'login',\n  component: LoginComponent,\n  data: {\n    breadcrumb: 'Přihlášení',\n    icon: 'box-arrow-in-right'\n  }\n}, {\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Dashboard',\n    icon: 'grid-fill'\n  }\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Uživatelé',\n    icon: 'people-fill'\n  }\n}, {\n  path: 'users/add',\n  component: UserDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat uživatele',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'users/:id',\n  component: UserDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Detail uživatele',\n    icon: 'person-fill'\n  }\n}, {\n  path: 'customers',\n  component: CustomersComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Zákazníci',\n    icon: 'building-fill'\n  }\n}, {\n  path: 'customers/add',\n  component: CustomerDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat zákazníka',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'customers/:id',\n  component: CustomerDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Detail zákazníka',\n    icon: 'info-circle-fill'\n  }\n}, {\n  path: 'versions',\n  component: VersionsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Verze DIS',\n    icon: 'tag-fill'\n  }\n}, {\n  path: 'versions/add',\n  component: VersionFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Přidat verzi',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'versions/:id',\n  component: VersionFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Detail verze',\n    icon: 'info-circle-fill'\n  }\n}, {\n  path: 'profile',\n  component: ProfileComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Profil',\n    icon: 'person-fill'\n  }\n}, {\n  path: 'security',\n  component: SecurityComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Bezpečnostní události',\n    icon: 'shield-exclamation-fill'\n  }\n}, {\n  path: 'certificates',\n  component: CertificatesComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Certifikáty',\n    icon: 'file-earmark-lock-fill'\n  }\n}, {\n  path: 'instance-wizard',\n  component: InstanceWizardComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Průvodce instancí',\n    icon: 'magic'\n  }\n}, {\n  path: 'monitoring',\n  loadChildren: () => import('./monitoring/monitoring.module').then(m => m.MonitoringModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Monitoring',\n    icon: 'graph-up'\n  }\n}, {\n  path: 'performance',\n  loadChildren: () => import('./performance/performance.module').then(m => m.PerformanceModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Výkon DIS',\n    icon: 'speedometer'\n  }\n}, {\n  path: 'instance-metrics/:id',\n  loadChildren: () => import('./instance-metrics/instance-metrics.module').then(m => m.InstanceMetricsModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Metriky instance',\n    icon: 'speedometer2'\n  }\n}, {\n  path: 'alerts',\n  component: AlertsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Upozornění',\n    icon: 'bell-fill'\n  }\n}, {\n  path: 'alerts/rules/add',\n  component: AlertRuleFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat pravidlo',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'alerts/rules/:id',\n  component: AlertRuleFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Upravit pravidlo',\n    icon: 'pencil-fill'\n  }\n}, {\n  path: 'certificate-rotation',\n  component: CertificateRotationSettingsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Nastavení automatické rotace DIS certifikátů',\n    icon: 'arrow-repeat'\n  }\n}, {\n  path: 'certificate-rotation/instance/:id',\n  component: InstanceCertificateSettingsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Nastavení certifikátů instance',\n    icon: 'gear-fill'\n  }\n}, {\n  path: 'ip-whitelisting/:id',\n  component: IpWhitelistingComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'IP Whitelist',\n    icon: 'list-check'\n  }\n}, {\n  path: 'logs',\n  loadChildren: () => import('./logs/logs.module').then(m => m.LogsModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Logy systému',\n    icon: 'journal-text'\n  }\n}, {\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Administrace',\n    icon: 'gear-fill'\n  }\n},\n// Fallback route\n{\n  path: '**',\n  redirectTo: ''\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}