using DISAdmin.Api.Models;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using Microsoft.AspNetCore.Mvc;
using DISAdmin.Core.Attributes;

namespace DISAdmin.Api.Controllers;

[ApiController]
[Route("api/security-event-filters")]
[Auth.Authorize(adminOnly: true)]
public class SecurityEventFilterController : ControllerBase
{
    private readonly SecurityEventFilterService _filterService;
    private readonly ILogger<SecurityEventFilterController> _logger;

    public SecurityEventFilterController(
        SecurityEventFilterService filterService,
        ILogger<SecurityEventFilterController> logger)
    {
        _filterService = filterService;
        _logger = logger;
    }

    /// <summary>
    /// Získá seznam všech filtrů bezpečnostních událostí
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>>> GetFilters()
    {
        try
        {
            _logger.LogInformation("Načítání seznamu filtrů bezpečnostních událostí");

            var filters = await _filterService.GetAllFiltersAsync();
            var response = filters.Select(f => new SecurityEventFilterResponse
            {
                Id = f.Id,
                EventType = (int)f.EventType,
                EventTypeName = GetEventTypeName(f.EventType),
                Description = f.Description,
                IpAddress = f.IpAddress,
                CreatedAt = f.CreatedAt,
                CreatedBy = f.CreatedBy
            }).ToList();

            return Ok(new SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>
            {
                Success = true,
                Data = response,
                Message = null
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při načítání filtrů bezpečnostních událostí");
            return StatusCode(500, new SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>
            {
                Success = false,
                Data = null,
                Message = "Chyba při načítání filtrů bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Vytvoří nový filtr bezpečnostních událostí
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<SecurityEventFilterApiResponse<SecurityEventFilterResponse>>> CreateFilter(
        [FromBody] CreateSecurityEventFilterRequest request)
    {
        try
        {
            _logger.LogInformation("Vytváření nového filtru bezpečnostních událostí: {EventType}, {Description}, {IpAddress}",
                request.EventType, request.Description, request.IpAddress);

            var eventType = (SecurityEventType)request.EventType;
            var username = User.Identity?.Name ?? "unknown";

            // Smazání existujících událostí podle vzoru (pokud je požadováno)
            if (request.DeleteExistingEvents)
            {
                var deletedCount = await _filterService.DeleteEventsByPatternAsync(
                    eventType, request.Description, request.IpAddress);
                _logger.LogInformation("Smazáno {Count} existujících bezpečnostních událostí podle vzoru", deletedCount);
            }

            // Vytvoření filtru
            var filter = await _filterService.CreateFilterAsync(
                eventType, request.Description, request.IpAddress, username);

            var response = new SecurityEventFilterResponse
            {
                Id = filter.Id,
                EventType = (int)filter.EventType,
                EventTypeName = GetEventTypeName(filter.EventType),
                Description = filter.Description,
                IpAddress = filter.IpAddress,
                CreatedAt = filter.CreatedAt,
                CreatedBy = filter.CreatedBy
            };

            return Ok(new SecurityEventFilterApiResponse<SecurityEventFilterResponse>
            {
                Success = true,
                Data = response,
                Message = "Filtr byl úspěšně vytvořen"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při vytváření filtru bezpečnostních událostí");
            return StatusCode(500, new SecurityEventFilterApiResponse<SecurityEventFilterResponse>
            {
                Success = false,
                Data = null,
                Message = "Chyba při vytváření filtru bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Smaže filtr bezpečnostních událostí
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<SecurityEventFilterApiResponse<object>>> DeleteFilter(int id)
    {
        try
        {
            _logger.LogInformation("Mazání filtru bezpečnostních událostí: {FilterId}", id);

            var success = await _filterService.DeleteFilterAsync(id);
            if (!success)
            {
                return NotFound(new SecurityEventFilterApiResponse<object>
                {
                    Success = false,
                    Data = null,
                    Message = "Filtr nebyl nalezen"
                });
            }

            return Ok(new SecurityEventFilterApiResponse<object>
            {
                Success = true,
                Data = null,
                Message = "Filtr byl úspěšně smazán"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při mazání filtru bezpečnostních událostí: {FilterId}", id);
            return StatusCode(500, new SecurityEventFilterApiResponse<object>
            {
                Success = false,
                Data = null,
                Message = "Chyba při mazání filtru bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Smaže existující bezpečnostní události podle vzoru
    /// </summary>
    [HttpDelete("events/by-pattern")]
    public async Task<ActionResult<SecurityEventFilterApiResponse<object>>> DeleteEventsByPattern(
        [FromBody] DeleteEventsByPatternRequest request)
    {
        try
        {
            _logger.LogInformation("Mazání bezpečnostních událostí podle vzoru: {EventType}, {Description}, {IpAddress}",
                request.EventType, request.Description, request.IpAddress);

            var eventType = (SecurityEventType)request.EventType;
            var deletedCount = await _filterService.DeleteEventsByPatternAsync(
                eventType, request.Description, request.IpAddress);

            return Ok(new SecurityEventFilterApiResponse<object>
            {
                Success = true,
                Data = new { DeletedCount = deletedCount },
                Message = $"Bylo smazáno {deletedCount} bezpečnostních událostí"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při mazání bezpečnostních událostí podle vzoru");
            return StatusCode(500, new SecurityEventFilterApiResponse<object>
            {
                Success = false,
                Data = null,
                Message = "Chyba při mazání bezpečnostních událostí podle vzoru"
            });
        }
    }

    private static string GetEventTypeName(SecurityEventType eventType)
    {
        return eventType switch
        {
            SecurityEventType.FailedAccessAttempt => "Neúspěšný pokus o přístup",
            SecurityEventType.SuspiciousActivity => "Podezřelá aktivita",
            SecurityEventType.IpBlocked => "Blokovaná IP adresa",
            SecurityEventType.CertificateValidationFailure => "Selhání validace certifikátu",
            SecurityEventType.ApiKeyMisuse => "Nesprávné použití API klíče",
            SecurityEventType.UnauthorizedAccess => "Neautorizovaný přístup",
            SecurityEventType.Other => "Ostatní",
            _ => eventType.ToString()
        };
    }
}
