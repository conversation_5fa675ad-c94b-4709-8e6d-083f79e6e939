<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Alerty a upozornění</h2>
    <button class="btn btn-primary" (click)="addRule()" *ngIf="isAdmin">
      <i class="bi bi-plus-circle me-2"></i><span class="d-none d-md-inline">Přidat pravidlo</span><span class="d-inline d-md-none">Přidat</span>
    </button>
  </div>

  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
  </div>

  <!-- Pokročilý filtr -->
  <app-advanced-filter
    [entityType]="'alerts'"
    [fields]="filterFields"
    (filterChange)="onFilterChange($event)">
  </app-advanced-filter>

  <!-- Seznam alertů -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Alerty</h5>
    </div>
    <div class="card-body">
      <div *ngIf="loading" class="d-flex justify-content-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Načítání...</span>
        </div>
      </div>

      <div *ngIf="!loading && alerts.length === 0" class="alert alert-info">
        Žádné alerty nebyly nalezeny.
      </div>

      <div *ngIf="!loading && alerts.length > 0" class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="dark-header table-header-override">
            <tr class="dark-header-row">
              <th>Datum a čas</th>
              <th>Název</th>
              <th>Závažnost</th>
              <th>Stav</th>
              <th>Instance</th>
              <th>Zákazník</th>
              <th>Zpráva</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let alert of alerts">
              <td>{{ alert.timestamp | localDate:'dd.MM.yyyy HH:mm:ss' }}</td>
              <td>{{ alert.name }}</td>
              <td>
                <span class="badge severity-badge" [ngClass]="{
                  'severity-high': alert.severity === 'critical',
                  'severity-medium': alert.severity === 'warning',
                  'severity-low': alert.severity === 'info'
                }">
                  {{ getSeverityText(alert.severity) }}
                </span>
              </td>
              <td>
                <span class="badge" style="border-radius: 0.375rem; padding: 0.5em 0.75em;" [ngClass]="getStatusClass(alert.status)">
                  {{ getStatusText(alert.status) }}
                </span>
              </td>
              <td>{{ alert.instanceName || '-' }}</td>
              <td>{{ alert.customerName || '-' }}</td>
              <td>{{ alert.message }}</td>
              <td>
                <button *ngIf="alert.status === 'active'" class="btn btn-sm btn-success" (click)="resolveAlert(alert.id)" title="Označit jako vyřešené">
                  <i class="bi bi-check-circle"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>



  <!-- Seznam pravidel pro alerty -->
  <div class="card mb-4" *ngIf="isAdmin">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Pravidla pro alerty</h5>
    </div>
    <div class="card-body">
      <div *ngIf="alertRules.length === 0" class="alert alert-info">
        Žádná pravidla nebyla nalezena.
      </div>

      <div *ngIf="alertRules.length > 0" class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="dark-header table-header-override">
            <tr class="dark-header-row">
              <th>Název</th>
              <th>Metrika</th>
              <th>Podmínka</th>
              <th>Threshold</th>
              <th>Závažnost</th>
              <th>Stav</th>
              <th>Notifikace</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let rule of alertRules">
              <td>{{ rule.name }}</td>
              <td>
                <span class="metric-type-icon">
                  <i class="bi" [ngClass]="getMetricTypeIcon(rule.metricType)" title="{{ getMetricTypeText(rule.metricType) }}"></i>
                </span>
                {{ getMetricTypeText(rule.metricType) }}
              </td>
              <td>{{ getConditionText(rule.condition) }}</td>
              <td>
                <span [ngClass]="getThresholdClass(rule.metricType, rule.threshold)">
                  {{ formatThreshold(rule.metricType, rule.threshold) }}
                </span>
              </td>
              <td>
                <span class="badge severity-badge" [ngClass]="{
                  'severity-high': rule.severity === 'critical',
                  'severity-medium': rule.severity === 'warning',
                  'severity-low': rule.severity === 'info'
                }">
                  {{ getSeverityText(rule.severity) }}
                </span>
              </td>
              <td>
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" [checked]="rule.enabled" (change)="toggleRuleStatus(rule)">
                  <label class="form-check-label">{{ rule.enabled ? 'Aktivní' : 'Neaktivní' }}</label>
                </div>
              </td>
              <td>
                <span *ngIf="rule.notifyByEmail">
                  <i class="bi bi-envelope-fill text-primary"></i> Email
                </span>
                <span *ngIf="!rule.notifyByEmail">-</span>
              </td>
              <td>
                <div class="btn-group">
                  <button class="btn btn-sm btn-outline-info" (click)="editRule(rule)" title="Upravit">
                    <i class="bi bi-pencil-fill"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-danger" (click)="deleteRule(rule.id)" title="Smazat">
                    <i class="bi bi-trash-fill"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>


