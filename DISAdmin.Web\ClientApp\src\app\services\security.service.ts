import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import {
  SecurityDashboardResponse,
  AlertResponse,
  ResolveAlertRequest,
  SecurityEventFilterResponse,
  CreateSecurityEventFilterRequest,
  SecurityEventFilterApiResponse
} from '../models/security.model';

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private apiUrl = `${environment.apiUrl}/dashboard`;

  constructor(private http: HttpClient) { }

  getSecurityDashboard(): Observable<SecurityDashboardResponse> {
    return this.http.get<SecurityDashboardResponse>(`${this.apiUrl}/security`);
  }

  getAlerts(includeResolved: boolean = false): Observable<AlertResponse[]> {
    return this.http.get<AlertResponse[]>(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);
  }

  resolveAlert(alertId: number, resolution: string): Observable<any> {
    const request: ResolveAlertRequest = { resolution };
    return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);
  }

  // Security Event Filters
  getSecurityEventFilters(): Observable<SecurityEventFilterApiResponse<SecurityEventFilterResponse[]>> {
    return this.http.get<SecurityEventFilterApiResponse<SecurityEventFilterResponse[]>>(`${environment.apiUrl}/security-event-filters`);
  }

  createSecurityEventFilter(request: CreateSecurityEventFilterRequest): Observable<SecurityEventFilterApiResponse<SecurityEventFilterResponse>> {
    return this.http.post<SecurityEventFilterApiResponse<SecurityEventFilterResponse>>(`${environment.apiUrl}/security-event-filters`, request);
  }

  deleteSecurityEventFilter(filterId: number): Observable<SecurityEventFilterApiResponse<any>> {
    return this.http.delete<SecurityEventFilterApiResponse<any>>(`${environment.apiUrl}/security-event-filters/${filterId}`);
  }
}
