export interface SecurityDashboardResponse {
  recentSecurityEvents: SecurityEventResponse[];
  activeAlerts: AlertResponse[];
  failedConnectionStats: FailedConnectionStatsResponse[];
}

export interface SecurityEventResponse {
  id: number;
  timestamp: Date;
  eventType: string;
  ipAddress: string;
  username?: string;
  description: string;
  severity: number;
  isResolved: boolean;
}

export interface AlertResponse {
  id: number;
  timestamp: Date;
  alertType: string;
  instanceId?: number;
  instanceName?: string;
  customerName?: string;
  description: string;
  severity: number;
  isResolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: string;
}

export interface FailedConnectionStatsResponse {
  instanceId: number;
  instanceName: string;
  failedCertificateValidationCount: number;
  lastFailedCertificateValidation?: Date;
  failedApiKeyValidationCount: number;
  lastFailedApiKeyValidation?: Date;
  lastKnownIpAddress?: string;
}

export interface ResolveAlertRequest {
  resolution: string;
}

export interface SecurityEventFilterResponse {
  id: number;
  eventType: number;
  eventTypeName: string;
  description?: string;
  ipAddress?: string;
  createdAt: Date;
  createdBy: string;
}

export interface CreateSecurityEventFilterRequest {
  eventType: number;
  description?: string;
  ipAddress?: string;
  deleteExistingEvents: boolean;
}

export interface SecurityEventFilterApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}
