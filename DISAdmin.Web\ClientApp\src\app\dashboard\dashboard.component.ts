import { Component, OnInit, OnD<PERSON>roy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { ChartService } from '../services/chart.service';
import { AuthService } from '../services/auth.service';
import { AlertService } from '../services/alert.service';
import { DashboardConfigService } from '../services/dashboard-config.service';
import { ChartModalService } from '../services/chart-modal.service';
import { MonitoringService } from '../services/monitoring.service';
import { Chart, registerables } from 'chart.js';
import { Subscription, interval } from 'rxjs';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { DashboardConfig, DashboardWidget } from '../models/dashboard-config.model';

// Registrace všech komponent Chart.js
Chart.register(...registerables);

// Import Bootstrap pro popover
declare var bootstrap: any;

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('apiCallsChart') apiCallsChartRef!: ElementRef;
  @ViewChild('apiPerformanceChart') apiPerformanceChartRef!: ElementRef;
  @ViewChild('instancesUsageChart') instancesUsageChartRef!: ElementRef;
  @ViewChild('securityEventsChart') securityEventsChartRef!: ElementRef;

  loading = true;
  error: string | null = null;
  currentUser: any;
  isAdmin = false;
  alerts: any[] = [];
  systemStatistics: any = {};

  // Grafy
  apiCallsChart: Chart | null = null;
  apiPerformanceChart: Chart | null = null;
  instancesUsageChart: Chart | null = null;
  securityEventsChart: Chart | null = null;

  // Filtry
  selectedDays = 30;
  selectedInstanceId: number | null = null;

  // Dashboard konfigurace
  dashboardConfig: DashboardConfig | null = null;
  widgets: DashboardWidget[] = [];
  editMode = false;

  // Aktualizace dat
  private updateSubscription: Subscription | null = null;

  constructor(
    private chartService: ChartService,
    private authService: AuthService,
    private alertService: AlertService,
    private dashboardConfigService: DashboardConfigService,
    private chartModalService: ChartModalService,
    private monitoringService: MonitoringService
  ) {
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.isAdmin = user?.isAdmin || false;
    });

    // Načtení uložené hodnoty počtu dní z local storage
    const savedDateRange = localStorage.getItem('monitoring_dateRange');
    if (savedDateRange) {
      this.selectedDays = parseInt(savedDateRange, 10);
    }
  }

  ngOnInit(): void {
    // Načtení konfigurace dashboardu
    this.loadDashboardConfig();

    this.loadAlerts();
    this.loadSystemStatistics();
  }

  /**
   * Načte konfiguraci dashboardu pro aktuálního uživatele
   */
  loadDashboardConfig(): void {
    this.dashboardConfigService.getUserDashboardConfig().subscribe({
      next: (config) => {
        this.dashboardConfig = config;
        this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
      },
      error: (error) => {
        console.error('Chyba při načítání konfigurace dashboardu', error);
        // Použijeme výchozí konfiguraci
        this.dashboardConfigService.resetDashboardConfig().subscribe(config => {
          this.dashboardConfig = config;
          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
        });
      }
    });
  }

  ngAfterViewInit(): void {
    // Inicializace grafů po načtení view
    setTimeout(() => {
      this.initCharts();
      // Inicializace popoverů s delším zpožděním, aby se zajistilo, že DOM je plně načten
      setTimeout(() => {
        this.initPopovers();
      }, 300);
    }, 500);
  }

  ngOnDestroy(): void {
    // Zrušení subscription při zničení komponenty
    if (this.updateSubscription) {
      this.updateSubscription.unsubscribe();
    }

    // Zničení grafů
    this.destroyCharts();
  }

  /**
   * Načtení upozornění
   */
  loadAlerts(): void {
    this.alertService.getDashboardAlerts().subscribe({
      next: (data) => {
        this.alerts = data.filter((alert: any) => !alert.isResolved);
      },
      error: (err) => {
        console.error('Chyba při načítání upozornění', err);
        this.error = 'Nepodařilo se načíst upozornění';
      }
    });
  }

  /**
   * Načtení systémových statistik
   */
  loadSystemStatistics(): void {
    this.monitoringService.getSystemStatistics().subscribe({
      next: (data) => {
        this.systemStatistics = data;
      },
      error: (err) => {
        console.error('Chyba při načítání systémových statistik', err);
        this.error = 'Nepodařilo se načíst systémové statistiky';
      }
    });
  }

  /**
   * Inicializace všech grafů
   */
  initCharts(): void {
    this.loading = true;

    // Načtení dat pro grafy
    this.loadApiCallsChart();
    this.loadApiPerformanceChart();
    this.loadInstancesUsageChart();

    if (this.isAdmin) {
      this.loadSecurityEventsChart();
    }

    this.loading = false;
  }

  /**
   * Aktualizace všech grafů
   */
  refreshCharts(): void {
    this.loadApiCallsChart();
    this.loadApiPerformanceChart();
    this.loadInstancesUsageChart();

    if (this.isAdmin) {
      this.loadSecurityEventsChart();
    }

    this.loadAlerts();
    this.loadSystemStatistics();
  }

  /**
   * Zničení všech grafů
   */
  destroyCharts(): void {
    if (this.apiCallsChart) {
      this.apiCallsChart.destroy();
      this.apiCallsChart = null;
    }

    if (this.apiPerformanceChart) {
      this.apiPerformanceChart.destroy();
      this.apiPerformanceChart = null;
    }

    if (this.instancesUsageChart) {
      this.instancesUsageChart.destroy();
      this.instancesUsageChart = null;
    }

    if (this.securityEventsChart) {
      this.securityEventsChart.destroy();
      this.securityEventsChart = null;
    }
  }

  /**
   * Načtení grafu API volání
   */
  loadApiCallsChart(): void {
    this.chartService.getApiCallsChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({
      next: (data) => {
        if (this.apiCallsChart) {
          this.apiCallsChart.data.labels = data.labels;
          this.apiCallsChart.data.datasets[0].data = data.data;
          this.apiCallsChart.update();
        } else if (this.apiCallsChartRef) {
          this.apiCallsChart = new Chart(this.apiCallsChartRef.nativeElement, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: [{
                label: 'Počet API volání',
                data: data.data,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet volání'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Datum'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf API volání', err);
        this.error = 'Nepodařilo se načíst data pro graf API volání';
      }
    });
  }

  /**
   * Načtení grafu výkonu API
   */
  loadApiPerformanceChart(): void {
    this.chartService.getApiPerformanceChartData(this.selectedInstanceId || undefined, this.selectedDays).subscribe({
      next: (data) => {
        if (this.apiPerformanceChart) {
          this.apiPerformanceChart.data.labels = data.labels;
          this.apiPerformanceChart.data.datasets[0].data = data.avgData;
          this.apiPerformanceChart.data.datasets[1].data = data.maxData;
          this.apiPerformanceChart.update();
        } else if (this.apiPerformanceChartRef) {
          this.apiPerformanceChart = new Chart(this.apiPerformanceChartRef.nativeElement, {
            type: 'bar',
            data: {
              labels: data.labels,
              datasets: [
                {
                  label: 'Průměrná doba odezvy (ms)',
                  data: data.avgData,
                  backgroundColor: 'rgba(54, 162, 235, 0.5)',
                  borderColor: 'rgba(54, 162, 235, 1)',
                  borderWidth: 1
                },
                {
                  label: 'Maximální doba odezvy (ms)',
                  data: data.maxData,
                  backgroundColor: 'rgba(255, 99, 132, 0.5)',
                  borderColor: 'rgba(255, 99, 132, 1)',
                  borderWidth: 1
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Doba odezvy (ms)'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Endpoint'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf výkonu API', err);
        this.error = 'Nepodařilo se načíst data pro graf výkonu API';
      }
    });
  }

  /**
   * Načtení grafu využití instancí
   */
  loadInstancesUsageChart(): void {
    this.chartService.getInstancesUsageChartData().subscribe({
      next: (data) => {
        if (this.instancesUsageChart) {
          this.instancesUsageChart.data.labels = data.labels;
          this.instancesUsageChart.data.datasets[0].data = data.data;
          this.instancesUsageChart.update();
        } else if (this.instancesUsageChartRef) {
          this.instancesUsageChart = new Chart(this.instancesUsageChartRef.nativeElement, {
            type: 'bar',
            data: {
              labels: data.labels,
              datasets: [{
                label: 'Počet API volání',
                data: data.data,
                backgroundColor: [
                  'rgba(255, 99, 132, 0.5)',
                  'rgba(54, 162, 235, 0.5)',
                  'rgba(255, 206, 86, 0.5)',
                  'rgba(75, 192, 192, 0.5)',
                  'rgba(153, 102, 255, 0.5)',
                  'rgba(255, 159, 64, 0.5)',
                  'rgba(199, 199, 199, 0.5)',
                  'rgba(83, 102, 255, 0.5)',
                  'rgba(40, 159, 64, 0.5)',
                  'rgba(210, 199, 199, 0.5)'
                ],
                borderColor: [
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(75, 192, 192, 1)',
                  'rgba(153, 102, 255, 1)',
                  'rgba(255, 159, 64, 1)',
                  'rgba(199, 199, 199, 1)',
                  'rgba(83, 102, 255, 1)',
                  'rgba(40, 159, 64, 1)',
                  'rgba(210, 199, 199, 1)'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              indexAxis: 'y',
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: false
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                x: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet volání'
                  }
                },
                y: {
                  title: {
                    display: true,
                    text: 'Instance'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf využití instancí', err);
        this.error = 'Nepodařilo se načíst data pro graf využití instancí';
      }
    });
  }

  /**
   * Načtení grafu bezpečnostních událostí
   */
  loadSecurityEventsChart(): void {
    this.chartService.getSecurityEventsChartData(this.selectedDays).subscribe({
      next: (data) => {
        if (this.securityEventsChart) {
          this.securityEventsChart.data.labels = data.labels;
          this.securityEventsChart.data.datasets = data.datasets;
          this.securityEventsChart.update();
        } else if (this.securityEventsChartRef) {
          this.securityEventsChart = new Chart(this.securityEventsChartRef.nativeElement, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: data.datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: data.title,
                  font: {
                    size: 16
                  }
                },
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Počet událostí'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Datum'
                  }
                }
              }
            }
          });
        }
      },
      error: (err) => {
        console.error('Chyba při načítání dat pro graf bezpečnostních událostí', err);
        this.error = 'Nepodařilo se načíst data pro graf bezpečnostních událostí';
      }
    });
  }

  /**
   * Změna filtru dnů
   */
  onDaysChange(days: number): void {
    this.selectedDays = days;

    // Uložení vybraného počtu dní do local storage
    localStorage.setItem('monitoring_dateRange', days.toString());

    this.refreshCharts();
  }

  /**
   * Změna filtru instance
   */
  onInstanceChange(instanceId: number | null): void {
    this.selectedInstanceId = instanceId;
    this.refreshCharts();
  }



  /**
   * Získání textu pro typ alertu
   */
  getAlertTypeText(alertType: string): string {
    switch (alertType) {
      case 'SecurityBreach':
        return 'Bezpečnostní incident';
      case 'CertificateExpiration':
        return 'Expirace certifikátu';
      case 'FailedConnectionAttempts':
        return 'Selhané připojení';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      default:
        return alertType;
    }
  }

  /**
   * Přepnutí režimu úprav dashboardu
   */
  toggleEditMode(): void {
    this.editMode = !this.editMode;
  }

  /**
   * Zpracování přetažení widgetu
   */
  onDrop(event: CdkDragDrop<DashboardWidget[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    // Získáme všechny viditelné widgety v pořadí, jak jsou zobrazeny v DOM
    const visibleWidgets = Array.from(document.querySelectorAll('.col-lg-6[cdkDrag]'))
      .map(element => {
        // Získáme ID widgetu z atributu *ngIf
        const widget = this.widgets.find(w => {
          const widgetId = w.id;
          return element.innerHTML.includes(`isWidgetVisible('${widgetId}')`);
        });
        return widget;
      })
      .filter(widget => widget !== undefined) as DashboardWidget[];

    // Provedeme přesun v poli viditelných widgetů
    moveItemInArray(visibleWidgets, event.previousIndex, event.currentIndex);

    // Aktualizujeme pozice všech widgetů
    let position = 0;

    // Nejprve aktualizujeme pozice viditelných widgetů
    visibleWidgets.forEach(visibleWidget => {
      const widget = this.widgets.find(w => w.id === visibleWidget.id);
      if (widget) {
        widget.position = position++;
      }
    });

    // Poté aktualizujeme pozice skrytých widgetů
    this.widgets
      .filter(widget => !visibleWidgets.some(vw => vw.id === widget.id))
      .forEach(widget => {
        widget.position = position++;
      });

    // Seřadíme widgety podle pozice
    this.widgets.sort((a, b) => a.position - b.position);

    // Uložení konfigurace
    this.saveDashboardConfig();
  }

  /**
   * Změna viditelnosti widgetu
   */
  toggleWidgetVisibility(widgetId: string): void {
    const widget = this.widgets.find(w => w.id === widgetId);
    if (widget) {
      widget.visible = !widget.visible;
      this.saveDashboardConfig();
    }
  }

  /**
   * Kontrola viditelnosti widgetu
   */
  isWidgetVisible(widgetId: string): boolean {
    const widget = this.widgets.find(w => w.id === widgetId);
    return widget ? widget.visible : true;
  }

  /**
   * Uložení konfigurace dashboardu
   */
  saveDashboardConfig(): void {
    if (!this.dashboardConfig) {
      return;
    }

    this.dashboardConfig.widgets = [...this.widgets];
    this.dashboardConfig.lastModified = new Date();

    this.dashboardConfigService.saveDashboardConfig(this.dashboardConfig).subscribe({
      next: (config) => {
        console.log('Konfigurace dashboardu byla úspěšně uložena');
      },
      error: (error) => {
        console.error('Chyba při ukládání konfigurace dashboardu', error);
      }
    });
  }

  /**
   * Reset konfigurace dashboardu na výchozí hodnoty
   */
  resetDashboardConfig(): void {
    if (confirm('Opravdu chcete resetovat dashboard na výchozí nastavení?')) {
      this.dashboardConfigService.resetDashboardConfig().subscribe({
        next: (config) => {
          this.dashboardConfig = config;
          this.widgets = [...config.widgets].sort((a, b) => a.position - b.position);
          this.editMode = false;
          this.refreshCharts();
        },
        error: (error) => {
          console.error('Chyba při resetování konfigurace dashboardu', error);
        }
      });
    }
  }

  /**
   * Otevře modální okno s grafem v režimu "full screen"
   * @param chart Instance grafu nebo reference na canvas element
   * @param title Titulek grafu
   */
  openFullscreenChart(chart: Chart | HTMLCanvasElement | null, title: string): void {
    // Pokud je chart typu HTMLCanvasElement, najdeme odpovídající instanci Chart
    if (chart instanceof HTMLCanvasElement) {
      // Najdeme instanci Chart pro daný canvas element
      const chartInstance = Chart.getChart(chart);
      this.chartModalService.openChartModal(chartInstance || null, title);
    } else {
      this.chartModalService.openChartModal(chart, title);
    }
  }

  /**
   * Inicializace popoverů pro nápovědu
   */
  private initPopovers(): void {
    // Definice obsahu nápověd
    const helpContent: Record<string, string> = {
      'api-performance': 'Graf zobrazuje top 10 nejpomalejších metod seřazených podle 95. percentilu doby odezvy. ' +
        'Pro každou metodu jsou zobrazeny dva sloupce: průměrná doba odezvy (modrý sloupec) a maximální doba odezvy (červený sloupec). ' +
        'Data jsou získávána z tabulky PerformanceMetrics za zvolené období.',
      'api-calls': 'Graf zobrazuje počet API volání v čase. Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',
      'instances-usage': 'Graf zobrazuje využití jednotlivých instancí podle počtu API volání. ' +
        'Ukazuje, které instance jsou nejvíce zatížené a pomáhá identifikovat nerovnoměrné rozložení zátěže. ' +
        'Data jsou získávána z tabulky DiagnosticLogs za zvolené období.',
      'security-events': 'Graf zobrazuje počet bezpečnostních událostí v čase. ' +
        'Zahrnuje události jako neúspěšné pokusy o přihlášení, podezřelé aktivity a porušení bezpečnosti. ' +
        'Data jsou získávána z tabulky SecurityLogs za zvolené období.',
      'system-info': 'Tento widget zobrazuje souhrnné systémové informace včetně počtu aktivních upozornění, ' +
        'dostupnosti API, průměrné odezvy DIS metod a počtu expirujících certifikátů. ' +
        'Poskytuje rychlý přehled o celkovém stavu systému.'
    };

    // Nejprve zrušíme všechny existující popovery
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const popover = bootstrap.Popover.getInstance(el);
      if (popover) {
        popover.dispose();
      }
    });

    // Inicializace popoverů pomocí Bootstrap API
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const helpType = el.getAttribute('data-help-type');
      console.log('Initializing popover for element with help-type:', helpType);

      if (helpType && helpType in helpContent) {
        try {
          new bootstrap.Popover(el, {
            content: helpContent[helpType as keyof typeof helpContent],
            html: true,
            trigger: 'hover',
            placement: 'top',
            container: 'body'
          });
        } catch (error) {
          console.error('Error initializing popover:', error);
        }
      } else if (helpType) {
        console.warn('Help content not found for type:', helpType);
      }
    });
  }


}
