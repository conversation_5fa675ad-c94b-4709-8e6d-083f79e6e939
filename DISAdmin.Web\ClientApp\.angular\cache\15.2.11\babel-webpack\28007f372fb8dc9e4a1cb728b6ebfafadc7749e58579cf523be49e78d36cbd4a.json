{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/alert.service\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/signalr.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../services/customer.service\";\nimport * as i6 from \"../services/instance.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"../shared/advanced-filter/advanced-filter.component\";\nimport * as i11 from \"../shared/pipes/local-date.pipe\";\nfunction AlertsComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.addRule());\n    });\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"span\", 15);\n    i0.ɵɵtext(3, \"P\\u0159idat pravidlo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 16);\n    i0.ɵɵtext(5, \"P\\u0159idat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AlertsComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AlertsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"span\", 21);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AlertsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 alerty nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_14_tr_21_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_14_tr_21_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const alert_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.resolveAlert(alert_r11.id));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"severity-high\": a0,\n    \"severity-medium\": a1,\n    \"severity-low\": a2\n  };\n};\nfunction AlertsComponent_div_14_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtemplate(19, AlertsComponent_div_14_tr_21_button_19_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const alert_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 10, alert_r11.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(alert_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(13, _c0, alert_r11.severity === \"critical\", alert_r11.severity === \"warning\", alert_r11.severity === \"info\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getSeverityText(alert_r11.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getStatusClass(alert_r11.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getStatusText(alert_r11.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.instanceName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.customerName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", alert_r11.status === \"active\");\n  }\n}\nfunction AlertsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"table\", 24)(2, \"thead\", 25)(3, \"tr\", 26)(4, \"th\");\n    i0.ɵɵtext(5, \"Datum a \\u010Das\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Zpr\\u00E1va\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_14_tr_21_Template, 20, 17, \"tr\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.alerts);\n  }\n}\nfunction AlertsComponent_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E1 pravidla nebyla nalezena. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" Email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 33);\n    i0.ɵɵelement(5, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 28);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 36)(17, \"input\", 37);\n    i0.ɵɵlistener(\"change\", function AlertsComponent_div_15_div_6_tr_21_Template_input_change_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.toggleRuleStatus(rule_r19));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"label\", 38);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_15_div_6_tr_21_span_21_Template, 3, 0, \"span\", 39);\n    i0.ɵɵtemplate(22, AlertsComponent_div_15_div_6_tr_21_span_22_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\")(24, \"div\", 40)(25, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_15_div_6_tr_21_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.editRule(rule_r19));\n    });\n    i0.ɵɵelement(26, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_15_div_6_tr_21_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.deleteRule(rule_r19.id));\n    });\n    i0.ɵɵelement(28, \"i\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const rule_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(rule_r19.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r18.getMetricTypeText(rule_r19.metricType));\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getMetricTypeIcon(rule_r19.metricType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getMetricTypeText(rule_r19.metricType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.getConditionText(rule_r19.condition));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getThresholdClass(rule_r19.metricType, rule_r19.threshold));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatThreshold(rule_r19.metricType, rule_r19.threshold), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(13, _c0, rule_r19.severity === \"critical\", rule_r19.severity === \"warning\", rule_r19.severity === \"info\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getSeverityText(rule_r19.severity), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", rule_r19.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(rule_r19.enabled ? \"Aktivn\\u00ED\" : \"Neaktivn\\u00ED\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", rule_r19.notifyByEmail);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rule_r19.notifyByEmail);\n  }\n}\nfunction AlertsComponent_div_15_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"table\", 24)(2, \"thead\", 25)(3, \"tr\", 26)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Metrika\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Podm\\u00EDnka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Threshold\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Notifikace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_15_div_6_tr_21_Template, 29, 17, \"tr\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.alertRules);\n  }\n}\nfunction AlertsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"h5\", 7);\n    i0.ɵɵtext(3, \"Pravidla pro alerty\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 8);\n    i0.ɵɵtemplate(5, AlertsComponent_div_15_div_5_Template, 2, 0, \"div\", 10);\n    i0.ɵɵtemplate(6, AlertsComponent_div_15_div_6_Template, 22, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.alertRules.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.alertRules.length > 0);\n  }\n}\nexport class AlertsComponent {\n  constructor(alertService, authService, signalRService, fb, customerService, instanceService, router, toastr) {\n    this.alertService = alertService;\n    this.authService = authService;\n    this.signalRService = signalRService;\n    this.fb = fb;\n    this.customerService = customerService;\n    this.instanceService = instanceService;\n    this.router = router;\n    this.toastr = toastr;\n    this.alerts = [];\n    this.alertRules = [];\n    this.loading = true;\n    this.error = null;\n    this.isAdmin = false;\n    // Vybraný záznam\n    this.selectedRule = null;\n    this.filterFields = [];\n    // Aktualizace dat\n    this.signalRSubscriptions = [];\n    this.updateSubscription = null;\n    // Data pro filtry\n    this.customers = [];\n    this.instances = [];\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n    this.alertRuleForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      description: ['', Validators.maxLength(500)],\n      metricType: ['', Validators.required],\n      condition: ['', [Validators.required, Validators.maxLength(50)]],\n      threshold: ['', [Validators.required, Validators.min(0)]],\n      severity: ['warning', [Validators.required, Validators.maxLength(20)]],\n      enabled: [true],\n      notifyByEmail: [false],\n      emailRecipients: ['', Validators.maxLength(500)],\n      customerId: [''],\n      instanceId: ['']\n    });\n    // Výchozí hodnoty filtru podle požadavků - odpovídají obrázku\n    this.filterForm = this.fb.group({\n      severity: ['all'],\n      status: ['all'],\n      dateRange: ['7'],\n      customerId: [''],\n      instanceId: ['']\n    });\n  }\n  ngOnInit() {\n    // Inicializace polí pro advancedFilter\n    this.initFilterFields();\n    // Načtení zákazníků pro filtry\n    this.loadCustomers();\n    this.loadAlerts();\n    this.loadAlertRules();\n    // Inicializace SignalR připojení\n    this.initSignalR();\n    // Validace emailů při změně notifyByEmail\n    this.alertRuleForm.get('notifyByEmail')?.valueChanges.subscribe(value => {\n      const emailRecipientsControl = this.alertRuleForm.get('emailRecipients');\n      if (value) {\n        emailRecipientsControl?.setValidators([Validators.required, Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(,\\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})*$/), Validators.maxLength(500)]);\n      } else {\n        emailRecipientsControl?.setValidators([Validators.maxLength(500)]);\n      }\n      emailRecipientsControl?.updateValueAndValidity();\n    });\n    // Sledování změny zákazníka pro načtení instancí\n    this.filterForm.get('customerId')?.valueChanges.subscribe(customerId => {\n      console.log('Změna zákazníka v hlavním formuláři:', customerId);\n      if (customerId) {\n        // Převod na číslo, protože hodnota z formuláře může být řetězec\n        this.loadInstances(Number(customerId));\n      } else {\n        this.instances = [];\n        this.updateInstanceOptions();\n      }\n    });\n    // Sledování změny zákazníka ve formuláři pro pravidla\n    this.alertRuleForm.get('customerId')?.valueChanges.subscribe(customerId => {\n      if (customerId) {\n        this.instanceService.getInstancesByCustomerId(Number(customerId)).subscribe({\n          next: instances => {\n            // Aktualizace seznamu instancí\n            this.instances = instances;\n            // Reset výběru instance\n            const instanceControl = this.alertRuleForm.get('instanceId');\n            instanceControl?.setValue('');\n          },\n          error: err => {\n            console.error('Chyba při načítání instancí', err);\n          }\n        });\n      } else {\n        this.instances = [];\n      }\n    });\n  }\n  /**\r\n   * Načtení seznamu zákazníků\r\n   */\n  loadCustomers() {\n    this.customerService.getCustomers().subscribe({\n      next: data => {\n        console.log('Načtení zákazníků:', data);\n        this.customers = data;\n        this.updateCustomerOptions();\n        // Pokud máme uložené ID zákazníka, načteme jeho instance\n        const customerId = this.filterForm.get('customerId')?.value;\n        if (customerId) {\n          console.log('Načítání instancí pro uložené ID zákazníka:', customerId);\n          this.loadInstances(Number(customerId));\n        }\n      },\n      error: err => {\n        console.error('Chyba při načítání zákazníků', err);\n      }\n    });\n  }\n  /**\r\n   * Načtení seznamu instancí pro vybraného zákazníka\r\n   */\n  loadInstances(customerId) {\n    console.log('Načítání instancí pro zákazníka ID:', customerId);\n    this.instanceService.getInstancesByCustomerId(customerId).subscribe({\n      next: data => {\n        console.log('Načtené instance:', data);\n        this.instances = data;\n        this.updateInstanceOptions();\n      },\n      error: err => {\n        console.error('Chyba při načítání instancí', err);\n      }\n    });\n  }\n  /**\r\n   * Aktualizace možností pro filtr zákazníků\r\n   */\n  updateCustomerOptions() {\n    // Najdeme pole pro zákazníka\n    const customerFieldIndex = this.filterFields.findIndex(f => f.name === 'customerId');\n    if (customerFieldIndex !== -1) {\n      // Vytvoříme nové pole options\n      const options = [{\n        value: '',\n        label: 'Všichni zákazníci'\n      }];\n      // Přidáme zákazníky\n      this.customers.forEach(customer => {\n        options.push({\n          value: customer.id,\n          label: customer.name\n        });\n      });\n      // Aktualizujeme pole\n      this.filterFields[customerFieldIndex].options = options;\n      // Aktualizace seznamu zákazníků v modálním okně pro pravidla\n      const modalCustomerSelect = document.getElementById('customerId');\n      if (modalCustomerSelect) {\n        // Vyčištění stávajících možností kromě první (Všichni zákazníci)\n        while (modalCustomerSelect.options.length > 1) {\n          modalCustomerSelect.remove(1);\n        }\n        // Přidání nových možností\n        this.customers.forEach(customer => {\n          const option = document.createElement('option');\n          option.value = customer.id.toString();\n          option.text = customer.name;\n          modalCustomerSelect.add(option);\n        });\n      }\n    }\n  }\n  /**\r\n   * Aktualizace možností pro filtr instancí\r\n   */\n  updateInstanceOptions() {\n    // Najdeme pole pro instance\n    const instanceFieldIndex = this.filterFields.findIndex(f => f.name === 'instanceId');\n    if (instanceFieldIndex !== -1) {\n      // Vytvoříme nové pole options\n      const options = [{\n        value: '',\n        label: 'Všechny instance'\n      }];\n      // Přidáme instance\n      this.instances.sort((a, b) => {\n        // Nejprve seřadíme podle zkratky zákazníka\n        const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n        // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n        return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n      }).forEach(instance => {\n        options.push({\n          value: instance.id,\n          label: `${instance.customerAbbreviation} - ${instance.name}`\n        });\n      });\n      // Aktualizujeme pole\n      this.filterFields[instanceFieldIndex].options = options;\n    }\n  }\n  /**\r\n   * Inicializace polí pro advancedFilter\r\n   */\n  initFilterFields() {\n    this.filterFields = [{\n      name: 'severity',\n      label: 'Závažnost',\n      type: 'select',\n      options: [{\n        value: 'all',\n        label: 'Všechny'\n      }, {\n        value: 'critical',\n        label: 'Kritické'\n      }, {\n        value: 'warning',\n        label: 'Varování'\n      }, {\n        value: 'info',\n        label: 'Informace'\n      }]\n    }, {\n      name: 'status',\n      label: 'Stav',\n      type: 'select',\n      options: [{\n        value: 'all',\n        label: 'Všechny'\n      }, {\n        value: 'active',\n        label: 'Aktivní'\n      }, {\n        value: 'resolved',\n        label: 'Vyřešené'\n      }, {\n        value: 'acknowledged',\n        label: 'Potvrzené'\n      }]\n    }, {\n      name: 'dateRange',\n      label: 'Časové období',\n      type: 'select',\n      options: [{\n        value: '1',\n        label: 'Posledních 1 den'\n      }, {\n        value: '7',\n        label: 'Posledních 7 dní'\n      }, {\n        value: '30',\n        label: 'Posledních 30 dní'\n      }, {\n        value: '90',\n        label: 'Posledních 90 dní'\n      }]\n    }, {\n      name: 'customerId',\n      label: 'Zákazník',\n      type: 'select',\n      options: [{\n        value: '',\n        label: 'Všichni zákazníci'\n      }\n      // Zde budou dynamicky načteni zákazníci\n      ]\n    }, {\n      name: 'instanceId',\n      label: 'Instance',\n      type: 'select',\n      options: [{\n        value: '',\n        label: 'Všechny instance'\n      }\n      // Zde budou dynamicky načteny instance\n      ]\n    }];\n  }\n\n  ngOnDestroy() {\n    // Zrušení SignalR subscription\n    this.signalRSubscriptions.forEach(sub => sub.unsubscribe());\n    this.signalRService.stopConnection();\n    // Zrušení subscription pro aktualizaci dat\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n  }\n  /**\r\n   * Načtení alertů\r\n   */\n  loadAlerts() {\n    this.loading = true;\n    const filters = this.getFilters();\n    this.alertService.getAlerts(filters.severity, filters.status, parseInt(filters.dateRange), filters.customerId, filters.instanceId).subscribe({\n      next: data => {\n        this.alerts = data;\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání alertů', err);\n        this.error = 'Nepodařilo se načíst alerty';\n        this.loading = false;\n      }\n    });\n  }\n  /**\r\n   * Načtení pravidel pro alerty\r\n   */\n  loadAlertRules() {\n    this.alertService.getAlertRules().subscribe({\n      next: data => {\n        this.alertRules = data;\n      },\n      error: err => {\n        console.error('Chyba při načítání pravidel pro alerty', err);\n        this.error = 'Nepodařilo se načíst pravidla pro alerty';\n      }\n    });\n  }\n  /**\r\n   * Získání aktuálních filtrů\r\n   */\n  getFilters() {\n    const formValues = this.filterForm.value;\n    return {\n      severity: formValues.severity,\n      status: formValues.status,\n      dateRange: formValues.dateRange,\n      customerId: formValues.customerId || undefined,\n      instanceId: formValues.instanceId || undefined\n    };\n  }\n  /**\r\n   * Zpracování změny filtru z komponenty advancedFilter\r\n   */\n  onFilterChange(filters) {\n    // Nastavení výchozích hodnot, pokud nejsou definovány\n    const filterValues = {\n      severity: filters.severity || 'all',\n      status: filters.status || 'all',\n      dateRange: filters.dateRange || '7',\n      customerId: filters.customerId || '',\n      instanceId: filters.instanceId || ''\n    };\n    console.log('Změna filtru:', filterValues);\n    // Uložení aktuální hodnoty customerId\n    const oldCustomerId = this.filterForm.get('customerId')?.value;\n    // Aktualizace formuláře\n    this.filterForm.patchValue(filterValues, {\n      emitEvent: false\n    });\n    // Pokud se změnil zákazník, načteme instance\n    if (oldCustomerId !== filterValues.customerId && filterValues.customerId) {\n      console.log('Změna zákazníka v onFilterChange:', oldCustomerId, '->', filterValues.customerId);\n      this.loadInstances(Number(filterValues.customerId));\n    }\n    // Načtení dat s novými filtry\n    this.loadAlerts();\n  }\n  /**\r\n   * Přechod na stránku pro přidání nového pravidla\r\n   */\n  addRule() {\n    this.router.navigate(['/alerts/rules/add']);\n  }\n  /**\r\n   * Přechod na stránku pro úpravu pravidla\r\n   */\n  editRule(rule) {\n    this.router.navigate(['/alerts/rules', rule.id]);\n  }\n  /**\r\n   * Smazání pravidla\r\n   */\n  deleteRule(ruleId) {\n    if (confirm('Opravdu chcete smazat toto pravidlo?')) {\n      this.alertService.deleteAlertRule(ruleId).subscribe({\n        next: () => {\n          this.toastr.success('Pravidlo bylo úspěšně smazáno', 'Úspěch');\n          this.loadAlertRules();\n        },\n        error: err => {\n          console.error('Chyba při mazání pravidla', err);\n          this.error = 'Nepodařilo se smazat pravidlo';\n        }\n      });\n    }\n  }\n  /**\r\n   * Změna stavu pravidla (povoleno/zakázáno)\r\n   */\n  toggleRuleStatus(rule) {\n    const updatedRule = {\n      ...rule,\n      enabled: !rule.enabled\n    };\n    this.alertService.updateAlertRule(updatedRule).subscribe({\n      next: () => {\n        this.toastr.success(`Pravidlo bylo ${updatedRule.enabled ? 'aktivováno' : 'deaktivováno'}`, 'Úspěch');\n        this.loadAlertRules();\n      },\n      error: err => {\n        console.error('Chyba při změně stavu pravidla', err);\n        this.error = 'Nepodařilo se změnit stav pravidla';\n      }\n    });\n  }\n  /**\r\n   * Vyřešení alertu\r\n   */\n  resolveAlert(alertId) {\n    this.alertService.resolveAlert(alertId).subscribe({\n      next: () => {\n        this.toastr.success('Alert byl úspěšně vyřešen', 'Úspěch');\n        this.loadAlerts();\n      },\n      error: err => {\n        console.error('Chyba při řešení alertu', err);\n        this.error = 'Nepodařilo se vyřešit alert';\n      }\n    });\n  }\n  /**\r\n   * Inicializace SignalR připojení\r\n   */\n  initSignalR() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('Initializing SignalR connection...');\n        yield _this.signalRService.startConnection();\n        // Otestování připojení\n        const isConnected = yield _this.signalRService.testConnection();\n        if (!isConnected) {\n          console.warn('SignalR connection test failed. Falling back to interval-based updates.');\n          return;\n        }\n        console.log('SignalR connection test successful. Joining groups...');\n        // Připojení do skupin pro odebírání alertů\n        yield _this.signalRService.joinGroup('alerts');\n        // Registrace subscription pro real-time aktualizace\n        _this.signalRSubscriptions.push(_this.signalRService.alerts$.subscribe(data => {\n          if (Object.keys(data).length > 0) {\n            _this.loadAlerts();\n          }\n        }));\n        console.log('SignalR initialization completed successfully.');\n      } catch (error) {\n        console.error('Error during SignalR initialization:', error);\n        console.warn('Falling back to interval-based updates.');\n      }\n    })();\n  }\n  /**\r\n   * Získání CSS třídy pro závažnost alertu\r\n   * @deprecated Použijte místo toho přímo třídy severity-high, severity-medium, severity-low\r\n   */\n  getSeverityClass(severity) {\n    switch (severity) {\n      case 'critical':\n        return 'severity-high';\n      case 'warning':\n        return 'severity-medium';\n      case 'info':\n        return 'severity-low';\n      default:\n        return 'bg-secondary';\n    }\n  }\n  /**\r\n   * Získání textu pro závažnost alertu\r\n   */\n  getSeverityText(severity) {\n    switch (severity) {\n      case 'critical':\n        return 'Kritický';\n      case 'warning':\n        return 'Varování';\n      case 'info':\n        return 'Informace';\n      default:\n        return severity;\n    }\n  }\n  /**\r\n   * Získání CSS třídy pro stav alertu\r\n   */\n  getStatusClass(status) {\n    switch (status) {\n      case 'active':\n        return 'bg-danger';\n      case 'resolved':\n        return 'bg-success';\n      case 'acknowledged':\n        return 'bg-warning text-dark';\n      default:\n        return 'bg-secondary';\n    }\n  }\n  /**\r\n   * Získání textu pro stav alertu\r\n   */\n  getStatusText(status) {\n    switch (status) {\n      case 'active':\n        return 'Aktivní';\n      case 'resolved':\n        return 'Vyřešený';\n      case 'acknowledged':\n        return 'Potvrzený';\n      default:\n        return status;\n    }\n  }\n  /**\r\n   * Získání textu pro typ metriky\r\n   */\n  getMetricTypeText(metricType) {\n    switch (metricType) {\n      // Existující metriky\n      case 'apiResponseTime':\n        return 'Doba odezvy API';\n      case 'apiCallsCount':\n        return 'Počet API volání';\n      case 'errorRate':\n        return 'Míra chyb';\n      case 'certificateExpiration':\n        return 'Expirace certifikátu';\n      case 'failedConnectionAttempts':\n        return 'Neúspěšné pokusy o připojení';\n      case 'suspiciousActivities':\n        return 'Podezřelé aktivity';\n      case 'apiAccessNonWorkHours':\n        return 'Přístupy k API mimo pracovní dobu';\n      case 'unauthorizedIpAccess':\n        return 'Přístupy z nepovolených IP adres';\n      case 'apiAccessHighCount':\n        return 'Neobvykle vysoký počet přístupů k API';\n      case 'apiAccessLowCount':\n        return 'Neobvykle nízký počet přístupů k API';\n      case 'failedLogins':\n        return 'Neúspěšné pokusy o přihlášení';\n      case 'securityEvents':\n        return 'Bezpečnostní události';\n      // Metriky pro výkonnostní anomálie - absolutní hodnoty\n      case 'methodResponseTime95Percentile':\n        return '95. percentil doby odezvy metody';\n      case 'methodResponseTimeMax':\n        return 'Maximální doba odezvy metody';\n      case 'methodResponseTimeStdDev':\n        return 'Variabilita doby odezvy metody';\n      case 'methodCallCount':\n        return 'Počet volání metody';\n      // Metriky pro výkonnostní anomálie - relativní změny\n      case 'methodResponseTimeChange':\n        return 'Změna doby odezvy metody';\n      case 'methodCallCountChange':\n        return 'Změna počtu volání metody';\n      // Metriky pro výkonnostní anomálie - trendy\n      case 'methodResponseTimeTrend':\n        return 'Trend doby odezvy metody';\n      case 'methodCallCountTrend':\n        return 'Trend počtu volání metody';\n      // Metriky pro výkonnostní anomálie - korelace\n      case 'methodResponseTimeCallCountCorrelation':\n        return 'Korelace odezvy a počtu volání';\n      case 'methodResponseTimeMedianRatio':\n        return 'Poměr průměr/medián doby odezvy';\n      default:\n        return metricType;\n    }\n  }\n  /**\r\n   * Získání ikony pro typ metriky\r\n   */\n  getMetricTypeIcon(metricType) {\n    switch (metricType) {\n      // Existující metriky\n      case 'apiResponseTime':\n        return 'bi-speedometer2';\n      case 'apiCallsCount':\n        return 'bi-graph-up';\n      case 'errorRate':\n        return 'bi-exclamation-triangle';\n      case 'certificateExpiration':\n        return 'bi-shield-lock';\n      case 'failedConnectionAttempts':\n        return 'bi-x-circle';\n      case 'suspiciousActivities':\n        return 'bi-eye';\n      case 'apiAccessNonWorkHours':\n        return 'bi-clock';\n      case 'unauthorizedIpAccess':\n        return 'bi-shield-x';\n      case 'apiAccessHighCount':\n        return 'bi-arrow-up-circle';\n      case 'apiAccessLowCount':\n        return 'bi-arrow-down-circle';\n      case 'failedLogins':\n        return 'bi-person-x';\n      case 'securityEvents':\n        return 'bi-shield-exclamation';\n      // Metriky pro výkonnostní anomálie - absolutní hodnoty\n      case 'methodResponseTime95Percentile':\n        return 'bi-stopwatch';\n      case 'methodResponseTimeMax':\n        return 'bi-alarm';\n      case 'methodResponseTimeStdDev':\n        return 'bi-distribute-vertical';\n      case 'methodCallCount':\n        return 'bi-hash';\n      // Metriky pro výkonnostní anomálie - relativní změny\n      case 'methodResponseTimeChange':\n        return 'bi-arrow-left-right';\n      case 'methodCallCountChange':\n        return 'bi-arrow-repeat';\n      // Metriky pro výkonnostní anomálie - trendy\n      case 'methodResponseTimeTrend':\n        return 'bi-graph-up-arrow';\n      case 'methodCallCountTrend':\n        return 'bi-bar-chart-line';\n      // Metriky pro výkonnostní anomálie - korelace\n      case 'methodResponseTimeCallCountCorrelation':\n        return 'bi-link';\n      case 'methodResponseTimeMedianRatio':\n        return 'bi-percent';\n      default:\n        return 'bi-question-circle';\n    }\n  }\n  /**\r\n   * Formátování hodnoty threshold podle typu metriky\r\n   */\n  formatThreshold(metricType, threshold) {\n    switch (metricType) {\n      // Metriky s jednotkou ms\n      case 'apiResponseTime':\n      case 'methodResponseTime95Percentile':\n      case 'methodResponseTimeMax':\n      case 'methodResponseTimeStdDev':\n        return `${threshold} ms`;\n      // Metriky s jednotkou %\n      case 'errorRate':\n      case 'methodResponseTimeChange':\n      case 'methodCallCountChange':\n      case 'methodResponseTimeTrend':\n      case 'methodCallCountTrend':\n      case 'methodResponseTimeCallCountCorrelation':\n      case 'apiAccessNonWorkHours':\n        return `${threshold} %`;\n      // Metriky s jednotkou dnů\n      case 'certificateExpiration':\n        return `${threshold} dnů`;\n      // Metriky bez jednotky\n      case 'apiCallsCount':\n      case 'methodCallCount':\n      case 'methodResponseTimeMedianRatio':\n      case 'failedConnectionAttempts':\n      case 'suspiciousActivities':\n      case 'apiAccessHighCount':\n      case 'apiAccessLowCount':\n      case 'failedLogins':\n      case 'unauthorizedIpAccess':\n        return threshold.toString();\n      default:\n        return threshold.toString();\n    }\n  }\n  /**\r\n   * Získání CSS třídy pro hodnotu threshold podle typu metriky\r\n   */\n  getThresholdClass(metricType, threshold) {\n    let cssClass = 'threshold-value';\n    switch (metricType) {\n      case 'apiResponseTime':\n        if (threshold > 1000) cssClass += ' threshold-critical';else if (threshold > 500) cssClass += ' threshold-warning';else if (threshold > 200) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n        break;\n      case 'errorRate':\n        if (threshold > 10) cssClass += ' threshold-critical';else if (threshold > 5) cssClass += ' threshold-warning';else if (threshold > 1) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n        break;\n      case 'certificateExpiration':\n        if (threshold < 7) cssClass += ' threshold-critical';else if (threshold < 14) cssClass += ' threshold-warning';else if (threshold < 30) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n        break;\n      case 'apiCallsCount':\n        if (threshold > 10000) cssClass += ' threshold-critical';else if (threshold > 5000) cssClass += ' threshold-warning';else if (threshold > 1000) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n        break;\n      default:\n        cssClass += ' threshold-normal';\n    }\n    return cssClass;\n  }\n  /**\r\n   * Získání textu pro podmínku\r\n   */\n  getConditionText(condition) {\n    switch (condition) {\n      case 'greaterThan':\n        return 'Větší než';\n      case 'lessThan':\n        return 'Menší než';\n      case 'equals':\n        return 'Rovno';\n      case 'notEquals':\n        return 'Nerovno';\n      default:\n        return condition;\n    }\n  }\n  static {\n    this.ɵfac = function AlertsComponent_Factory(t) {\n      return new (t || AlertsComponent)(i0.ɵɵdirectiveInject(i1.AlertService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.SignalRService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.CustomerService), i0.ɵɵdirectiveInject(i6.InstanceService), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AlertsComponent,\n      selectors: [[\"app-alerts\"]],\n      decls: 16,\n      vars: 8,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"entityType\", \"fields\", \"filterChange\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"card-body\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"d-none\", \"d-md-inline\"], [1, \"d-inline\", \"d-md-none\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"d-flex\", \"justify-content-center\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"severity-badge\", 3, \"ngClass\"], [1, \"badge\", 2, \"border-radius\", \"0.375rem\", \"padding\", \"0.5em 0.75em\", 3, \"ngClass\"], [\"class\", \"btn btn-sm btn-success\", \"title\", \"Ozna\\u010Dit jako vy\\u0159e\\u0161en\\u00E9\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Ozna\\u010Dit jako vy\\u0159e\\u0161en\\u00E9\", 1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"click\"], [1, \"bi\", \"bi-check-circle\"], [1, \"metric-type-icon\"], [1, \"bi\", 3, \"ngClass\", \"title\"], [3, \"ngClass\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"form-check-label\"], [4, \"ngIf\"], [1, \"btn-group\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"bi\", \"bi-envelope-fill\", \"text-primary\"]],\n      template: function AlertsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Alerty a upozorn\\u011Bn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AlertsComponent_button_4_Template, 6, 0, \"button\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, AlertsComponent_div_5_Template, 3, 1, \"div\", 3);\n          i0.ɵɵelementStart(6, \"app-advanced-filter\", 4);\n          i0.ɵɵlistener(\"filterChange\", function AlertsComponent_Template_app_advanced_filter_filterChange_6_listener($event) {\n            return ctx.onFilterChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"h5\", 7);\n          i0.ɵɵtext(10, \"Alerty\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtemplate(12, AlertsComponent_div_12_Template, 4, 0, \"div\", 9);\n          i0.ɵɵtemplate(13, AlertsComponent_div_13_Template, 2, 0, \"div\", 10);\n          i0.ɵɵtemplate(14, AlertsComponent_div_14_Template, 22, 1, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(15, AlertsComponent_div_15_Template, 7, 2, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"entityType\", \"alerts\")(\"fields\", ctx.filterFields);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.alerts.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.alerts.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n        }\n      },\n      dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i10.AdvancedFilterComponent, i11.LocalDatePipe],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  border-radius: 0.5rem;\\n  border: 1px solid rgba(0, 0, 0, 0.125);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.03);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\\n}\\n\\n.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #86b7fe;\\n  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\"*\\\";\\n  color: red;\\n  margin-left: 4px;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.modal-backdrop[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .card-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWxlcnRzL2FsZXJ0cy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsbURBQW1EO0VBQ25ELHFCQUFxQjtFQUNyQixzQ0FBc0M7QUFDeEM7O0FBRUE7RUFDRSxxQ0FBcUM7RUFDckMsNkNBQTZDO0FBQy9DOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGtEQUFrRDtBQUNwRDs7QUFFQTtFQUNFLFlBQVk7RUFDWixVQUFVO0VBQ1YsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLFlBQVk7QUFDZDs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRTtJQUNFLGVBQWU7RUFDakI7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5jYXJkIHtcbiAgYm94LXNoYWRvdzogMCAwLjEyNXJlbSAwLjI1cmVtIHJnYmEoMCwgMCwgMCwgMC4wNzUpO1xuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgMC4xMjUpO1xufVxuXG4uY2FyZC1oZWFkZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMDMpO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjEyNSk7XG59XG5cbi5mb3JtLXNlbGVjdDpmb2N1cywgLmZvcm0tY29udHJvbDpmb2N1cyB7XG4gIGJvcmRlci1jb2xvcjogIzg2YjdmZTtcbiAgYm94LXNoYWRvdzogMCAwIDAgMC4yNXJlbSByZ2JhKDEzLCAxMTAsIDI1MywgMC4yNSk7XG59XG5cbi5yZXF1aXJlZC1maWVsZDo6YWZ0ZXIge1xuICBjb250ZW50OiBcIipcIjtcbiAgY29sb3I6IHJlZDtcbiAgbWFyZ2luLWxlZnQ6IDRweDtcbn1cblxuLmZvcm0tY2hlY2staW5wdXQ6Y2hlY2tlZCB7XG4gIGJhY2tncm91bmQtY29sb3I6ICMwZDZlZmQ7XG4gIGJvcmRlci1jb2xvcjogIzBkNmVmZDtcbn1cblxuLm1vZGFsLWJhY2tkcm9wIHtcbiAgb3BhY2l0eTogMC41O1xufVxuXG4vKiBSZXNwb256aXZuw4PCrSDDg8K6cHJhdnkgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuY2FyZC10aXRsZSB7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICE/DC,kCAAoE;IAApCA;MAAAA;MAAA;MAAA,OAASA,+BAAS;IAAA,EAAC;IACjDA,wBAAsC;IAAAA,gCAAiC;IAAAA,oCAAe;IAAAA,iBAAO;IAAAA,gCAAiC;IAAAA,2BAAM;IAAAA,iBAAO;;;;;;IAI/IA,+BAAuF;IACrFA,YACA;IAAAA,kCAAkF;IAA1CA;MAAAA;MAAA;MAAA,qCAAiB,IAAI;IAAA,EAAC;IAAoBA,iBAAS;;;;IAD3FA,eACA;IADAA,6CACA;;;;;IAgBEA,+BAA2D;IAEzBA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAAsE;IACpEA,8DACF;IAAAA,iBAAM;;;;;;IAsCIA,kCAAwI;IAA/DA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAsB;IAAA,EAAC;IACvGA,wBAAkC;IACpCA,iBAAS;;;;;;;;;;;;IAvBbA,0BAAiC;IAC3BA,YAAuD;;IAAAA,iBAAK;IAChEA,0BAAI;IAAAA,YAAgB;IAAAA,iBAAK;IACzBA,0BAAI;IAMAA,YACF;IAAAA,iBAAO;IAETA,0BAAI;IAEAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IAAAA,aAAmB;IAAAA,iBAAK;IAC5BA,2BAAI;IACFA,sFAES;IACXA,iBAAK;;;;;IAvBDA,eAAuD;IAAvDA,uFAAuD;IACvDA,eAAgB;IAAhBA,oCAAgB;IAEiBA,eAIjC;IAJiCA,yJAIjC;IACAA,eACF;IADEA,4EACF;IAG4EA,eAAwC;IAAxCA,kEAAwC;IAClHA,eACF;IADEA,wEACF;IAEEA,eAA+B;IAA/BA,mDAA+B;IAC/BA,eAA+B;IAA/BA,mDAA+B;IAC/BA,eAAmB;IAAnBA,uCAAmB;IAEZA,eAA+B;IAA/BA,oDAA+B;;;;;IApClDA,+BAAoE;IAIxDA,gCAAW;IAAAA,iBAAK;IACpBA,0BAAI;IAAAA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,mCAAS;IAAAA,iBAAK;IAClBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,yBAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,mCAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,4BAAM;IAAAA,iBAAK;IACfA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,0EAyBK;IACPA,iBAAQ;;;;IA1BgBA,gBAAS;IAATA,uCAAS;;;;;IAwCrCA,+BAA8D;IAC5DA,gEACF;IAAAA,iBAAM;;;;;IA+CIA,4BAAiC;IAC/BA,wBAAgD;IAACA,uBACnD;IAAAA,iBAAO;;;;;IACPA,4BAAkC;IAAAA,iBAAC;IAAAA,iBAAO;;;;;;IAjC9CA,0BAAoC;IAC9BA,YAAe;IAAAA,iBAAK;IACxBA,0BAAI;IAEAA,wBAAkH;IACpHA,iBAAO;IACPA,YACF;IAAAA,iBAAK;IACLA,0BAAI;IAAAA,YAAsC;IAAAA,iBAAK;IAC/CA,0BAAI;IAEAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAMAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAEyEA;MAAA;MAAA;MAAA;MAAA,OAAUA,iDAAsB;IAAA,EAAC;IAA1GA,iBAA2G;IAC3GA,kCAAgC;IAAAA,aAA4C;IAAAA,iBAAQ;IAGxFA,2BAAI;IACFA,wFAEO;IACPA,wFAA0C;IAC5CA,iBAAK;IACLA,2BAAI;IAE4CA;MAAA;MAAA;MAAA;MAAA,OAASA,yCAAc;IAAA,EAAC;IAClEA,yBAAiC;IACnCA,iBAAS;IACTA,mCAA2F;IAA7CA;MAAA;MAAA;MAAA;MAAA,OAASA,8CAAmB;IAAA,EAAC;IACzEA,yBAAgC;IAClCA,iBAAS;;;;;IAzCTA,eAAe;IAAfA,mCAAe;IAG8CA,eAAgD;IAAhDA,iFAAgD;IAA/FA,wEAA8C;IAE9DA,eACF;IADEA,+EACF;IACIA,eAAsC;IAAtCA,kEAAsC;IAElCA,eAA8D;IAA9DA,4FAA8D;IAClEA,eACF;IADEA,iGACF;IAGmCA,eAIjC;IAJiCA,sJAIjC;IACAA,eACF;IADEA,2EACF;IAIkDA,eAAwB;IAAxBA,0CAAwB;IACxCA,eAA4C;IAA5CA,0EAA4C;IAIvEA,eAAwB;IAAxBA,6CAAwB;IAGxBA,eAAyB;IAAzBA,8CAAyB;;;;;IAhD1CA,+BAA4D;IAIhDA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,uBAAO;IAAAA,iBAAK;IAChBA,0BAAI;IAAAA,6BAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,0BAAS;IAAAA,iBAAK;IAClBA,2BAAI;IAAAA,oCAAS;IAAAA,iBAAK;IAClBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,2BAAU;IAAAA,iBAAK;IACnBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,gFA6CK;IACPA,iBAAQ;;;;IA9CeA,gBAAa;IAAbA,4CAAa;;;;;IAxB5CA,8BAAuC;IAElBA,mCAAmB;IAAAA,iBAAK;IAE3CA,8BAAuB;IACrBA,wEAEM;IAENA,yEA+DM;IACRA,iBAAM;;;;IApEEA,eAA6B;IAA7BA,qDAA6B;IAI7BA,eAA2B;IAA3BA,mDAA2B;;;AD9EvC,OAAM,MAAOC,eAAe;EA0B1BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,cAA8B,EAC9BC,EAAe,EACfC,eAAgC,EAChCC,eAAgC,EAChCC,MAAc,EACdC,MAAqB;IAPrB,iBAAY,GAAZP,YAAY;IACZ,gBAAW,GAAXC,WAAW;IACX,mBAAc,GAAdC,cAAc;IACd,OAAE,GAAFC,EAAE;IACF,oBAAe,GAAfC,eAAe;IACf,oBAAe,GAAfC,eAAe;IACf,WAAM,GAANC,MAAM;IACN,WAAM,GAANC,MAAM;IAjChB,WAAM,GAAU,EAAE;IAClB,eAAU,GAAU,EAAE;IACtB,YAAO,GAAG,IAAI;IACd,UAAK,GAAkB,IAAI;IAE3B,YAAO,GAAG,KAAK;IAKf;IACA,iBAAY,GAAQ,IAAI;IAIxB,iBAAY,GAAkB,EAAE;IAEhC;IACQ,yBAAoB,GAAmB,EAAE;IACzC,uBAAkB,GAAwB,IAAI;IAEtD;IACA,cAAS,GAAU,EAAE;IACrB,cAAS,GAAU,EAAE;IAYnB,IAAI,CAACN,WAAW,CAACO,WAAW,CAACC,SAAS,CAACC,IAAI,IAAG;MAC5C,IAAI,CAACF,WAAW,GAAGE,IAAI;MACvB,IAAI,CAACC,OAAO,GAAGD,IAAI,EAAEC,OAAO,IAAI,KAAK;IACvC,CAAC,CAAC;IAEF,IAAI,CAACC,aAAa,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MACjCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DC,WAAW,EAAE,CAAC,EAAE,EAAErB,UAAU,CAACoB,SAAS,CAAC,GAAG,CAAC,CAAC;MAC5CE,UAAU,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACmB,QAAQ,CAAC;MACrCI,SAAS,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAChEI,SAAS,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC1B,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACtEO,OAAO,EAAE,CAAC,IAAI,CAAC;MACfC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,eAAe,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAACoB,SAAS,CAAC,GAAG,CAAC,CAAC;MAChDU,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;IAEF;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACzB,EAAE,CAACU,KAAK,CAAC;MAC9BS,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBO,MAAM,EAAE,CAAC,KAAK,CAAC;MACfC,SAAS,EAAE,CAAC,GAAG,CAAC;MAChBJ,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;EACJ;EAEAI,QAAQ;IACN;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,WAAW,EAAE;IAElB;IACA,IAAI,CAACxB,aAAa,CAACyB,GAAG,CAAC,eAAe,CAAC,EAAEC,YAAY,CAAC7B,SAAS,CAAC8B,KAAK,IAAG;MACtE,MAAMC,sBAAsB,GAAG,IAAI,CAAC5B,aAAa,CAACyB,GAAG,CAAC,iBAAiB,CAAC;MACxE,IAAIE,KAAK,EAAE;QACTC,sBAAsB,EAAEC,aAAa,CAAC,CACpC7C,UAAU,CAACmB,QAAQ,EACnBnB,UAAU,CAAC8C,OAAO,CAAC,uGAAuG,CAAC,EAC3H9C,UAAU,CAACoB,SAAS,CAAC,GAAG,CAAC,CAC1B,CAAC;OACH,MAAM;QACLwB,sBAAsB,EAAEC,aAAa,CAAC,CAAC7C,UAAU,CAACoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;;MAEpEwB,sBAAsB,EAAEG,sBAAsB,EAAE;IAClD,CAAC,CAAC;IAEF;IACA,IAAI,CAACf,UAAU,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEC,YAAY,CAAC7B,SAAS,CAACiB,UAAU,IAAG;MACrEkB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEnB,UAAU,CAAC;MAC/D,IAAIA,UAAU,EAAE;QACd;QACA,IAAI,CAACoB,aAAa,CAACC,MAAM,CAACrB,UAAU,CAAC,CAAC;OACvC,MAAM;QACL,IAAI,CAACsB,SAAS,GAAG,EAAE;QACnB,IAAI,CAACC,qBAAqB,EAAE;;IAEhC,CAAC,CAAC;IAEF;IACA,IAAI,CAACrC,aAAa,CAACyB,GAAG,CAAC,YAAY,CAAC,EAAEC,YAAY,CAAC7B,SAAS,CAACiB,UAAU,IAAG;MACxE,IAAIA,UAAU,EAAE;QACd,IAAI,CAACrB,eAAe,CAAC6C,wBAAwB,CAACH,MAAM,CAACrB,UAAU,CAAC,CAAC,CAACjB,SAAS,CAAC;UAC1E0C,IAAI,EAAGH,SAAS,IAAI;YAClB;YACA,IAAI,CAACA,SAAS,GAAGA,SAAS;YAE1B;YACA,MAAMI,eAAe,GAAG,IAAI,CAACxC,aAAa,CAACyB,GAAG,CAAC,YAAY,CAAC;YAC5De,eAAe,EAAEC,QAAQ,CAAC,EAAE,CAAC;UAC/B,CAAC;UACDC,KAAK,EAAGC,GAAG,IAAI;YACbX,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;UACnD;SACD,CAAC;OACH,MAAM;QACL,IAAI,CAACP,SAAS,GAAG,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEA;;;EAGAf,aAAa;IACX,IAAI,CAAC7B,eAAe,CAACoD,YAAY,EAAE,CAAC/C,SAAS,CAAC;MAC5C0C,IAAI,EAAGM,IAAI,IAAI;QACbb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEY,IAAI,CAAC;QACvC,IAAI,CAACC,SAAS,GAAGD,IAAI;QACrB,IAAI,CAACE,qBAAqB,EAAE;QAE5B;QACA,MAAMjC,UAAU,GAAG,IAAI,CAACE,UAAU,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEE,KAAK;QAC3D,IAAIb,UAAU,EAAE;UACdkB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEnB,UAAU,CAAC;UACtE,IAAI,CAACoB,aAAa,CAACC,MAAM,CAACrB,UAAU,CAAC,CAAC;;MAE1C,CAAC;MACD4B,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;MACpD;KACD,CAAC;EACJ;EAEA;;;EAGAT,aAAa,CAACpB,UAAkB;IAC9BkB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEnB,UAAU,CAAC;IAC9D,IAAI,CAACrB,eAAe,CAAC6C,wBAAwB,CAACxB,UAAU,CAAC,CAACjB,SAAS,CAAC;MAClE0C,IAAI,EAAGM,IAAI,IAAI;QACbb,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,IAAI,CAAC;QACtC,IAAI,CAACT,SAAS,GAAGS,IAAI;QACrB,IAAI,CAACR,qBAAqB,EAAE;MAC9B,CAAC;MACDK,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAEA;;;EAGAI,qBAAqB;IACnB;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACC,YAAY,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACjD,IAAI,KAAK,YAAY,CAAC;IACpF,IAAI8C,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7B;MACA,MAAMI,OAAO,GAAG,CACd;QAAEzB,KAAK,EAAE,EAAE;QAAE0B,KAAK,EAAE;MAAmB,CAAE,CAC1C;MAED;MACA,IAAI,CAACP,SAAS,CAACQ,OAAO,CAACC,QAAQ,IAAG;QAChCH,OAAO,CAACI,IAAI,CAAC;UAAE7B,KAAK,EAAE4B,QAAQ,CAACE,EAAE;UAAEJ,KAAK,EAAEE,QAAQ,CAACrD;QAAI,CAAE,CAAC;MAC5D,CAAC,CAAC;MAEF;MACA,IAAI,CAAC+C,YAAY,CAACD,kBAAkB,CAAC,CAACI,OAAO,GAAGA,OAAO;MAEvD;MACA,MAAMM,mBAAmB,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAsB;MACtF,IAAIF,mBAAmB,EAAE;QACvB;QACA,OAAOA,mBAAmB,CAACN,OAAO,CAACS,MAAM,GAAG,CAAC,EAAE;UAC7CH,mBAAmB,CAACI,MAAM,CAAC,CAAC,CAAC;;QAG/B;QACA,IAAI,CAAChB,SAAS,CAACQ,OAAO,CAACC,QAAQ,IAAG;UAChC,MAAMQ,MAAM,GAAGJ,QAAQ,CAACK,aAAa,CAAC,QAAQ,CAAC;UAC/CD,MAAM,CAACpC,KAAK,GAAG4B,QAAQ,CAACE,EAAE,CAACQ,QAAQ,EAAE;UACrCF,MAAM,CAACG,IAAI,GAAGX,QAAQ,CAACrD,IAAI;UAC3BwD,mBAAmB,CAACS,GAAG,CAACJ,MAAM,CAAC;QACjC,CAAC,CAAC;;;EAGR;EAEA;;;EAGA1B,qBAAqB;IACnB;IACA,MAAM+B,kBAAkB,GAAG,IAAI,CAACnB,YAAY,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACjD,IAAI,KAAK,YAAY,CAAC;IACpF,IAAIkE,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7B;MACA,MAAMhB,OAAO,GAAG,CACd;QAAEzB,KAAK,EAAE,EAAE;QAAE0B,KAAK,EAAE;MAAkB,CAAE,CACzC;MAED;MACA,IAAI,CAACjB,SAAS,CACXiC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACb;QACA,MAAMC,aAAa,GAAGF,CAAC,CAACG,oBAAoB,CAACC,aAAa,CAACH,CAAC,CAACE,oBAAoB,CAAC;QAClF;QACA,OAAOD,aAAa,KAAK,CAAC,GAAGA,aAAa,GAAGF,CAAC,CAACpE,IAAI,CAACwE,aAAa,CAACH,CAAC,CAACrE,IAAI,CAAC;MAC3E,CAAC,CAAC,CACDoD,OAAO,CAACqB,QAAQ,IAAG;QAClBvB,OAAO,CAACI,IAAI,CAAC;UAAE7B,KAAK,EAAEgD,QAAQ,CAAClB,EAAE;UAAEJ,KAAK,EAAE,GAAGsB,QAAQ,CAACF,oBAAoB,MAAME,QAAQ,CAACzE,IAAI;QAAE,CAAE,CAAC;MACpG,CAAC,CAAC;MAEJ;MACA,IAAI,CAAC+C,YAAY,CAACmB,kBAAkB,CAAC,CAAChB,OAAO,GAAGA,OAAO;;EAE3D;EAIA;;;EAGAhC,gBAAgB;IACd,IAAI,CAAC6B,YAAY,GAAG,CAClB;MACE/C,IAAI,EAAE,UAAU;MAChBmD,KAAK,EAAE,WAAW;MAClBuB,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,KAAK;QAAE0B,KAAK,EAAE;MAAS,CAAE,EAClC;QAAE1B,KAAK,EAAE,UAAU;QAAE0B,KAAK,EAAE;MAAU,CAAE,EACxC;QAAE1B,KAAK,EAAE,SAAS;QAAE0B,KAAK,EAAE;MAAU,CAAE,EACvC;QAAE1B,KAAK,EAAE,MAAM;QAAE0B,KAAK,EAAE;MAAW,CAAE;KAExC,EACD;MACEnD,IAAI,EAAE,QAAQ;MACdmD,KAAK,EAAE,MAAM;MACbuB,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,KAAK;QAAE0B,KAAK,EAAE;MAAS,CAAE,EAClC;QAAE1B,KAAK,EAAE,QAAQ;QAAE0B,KAAK,EAAE;MAAS,CAAE,EACrC;QAAE1B,KAAK,EAAE,UAAU;QAAE0B,KAAK,EAAE;MAAU,CAAE,EACxC;QAAE1B,KAAK,EAAE,cAAc;QAAE0B,KAAK,EAAE;MAAW,CAAE;KAEhD,EACD;MACEnD,IAAI,EAAE,WAAW;MACjBmD,KAAK,EAAE,eAAe;MACtBuB,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,GAAG;QAAE0B,KAAK,EAAE;MAAkB,CAAE,EACzC;QAAE1B,KAAK,EAAE,GAAG;QAAE0B,KAAK,EAAE;MAAkB,CAAE,EACzC;QAAE1B,KAAK,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAmB,CAAE,EAC3C;QAAE1B,KAAK,EAAE,IAAI;QAAE0B,KAAK,EAAE;MAAmB,CAAE;KAE9C,EACD;MACEnD,IAAI,EAAE,YAAY;MAClBmD,KAAK,EAAE,UAAU;MACjBuB,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,EAAE;QAAE0B,KAAK,EAAE;MAAmB;MACvC;MAAA;KAEH,EACD;MACEnD,IAAI,EAAE,YAAY;MAClBmD,KAAK,EAAE,UAAU;MACjBuB,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAE,CACP;QAAEzB,KAAK,EAAE,EAAE;QAAE0B,KAAK,EAAE;MAAkB;MACtC;MAAA;KAEH,CACF;EACH;;EAEAwB,WAAW;IACT;IACA,IAAI,CAACC,oBAAoB,CAACxB,OAAO,CAACyB,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IAC3D,IAAI,CAAC1F,cAAc,CAAC2F,cAAc,EAAE;IAEpC;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACF,WAAW,EAAE;;EAEzC;EAEA;;;EAGA1D,UAAU;IACR,IAAI,CAAC6D,OAAO,GAAG,IAAI;IACnB,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IAEjC,IAAI,CAACjG,YAAY,CAACkG,SAAS,CACzBF,OAAO,CAAC1E,QAAQ,EAChB0E,OAAO,CAACnE,MAAM,EACdsE,QAAQ,CAACH,OAAO,CAAClE,SAAS,CAAC,EAC3BkE,OAAO,CAACtE,UAAU,EAClBsE,OAAO,CAACrE,UAAU,CACnB,CAAClB,SAAS,CAAC;MACV0C,IAAI,EAAGM,IAAI,IAAI;QACb,IAAI,CAAC2C,MAAM,GAAG3C,IAAI;QAClB,IAAI,CAACsC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDzC,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEC,GAAG,CAAC;QAC/C,IAAI,CAACD,KAAK,GAAG,6BAA6B;QAC1C,IAAI,CAACyC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;;;EAGA5D,cAAc;IACZ,IAAI,CAACnC,YAAY,CAACqG,aAAa,EAAE,CAAC5F,SAAS,CAAC;MAC1C0C,IAAI,EAAGM,IAAI,IAAI;QACb,IAAI,CAAC6C,UAAU,GAAG7C,IAAI;MACxB,CAAC;MACDH,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,wCAAwC,EAAEC,GAAG,CAAC;QAC5D,IAAI,CAACD,KAAK,GAAG,0CAA0C;MACzD;KACD,CAAC;EACJ;EAEA;;;EAGA2C,UAAU;IACR,MAAMM,UAAU,GAAG,IAAI,CAAC3E,UAAU,CAACW,KAAK;IACxC,OAAO;MACLjB,QAAQ,EAAEiF,UAAU,CAACjF,QAAQ;MAC7BO,MAAM,EAAE0E,UAAU,CAAC1E,MAAM;MACzBC,SAAS,EAAEyE,UAAU,CAACzE,SAAS;MAC/BJ,UAAU,EAAE6E,UAAU,CAAC7E,UAAU,IAAI8E,SAAS;MAC9C7E,UAAU,EAAE4E,UAAU,CAAC5E,UAAU,IAAI6E;KACtC;EACH;EAEA;;;EAGAC,cAAc,CAACT,OAAY;IACzB;IACA,MAAMU,YAAY,GAAG;MACnBpF,QAAQ,EAAE0E,OAAO,CAAC1E,QAAQ,IAAI,KAAK;MACnCO,MAAM,EAAEmE,OAAO,CAACnE,MAAM,IAAI,KAAK;MAC/BC,SAAS,EAAEkE,OAAO,CAAClE,SAAS,IAAI,GAAG;MACnCJ,UAAU,EAAEsE,OAAO,CAACtE,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAEqE,OAAO,CAACrE,UAAU,IAAI;KACnC;IAEDiB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6D,YAAY,CAAC;IAE1C;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC/E,UAAU,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEE,KAAK;IAE9D;IACA,IAAI,CAACX,UAAU,CAACgF,UAAU,CAACF,YAAY,EAAE;MAAEG,SAAS,EAAE;IAAK,CAAE,CAAC;IAE9D;IACA,IAAIF,aAAa,KAAKD,YAAY,CAAChF,UAAU,IAAIgF,YAAY,CAAChF,UAAU,EAAE;MACxEkB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8D,aAAa,EAAE,IAAI,EAAED,YAAY,CAAChF,UAAU,CAAC;MAC9F,IAAI,CAACoB,aAAa,CAACC,MAAM,CAAC2D,YAAY,CAAChF,UAAU,CAAC,CAAC;;IAGrD;IACA,IAAI,CAACQ,UAAU,EAAE;EACnB;EAEA;;;EAGA4E,OAAO;IACL,IAAI,CAACxG,MAAM,CAACyG,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEA;;;EAGAC,QAAQ,CAACC,IAAS;IAChB,IAAI,CAAC3G,MAAM,CAACyG,QAAQ,CAAC,CAAC,eAAe,EAAEE,IAAI,CAAC5C,EAAE,CAAC,CAAC;EAClD;EAIA;;;EAGA6C,UAAU,CAACC,MAAc;IACvB,IAAIC,OAAO,CAAC,sCAAsC,CAAC,EAAE;MACnD,IAAI,CAACpH,YAAY,CAACqH,eAAe,CAACF,MAAM,CAAC,CAAC1G,SAAS,CAAC;QAClD0C,IAAI,EAAE,MAAK;UACT,IAAI,CAAC5C,MAAM,CAAC+G,OAAO,CAAC,+BAA+B,EAAE,QAAQ,CAAC;UAC9D,IAAI,CAACnF,cAAc,EAAE;QACvB,CAAC;QACDmB,KAAK,EAAGC,GAAG,IAAI;UACbX,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEC,GAAG,CAAC;UAC/C,IAAI,CAACD,KAAK,GAAG,+BAA+B;QAC9C;OACD,CAAC;;EAEN;EAEA;;;EAGAiE,gBAAgB,CAACN,IAAS;IACxB,MAAMO,WAAW,GAAG;MAAE,GAAGP,IAAI;MAAE1F,OAAO,EAAE,CAAC0F,IAAI,CAAC1F;IAAO,CAAE;IACvD,IAAI,CAACvB,YAAY,CAACyH,eAAe,CAACD,WAAW,CAAC,CAAC/G,SAAS,CAAC;MACvD0C,IAAI,EAAE,MAAK;QACT,IAAI,CAAC5C,MAAM,CAAC+G,OAAO,CAAC,iBAAiBE,WAAW,CAACjG,OAAO,GAAG,YAAY,GAAG,cAAc,EAAE,EAAE,QAAQ,CAAC;QACrG,IAAI,CAACY,cAAc,EAAE;MACvB,CAAC;MACDmB,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,gCAAgC,EAAEC,GAAG,CAAC;QACpD,IAAI,CAACD,KAAK,GAAG,oCAAoC;MACnD;KACD,CAAC;EACJ;EAEA;;;EAGAoE,YAAY,CAACC,OAAe;IAC1B,IAAI,CAAC3H,YAAY,CAAC0H,YAAY,CAACC,OAAO,CAAC,CAAClH,SAAS,CAAC;MAChD0C,IAAI,EAAE,MAAK;QACT,IAAI,CAAC5C,MAAM,CAAC+G,OAAO,CAAC,2BAA2B,EAAE,QAAQ,CAAC;QAC1D,IAAI,CAACpF,UAAU,EAAE;MACnB,CAAC;MACDoB,KAAK,EAAGC,GAAG,IAAI;QACbX,OAAO,CAACU,KAAK,CAAC,yBAAyB,EAAEC,GAAG,CAAC;QAC7C,IAAI,CAACD,KAAK,GAAG,6BAA6B;MAC5C;KACD,CAAC;EACJ;EAEA;;;EAGclB,WAAW;IAAA;IAAA;MACvB,IAAI;QACFQ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAM,KAAI,CAAC3C,cAAc,CAAC0H,eAAe,EAAE;QAE3C;QACA,MAAMC,WAAW,SAAS,KAAI,CAAC3H,cAAc,CAAC4H,cAAc,EAAE;QAC9D,IAAI,CAACD,WAAW,EAAE;UAChBjF,OAAO,CAACmF,IAAI,CAAC,yEAAyE,CAAC;UACvF;;QAGFnF,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QAEpE;QACA,MAAM,KAAI,CAAC3C,cAAc,CAAC8H,SAAS,CAAC,QAAQ,CAAC;QAE7C;QACA,KAAI,CAACtC,oBAAoB,CAACtB,IAAI,CAC5B,KAAI,CAAClE,cAAc,CAAC+H,OAAO,CAACxH,SAAS,CAACgD,IAAI,IAAG;UAC3C,IAAIyE,MAAM,CAACC,IAAI,CAAC1E,IAAI,CAAC,CAACgB,MAAM,GAAG,CAAC,EAAE;YAChC,KAAI,CAACvC,UAAU,EAAE;;QAErB,CAAC,CAAC,CACH;QAEDU,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;OAC9D,CAAC,OAAOS,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DV,OAAO,CAACmF,IAAI,CAAC,yCAAyC,CAAC;;IACxD;EACH;EAEA;;;;EAIAK,gBAAgB,CAAC9G,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,UAAU;QACb,OAAO,eAAe;MACxB,KAAK,SAAS;QACZ,OAAO,iBAAiB;MAC1B,KAAK,MAAM;QACT,OAAO,cAAc;MACvB;QACE,OAAO,cAAc;IAAC;EAE5B;EAEA;;;EAGA+G,eAAe,CAAC/G,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,SAAS;QACZ,OAAO,UAAU;MACnB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB;QACE,OAAOA,QAAQ;IAAC;EAEtB;EAEA;;;EAGAgH,cAAc,CAACzG,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,WAAW;MACpB,KAAK,UAAU;QACb,OAAO,YAAY;MACrB,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B;QACE,OAAO,cAAc;IAAC;EAE5B;EAEA;;;EAGA0G,aAAa,CAAC1G,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,cAAc;QACjB,OAAO,WAAW;MACpB;QACE,OAAOA,MAAM;IAAC;EAEpB;EAEA;;;EAGA2G,iBAAiB,CAACtH,UAAkB;IAClC,QAAQA,UAAU;MAChB;MACA,KAAK,iBAAiB;QACpB,OAAO,iBAAiB;MAC1B,KAAK,eAAe;QAClB,OAAO,kBAAkB;MAC3B,KAAK,WAAW;QACd,OAAO,WAAW;MACpB,KAAK,uBAAuB;QAC1B,OAAO,sBAAsB;MAC/B,KAAK,0BAA0B;QAC7B,OAAO,8BAA8B;MACvC,KAAK,sBAAsB;QACzB,OAAO,oBAAoB;MAC7B,KAAK,uBAAuB;QAC1B,OAAO,mCAAmC;MAC5C,KAAK,sBAAsB;QACzB,OAAO,kCAAkC;MAC3C,KAAK,oBAAoB;QACvB,OAAO,uCAAuC;MAChD,KAAK,mBAAmB;QACtB,OAAO,sCAAsC;MAC/C,KAAK,cAAc;QACjB,OAAO,+BAA+B;MACxC,KAAK,gBAAgB;QACnB,OAAO,uBAAuB;MAEhC;MACA,KAAK,gCAAgC;QACnC,OAAO,kCAAkC;MAC3C,KAAK,uBAAuB;QAC1B,OAAO,8BAA8B;MACvC,KAAK,0BAA0B;QAC7B,OAAO,gCAAgC;MACzC,KAAK,iBAAiB;QACpB,OAAO,qBAAqB;MAE9B;MACA,KAAK,0BAA0B;QAC7B,OAAO,0BAA0B;MACnC,KAAK,uBAAuB;QAC1B,OAAO,2BAA2B;MAEpC;MACA,KAAK,yBAAyB;QAC5B,OAAO,0BAA0B;MACnC,KAAK,sBAAsB;QACzB,OAAO,2BAA2B;MAEpC;MACA,KAAK,wCAAwC;QAC3C,OAAO,gCAAgC;MACzC,KAAK,+BAA+B;QAClC,OAAO,iCAAiC;MAE1C;QACE,OAAOA,UAAU;IAAC;EAExB;EAEA;;;EAGAuH,iBAAiB,CAACvH,UAAkB;IAClC,QAAQA,UAAU;MAChB;MACA,KAAK,iBAAiB;QACpB,OAAO,iBAAiB;MAC1B,KAAK,eAAe;QAClB,OAAO,aAAa;MACtB,KAAK,WAAW;QACd,OAAO,yBAAyB;MAClC,KAAK,uBAAuB;QAC1B,OAAO,gBAAgB;MACzB,KAAK,0BAA0B;QAC7B,OAAO,aAAa;MACtB,KAAK,sBAAsB;QACzB,OAAO,QAAQ;MACjB,KAAK,uBAAuB;QAC1B,OAAO,UAAU;MACnB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,mBAAmB;QACtB,OAAO,sBAAsB;MAC/B,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,gBAAgB;QACnB,OAAO,uBAAuB;MAEhC;MACA,KAAK,gCAAgC;QACnC,OAAO,cAAc;MACvB,KAAK,uBAAuB;QAC1B,OAAO,UAAU;MACnB,KAAK,0BAA0B;QAC7B,OAAO,wBAAwB;MACjC,KAAK,iBAAiB;QACpB,OAAO,SAAS;MAElB;MACA,KAAK,0BAA0B;QAC7B,OAAO,qBAAqB;MAC9B,KAAK,uBAAuB;QAC1B,OAAO,iBAAiB;MAE1B;MACA,KAAK,yBAAyB;QAC5B,OAAO,mBAAmB;MAC5B,KAAK,sBAAsB;QACzB,OAAO,mBAAmB;MAE5B;MACA,KAAK,wCAAwC;QAC3C,OAAO,SAAS;MAClB,KAAK,+BAA+B;QAClC,OAAO,YAAY;MAErB;QACE,OAAO,oBAAoB;IAAC;EAElC;EAEA;;;EAGAwH,eAAe,CAACxH,UAAkB,EAAEE,SAAiB;IACnD,QAAQF,UAAU;MAChB;MACA,KAAK,iBAAiB;MACtB,KAAK,gCAAgC;MACrC,KAAK,uBAAuB;MAC5B,KAAK,0BAA0B;QAC7B,OAAO,GAAGE,SAAS,KAAK;MAE1B;MACA,KAAK,WAAW;MAChB,KAAK,0BAA0B;MAC/B,KAAK,uBAAuB;MAC5B,KAAK,yBAAyB;MAC9B,KAAK,sBAAsB;MAC3B,KAAK,wCAAwC;MAC7C,KAAK,uBAAuB;QAC1B,OAAO,GAAGA,SAAS,IAAI;MAEzB;MACA,KAAK,uBAAuB;QAC1B,OAAO,GAAGA,SAAS,MAAM;MAE3B;MACA,KAAK,eAAe;MACpB,KAAK,iBAAiB;MACtB,KAAK,+BAA+B;MACpC,KAAK,0BAA0B;MAC/B,KAAK,sBAAsB;MAC3B,KAAK,oBAAoB;MACzB,KAAK,mBAAmB;MACxB,KAAK,cAAc;MACnB,KAAK,sBAAsB;QACzB,OAAOA,SAAS,CAACyD,QAAQ,EAAE;MAE7B;QACE,OAAOzD,SAAS,CAACyD,QAAQ,EAAE;IAAC;EAElC;EAEA;;;EAGA8D,iBAAiB,CAACzH,UAAkB,EAAEE,SAAiB;IACrD,IAAIwH,QAAQ,GAAG,iBAAiB;IAEhC,QAAQ1H,UAAU;MAChB,KAAK,iBAAiB;QACpB,IAAIE,SAAS,GAAG,IAAI,EAAEwH,QAAQ,IAAI,qBAAqB,CAAC,KACnD,IAAIxH,SAAS,GAAG,GAAG,EAAEwH,QAAQ,IAAI,oBAAoB,CAAC,KACtD,IAAIxH,SAAS,GAAG,GAAG,EAAEwH,QAAQ,IAAI,iBAAiB,CAAC,KACnDA,QAAQ,IAAI,mBAAmB;QACpC;MACF,KAAK,WAAW;QACd,IAAIxH,SAAS,GAAG,EAAE,EAAEwH,QAAQ,IAAI,qBAAqB,CAAC,KACjD,IAAIxH,SAAS,GAAG,CAAC,EAAEwH,QAAQ,IAAI,oBAAoB,CAAC,KACpD,IAAIxH,SAAS,GAAG,CAAC,EAAEwH,QAAQ,IAAI,iBAAiB,CAAC,KACjDA,QAAQ,IAAI,mBAAmB;QACpC;MACF,KAAK,uBAAuB;QAC1B,IAAIxH,SAAS,GAAG,CAAC,EAAEwH,QAAQ,IAAI,qBAAqB,CAAC,KAChD,IAAIxH,SAAS,GAAG,EAAE,EAAEwH,QAAQ,IAAI,oBAAoB,CAAC,KACrD,IAAIxH,SAAS,GAAG,EAAE,EAAEwH,QAAQ,IAAI,iBAAiB,CAAC,KAClDA,QAAQ,IAAI,mBAAmB;QACpC;MACF,KAAK,eAAe;QAClB,IAAIxH,SAAS,GAAG,KAAK,EAAEwH,QAAQ,IAAI,qBAAqB,CAAC,KACpD,IAAIxH,SAAS,GAAG,IAAI,EAAEwH,QAAQ,IAAI,oBAAoB,CAAC,KACvD,IAAIxH,SAAS,GAAG,IAAI,EAAEwH,QAAQ,IAAI,iBAAiB,CAAC,KACpDA,QAAQ,IAAI,mBAAmB;QACpC;MACF;QACEA,QAAQ,IAAI,mBAAmB;IAAC;IAGpC,OAAOA,QAAQ;EACjB;EAEA;;;EAGAC,gBAAgB,CAAC1H,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,aAAa;QAChB,OAAO,WAAW;MACpB,KAAK,UAAU;QACb,OAAO,WAAW;MACpB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB;QACE,OAAOA,SAAS;IAAC;EAEvB;;;uBAlxBWrB,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAgJ;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCjB5BrJ,8BAAuB;UAEfA,6CAAmB;UAAAA,iBAAK;UAC5BA,sEAES;UACXA,iBAAM;UAENA,gEAGM;UAGNA,8CAG0C;UAAxCA;YAAA,OAAgBsJ,0BAAsB;UAAA,EAAC;UACzCtJ,iBAAsB;UAGtBA,8BAAuB;UAEFA,uBAAM;UAAAA,iBAAK;UAE9BA,+BAAuB;UACrBA,kEAIM;UAENA,mEAEM;UAENA,oEA2CM;UACRA,iBAAM;UAMRA,mEA0EM;UACRA,iBAAM;;;UA9JmDA,eAAa;UAAbA,kCAAa;UAK9DA,eAAW;UAAXA,gCAAW;UAOfA,eAAuB;UAAvBA,qCAAuB;UAWfA,eAAa;UAAbA,kCAAa;UAMbA,eAAqC;UAArCA,8DAAqC;UAIrCA,eAAmC;UAAnCA,4DAAmC;UAkDrBA,eAAa;UAAbA,kCAAa", "names": ["Validators", "i0", "AlertsComponent", "constructor", "alertService", "authService", "signalRService", "fb", "customerService", "instanceService", "router", "toastr", "currentUser", "subscribe", "user", "isAdmin", "alertRuleForm", "group", "name", "required", "max<PERSON><PERSON><PERSON>", "description", "metricType", "condition", "threshold", "min", "severity", "enabled", "notifyByEmail", "emailRecipients", "customerId", "instanceId", "filterForm", "status", "date<PERSON><PERSON><PERSON>", "ngOnInit", "in<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "loadCustomers", "loadAlerts", "loadAlertRules", "initSignalR", "get", "valueChanges", "value", "emailRecipientsControl", "setValidators", "pattern", "updateValueAndValidity", "console", "log", "loadInstances", "Number", "instances", "updateInstanceOptions", "getInstancesByCustomerId", "next", "instanceControl", "setValue", "error", "err", "getCustomers", "data", "customers", "updateCustomerOptions", "customerFieldIndex", "filterFields", "findIndex", "f", "options", "label", "for<PERSON>ach", "customer", "push", "id", "modalCustomerSelect", "document", "getElementById", "length", "remove", "option", "createElement", "toString", "text", "add", "instanceFieldIndex", "sort", "a", "b", "abbrevCompare", "customerAbbreviation", "localeCompare", "instance", "type", "ngOnDestroy", "signalRSubscriptions", "sub", "unsubscribe", "stopConnection", "updateSubscription", "loading", "filters", "getFilters", "get<PERSON><PERSON><PERSON>", "parseInt", "alerts", "getAlertRules", "alertRules", "formValues", "undefined", "onFilterChange", "filterValues", "oldCustomerId", "patchValue", "emitEvent", "addRule", "navigate", "editRule", "rule", "deleteRule", "ruleId", "confirm", "deleteAlertRule", "success", "toggleRuleStatus", "updatedRule", "updateAlertRule", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "startConnection", "isConnected", "testConnection", "warn", "joinGroup", "alerts$", "Object", "keys", "getSeverityClass", "getSeverityText", "getStatusClass", "getStatusText", "getMetricTypeText", "getMetricTypeIcon", "formatThreshold", "getThresholdClass", "cssClass", "getConditionText", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\alerts\\alerts.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\alerts\\alerts.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AlertService } from '../services/alert.service';\nimport { AuthService } from '../services/auth.service';\nimport { SignalRService } from '../services/signalr.service';\nimport { Subscription, interval } from 'rxjs';\nimport { FilterField } from '../shared/advanced-filter/advanced-filter.component';\nimport { CustomerService } from '../services/customer.service';\nimport { InstanceService } from '../services/instance.service';\nimport { ToastrService } from 'ngx-toastr';\n\n@Component({\n  selector: 'app-alerts',\n  templateUrl: './alerts.component.html',\n  styleUrls: ['./alerts.component.css']\n})\nexport class AlertsComponent implements OnInit, OnDestroy {\n  alerts: any[] = [];\n  alertRules: any[] = [];\n  loading = true;\n  error: string | null = null;\n  currentUser: any;\n  isAdmin = false;\n\n  // Formuláře\n  alertRuleForm: FormGroup;\n\n  // Vybraný záznam\n  selectedRule: any = null;\n\n  // Filtry\n  filterForm: FormGroup;\n  filterFields: FilterField[] = [];\n\n  // Aktualizace dat\n  private signalRSubscriptions: Subscription[] = [];\n  private updateSubscription: Subscription | null = null;\n\n  // Data pro filtry\n  customers: any[] = [];\n  instances: any[] = [];\n\n  constructor(\n    private alertService: AlertService,\n    private authService: AuthService,\n    private signalRService: SignalRService,\n    private fb: FormBuilder,\n    private customerService: CustomerService,\n    private instanceService: InstanceService,\n    private router: Router,\n    private toastr: ToastrService\n  ) {\n    this.authService.currentUser.subscribe(user => {\n      this.currentUser = user;\n      this.isAdmin = user?.isAdmin || false;\n    });\n\n    this.alertRuleForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      description: ['', Validators.maxLength(500)],\n      metricType: ['', Validators.required],\n      condition: ['', [Validators.required, Validators.maxLength(50)]],\n      threshold: ['', [Validators.required, Validators.min(0)]],\n      severity: ['warning', [Validators.required, Validators.maxLength(20)]],\n      enabled: [true],\n      notifyByEmail: [false],\n      emailRecipients: ['', Validators.maxLength(500)],\n      customerId: [''],\n      instanceId: ['']\n    });\n\n    // Výchozí hodnoty filtru podle požadavků - odpovídají obrázku\n    this.filterForm = this.fb.group({\n      severity: ['all'],\n      status: ['all'],\n      dateRange: ['7'],\n      customerId: [''],\n      instanceId: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    // Inicializace polí pro advancedFilter\n    this.initFilterFields();\n\n    // Načtení zákazníků pro filtry\n    this.loadCustomers();\n\n    this.loadAlerts();\n    this.loadAlertRules();\n\n    // Inicializace SignalR připojení\n    this.initSignalR();\n\n    // Validace emailů při změně notifyByEmail\n    this.alertRuleForm.get('notifyByEmail')?.valueChanges.subscribe(value => {\n      const emailRecipientsControl = this.alertRuleForm.get('emailRecipients');\n      if (value) {\n        emailRecipientsControl?.setValidators([\n          Validators.required,\n          Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(,\\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})*$/),\n          Validators.maxLength(500)\n        ]);\n      } else {\n        emailRecipientsControl?.setValidators([Validators.maxLength(500)]);\n      }\n      emailRecipientsControl?.updateValueAndValidity();\n    });\n\n    // Sledování změny zákazníka pro načtení instancí\n    this.filterForm.get('customerId')?.valueChanges.subscribe(customerId => {\n      console.log('Změna zákazníka v hlavním formuláři:', customerId);\n      if (customerId) {\n        // Převod na číslo, protože hodnota z formuláře může být řetězec\n        this.loadInstances(Number(customerId));\n      } else {\n        this.instances = [];\n        this.updateInstanceOptions();\n      }\n    });\n\n    // Sledování změny zákazníka ve formuláři pro pravidla\n    this.alertRuleForm.get('customerId')?.valueChanges.subscribe(customerId => {\n      if (customerId) {\n        this.instanceService.getInstancesByCustomerId(Number(customerId)).subscribe({\n          next: (instances) => {\n            // Aktualizace seznamu instancí\n            this.instances = instances;\n\n            // Reset výběru instance\n            const instanceControl = this.alertRuleForm.get('instanceId');\n            instanceControl?.setValue('');\n          },\n          error: (err) => {\n            console.error('Chyba při načítání instancí', err);\n          }\n        });\n      } else {\n        this.instances = [];\n      }\n    });\n  }\n\n  /**\n   * Načtení seznamu zákazníků\n   */\n  loadCustomers(): void {\n    this.customerService.getCustomers().subscribe({\n      next: (data) => {\n        console.log('Načtení zákazníků:', data);\n        this.customers = data;\n        this.updateCustomerOptions();\n\n        // Pokud máme uložené ID zákazníka, načteme jeho instance\n        const customerId = this.filterForm.get('customerId')?.value;\n        if (customerId) {\n          console.log('Načítání instancí pro uložené ID zákazníka:', customerId);\n          this.loadInstances(Number(customerId));\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při načítání zákazníků', err);\n      }\n    });\n  }\n\n  /**\n   * Načtení seznamu instancí pro vybraného zákazníka\n   */\n  loadInstances(customerId: number): void {\n    console.log('Načítání instancí pro zákazníka ID:', customerId);\n    this.instanceService.getInstancesByCustomerId(customerId).subscribe({\n      next: (data) => {\n        console.log('Načtené instance:', data);\n        this.instances = data;\n        this.updateInstanceOptions();\n      },\n      error: (err) => {\n        console.error('Chyba při načítání instancí', err);\n      }\n    });\n  }\n\n  /**\n   * Aktualizace možností pro filtr zákazníků\n   */\n  updateCustomerOptions(): void {\n    // Najdeme pole pro zákazníka\n    const customerFieldIndex = this.filterFields.findIndex(f => f.name === 'customerId');\n    if (customerFieldIndex !== -1) {\n      // Vytvoříme nové pole options\n      const options = [\n        { value: '', label: 'Všichni zákazníci' }\n      ];\n\n      // Přidáme zákazníky\n      this.customers.forEach(customer => {\n        options.push({ value: customer.id, label: customer.name });\n      });\n\n      // Aktualizujeme pole\n      this.filterFields[customerFieldIndex].options = options;\n\n      // Aktualizace seznamu zákazníků v modálním okně pro pravidla\n      const modalCustomerSelect = document.getElementById('customerId') as HTMLSelectElement;\n      if (modalCustomerSelect) {\n        // Vyčištění stávajících možností kromě první (Všichni zákazníci)\n        while (modalCustomerSelect.options.length > 1) {\n          modalCustomerSelect.remove(1);\n        }\n\n        // Přidání nových možností\n        this.customers.forEach(customer => {\n          const option = document.createElement('option');\n          option.value = customer.id.toString();\n          option.text = customer.name;\n          modalCustomerSelect.add(option);\n        });\n      }\n    }\n  }\n\n  /**\n   * Aktualizace možností pro filtr instancí\n   */\n  updateInstanceOptions(): void {\n    // Najdeme pole pro instance\n    const instanceFieldIndex = this.filterFields.findIndex(f => f.name === 'instanceId');\n    if (instanceFieldIndex !== -1) {\n      // Vytvoříme nové pole options\n      const options = [\n        { value: '', label: 'Všechny instance' }\n      ];\n\n      // Přidáme instance\n      this.instances\n        .sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        })\n        .forEach(instance => {\n          options.push({ value: instance.id, label: `${instance.customerAbbreviation} - ${instance.name}` });\n        });\n\n      // Aktualizujeme pole\n      this.filterFields[instanceFieldIndex].options = options;\n    }\n  }\n\n\n\n  /**\n   * Inicializace polí pro advancedFilter\n   */\n  initFilterFields(): void {\n    this.filterFields = [\n      {\n        name: 'severity',\n        label: 'Závažnost',\n        type: 'select',\n        options: [\n          { value: 'all', label: 'Všechny' },\n          { value: 'critical', label: 'Kritické' },\n          { value: 'warning', label: 'Varování' },\n          { value: 'info', label: 'Informace' }\n        ]\n      },\n      {\n        name: 'status',\n        label: 'Stav',\n        type: 'select',\n        options: [\n          { value: 'all', label: 'Všechny' },\n          { value: 'active', label: 'Aktivní' },\n          { value: 'resolved', label: 'Vyřešené' },\n          { value: 'acknowledged', label: 'Potvrzené' }\n        ]\n      },\n      {\n        name: 'dateRange',\n        label: 'Časové období',\n        type: 'select',\n        options: [\n          { value: '1', label: 'Posledních 1 den' },\n          { value: '7', label: 'Posledních 7 dní' },\n          { value: '30', label: 'Posledních 30 dní' },\n          { value: '90', label: 'Posledních 90 dní' }\n        ]\n      },\n      {\n        name: 'customerId',\n        label: 'Zákazník',\n        type: 'select',\n        options: [\n          { value: '', label: 'Všichni zákazníci' }\n          // Zde budou dynamicky načteni zákazníci\n        ]\n      },\n      {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [\n          { value: '', label: 'Všechny instance' }\n          // Zde budou dynamicky načteny instance\n        ]\n      }\n    ];\n  }\n\n  ngOnDestroy(): void {\n    // Zrušení SignalR subscription\n    this.signalRSubscriptions.forEach(sub => sub.unsubscribe());\n    this.signalRService.stopConnection();\n\n    // Zrušení subscription pro aktualizaci dat\n    if (this.updateSubscription) {\n      this.updateSubscription.unsubscribe();\n    }\n  }\n\n  /**\n   * Načtení alertů\n   */\n  loadAlerts(): void {\n    this.loading = true;\n    const filters = this.getFilters();\n\n    this.alertService.getAlerts(\n      filters.severity,\n      filters.status,\n      parseInt(filters.dateRange),\n      filters.customerId,\n      filters.instanceId\n    ).subscribe({\n      next: (data) => {\n        this.alerts = data;\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání alertů', err);\n        this.error = 'Nepodařilo se načíst alerty';\n        this.loading = false;\n      }\n    });\n  }\n\n  /**\n   * Načtení pravidel pro alerty\n   */\n  loadAlertRules(): void {\n    this.alertService.getAlertRules().subscribe({\n      next: (data) => {\n        this.alertRules = data;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání pravidel pro alerty', err);\n        this.error = 'Nepodařilo se načíst pravidla pro alerty';\n      }\n    });\n  }\n\n  /**\n   * Získání aktuálních filtrů\n   */\n  getFilters(): any {\n    const formValues = this.filterForm.value;\n    return {\n      severity: formValues.severity,\n      status: formValues.status,\n      dateRange: formValues.dateRange,\n      customerId: formValues.customerId || undefined,\n      instanceId: formValues.instanceId || undefined\n    };\n  }\n\n  /**\n   * Zpracování změny filtru z komponenty advancedFilter\n   */\n  onFilterChange(filters: any): void {\n    // Nastavení výchozích hodnot, pokud nejsou definovány\n    const filterValues = {\n      severity: filters.severity || 'all',\n      status: filters.status || 'all',\n      dateRange: filters.dateRange || '7',\n      customerId: filters.customerId || '',\n      instanceId: filters.instanceId || ''\n    };\n\n    console.log('Změna filtru:', filterValues);\n\n    // Uložení aktuální hodnoty customerId\n    const oldCustomerId = this.filterForm.get('customerId')?.value;\n\n    // Aktualizace formuláře\n    this.filterForm.patchValue(filterValues, { emitEvent: false });\n\n    // Pokud se změnil zákazník, načteme instance\n    if (oldCustomerId !== filterValues.customerId && filterValues.customerId) {\n      console.log('Změna zákazníka v onFilterChange:', oldCustomerId, '->', filterValues.customerId);\n      this.loadInstances(Number(filterValues.customerId));\n    }\n\n    // Načtení dat s novými filtry\n    this.loadAlerts();\n  }\n\n  /**\n   * Přechod na stránku pro přidání nového pravidla\n   */\n  addRule(): void {\n    this.router.navigate(['/alerts/rules/add']);\n  }\n\n  /**\n   * Přechod na stránku pro úpravu pravidla\n   */\n  editRule(rule: any): void {\n    this.router.navigate(['/alerts/rules', rule.id]);\n  }\n\n\n\n  /**\n   * Smazání pravidla\n   */\n  deleteRule(ruleId: number): void {\n    if (confirm('Opravdu chcete smazat toto pravidlo?')) {\n      this.alertService.deleteAlertRule(ruleId).subscribe({\n        next: () => {\n          this.toastr.success('Pravidlo bylo úspěšně smazáno', 'Úspěch');\n          this.loadAlertRules();\n        },\n        error: (err) => {\n          console.error('Chyba při mazání pravidla', err);\n          this.error = 'Nepodařilo se smazat pravidlo';\n        }\n      });\n    }\n  }\n\n  /**\n   * Změna stavu pravidla (povoleno/zakázáno)\n   */\n  toggleRuleStatus(rule: any): void {\n    const updatedRule = { ...rule, enabled: !rule.enabled };\n    this.alertService.updateAlertRule(updatedRule).subscribe({\n      next: () => {\n        this.toastr.success(`Pravidlo bylo ${updatedRule.enabled ? 'aktivováno' : 'deaktivováno'}`, 'Úspěch');\n        this.loadAlertRules();\n      },\n      error: (err) => {\n        console.error('Chyba při změně stavu pravidla', err);\n        this.error = 'Nepodařilo se změnit stav pravidla';\n      }\n    });\n  }\n\n  /**\n   * Vyřešení alertu\n   */\n  resolveAlert(alertId: number): void {\n    this.alertService.resolveAlert(alertId).subscribe({\n      next: () => {\n        this.toastr.success('Alert byl úspěšně vyřešen', 'Úspěch');\n        this.loadAlerts();\n      },\n      error: (err) => {\n        console.error('Chyba při řešení alertu', err);\n        this.error = 'Nepodařilo se vyřešit alert';\n      }\n    });\n  }\n\n  /**\n   * Inicializace SignalR připojení\n   */\n  private async initSignalR(): Promise<void> {\n    try {\n      console.log('Initializing SignalR connection...');\n      await this.signalRService.startConnection();\n\n      // Otestování připojení\n      const isConnected = await this.signalRService.testConnection();\n      if (!isConnected) {\n        console.warn('SignalR connection test failed. Falling back to interval-based updates.');\n        return;\n      }\n\n      console.log('SignalR connection test successful. Joining groups...');\n\n      // Připojení do skupin pro odebírání alertů\n      await this.signalRService.joinGroup('alerts');\n\n      // Registrace subscription pro real-time aktualizace\n      this.signalRSubscriptions.push(\n        this.signalRService.alerts$.subscribe(data => {\n          if (Object.keys(data).length > 0) {\n            this.loadAlerts();\n          }\n        })\n      );\n\n      console.log('SignalR initialization completed successfully.');\n    } catch (error) {\n      console.error('Error during SignalR initialization:', error);\n      console.warn('Falling back to interval-based updates.');\n    }\n  }\n\n  /**\n   * Získání CSS třídy pro závažnost alertu\n   * @deprecated Použijte místo toho přímo třídy severity-high, severity-medium, severity-low\n   */\n  getSeverityClass(severity: string): string {\n    switch (severity) {\n      case 'critical':\n        return 'severity-high';\n      case 'warning':\n        return 'severity-medium';\n      case 'info':\n        return 'severity-low';\n      default:\n        return 'bg-secondary';\n    }\n  }\n\n  /**\n   * Získání textu pro závažnost alertu\n   */\n  getSeverityText(severity: string): string {\n    switch (severity) {\n      case 'critical':\n        return 'Kritický';\n      case 'warning':\n        return 'Varování';\n      case 'info':\n        return 'Informace';\n      default:\n        return severity;\n    }\n  }\n\n  /**\n   * Získání CSS třídy pro stav alertu\n   */\n  getStatusClass(status: string): string {\n    switch (status) {\n      case 'active':\n        return 'bg-danger';\n      case 'resolved':\n        return 'bg-success';\n      case 'acknowledged':\n        return 'bg-warning text-dark';\n      default:\n        return 'bg-secondary';\n    }\n  }\n\n  /**\n   * Získání textu pro stav alertu\n   */\n  getStatusText(status: string): string {\n    switch (status) {\n      case 'active':\n        return 'Aktivní';\n      case 'resolved':\n        return 'Vyřešený';\n      case 'acknowledged':\n        return 'Potvrzený';\n      default:\n        return status;\n    }\n  }\n\n  /**\n   * Získání textu pro typ metriky\n   */\n  getMetricTypeText(metricType: string): string {\n    switch (metricType) {\n      // Existující metriky\n      case 'apiResponseTime':\n        return 'Doba odezvy API';\n      case 'apiCallsCount':\n        return 'Počet API volání';\n      case 'errorRate':\n        return 'Míra chyb';\n      case 'certificateExpiration':\n        return 'Expirace certifikátu';\n      case 'failedConnectionAttempts':\n        return 'Neúspěšné pokusy o připojení';\n      case 'suspiciousActivities':\n        return 'Podezřelé aktivity';\n      case 'apiAccessNonWorkHours':\n        return 'Přístupy k API mimo pracovní dobu';\n      case 'unauthorizedIpAccess':\n        return 'Přístupy z nepovolených IP adres';\n      case 'apiAccessHighCount':\n        return 'Neobvykle vysoký počet přístupů k API';\n      case 'apiAccessLowCount':\n        return 'Neobvykle nízký počet přístupů k API';\n      case 'failedLogins':\n        return 'Neúspěšné pokusy o přihlášení';\n      case 'securityEvents':\n        return 'Bezpečnostní události';\n\n      // Metriky pro výkonnostní anomálie - absolutní hodnoty\n      case 'methodResponseTime95Percentile':\n        return '95. percentil doby odezvy metody';\n      case 'methodResponseTimeMax':\n        return 'Maximální doba odezvy metody';\n      case 'methodResponseTimeStdDev':\n        return 'Variabilita doby odezvy metody';\n      case 'methodCallCount':\n        return 'Počet volání metody';\n\n      // Metriky pro výkonnostní anomálie - relativní změny\n      case 'methodResponseTimeChange':\n        return 'Změna doby odezvy metody';\n      case 'methodCallCountChange':\n        return 'Změna počtu volání metody';\n\n      // Metriky pro výkonnostní anomálie - trendy\n      case 'methodResponseTimeTrend':\n        return 'Trend doby odezvy metody';\n      case 'methodCallCountTrend':\n        return 'Trend počtu volání metody';\n\n      // Metriky pro výkonnostní anomálie - korelace\n      case 'methodResponseTimeCallCountCorrelation':\n        return 'Korelace odezvy a počtu volání';\n      case 'methodResponseTimeMedianRatio':\n        return 'Poměr průměr/medián doby odezvy';\n\n      default:\n        return metricType;\n    }\n  }\n\n  /**\n   * Získání ikony pro typ metriky\n   */\n  getMetricTypeIcon(metricType: string): string {\n    switch (metricType) {\n      // Existující metriky\n      case 'apiResponseTime':\n        return 'bi-speedometer2';\n      case 'apiCallsCount':\n        return 'bi-graph-up';\n      case 'errorRate':\n        return 'bi-exclamation-triangle';\n      case 'certificateExpiration':\n        return 'bi-shield-lock';\n      case 'failedConnectionAttempts':\n        return 'bi-x-circle';\n      case 'suspiciousActivities':\n        return 'bi-eye';\n      case 'apiAccessNonWorkHours':\n        return 'bi-clock';\n      case 'unauthorizedIpAccess':\n        return 'bi-shield-x';\n      case 'apiAccessHighCount':\n        return 'bi-arrow-up-circle';\n      case 'apiAccessLowCount':\n        return 'bi-arrow-down-circle';\n      case 'failedLogins':\n        return 'bi-person-x';\n      case 'securityEvents':\n        return 'bi-shield-exclamation';\n\n      // Metriky pro výkonnostní anomálie - absolutní hodnoty\n      case 'methodResponseTime95Percentile':\n        return 'bi-stopwatch';\n      case 'methodResponseTimeMax':\n        return 'bi-alarm';\n      case 'methodResponseTimeStdDev':\n        return 'bi-distribute-vertical';\n      case 'methodCallCount':\n        return 'bi-hash';\n\n      // Metriky pro výkonnostní anomálie - relativní změny\n      case 'methodResponseTimeChange':\n        return 'bi-arrow-left-right';\n      case 'methodCallCountChange':\n        return 'bi-arrow-repeat';\n\n      // Metriky pro výkonnostní anomálie - trendy\n      case 'methodResponseTimeTrend':\n        return 'bi-graph-up-arrow';\n      case 'methodCallCountTrend':\n        return 'bi-bar-chart-line';\n\n      // Metriky pro výkonnostní anomálie - korelace\n      case 'methodResponseTimeCallCountCorrelation':\n        return 'bi-link';\n      case 'methodResponseTimeMedianRatio':\n        return 'bi-percent';\n\n      default:\n        return 'bi-question-circle';\n    }\n  }\n\n  /**\n   * Formátování hodnoty threshold podle typu metriky\n   */\n  formatThreshold(metricType: string, threshold: number): string {\n    switch (metricType) {\n      // Metriky s jednotkou ms\n      case 'apiResponseTime':\n      case 'methodResponseTime95Percentile':\n      case 'methodResponseTimeMax':\n      case 'methodResponseTimeStdDev':\n        return `${threshold} ms`;\n\n      // Metriky s jednotkou %\n      case 'errorRate':\n      case 'methodResponseTimeChange':\n      case 'methodCallCountChange':\n      case 'methodResponseTimeTrend':\n      case 'methodCallCountTrend':\n      case 'methodResponseTimeCallCountCorrelation':\n      case 'apiAccessNonWorkHours':\n        return `${threshold} %`;\n\n      // Metriky s jednotkou dnů\n      case 'certificateExpiration':\n        return `${threshold} dnů`;\n\n      // Metriky bez jednotky\n      case 'apiCallsCount':\n      case 'methodCallCount':\n      case 'methodResponseTimeMedianRatio':\n      case 'failedConnectionAttempts':\n      case 'suspiciousActivities':\n      case 'apiAccessHighCount':\n      case 'apiAccessLowCount':\n      case 'failedLogins':\n      case 'unauthorizedIpAccess':\n        return threshold.toString();\n\n      default:\n        return threshold.toString();\n    }\n  }\n\n  /**\n   * Získání CSS třídy pro hodnotu threshold podle typu metriky\n   */\n  getThresholdClass(metricType: string, threshold: number): string {\n    let cssClass = 'threshold-value';\n\n    switch (metricType) {\n      case 'apiResponseTime':\n        if (threshold > 1000) cssClass += ' threshold-critical';\n        else if (threshold > 500) cssClass += ' threshold-warning';\n        else if (threshold > 200) cssClass += ' threshold-info';\n        else cssClass += ' threshold-normal';\n        break;\n      case 'errorRate':\n        if (threshold > 10) cssClass += ' threshold-critical';\n        else if (threshold > 5) cssClass += ' threshold-warning';\n        else if (threshold > 1) cssClass += ' threshold-info';\n        else cssClass += ' threshold-normal';\n        break;\n      case 'certificateExpiration':\n        if (threshold < 7) cssClass += ' threshold-critical';\n        else if (threshold < 14) cssClass += ' threshold-warning';\n        else if (threshold < 30) cssClass += ' threshold-info';\n        else cssClass += ' threshold-normal';\n        break;\n      case 'apiCallsCount':\n        if (threshold > 10000) cssClass += ' threshold-critical';\n        else if (threshold > 5000) cssClass += ' threshold-warning';\n        else if (threshold > 1000) cssClass += ' threshold-info';\n        else cssClass += ' threshold-normal';\n        break;\n      default:\n        cssClass += ' threshold-normal';\n    }\n\n    return cssClass;\n  }\n\n  /**\n   * Získání textu pro podmínku\n   */\n  getConditionText(condition: string): string {\n    switch (condition) {\n      case 'greaterThan':\n        return 'Větší než';\n      case 'lessThan':\n        return 'Menší než';\n      case 'equals':\n        return 'Rovno';\n      case 'notEquals':\n        return 'Nerovno';\n      default:\n        return condition;\n    }\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Alerty a upozornění</h2>\n    <button class=\"btn btn-primary\" (click)=\"addRule()\" *ngIf=\"isAdmin\">\n      <i class=\"bi bi-plus-circle me-2\"></i><span class=\"d-none d-md-inline\">Přidat pravidlo</span><span class=\"d-inline d-md-none\">Přidat</span>\n    </button>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n    {{ error }}\n    <button type=\"button\" class=\"btn-close\" (click)=\"error = null\" aria-label=\"Close\"></button>\n  </div>\n\n  <!-- Pokročilý filtr -->\n  <app-advanced-filter\n    [entityType]=\"'alerts'\"\n    [fields]=\"filterFields\"\n    (filterChange)=\"onFilterChange($event)\">\n  </app-advanced-filter>\n\n  <!-- Seznam alertů -->\n  <div class=\"card mb-4\">\n    <div class=\"card-header d-flex justify-content-between align-items-center\">\n      <h5 class=\"mb-0\">Alerty</h5>\n    </div>\n    <div class=\"card-body\">\n      <div *ngIf=\"loading\" class=\"d-flex justify-content-center\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Načítání...</span>\n        </div>\n      </div>\n\n      <div *ngIf=\"!loading && alerts.length === 0\" class=\"alert alert-info\">\n        Žádné alerty nebyly nalezeny.\n      </div>\n\n      <div *ngIf=\"!loading && alerts.length > 0\" class=\"table-responsive\">\n        <table class=\"table table-striped table-hover\">\n          <thead class=\"dark-header table-header-override\">\n            <tr class=\"dark-header-row\">\n              <th>Datum a čas</th>\n              <th>Název</th>\n              <th>Závažnost</th>\n              <th>Stav</th>\n              <th>Instance</th>\n              <th>Zákazník</th>\n              <th>Zpráva</th>\n              <th>Akce</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let alert of alerts\">\n              <td>{{ alert.timestamp | localDate:'dd.MM.yyyy HH:mm:ss' }}</td>\n              <td>{{ alert.name }}</td>\n              <td>\n                <span class=\"badge severity-badge\" [ngClass]=\"{\n                  'severity-high': alert.severity === 'critical',\n                  'severity-medium': alert.severity === 'warning',\n                  'severity-low': alert.severity === 'info'\n                }\">\n                  {{ getSeverityText(alert.severity) }}\n                </span>\n              </td>\n              <td>\n                <span class=\"badge\" style=\"border-radius: 0.375rem; padding: 0.5em 0.75em;\" [ngClass]=\"getStatusClass(alert.status)\">\n                  {{ getStatusText(alert.status) }}\n                </span>\n              </td>\n              <td>{{ alert.instanceName || '-' }}</td>\n              <td>{{ alert.customerName || '-' }}</td>\n              <td>{{ alert.message }}</td>\n              <td>\n                <button *ngIf=\"alert.status === 'active'\" class=\"btn btn-sm btn-success\" (click)=\"resolveAlert(alert.id)\" title=\"Označit jako vyřešené\">\n                  <i class=\"bi bi-check-circle\"></i>\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n\n\n\n  <!-- Seznam pravidel pro alerty -->\n  <div class=\"card mb-4\" *ngIf=\"isAdmin\">\n    <div class=\"card-header d-flex justify-content-between align-items-center\">\n      <h5 class=\"mb-0\">Pravidla pro alerty</h5>\n    </div>\n    <div class=\"card-body\">\n      <div *ngIf=\"alertRules.length === 0\" class=\"alert alert-info\">\n        Žádná pravidla nebyla nalezena.\n      </div>\n\n      <div *ngIf=\"alertRules.length > 0\" class=\"table-responsive\">\n        <table class=\"table table-striped table-hover\">\n          <thead class=\"dark-header table-header-override\">\n            <tr class=\"dark-header-row\">\n              <th>Název</th>\n              <th>Metrika</th>\n              <th>Podmínka</th>\n              <th>Threshold</th>\n              <th>Závažnost</th>\n              <th>Stav</th>\n              <th>Notifikace</th>\n              <th>Akce</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let rule of alertRules\">\n              <td>{{ rule.name }}</td>\n              <td>\n                <span class=\"metric-type-icon\">\n                  <i class=\"bi\" [ngClass]=\"getMetricTypeIcon(rule.metricType)\" title=\"{{ getMetricTypeText(rule.metricType) }}\"></i>\n                </span>\n                {{ getMetricTypeText(rule.metricType) }}\n              </td>\n              <td>{{ getConditionText(rule.condition) }}</td>\n              <td>\n                <span [ngClass]=\"getThresholdClass(rule.metricType, rule.threshold)\">\n                  {{ formatThreshold(rule.metricType, rule.threshold) }}\n                </span>\n              </td>\n              <td>\n                <span class=\"badge severity-badge\" [ngClass]=\"{\n                  'severity-high': rule.severity === 'critical',\n                  'severity-medium': rule.severity === 'warning',\n                  'severity-low': rule.severity === 'info'\n                }\">\n                  {{ getSeverityText(rule.severity) }}\n                </span>\n              </td>\n              <td>\n                <div class=\"form-check form-switch\">\n                  <input class=\"form-check-input\" type=\"checkbox\" [checked]=\"rule.enabled\" (change)=\"toggleRuleStatus(rule)\">\n                  <label class=\"form-check-label\">{{ rule.enabled ? 'Aktivní' : 'Neaktivní' }}</label>\n                </div>\n              </td>\n              <td>\n                <span *ngIf=\"rule.notifyByEmail\">\n                  <i class=\"bi bi-envelope-fill text-primary\"></i> Email\n                </span>\n                <span *ngIf=\"!rule.notifyByEmail\">-</span>\n              </td>\n              <td>\n                <div class=\"btn-group\">\n                  <button class=\"btn btn-sm btn-outline-info\" (click)=\"editRule(rule)\" title=\"Upravit\">\n                    <i class=\"bi bi-pencil-fill\"></i>\n                  </button>\n                  <button class=\"btn btn-sm btn-outline-danger\" (click)=\"deleteRule(rule.id)\" title=\"Smazat\">\n                    <i class=\"bi bi-trash-fill\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}