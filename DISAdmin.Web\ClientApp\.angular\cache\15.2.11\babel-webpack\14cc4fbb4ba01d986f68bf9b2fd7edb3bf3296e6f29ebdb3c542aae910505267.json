{"ast": null, "code": "import * as __NgCli_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nexport function getBaseUrl() {\n  return document.getElementsByTagName('base')[0].href;\n}\nconst providers = [{\n  provide: 'BASE_URL',\n  useFactory: getBaseUrl,\n  deps: []\n}];\nif (environment.production) {\n  enableProdMode();\n}\n__NgCli_bootstrap_1.platformBrowser(providers).bootstrapModule(AppModule).then(() => {\n  // Inicializace Bootstrap dropdown po načtení aplikace\n  setTimeout(() => {\n    console.log('Initializing Bootstrap dropdowns...');\n    if (typeof window.bootstrap !== 'undefined') {\n      const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\n      console.log('Found dropdown elements:', dropdownElements.length);\n      dropdownElements.forEach(element => {\n        try {\n          if (!window.bootstrap.Dropdown.getInstance(element)) {\n            new window.bootstrap.Dropdown(element);\n            console.log('Initialized dropdown for element:', element);\n          }\n        } catch (error) {\n          console.warn('Error initializing dropdown:', error);\n        }\n      });\n    } else {\n      console.warn('Bootstrap is not available in main.ts');\n    }\n  }, 2000);\n}).catch(err => console.log(err));\n// Odstraněn MutationObserver, který může způsobovat problémy\n// const observer = new MutationObserver(() => {\n//   const overlays = document.querySelectorAll('.modal-backdrop');\n//   console.log('Počet overlayů:', overlays.length);\n//   overlays.forEach((overlay, index) => {\n//     console.log(`Overlay ${index + 1}:`, overlay.outerHTML);\n//   });\n// });\n// observer.observe(document.body, { childList: true, subtree: true });", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}