# Konfigurace Console Logging v DISAdmin.DISApi

## Přehled

DISAdmin.DISApi podporuje automatické přesměrování všech `Console.WriteLine` volání do souborů v produkčním prostředí. Tato funkce je užitečná pro Windows Service nasazení, kde standardní Console výstup není dostupný.

## Konfigurace

### appsettings.Production.json

```json
{
  "ConsoleLogging": {
    "Enabled": true,
    "LogDirectory": "Logs",
    "FileNamePrefix": "console",
    "IncludeTimestamp": true,
    "RedirectErrors": true
  }
}
```

### Parametry konfigurace

| Parametr | Typ | Výchozí hodnota | Popis |
|----------|-----|-----------------|-------|
| `Enabled` | bool | `true` | Zapne/vypne Console logging do souboru |
| `LogDirectory` | string | `"Logs"` | Adresář pro ukládání log souborů (relativní nebo absolutní cesta) |
| `FileNamePrefix` | string | `"console"` | Prefix názvu log souboru |
| `IncludeTimestamp` | bool | `true` | Přidá časové razítko do názvu souboru (YYYY-MM-DD) |
| `RedirectErrors` | bool | `true` | Přesměruje také `Console.Error` do souboru |

## Způsoby implementace

### 1. Přímé přesměrování Console (aktuální implementace)

**Výhody:**
- Zachytává všechny `Console.WriteLine` volání bez změn kódu
- Jednoduchá implementace
- Funguje s existujícím kódem

**Nevýhody:**
- Přesměruje celý Console výstup
- Méně flexibilní formátování

**Použití:**
```csharp
// Automaticky se aktivuje v produkčním prostředí
Console.WriteLine("Tato zpráva bude zapsána do souboru");
```

### 2. Serilog wrapper (alternativní řešení)

**Výhody:**
- Lepší formátování a strukturované logování
- Integrace s existujícím Serilog systémem
- Více možností konfigurace

**Nevýhody:**
- Složitější konfigurace
- Vyžaduje změny v kódu pro optimální využití

**Použití:**
```csharp
// V Program.cs
builder.Host.AddConsoleFileLogging();

// V kódu
ConsoleFileLogger.WriteLine("Strukturovaná zpráva");
```

### 3. Jednoduchý wrapper (nejjednodušší řešení)

**Výhody:**
- Zachovává standardní Console výstup i file logging
- Jednoduché na použití
- Časové razítko u každé zprávy

**Nevýhody:**
- Vyžaduje změny v kódu
- Duplikuje výstup (Console + soubor)

**Použití:**
```csharp
// Inicializace
ConsoleFileLogger.Initialize();

// Použití
ConsoleFileLogger.WriteLine("Zpráva s časovým razítkem");
```

## Struktura log souborů

### Název souboru
- S časovým razítkem: `console-2024-01-15.log`
- Bez časového razítka: `console.log`

### Formát záznamů

**Přímé přesměrování:**
```
=== Console logging started at 2024-01-15 10:30:45 ===
Environment: Production
Application: DISAdmin.DISApi
Log file: C:\path\to\Logs\console-2024-01-15.log
Configuration: Enabled=True, IncludeTimestamp=True, RedirectErrors=True
=== Console output will be written to this file ===

Konfigurace pro Windows Service...
Windows Service podpora aktivována
Prostředí: Production
Běží v IIS: False
```

**Serilog wrapper:**
```
2024-01-15 10:30:45.123 [INF] [Console] Konfigurace pro Windows Service...
2024-01-15 10:30:45.124 [INF] [Console] Windows Service podpora aktivována
2024-01-15 10:30:45.125 [INF] [Console] Prostředí: Production
```

**Jednoduchý wrapper:**
```
2024-01-15 10:30:45.123 Konfigurace pro Windows Service...
2024-01-15 10:30:45.124 Windows Service podpora aktivována
2024-01-15 10:30:45.125 Prostředí: Production
```

## Umístění log souborů

### Výchozí umístění
- **Development**: Console výstup zůstává na standardním výstupu
- **Production**: `{ApplicationDirectory}\Logs\console-{YYYY-MM-DD}.log`

### Vlastní umístění
```json
{
  "ConsoleLogging": {
    "LogDirectory": "C:\\CustomLogs\\DISApi"
  }
}
```

## Rotace a údržba

### Automatická rotace
- Nový soubor se vytváří každý den (pokud je `IncludeTimestamp: true`)
- Starší soubory se automaticky nemazají

### Ruční údržba
Doporučujeme vytvořit PowerShell skript pro čištění starých log souborů:

```powershell
# Smazání Console log souborů starších než 30 dní
$logPath = "C:\path\to\Logs"
$daysToKeep = 30
Get-ChildItem -Path $logPath -Name "console-*.log" | 
    Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-$daysToKeep) } | 
    Remove-Item -Force
```

## Troubleshooting

### Console logging nefunguje

1. **Zkontrolujte konfiguraci:**
   ```json
   "ConsoleLogging": {
     "Enabled": true
   }
   ```

2. **Zkontrolujte oprávnění:**
   - Aplikace musí mít oprávnění k zápisu do log adresáře
   - Pro Windows Service zkontrolujte oprávnění účtu služby

3. **Zkontrolujte log soubory:**
   - Podívejte se do `Logs` adresáře
   - Hledejte soubory `console-*.log`

### Chybové zprávy

**"Chyba při konfiguraci Console logging"**
- Zkontrolujte oprávnění k zápisu
- Ověřte, že cesta k log adresáři je platná

**"Console logging je vypnut v konfiguraci"**
- Nastavte `"Enabled": true` v konfiguraci

## Doporučení pro produkci

1. **Monitorování velikosti souborů:**
   - Console logy mohou být velmi rozsáhlé
   - Implementujte rotaci nebo omezení velikosti

2. **Bezpečnost:**
   - Console logy mohou obsahovat citlivé informace
   - Zajistěte odpovídající oprávnění k souborům

3. **Výkon:**
   - Časté `Console.WriteLine` volání mohou ovlivnit výkon
   - Zvažte použití asynchronního loggingu pro vysoké zatížení

4. **Integrace s monitoring systémy:**
   - Console logy lze integrovat s ELK Stack, Splunk nebo jinými systémy
   - Použijte strukturované logování pro lepší analýzu

## Příklady použití

### Windows Service nasazení
```powershell
# Instalace služby s Console loggingem
.\scripts\Install-DISApi-SimpleService.ps1 -ServiceName "DISAdmin.DISApi"

# Kontrola Console logů
Get-Content "C:\path\to\DISApi\Logs\console-*.log" -Tail 50
```

### IIS nasazení
Console logging funguje i v IIS, ale doporučujeme používat standardní ASP.NET Core logging.

### Debugging v produkci
```csharp
// Přidání debug informací do Console logů
Console.WriteLine($"Debug: Certifikát načten - {certificate.Subject}");
Console.WriteLine($"Debug: Konfigurace - SkipValidation={skipValidation}");
```
