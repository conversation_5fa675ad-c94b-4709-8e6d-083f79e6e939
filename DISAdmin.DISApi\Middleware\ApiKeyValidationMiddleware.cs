using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using System.Reflection;
using DISAdmin.DISApi.Attributes;

namespace DISAdmin.DISApi.Middleware;

public class ApiKeyValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiKeyValidationMiddleware> _logger;
    private readonly DISAdminDbContext _dbContext;
    private readonly SecurityEventFilterService _filterService;

    public ApiKeyValidationMiddleware(RequestDelegate next, ILogger<ApiKeyValidationMiddleware> logger, DISAdminDbContext dbContext, SecurityEventFilterService filterService)
    {
        _next = next;
        _logger = logger;
        _dbContext = dbContext;
        _filterService = filterService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Kontrol<PERSON>, zda endpoint má atribut AllowAnonymous
        if (HasAllowAnonymousAttribute(context))
        {
            _logger.LogInformation("Přeskočení validace API klíče pro endpoint s atributem AllowAnonymous");
            await _next(context);
            return;
        }

        var apiKey = GetApiKeyFromRequest(context);
        var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";

        if (string.IsNullOrEmpty(apiKey))
        {
            _logger.LogWarning("Požadavek bez API klíče");
            await LogSecurityEvent(context, "MISSING_API_KEY", ipAddress);
            context.Response.StatusCode = 401; // Unauthorized
            await context.Response.WriteAsync("API klíč je povinný");
            return;
        }

        var instance = await ValidateApiKey(apiKey);
        if (instance == null)
        {
            _logger.LogWarning("Neplatný API klíč: {ApiKey}", apiKey);
            await LogSecurityEvent(context, "INVALID_API_KEY", ipAddress, apiKey);
            context.Response.StatusCode = 401; // Unauthorized
            await context.Response.WriteAsync("Neplatný API klíč");
            return;
        }

        // Kontrola, zda instance není blokována
        if (instance.Status == InstanceStatus.Blocked)
        {
            _logger.LogWarning("Blokovaná instance se pokouší o přístup: {InstanceId}, {InstanceName}", instance.Id, instance.Name);
            await LogSecurityEvent(context, "BLOCKED_INSTANCE", ipAddress, apiKey, instance.Id);
            context.Response.StatusCode = 403; // Forbidden
            await context.Response.WriteAsync($"Instance je blokována. Důvod: {instance.BlockReason}");
            return;
        }

        // Aktualizace informací o instanci
        await UpdateInstanceConnectionInfo(instance, ipAddress);

        // Přidání informací o instanci do kontextu
        context.Items["InstanceId"] = instance.Id;
        context.Items["InstanceName"] = instance.Name;
        context.Items["ApiKey"] = apiKey;

        await _next(context);
    }

    private static string? GetApiKeyFromRequest(HttpContext context)
    {
        // Nejprve zkusíme získat API klíč z hlavičky
        if (context.Request.Headers.TryGetValue("X-API-Key", out var headerApiKey))
        {
            return headerApiKey;
        }

        // Pokud není v hlavičce, zkusíme query parametr
        if (context.Request.Query.TryGetValue("apiKey", out var queryApiKey))
        {
            return queryApiKey;
        }

        return null;
    }

    private async Task<DISInstance?> ValidateApiKey(string apiKey)
    {
        try
        {
            var instance = await _dbContext.DISInstances
                .FirstOrDefaultAsync(i => i.ApiKey == apiKey);

            return instance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při validaci API klíče");
            return null;
        }
    }

    private async Task UpdateInstanceConnectionInfo(DISInstance instance, string ipAddress)
    {
        try
        {
            instance.LastConnectionDate = DateTime.UtcNow;
            instance.LastKnownIpAddress = ipAddress;

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při aktualizaci informací o připojení instance");
        }
    }

    private static bool HasAllowAnonymousAttribute(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        if (endpoint == null)
            return false;

        // Kontrola atributu AllowAnonymous na endpointu
        if (endpoint.Metadata.GetMetadata<IAllowAnonymous>() != null)
            return true;

        // Kontrola atributu SkipApiKeyValidation na endpointu
        if (endpoint.Metadata.GetMetadata<SkipApiKeyValidationAttribute>() != null)
            return true;

        // Kontrola atributu AllowAnonymous nebo SkipApiKeyValidation na kontroleru nebo akci
        if (endpoint.Metadata.GetMetadata<ControllerActionDescriptor>() is ControllerActionDescriptor controllerActionDescriptor)
        {
            // Kontrola atributu na akci
            if (controllerActionDescriptor.MethodInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null ||
                controllerActionDescriptor.MethodInfo.GetCustomAttribute<SkipApiKeyValidationAttribute>() != null)
                return true;

            // Kontrola atributu na kontroleru
            if (controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null ||
                controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<SkipApiKeyValidationAttribute>() != null)
                return true;
        }

        return false;
    }

    private async Task LogSecurityEvent(HttpContext context, string eventType, string ipAddress, string? apiKey = null, int? instanceId = null)
    {
        try
        {
            var description = $"{eventType}: {context.Request.Method} {context.Request.Path}";

            // Kontrola, zda událost odpovídá některému z filtrů
            var shouldFilter = await _filterService.ShouldFilterEventAsync(
                SecurityEventType.ApiKeyMisuse, description, ipAddress);

            if (shouldFilter)
            {
                _logger.LogDebug("Bezpečnostní událost byla filtrována: {EventType}, IP: {IpAddress}", eventType, ipAddress);
                return;
            }

            var securityEvent = new SecurityEvent
            {
                Timestamp = DateTime.UtcNow,
                EventType = SecurityEventType.ApiKeyMisuse,
                IpAddress = ipAddress,
                Description = description,
                Username = apiKey ?? "unknown",
                Severity = 2, // Střední závažnost
                IsResolved = false
            };

            _dbContext.SecurityEvents.Add(securityEvent);
            await _dbContext.SaveChangesAsync();

            // Počítadlo neúspěšných pokusů bylo odstraněno
            // Místo toho pouze logujeme bezpečnostní incident

            _logger.LogWarning("Bezpečnostní incident: {EventType}, IP: {IpAddress}", eventType, ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při logování bezpečnostního incidentu");
        }
    }
}
