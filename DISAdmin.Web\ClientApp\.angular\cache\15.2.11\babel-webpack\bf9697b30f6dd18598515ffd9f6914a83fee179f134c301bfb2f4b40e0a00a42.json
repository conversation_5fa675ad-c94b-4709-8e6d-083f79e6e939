{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { environment } from '../../environments/environment';\nimport { InstanceStatus } from '../models/instance.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/customer.service\";\nimport * as i3 from \"../services/contact.service\";\nimport * as i4 from \"../services/instance.service\";\nimport * as i5 from \"../services/instance-version.service\";\nimport * as i6 from \"../services/version.service\";\nimport * as i7 from \"../services/user.service\";\nimport * as i8 from \"../services/auth.service\";\nimport * as i9 from \"../services/certificate.service\";\nimport * as i10 from \"../services/modal.service\";\nimport * as i11 from \"@angular/common/http\";\nimport * as i12 from \"../services/monitoring.service\";\nimport * as i13 from \"@angular/router\";\nimport * as i14 from \"ngx-toastr\";\nimport * as i15 from \"@angular/common\";\nimport * as i16 from \"../shared/advanced-filter/advanced-filter.component\";\nconst _c0 = function () {\n  return [\"/instance-wizard\"];\n};\nfunction CustomersComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 136);\n    i0.ɵɵelement(1, \"i\", 137);\n    i0.ɵɵelementStart(2, \"span\", 138);\n    i0.ɵɵtext(3, \"Pr\\u016Fvodce vytvo\\u0159en\\u00EDm instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 139);\n    i0.ɵɵtext(5, \"Pr\\u016Fvodce\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nconst _c1 = function () {\n  return [\"/customers/add\"];\n};\nfunction CustomersComponent_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 140);\n    i0.ɵɵelement(1, \"i\", 141);\n    i0.ɵɵelementStart(2, \"span\", 138);\n    i0.ɵɵtext(3, \"P\\u0159idat z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 139);\n    i0.ɵɵtext(5, \"P\\u0159idat\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction CustomersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nfunction CustomersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00ED z\\u00E1kazn\\u00EDci nebyli nalezeni. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const customer_r42 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.editCustomer(customer_r42));\n    });\n    i0.ɵɵelement(1, \"i\", 166);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const customer_r42 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.deleteCustomer(customer_r42));\n    });\n    i0.ɵɵelement(1, \"i\", 168);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_13_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 156);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 156);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 156);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 157);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 158);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 158);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 158);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 159);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"div\", 160)(22, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_tr_22_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const customer_r42 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.viewCustomerDetail(customer_r42));\n    });\n    i0.ɵɵelement(23, \"i\", 162);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, CustomersComponent_div_13_tr_22_button_24_Template, 2, 0, \"button\", 163);\n    i0.ɵɵtemplate(25, CustomersComponent_div_13_tr_22_button_25_Template, 2, 0, \"button\", 164);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const customer_r42 = ctx.$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(customer_r42.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Zkratka: \", customer_r42.abbreviation, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"I\\u010C: \", customer_r42.companyId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Kontakty: \", customer_r42.contactsCount || 0, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Instance: \", customer_r42.instancesCount || 0, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.abbreviation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.companyId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.contactsCount || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customer_r42.instancesCount || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isAdmin);\n  }\n}\nfunction CustomersComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\", 151);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_4_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onSort(\"name\"));\n    });\n    i0.ɵɵtext(5, \" N\\u00E1zev \");\n    i0.ɵɵelement(6, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_7_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onSort(\"abbreviation\"));\n    });\n    i0.ɵɵtext(8, \" Zkratka \");\n    i0.ɵɵelement(9, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_10_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.onSort(\"companyId\"));\n    });\n    i0.ɵɵtext(11, \" I\\u010C \");\n    i0.ɵɵelement(12, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 153);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_13_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onSort(\"contactsCount\"));\n    });\n    i0.ɵɵtext(14, \" Kontakty \");\n    i0.ɵɵelement(15, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 154);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_13_Template_th_click_16_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onSort(\"instancesCount\"));\n    });\n    i0.ɵɵtext(17, \" Instance \");\n    i0.ɵɵelement(18, \"i\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"tbody\");\n    i0.ɵɵtemplate(22, CustomersComponent_div_13_tr_22_Template, 26, 11, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"name\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"name\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"name\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"abbreviation\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"abbreviation\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"abbreviation\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"companyId\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"companyId\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"companyId\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"contactsCount\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"contactsCount\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"contactsCount\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"bi-sort-up\", ctx_r5.sortColumn === \"instancesCount\" && ctx_r5.sortDirection === \"asc\")(\"bi-sort-down\", ctx_r5.sortColumn === \"instancesCount\" && ctx_r5.sortDirection === \"desc\")(\"bi-sort\", ctx_r5.sortColumn !== \"instancesCount\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.paginatedCustomers);\n  }\n}\nfunction CustomersComponent_div_14_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 173)(1, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_li_11_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const page_r60 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.onPageChange(page_r60));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r60 = ctx.$implicit;\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r60 === ctx_r59.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r60);\n  }\n}\nfunction CustomersComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 169)(1, \"div\", 170);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nav\", 171)(4, \"ul\", 172)(5, \"li\", 173)(6, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onPageChange(1));\n    });\n    i0.ɵɵelement(7, \"i\", 175);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"li\", 173)(9, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.onPageChange(ctx_r65.currentPage - 1));\n    });\n    i0.ɵɵelement(10, \"i\", 176);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, CustomersComponent_div_14_li_11_Template, 3, 3, \"li\", 177);\n    i0.ɵɵelementStart(12, \"li\", 173)(13, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onPageChange(ctx_r66.currentPage + 1));\n    });\n    i0.ɵɵelement(14, \"i\", 178);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\", 173)(16, \"a\", 174);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_14_Template_a_click_16_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.onPageChange(ctx_r67.totalPages));\n    });\n    i0.ɵɵelement(17, \"i\", 179);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" Zobrazeno \", (ctx_r6.currentPage - 1) * ctx_r6.pageSize + 1, \" - \", ctx_r6.Math.min(ctx_r6.currentPage * ctx_r6.pageSize, ctx_r6.filteredCustomers.length), \" z \", ctx_r6.filteredCustomers.length, \" z\\u00E1znam\\u016F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.pageRange);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r6.currentPage === ctx_r6.totalPages);\n  }\n}\nfunction CustomersComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.error, \" \");\n  }\n}\nfunction CustomersComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" N\\u00E1zev je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Zkratka je povinn\\u00E1 a nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 50 znak\\u016F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_45_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtemplate(1, CustomersComponent_div_45_span_1_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.customerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n  }\n}\nfunction CustomersComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Ulice je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" M\\u011Bsto je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" PS\\u010C je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Zem\\u011B je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_81_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 kontaktn\\u00ED osoby nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 197);\n    i0.ɵɵtext(1, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r73 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r73.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r73.email);\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_a_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r73 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r73.phone, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r73.phone);\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_81_div_9_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersComponent_div_81_div_9_tr_15_span_4_Template, 2, 0, \"span\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtemplate(8, CustomersComponent_div_81_div_9_tr_15_a_8_Template, 2, 2, \"a\", 194);\n    i0.ɵɵtemplate(9, CustomersComponent_div_81_div_9_tr_15_span_9_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_81_div_9_tr_15_a_11_Template, 2, 2, \"a\", 194);\n    i0.ɵɵtemplate(12, CustomersComponent_div_81_div_9_tr_15_span_12_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"div\", 160)(15, \"button\", 195);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_div_9_tr_15_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const contact_r73 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r81.editContact(contact_r73));\n    });\n    i0.ɵɵelement(16, \"i\", 166);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_div_9_tr_15_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const contact_r73 = restoredCtx.$implicit;\n      const ctx_r83 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r83.deleteContact(contact_r73));\n    });\n    i0.ɵɵelement(18, \"i\", 168);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const contact_r73 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fw-bold\", contact_r73.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", contact_r73.firstName, \" \", contact_r73.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.isPrimary);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r73.position || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r73.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r73.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r73.phone);\n  }\n}\nfunction CustomersComponent_div_81_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomersComponent_div_81_div_9_tr_15_Template, 19, 10, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r71.contacts);\n  }\n}\nfunction CustomersComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"div\", 183)(2, \"h5\", 184);\n    i0.ɵɵtext(3, \"Kontaktn\\u00ED osoby\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_81_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.openAddContactModal());\n    });\n    i0.ɵɵelement(5, \"i\", 186);\n    i0.ɵɵtext(6, \"P\\u0159idat kontakt \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CustomersComponent_div_81_div_7_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(8, CustomersComponent_div_81_div_8_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(9, CustomersComponent_div_81_div_9_Template, 16, 1, \"div\", 189);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.loadingContacts && ctx_r15.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.loadingContacts && ctx_r15.contacts.length > 0);\n  }\n}\nfunction CustomersComponent_div_82_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_82_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 instance DIS nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_82_div_9_tr_21_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 205)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomersComponent_div_82_div_9_tr_21_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 206);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_9_tr_21_span_12_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r95);\n      const instance_r90 = i0.ɵɵnextContext().$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r93.openAddInstanceVersionModal(instance_r90));\n    });\n    i0.ɵɵelement(3, \"i\", 207);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const instance_r90 = i0.ɵɵnextContext().$implicit;\n    const ctx_r92 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r92.getLatestVersion(instance_r90.id), \" \");\n  }\n}\nconst _c2 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"bg-success\": a0,\n    \"bg-danger\": a1,\n    \"bg-warning\": a2,\n    \"bg-secondary\": a3,\n    \"bg-dark\": a4\n  };\n};\nconst _c3 = function (a1) {\n  return [\"/instance-metrics\", a1];\n};\nfunction CustomersComponent_div_82_div_9_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 200);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_9_tr_21_Template_a_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r97 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r97.viewInstanceDetail(instance_r90));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"td\");\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 201);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_82_div_9_tr_21_span_11_Template, 3, 0, \"span\", 202);\n    i0.ɵɵtemplate(12, CustomersComponent_div_82_div_9_tr_21_span_12_Template, 4, 1, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\")(20, \"div\", 160)(21, \"button\", 195);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_9_tr_21_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r99 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r99.editInstance(instance_r90));\n    });\n    i0.ɵɵelement(22, \"i\", 166);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 203);\n    i0.ɵɵelement(24, \"i\", 204);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_div_9_tr_21_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const instance_r90 = restoredCtx.$implicit;\n      const ctx_r100 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r100.deleteInstance(instance_r90));\n    });\n    i0.ɵɵelement(26, \"i\", 168);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const instance_r90 = ctx.$implicit;\n    const ctx_r89 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r90.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r90.serverUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(15, _c2, instance_r90.status === ctx_r89.InstanceStatus.Active, instance_r90.status === ctx_r89.InstanceStatus.Blocked, instance_r90.status === ctx_r89.InstanceStatus.Trial, instance_r90.status === ctx_r89.InstanceStatus.Maintenance, instance_r90.status === ctx_r89.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r89.getInstanceStatusText(instance_r90.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r89.loadingInstanceVersions[instance_r90.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r89.loadingInstanceVersions[instance_r90.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, instance_r90.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r90.expirationDate ? i0.ɵɵpipeBind2(18, 12, instance_r90.expirationDate, \"dd.MM.yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(21, _c3, instance_r90.id));\n  }\n}\nfunction CustomersComponent_div_82_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Datab\\u00E1ze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Datum expirace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, CustomersComponent_div_82_div_9_tr_21_Template, 27, 23, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r88.instances);\n  }\n}\nfunction CustomersComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"div\", 183)(2, \"h5\", 184);\n    i0.ɵɵtext(3, \"Instance DIS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_82_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.openAddInstanceModal());\n    });\n    i0.ɵɵelement(5, \"i\", 199);\n    i0.ɵɵtext(6, \"P\\u0159idat instanci \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CustomersComponent_div_82_div_7_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(8, CustomersComponent_div_82_div_8_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(9, CustomersComponent_div_82_div_9_Template, 22, 1, \"div\", 189);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.loadingInstances && ctx_r16.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.loadingInstances && ctx_r16.instances.length > 0);\n  }\n}\nfunction CustomersComponent_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_98_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"I\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r103 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r103.selectedCustomer.companyId, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"DI\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r104 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r104.selectedCustomer.taxId, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r105.selectedCustomer.email, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Telefon:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r106 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r106.selectedCustomer.phone, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Web:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r107 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r107.selectedCustomer.website, \"\");\n  }\n}\nfunction CustomersComponent_div_98_p_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Pozn\\u00E1mka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r108 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r108.selectedCustomer.notes, \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 217)(2, \"div\", 218)(3, \"div\", 219)(4, \"h3\", 220);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 184);\n    i0.ɵɵtext(7, \"Celkem instanc\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 217)(9, \"div\", 221)(10, \"div\", 219)(11, \"h3\", 220);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 184);\n    i0.ɵɵtext(14, \"Aktivn\\u00EDch instanc\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 217)(16, \"div\", 222)(17, \"div\", 219)(18, \"h3\", 220);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 184);\n    i0.ɵɵtext(21, \"API vol\\u00E1n\\u00ED (24h)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 217)(23, \"div\", 223)(24, \"div\", 219)(25, \"h3\", 220);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 184);\n    i0.ɵɵtext(28, \"Expiruj\\u00EDc\\u00ED certifik\\u00E1ty\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 23)(30, \"div\", 5)(31, \"div\", 6)(32, \"h5\", 224);\n    i0.ɵɵtext(33, \"V\\u00FDkon API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\", 184)(35, \"strong\");\n    i0.ɵɵtext(36, \"Pr\\u016Fm\\u011Brn\\u00E1 doba odezvy:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 184)(40, \"strong\");\n    i0.ɵɵtext(41, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti (24h):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 23)(44, \"div\", 5)(45, \"div\", 6)(46, \"h5\", 224);\n    i0.ɵɵtext(47, \"Nejpou\\u017E\\u00EDvan\\u011Bj\\u0161\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\", 184)(49, \"strong\");\n    i0.ɵɵtext(50, \"Verze:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"p\", 184)(53, \"strong\");\n    i0.ɵɵtext(54, \"Po\\u010Det instanc\\u00ED:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r110 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.instancesCount);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.activeInstancesCount);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.apiCallsLast24h);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r110.customerStatistics.expiringCertificatesCount);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(38, 8, ctx_r110.customerStatistics.avgApiResponseTime, \"1.2-2\"), \" ms\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.securityEventsCount, \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.mostUsedVersion || \"Nen\\u00ED k dispozici\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r110.customerStatistics.mostUsedVersionCount || 0, \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r111 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r111.statisticsError, \" \");\n  }\n}\nfunction CustomersComponent_div_98_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 kontaktn\\u00ED osoby nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 197);\n    i0.ɵɵtext(1, \"Prim\\u00E1rn\\u00ED\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r119 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r119.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r119.email);\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 198);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r119 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r119.phone, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r119.phone);\n  }\n}\nfunction CustomersComponent_div_98_div_45_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomersComponent_div_98_div_45_tr_13_span_4_Template, 2, 0, \"span\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtemplate(8, CustomersComponent_div_98_div_45_tr_13_a_8_Template, 2, 2, \"a\", 194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, CustomersComponent_div_98_div_45_tr_13_a_10_Template, 2, 2, \"a\", 194);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const contact_r119 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fw-bold\", contact_r119.isPrimary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", contact_r119.firstName, \" \", contact_r119.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.isPrimary);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(contact_r119.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", contact_r119.phone);\n  }\n}\nfunction CustomersComponent_div_98_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Jm\\u00E9no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Pozice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Telefon\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, CustomersComponent_div_98_div_45_tr_13_Template, 11, 8, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r114 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r114.contacts);\n  }\n}\nfunction CustomersComponent_div_98_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_98_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 instance DIS nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 205)(1, \"span\", 144);\n    i0.ɵɵtext(2, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r126 = i0.ɵɵnextContext().$implicit;\n    const ctx_r128 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r128.getLatestVersion(instance_r126.id));\n  }\n}\nfunction CustomersComponent_div_98_div_51_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 200);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_div_51_tr_19_Template_a_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r131);\n      const instance_r126 = restoredCtx.$implicit;\n      const ctx_r130 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r130.viewInstanceDetail(instance_r126));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"td\");\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 201);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, CustomersComponent_div_98_div_51_tr_19_span_11_Template, 3, 0, \"span\", 202);\n    i0.ɵɵtemplate(12, CustomersComponent_div_98_div_51_tr_19_span_12_Template, 2, 1, \"span\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const instance_r126 = ctx.$implicit;\n    const ctx_r125 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r126.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(instance_r126.serverUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(14, _c2, instance_r126.status === ctx_r125.InstanceStatus.Active, instance_r126.status === ctx_r125.InstanceStatus.Blocked, instance_r126.status === ctx_r125.InstanceStatus.Trial, instance_r126.status === ctx_r125.InstanceStatus.Maintenance, instance_r126.status === ctx_r125.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r125.getInstanceStatusText(instance_r126.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r125.loadingInstanceVersions[instance_r126.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r125.loadingInstanceVersions[instance_r126.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 8, instance_r126.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(instance_r126.expirationDate ? i0.ɵɵpipeBind2(18, 11, instance_r126.expirationDate, \"dd.MM.yyyy\") : \"\");\n  }\n}\nfunction CustomersComponent_div_98_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Datab\\u00E1ze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Aktu\\u00E1ln\\u00ED verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Datum expirace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, CustomersComponent_div_98_div_51_tr_19_Template, 19, 20, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r117 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r117.instances);\n  }\n}\nfunction CustomersComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r133 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 210)(2, \"div\", 211)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Zkratka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, CustomersComponent_div_98_p_9_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(10, CustomersComponent_div_98_p_10_Template, 4, 1, \"p\", 124);\n    i0.ɵɵelementStart(11, \"p\")(12, \"strong\");\n    i0.ɵɵtext(13, \"Ulice:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\")(16, \"strong\");\n    i0.ɵɵtext(17, \"M\\u011Bsto:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"PS\\u010C:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25, \"Zem\\u011B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 211);\n    i0.ɵɵtemplate(28, CustomersComponent_div_98_p_28_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(29, CustomersComponent_div_98_p_29_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(30, CustomersComponent_div_98_p_30_Template, 4, 1, \"p\", 124);\n    i0.ɵɵtemplate(31, CustomersComponent_div_98_p_31_Template, 4, 1, \"p\", 124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 212)(33, \"div\", 213)(34, \"h5\", 184);\n    i0.ɵɵtext(35, \"Statistiky z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 6);\n    i0.ɵɵtemplate(37, CustomersComponent_div_98_div_37_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(38, CustomersComponent_div_98_div_38_Template, 56, 11, \"div\", 214);\n    i0.ɵɵtemplate(39, CustomersComponent_div_98_div_39_Template, 2, 1, \"div\", 215);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 47)(41, \"h5\", 184);\n    i0.ɵɵtext(42, \"Kontaktn\\u00ED osoby\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(43, CustomersComponent_div_98_div_43_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(44, CustomersComponent_div_98_div_44_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(45, CustomersComponent_div_98_div_45_Template, 14, 1, \"div\", 189);\n    i0.ɵɵelementStart(46, \"div\", 216)(47, \"h5\", 184);\n    i0.ɵɵtext(48, \"Instance DIS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(49, CustomersComponent_div_98_div_49_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(50, CustomersComponent_div_98_div_50_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(51, CustomersComponent_div_98_div_51_Template, 20, 1, \"div\", 189);\n    i0.ɵɵelementStart(52, \"div\", 125)(53, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r133);\n      const ctx_r132 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r132.closeCustomerDetailModal());\n    });\n    i0.ɵɵelement(54, \"i\", 53);\n    i0.ɵɵtext(55, \"Zav\\u0159\\u00EDt \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_98_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r133);\n      const ctx_r134 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r134.editSelectedCustomer());\n    });\n    i0.ɵɵelement(57, \"i\", 129);\n    i0.ɵɵtext(58, \"Upravit \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r19.selectedCustomer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.abbreviation, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.companyId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.taxId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.street, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.postalCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.selectedCustomer.country, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.phone);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.website);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.selectedCustomer.notes);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingStatistics);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingStatistics && ctx_r19.customerStatistics);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingStatistics && ctx_r19.statisticsError);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingContacts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingContacts && ctx_r19.contacts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingContacts && ctx_r19.contacts.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loadingInstances);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingInstances && ctx_r19.instances.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.loadingInstances && ctx_r19.instances.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r19.selectedCustomer);\n  }\n}\nfunction CustomersComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.contactError, \" \");\n  }\n}\nfunction CustomersComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226);\n    i0.ɵɵtext(1, \" Jm\\u00E9no je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226);\n    i0.ɵɵtext(1, \" P\\u0159\\u00EDjmen\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_128_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Neplatn\\u00FD form\\u00E1t emailu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtemplate(1, CustomersComponent_div_128_span_1_Template, 2, 0, \"span\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r23.contactForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"email\"]);\n  }\n}\nfunction CustomersComponent_span_146_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_147_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.instanceError, \" \");\n  }\n}\nfunction CustomersComponent_div_163_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" N\\u00E1zev instance je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_168_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" URL serveru je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_187_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"label\", 227);\n    i0.ɵɵtext(2, \"D\\u016Fvod blokace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 228);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_span_220_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_221_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_231_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.instanceVersionError, \" \");\n  }\n}\nfunction CustomersComponent_option_243_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 229);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r136 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", version_r136.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", version_r136.versionNumber, \" (\", i0.ɵɵpipeBind2(2, 3, version_r136.releaseDate, \"dd.MM.yyyy\"), \")\");\n  }\n}\nfunction CustomersComponent_div_244_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" Verze je povinn\\u00E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_option_251_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 229);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r137 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r137.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", user_r137.firstName, \" \", user_r137.lastName, \"\");\n  }\n}\nfunction CustomersComponent_div_252_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 181);\n    i0.ɵɵtext(1, \" U\\u017Eivatel je povinn\\u00FD \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_span_262_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 208);\n  }\n}\nfunction CustomersComponent_i_263_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 209);\n  }\n}\nfunction CustomersComponent_div_273_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 205);\n  }\n}\nfunction CustomersComponent_div_273_i_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 250);\n  }\n}\nfunction CustomersComponent_div_273_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 251);\n  }\n}\nfunction CustomersComponent_div_273_i_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 252);\n  }\n}\nfunction CustomersComponent_div_273_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_273_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵelement(1, \"i\", 253);\n    i0.ɵɵtext(2, \" Instance nem\\u00E1 p\\u0159i\\u0159azen\\u00FD certifik\\u00E1t. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 258);\n    i0.ɵɵtext(1, \"Platn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 259);\n    i0.ɵɵtext(1, \"Neplatn\\u00FD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_71_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 260);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r151 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r151.getCertificateExpirationClass(ctx_r151.certificateInfo.daysToExpiration));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r151.certificateInfo.daysToExpiration, \" dn\\u00ED do expirace) \");\n  }\n}\nconst _c4 = function (a1) {\n  return [\"/certificate-rotation/instance\", a1];\n};\nfunction CustomersComponent_div_273_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r153 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 22)(3, \"div\", 211)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Subject:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Issuer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 211)(17, \"p\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\")(23, \"strong\");\n    i0.ɵɵtext(24, \"Posledn\\u00ED validace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\")(28, \"a\", 241);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_div_71_Template_a_click_28_listener() {\n      i0.ɵɵrestoreView(_r153);\n      const ctx_r152 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r152.closeInstanceDetailModal());\n    });\n    i0.ɵɵelement(29, \"i\", 254);\n    i0.ɵɵtext(30, \"Nastaven\\u00ED automatick\\u00E9 rotace \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"p\")(32, \"strong\");\n    i0.ɵɵtext(33, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CustomersComponent_div_273_div_71_span_34_Template, 2, 0, \"span\", 255);\n    i0.ɵɵtemplate(35, CustomersComponent_div_273_div_71_span_35_Template, 2, 0, \"span\", 256);\n    i0.ɵɵtemplate(36, CustomersComponent_div_273_div_71_span_36_Template, 2, 2, \"span\", 257);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r145 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.thumbprint, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.subject, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r145.certificateInfo.issuer, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 9, ctx_r145.certificateInfo.expirationDate, \"dd.MM.yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(26, 12, ctx_r145.certificateInfo.lastValidation, \"dd.MM.yyyy HH:mm\" || \"-\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c4, ctx_r145.selectedInstanceForVersion.id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r145.certificateInfo.isValid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r145.certificateInfo.isValid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r145.certificateInfo.daysToExpiration > 0);\n  }\n}\nfunction CustomersComponent_div_273_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"div\", 143)(2, \"span\", 144);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomersComponent_div_273_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 verze nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomersComponent_div_273_div_81_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const version_r155 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(version_r155.versionNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 4, version_r155.installedAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(version_r155.installedByUserName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(version_r155.notes || \"-\");\n  }\n}\nfunction CustomersComponent_div_273_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"table\", 148)(2, \"thead\", 149)(3, \"tr\", 150)(4, \"th\");\n    i0.ɵɵtext(5, \"Verze\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 261);\n    i0.ɵɵtext(7, \"Datum instalace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Instaloval\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Pozn\\u00E1mka\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, CustomersComponent_div_273_div_81_tr_13_Template, 10, 7, \"tr\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r148 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r148.instanceVersions[ctx_r148.selectedInstanceForVersion.id]);\n  }\n}\nconst _c5 = function (a1) {\n  return [\"/ip-whitelisting\", a1];\n};\nfunction CustomersComponent_div_273_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r157 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 230)(2, \"div\", 211)(3, \"h6\");\n    i0.ɵɵtext(4, \"N\\u00E1zev:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 211)(8, \"h6\");\n    i0.ɵɵtext(9, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"span\", 201);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 230)(14, \"div\", 211)(15, \"h6\");\n    i0.ɵɵtext(16, \"URL serveru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 211)(20, \"h6\");\n    i0.ɵɵtext(21, \"Datum posledn\\u00ED komunikace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 230)(26, \"div\", 231)(27, \"h6\");\n    i0.ɵɵtext(28, \"API kl\\u00ED\\u010D:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 232);\n    i0.ɵɵelement(30, \"input\", 233, 234);\n    i0.ɵɵelementStart(32, \"button\", 235);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const _r138 = i0.ɵɵreference(31);\n      const ctx_r156 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r156.copyApiKey(_r138));\n    });\n    i0.ɵɵelement(33, \"i\", 236);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 237);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r158 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r158.regenerateApiKey(ctx_r158.selectedInstanceForVersion.id));\n    });\n    i0.ɵɵtemplate(35, CustomersComponent_div_273_span_35_Template, 1, 0, \"span\", 202);\n    i0.ɵɵtemplate(36, CustomersComponent_div_273_i_36_Template, 1, 0, \"i\", 238);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 239)(38, \"small\", 240);\n    i0.ɵɵtext(39, \"Tento kl\\u00ED\\u010D je pot\\u0159eba zadat do aplikace DIS pro komunikaci s DIS Admin.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 241);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_a_click_40_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r159 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r159.closeInstanceDetailModal());\n    });\n    i0.ɵɵelement(41, \"i\", 242);\n    i0.ɵɵtext(42, \"IP Whitelisting \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 230)(44, \"div\", 211)(45, \"h6\");\n    i0.ɵɵtext(46, \"Datum instalace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\");\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 211)(51, \"h6\");\n    i0.ɵɵtext(52, \"Datum expirace:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"p\");\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"div\", 47)(57, \"h6\");\n    i0.ɵɵtext(58, \"Pozn\\u00E1mka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\");\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 243)(62, \"div\", 183)(63, \"h5\", 184);\n    i0.ɵɵtext(64, \"Certifik\\u00E1t\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 244);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r160 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r160.generateCertificate(ctx_r160.selectedInstanceForVersion.id));\n    });\n    i0.ɵɵtemplate(66, CustomersComponent_div_273_span_66_Template, 1, 0, \"span\", 245);\n    i0.ɵɵtemplate(67, CustomersComponent_div_273_i_67_Template, 1, 0, \"i\", 246);\n    i0.ɵɵtext(68, \"Vygenerovat nov\\u00FD certifik\\u00E1t \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(69, CustomersComponent_div_273_div_69_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(70, CustomersComponent_div_273_div_70_Template, 3, 0, \"div\", 188);\n    i0.ɵɵtemplate(71, CustomersComponent_div_273_div_71_Template, 37, 17, \"div\", 247);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 47)(73, \"div\", 183)(74, \"h5\", 184);\n    i0.ɵɵtext(75, \"Historie verz\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 248);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_273_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r161 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r161.openAddInstanceVersionModal(ctx_r161.selectedInstanceForVersion));\n    });\n    i0.ɵɵelement(77, \"i\", 249);\n    i0.ɵɵtext(78, \"P\\u0159idat verzi \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, CustomersComponent_div_273_div_79_Template, 4, 0, \"div\", 187);\n    i0.ɵɵtemplate(80, CustomersComponent_div_273_div_80_Template, 2, 0, \"div\", 188);\n    i0.ɵɵtemplate(81, CustomersComponent_div_273_div_81_Template, 14, 1, \"div\", 189);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(31, _c2, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Active, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Blocked, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Trial, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Maintenance, ctx_r39.selectedInstanceForVersion.status === ctx_r39.InstanceStatus.Expired));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.getInstanceStatusText(ctx_r39.selectedInstanceForVersion.status), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.serverUrl || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.lastConnectionDate ? i0.ɵɵpipeBind2(24, 22, ctx_r39.selectedInstanceForVersion.lastConnectionDate, \"dd.MM.yyyy HH:mm\") : \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r39.selectedInstanceForVersion.apiKey || \"-\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.regeneratingApiKey);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(37, _c5, ctx_r39.selectedInstanceForVersion.id));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(49, 25, ctx_r39.selectedInstanceForVersion.installationDate, \"dd.MM.yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.expirationDate ? i0.ɵɵpipeBind2(55, 28, ctx_r39.selectedInstanceForVersion.expirationDate, \"dd.MM.yyyy\") : \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r39.selectedInstanceForVersion.notes || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.generatingCertificate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loadingCertificateInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingCertificateInfo && !ctx_r39.certificateInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingCertificateInfo && ctx_r39.certificateInfo);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id] && (!ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id] || ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id].length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loadingInstanceVersions[ctx_r39.selectedInstanceForVersion.id] && ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id] && ctx_r39.instanceVersions[ctx_r39.selectedInstanceForVersion.id].length > 0);\n  }\n}\nfunction CustomersComponent_div_295_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r163 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 262);\n    i0.ɵɵtext(2, \"Informace o certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 263)(4, \"li\", 264)(5, \"span\");\n    i0.ɵɵtext(6, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 240);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 264)(10, \"span\");\n    i0.ɵɵtext(11, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 240);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\", 264)(16, \"span\");\n    i0.ɵɵtext(17, \"Heslo k certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 265);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 266);\n    i0.ɵɵelement(21, \"i\", 267);\n    i0.ɵɵelementStart(22, \"strong\");\n    i0.ɵɵtext(23, \"D\\u016Fle\\u017Eit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Toto heslo si poznamenejte. Budete ho pot\\u0159ebovat p\\u0159i instalaci certifik\\u00E1tu. Z bezpe\\u010Dnostn\\u00EDch d\\u016Fvod\\u016F nen\\u00ED heslo nikde ulo\\u017Eeno a nebude mo\\u017En\\u00E9 ho pozd\\u011Bji zobrazit. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 262)(26, \"button\", 268);\n    i0.ɵɵlistener(\"click\", function CustomersComponent_div_295_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r163);\n      const ctx_r162 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r162.downloadCertificate());\n    });\n    i0.ɵɵelement(27, \"i\", 269);\n    i0.ɵɵtext(28, \"St\\u00E1hnout certifik\\u00E1t \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r40.generatedCertificate.thumbprint);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 3, ctx_r40.generatedCertificate.expirationDate, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r40.generatedCertificate.password);\n  }\n}\nconst _c6 = function (a0, a1) {\n  return {\n    \"required-field\": a0,\n    \"valid-field\": a1\n  };\n};\nexport class CustomersComponent {\n  /**\r\n   * Pomocná metoda pro správné zavření modálu\r\n   * @param modalId ID modálu, který chceme zavřít\r\n   */\n  closeModal(modalId) {\n    this.modalService.close(modalId);\n  }\n  /**\r\n   * Pomocná metoda pro otevření modálu\r\n   * @param modalId ID modálu, který chceme otevřít\r\n   */\n  openModal(modalId) {\n    this.modalService.open(modalId);\n  }\n  closeCustomerModal() {\n    this.closeModal('customerModal');\n  }\n  closeContactModal() {\n    this.closeModal('contactModal');\n  }\n  closeCustomerDetailModal() {\n    this.closeModal('customerDetailModal');\n  }\n  editSelectedCustomer() {\n    if (this.selectedCustomer) {\n      this.editCustomer(this.selectedCustomer);\n    }\n  }\n  openAddInstanceModal() {\n    console.log('Open add instance modal');\n    if (!this.selectedCustomer) {\n      console.error('No customer selected');\n      return;\n    }\n    // Reset formuláře a chybové zprávy\n    this.instanceForm.reset({\n      name: '',\n      serverUrl: '',\n      notes: ''\n    });\n    this.isEditInstanceMode = false;\n    this.instanceError = null;\n    // Otevřít modal\n    this.openModal('instanceModal');\n  }\n  editInstance(instance) {\n    console.log('Edit instance', instance);\n    // Naplnění formuláře daty instance\n    this.instanceForm.patchValue({\n      name: instance.name,\n      serverUrl: instance.serverUrl || '',\n      expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n      status: instance.status || 'Active',\n      blockReason: instance.blockReason || '',\n      moduleReporting: instance.moduleReporting,\n      moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n      moduleApiIntegration: instance.moduleApiIntegration,\n      moduleDataExport: instance.moduleDataExport,\n      moduleCustomization: instance.moduleCustomization,\n      notes: instance.notes || ''\n    });\n    this.isEditInstanceMode = true;\n    this.selectedInstance = instance;\n    this.instanceError = null;\n    // Otevřít modal\n    this.openModal('instanceModal');\n  }\n  deleteInstance(instance) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      console.log('Delete instance', instance);\n      const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat instanci ${instance.name}?`, 'Smazání instance', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this.instanceService.deleteInstance(instance.id).subscribe({\n          next: () => {\n            console.log('Instance deleted successfully');\n            _this.toastr.success(`Instance ${instance.name} byla úspěšně smazána`, 'Úspěch');\n            // Odstranit instanci ze seznamu\n            _this.instances = _this.instances.filter(i => i.id !== instance.id);\n          },\n          error: err => {\n            console.error('Chyba při mazání instance', err);\n            _this.modalService.alert(`Chyba při mazání instance: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      }\n    })();\n  }\n  saveInstance() {\n    console.log('Save instance', this.instanceForm.value);\n    if (this.instanceForm.invalid) {\n      this.instanceError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceForm.controls).forEach(key => {\n        const control = this.instanceForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n    if (!this.selectedCustomer) {\n      this.instanceError = 'Není vybrán žádný zákazník.';\n      return;\n    }\n    this.savingInstance = true;\n    const instanceData = this.instanceForm.value;\n    if (this.isEditInstanceMode && this.selectedInstance) {\n      // Aktualizace existující instance\n      const updatedInstance = {\n        name: instanceData.name || '',\n        serverUrl: instanceData.serverUrl || '',\n        expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n        status: instanceData.status,\n        blockReason: instanceData.blockReason,\n        moduleReporting: instanceData.moduleReporting,\n        moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n        moduleApiIntegration: instanceData.moduleApiIntegration,\n        moduleDataExport: instanceData.moduleDataExport,\n        moduleCustomization: instanceData.moduleCustomization,\n        notes: instanceData.notes || ''\n      };\n      console.log('Sending updated instance data:', updatedInstance);\n      this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance).subscribe({\n        next: updatedInstance => {\n          console.log('Instance updated successfully:', updatedInstance);\n          this.savingInstance = false;\n          // Aktualizovat instanci v seznamu\n          const index = this.instances.findIndex(i => i.id === updatedInstance.id);\n          if (index !== -1) {\n            this.instances[index] = updatedInstance;\n          }\n          // Zavřít modal\n          this.closeInstanceModal();\n        },\n        error: err => {\n          console.error('Chyba při aktualizaci instance', err);\n          this.instanceError = `Chyba při aktualizaci instance: ${err.status} ${err.statusText}`;\n          this.savingInstance = false;\n        }\n      });\n    } else {\n      // Vytvoření nové instance\n      const newInstance = {\n        customerId: this.selectedCustomer.id,\n        name: instanceData.name || '',\n        serverUrl: instanceData.serverUrl || '',\n        expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n        status: instanceData.status,\n        moduleReporting: instanceData.moduleReporting,\n        moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n        moduleApiIntegration: instanceData.moduleApiIntegration,\n        moduleDataExport: instanceData.moduleDataExport,\n        moduleCustomization: instanceData.moduleCustomization,\n        notes: instanceData.notes || ''\n      };\n      console.log('Sending new instance data:', newInstance);\n      this.instanceService.createInstance(newInstance).subscribe({\n        next: createdInstance => {\n          console.log('Instance created successfully:', createdInstance);\n          this.savingInstance = false;\n          // Přidat novou instanci do seznamu\n          this.instances.push(createdInstance);\n          // Zavřít modal\n          this.closeInstanceModal();\n        },\n        error: err => {\n          console.error('Chyba při vytváření instance', err);\n          this.instanceError = `Chyba při vytváření instance: ${err.status} ${err.statusText}`;\n          this.savingInstance = false;\n        }\n      });\n    }\n  }\n  closeInstanceModal() {\n    this.closeModal('instanceModal');\n  }\n  getInstanceStatusText(status) {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active':\n          return 'Aktivní';\n        case 'Blocked':\n          return 'Blokovaná';\n        case 'Expired':\n          return 'Expirovaná';\n        case 'Trial':\n          return 'Zkušební';\n        case 'Maintenance':\n          return 'Údržba';\n        default:\n          return status;\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active:\n          return 'Aktivní';\n        case InstanceStatus.Blocked:\n          return 'Blokovaná';\n        case InstanceStatus.Expired:\n          return 'Expirovaná';\n        case InstanceStatus.Trial:\n          return 'Zkušební';\n        case InstanceStatus.Maintenance:\n          return 'Údržba';\n        default:\n          return String(status);\n      }\n    }\n  }\n  openAddContactModal() {\n    console.log('Open add contact modal');\n    if (!this.selectedCustomer) {\n      console.error('No customer selected');\n      return;\n    }\n    // Reset formuláře a chybové zprávy\n    this.contactForm.reset({\n      firstName: '',\n      lastName: '',\n      position: '',\n      email: '',\n      phone: '',\n      notes: '',\n      isPrimary: false\n    });\n    this.isEditContactMode = false;\n    this.contactError = null;\n    // Otevřít modal\n    this.openModal('contactModal');\n  }\n  editContact(contact) {\n    console.log('Edit contact', contact);\n    // Naplnění formuláře daty kontaktu\n    this.contactForm.setValue({\n      firstName: contact.firstName,\n      lastName: contact.lastName,\n      position: contact.position || '',\n      email: contact.email || '',\n      phone: contact.phone || '',\n      notes: contact.notes || '',\n      isPrimary: contact.isPrimary\n    });\n    this.isEditContactMode = true;\n    this.selectedContact = contact;\n    this.contactError = null;\n    // Otevřít modal\n    this.openModal('contactModal');\n  }\n  deleteContact(contact) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log('Delete contact', contact);\n      const confirmed = yield _this2.modalService.confirm(`Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`, 'Smazání kontaktu', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this2.contactService.deleteContact(contact.id).subscribe({\n          next: () => {\n            console.log('Contact deleted successfully');\n            _this2.toastr.success(`Kontakt ${contact.firstName} ${contact.lastName} byl úspěšně smazán`, 'Úspěch');\n            // Odstranit kontakt ze seznamu\n            _this2.contacts = _this2.contacts.filter(c => c.id !== contact.id);\n          },\n          error: err => {\n            console.error('Chyba při mazání kontaktu', err);\n            _this2.modalService.alert(`Chyba při mazání kontaktu: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      }\n    })();\n  }\n  saveContact() {\n    console.log('Save contact', this.contactForm.value);\n    if (this.contactForm.invalid) {\n      this.contactError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.contactForm.controls).forEach(key => {\n        const control = this.contactForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n    if (!this.selectedCustomer) {\n      this.contactError = 'Není vybrán žádný zákazník.';\n      return;\n    }\n    this.savingContact = true;\n    const contactData = this.contactForm.value;\n    if (this.isEditContactMode && this.selectedContact) {\n      // Aktualizace existujícího kontaktu\n      const updatedContact = {\n        firstName: contactData.firstName || '',\n        lastName: contactData.lastName || '',\n        position: contactData.position || '',\n        email: contactData.email || '',\n        phone: contactData.phone || '',\n        notes: contactData.notes || '',\n        isPrimary: contactData.isPrimary || false\n      };\n      console.log('Sending updated contact data:', updatedContact);\n      this.contactService.updateContact(this.selectedContact.id, updatedContact).subscribe({\n        next: updatedContact => {\n          console.log('Contact updated successfully:', updatedContact);\n          this.savingContact = false;\n          // Aktualizovat kontakt v seznamu\n          const index = this.contacts.findIndex(c => c.id === updatedContact.id);\n          if (index !== -1) {\n            this.contacts[index] = updatedContact;\n          }\n          // Zavřít modal\n          this.closeModal('contactModal');\n        },\n        error: err => {\n          console.error('Chyba při aktualizaci kontaktu', err);\n          this.contactError = `Chyba při aktualizaci kontaktu: ${err.status} ${err.statusText}`;\n          this.savingContact = false;\n        }\n      });\n    } else {\n      // Vytvoření nového kontaktu\n      const newContact = {\n        customerId: this.selectedCustomer.id,\n        firstName: contactData.firstName || '',\n        lastName: contactData.lastName || '',\n        position: contactData.position || '',\n        email: contactData.email || '',\n        phone: contactData.phone || '',\n        notes: contactData.notes || '',\n        isPrimary: contactData.isPrimary || false\n      };\n      console.log('Sending contact data:', newContact);\n      this.contactService.createContact(newContact).subscribe({\n        next: createdContact => {\n          console.log('Contact created successfully:', createdContact);\n          this.savingContact = false;\n          // Přidat kontakt do seznamu\n          this.contacts.push(createdContact);\n          this.closeContactModal();\n        },\n        error: err => {\n          console.error('Chyba při vytváření kontaktu', err);\n          console.error('Error details:', err.error);\n          // Zobrazit detailnější chybovou zprávu\n          if (err.error && err.error.errors) {\n            const errorMessages = [];\n            for (const key in err.error.errors) {\n              if (err.error.errors.hasOwnProperty(key)) {\n                errorMessages.push(`${key}: ${err.error.errors[key].join(', ')}`);\n              }\n            }\n            this.contactError = `Chyba při vytváření kontaktu: ${errorMessages.join('; ')}`;\n          } else {\n            this.contactError = `Chyba při vytváření kontaktu: ${err.status} ${err.statusText}`;\n          }\n          this.savingContact = false;\n        }\n      });\n    }\n  }\n  // Vlastní validátor pro email - kontroluje formát pouze pokud je pole vyplněno\n  emailValidator() {\n    return control => {\n      const value = control.value;\n      if (!value || value.length === 0) {\n        return null; // Pokud je pole prázdné, validace projde\n      }\n      // Použijeme standardní validátor emailu\n      return Validators.email(control);\n    };\n  }\n  constructor(fb, customerService, contactService, instanceService, instanceVersionService, versionService, userService, authService, certificateService, modalService, http, monitoringService, router, toastr) {\n    this.fb = fb;\n    this.customerService = customerService;\n    this.contactService = contactService;\n    this.instanceService = instanceService;\n    this.instanceVersionService = instanceVersionService;\n    this.versionService = versionService;\n    this.userService = userService;\n    this.authService = authService;\n    this.certificateService = certificateService;\n    this.modalService = modalService;\n    this.http = http;\n    this.monitoringService = monitoringService;\n    this.router = router;\n    this.toastr = toastr;\n    // Zpřístupnění enumu InstanceStatus pro šablonu\n    this.InstanceStatus = InstanceStatus;\n    this.loading = false;\n    this.error = null;\n    this.customers = [];\n    this.filteredCustomers = [];\n    this.paginatedCustomers = [];\n    this.selectedCustomer = null;\n    this.contacts = [];\n    this.instances = [];\n    this.loadingContacts = false;\n    this.loadingInstances = false;\n    this.savingContact = false;\n    this.savingInstance = false;\n    this.isEditContactMode = false;\n    this.isEditInstanceMode = false;\n    this.contactError = null;\n    this.instanceError = null;\n    this.selectedContact = null;\n    this.selectedInstance = null;\n    // Filtrování a řazení\n    this.filterFields = [{\n      name: 'name',\n      label: 'Název',\n      type: 'text'\n    }, {\n      name: 'contact',\n      label: 'Kontakt',\n      type: 'text',\n      isCustom: true\n    }];\n    this.currentFilters = {};\n    this.sortColumn = 'name';\n    this.sortDirection = 'asc';\n    // Stránkování\n    this.currentPage = 1;\n    this.pageSize = 10;\n    this.Math = Math; // Pro použití v šabloně\n    this.isEditMode = false;\n    this.saving = false;\n    this.isAdmin = false;\n    // Proměnné pro správu verzí instancí\n    this.instanceVersions = {};\n    this.loadingInstanceVersions = {};\n    this.availableVersions = [];\n    this.loadingAvailableVersions = false;\n    this.availableUsers = [];\n    this.loadingAvailableUsers = false;\n    this.selectedInstanceVersion = null;\n    this.isEditInstanceVersionMode = false;\n    this.instanceVersionError = null;\n    this.savingInstanceVersion = false;\n    this.selectedInstanceForVersion = null;\n    // Proměnné pro práci s certifikáty\n    this.loadingCertificateInfo = false;\n    this.certificateInfo = null;\n    this.generatingCertificate = false;\n    this.generatedCertificate = null;\n    // Proměnné pro práci s API klíčem\n    this.regeneratingApiKey = false;\n    // Proměnné pro statistiky\n    this.loadingStatistics = false;\n    this.customerStatistics = null;\n    this.statisticsError = null;\n    this.customerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n      companyId: ['', Validators.maxLength(20)],\n      taxId: ['', Validators.maxLength(20)],\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      website: ['', Validators.maxLength(255)],\n      street: ['', [Validators.required, Validators.maxLength(255)]],\n      city: ['', [Validators.required, Validators.maxLength(255)]],\n      postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n      country: ['', [Validators.required, Validators.maxLength(100)]],\n      notes: ['', Validators.maxLength(500)] // Changed from note to notes to match API model\n    });\n\n    this.contactForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.maxLength(100)]],\n      lastName: ['', [Validators.required, Validators.maxLength(100)]],\n      position: ['', Validators.maxLength(100)],\n      email: ['', [this.emailValidator(), Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      notes: ['', Validators.maxLength(100)],\n      isPrimary: [false]\n    });\n    this.instanceForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      status: ['Active'],\n      blockReason: ['', Validators.maxLength(500)],\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false],\n      notes: ['', Validators.maxLength(500)]\n    });\n    this.instanceVersionForm = this.fb.group({\n      versionId: ['', Validators.required],\n      installedByUserId: ['', Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n    this.loadCustomers();\n  }\n  openAddCustomerModal() {\n    // Přesměrujeme na stránku pro vytvoření nového zákazníka\n    this.router.navigate(['/customers/add']);\n  }\n  viewCustomerDetail(customer) {\n    // Místo otevření modálu přesměrujeme na detailní stránku\n    this.router.navigate(['/customers', customer.id]);\n  }\n  loadInstanceVersions(instanceId) {\n    this.loadingInstanceVersions[instanceId] = true;\n    this.instanceVersions[instanceId] = [];\n    this.instanceVersionService.getInstanceVersions(instanceId).subscribe({\n      next: versions => {\n        console.log(`Loaded versions for instance ${instanceId}:`, versions);\n        this.instanceVersions[instanceId] = versions;\n        this.loadingInstanceVersions[instanceId] = false;\n      },\n      error: err => {\n        console.error(`Chyba při načítání verzí instance ${instanceId}`, err);\n        this.loadingInstanceVersions[instanceId] = false;\n      }\n    });\n  }\n  getLatestVersion(instanceId) {\n    if (!this.instanceVersions[instanceId] || this.instanceVersions[instanceId].length === 0) {\n      return '-';\n    }\n    // Verze jsou seřazeny podle data instalace sestupně, takže první je nejnovější\n    return this.instanceVersions[instanceId][0].versionNumber;\n  }\n  openAddInstanceVersionModal(instance) {\n    console.log('Open add instance version modal', instance);\n    this.selectedInstanceForVersion = instance;\n    this.isEditInstanceVersionMode = false;\n    this.instanceVersionError = null;\n    // Reset formuláře\n    this.instanceVersionForm.reset({\n      versionId: '',\n      installedByUserId: '',\n      notes: ''\n    });\n    // Načtení dostupných verzí\n    this.loadAvailableVersions();\n    // Načtení dostupných uživatelů\n    this.loadAvailableUsers();\n    // Otevření modalu\n    this.openModal('instanceVersionModal');\n  }\n  loadAvailableVersions() {\n    this.loadingAvailableVersions = true;\n    this.availableVersions = [];\n    this.versionService.getVersions().subscribe({\n      next: versions => {\n        console.log('Loaded available versions:', versions);\n        this.availableVersions = versions;\n        this.loadingAvailableVersions = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání dostupných verzí', err);\n        this.loadingAvailableVersions = false;\n        this.instanceVersionError = `Chyba při načítání dostupných verzí: ${err.status} ${err.statusText}`;\n      }\n    });\n  }\n  loadAvailableUsers() {\n    this.loadingAvailableUsers = true;\n    this.availableUsers = [];\n    this.userService.getUsers().subscribe({\n      next: users => {\n        console.log('Loaded available users:', users);\n        this.availableUsers = users;\n        this.loadingAvailableUsers = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání dostupných uživatelů', err);\n        this.loadingAvailableUsers = false;\n        this.instanceVersionError = `Chyba při načítání dostupných uživatelů: ${err.status} ${err.statusText}`;\n      }\n    });\n  }\n  saveInstanceVersion() {\n    if (this.instanceVersionForm.invalid) {\n      this.instanceVersionError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceVersionForm.controls).forEach(key => {\n        const control = this.instanceVersionForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n    if (!this.selectedInstanceForVersion) {\n      this.instanceVersionError = 'Není vybrána žádná instance.';\n      return;\n    }\n    this.savingInstanceVersion = true;\n    const instanceVersionData = this.instanceVersionForm.value;\n    if (this.isEditInstanceVersionMode && this.selectedInstanceVersion) {\n      // Aktualizace existující verze instance\n      const updatedInstanceVersion = {\n        notes: instanceVersionData.notes || ''\n      };\n      console.log('Sending updated instance version data:', updatedInstanceVersion);\n      this.instanceVersionService.updateInstanceVersion(this.selectedInstanceForVersion.id, this.selectedInstanceVersion.id, updatedInstanceVersion).subscribe({\n        next: updatedInstanceVersion => {\n          console.log('Instance version updated successfully:', updatedInstanceVersion);\n          this.savingInstanceVersion = false;\n          // Aktualizovat verzi v seznamu\n          this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n          // Zavřít modal\n          this.closeInstanceVersionModal();\n        },\n        error: err => {\n          console.error('Chyba při aktualizaci verze instance', err);\n          this.instanceVersionError = `Chyba při aktualizaci verze instance: ${err.status} ${err.statusText}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n    } else {\n      // Vytvoření nové verze instance\n      const newInstanceVersion = {\n        versionId: parseInt(instanceVersionData.versionId),\n        installedByUserId: parseInt(instanceVersionData.installedByUserId),\n        notes: instanceVersionData.notes || ''\n      };\n      console.log('Sending new instance version data:', newInstanceVersion);\n      this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion).subscribe({\n        next: createdInstanceVersion => {\n          console.log('Instance version created successfully:', createdInstanceVersion);\n          this.savingInstanceVersion = false;\n          // Aktualizovat seznam verzí\n          this.loadInstanceVersions(this.selectedInstanceForVersion.id);\n          // Zavřít modal\n          this.closeInstanceVersionModal();\n        },\n        error: err => {\n          console.error('Chyba při vytváření verze instance', err);\n          this.instanceVersionError = `Chyba při vytváření verze instance: ${err.status} ${err.statusText}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n    }\n  }\n  closeInstanceVersionModal() {\n    this.closeModal('instanceVersionModal');\n  }\n  viewInstanceDetail(instance) {\n    console.log('View instance detail', instance);\n    this.selectedInstanceForVersion = instance;\n    // Načtení verzí instance\n    this.loadInstanceVersions(instance.id);\n    // Načtení informací o certifikátu\n    console.log('Loading certificate info for instance ID:', instance.id);\n    this.loadCertificateInfo(instance.id);\n    // Otevřít modal\n    this.openModal('instanceDetailModal');\n  }\n  closeInstanceDetailModal() {\n    this.closeModal('instanceDetailModal');\n  }\n  // Metoda pro načtení informací o certifikátu\n  loadCertificateInfo(instanceId) {\n    console.log('loadCertificateInfo called with instanceId:', instanceId);\n    this.loadingCertificateInfo = true;\n    this.certificateInfo = null;\n    const url = `${environment.apiUrl}/certificates/instance/${instanceId}`;\n    console.log('Calling API endpoint:', url);\n    // Použijeme přímo HttpClient místo service\n    this.http.get(url).subscribe({\n      next: response => {\n        console.log('Certificate info loaded:', response);\n        this.certificateInfo = response;\n        this.loadingCertificateInfo = false;\n      },\n      error: err => {\n        console.error('Error loading certificate info', err);\n        console.error('Error details:', err.status, err.statusText);\n        if (err.error) {\n          console.error('Error message:', err.error.message || err.error);\n        }\n        this.loadingCertificateInfo = false;\n        // Pokud je chyba 404, znamená to, že instance nemá certifikát\n        if (err.status === 404) {\n          console.log('Instance nemá certifikát (404)');\n          this.certificateInfo = null;\n        } else {\n          // Pro ostatní chyby zkusíme vytvořit prázdný objekt, aby se zobrazila sekce\n          console.log('Vytvářím prázdný objekt certifikátu pro zobrazení UI');\n          this.certificateInfo = {\n            instanceId: this.selectedInstanceForVersion?.id || 0,\n            instanceName: this.selectedInstanceForVersion?.name || '',\n            customerName: '',\n            thumbprint: 'Nedostupné',\n            subject: 'Nedostupné',\n            issuer: 'Nedostupné',\n            isValid: false,\n            daysToExpiration: 0\n          };\n        }\n      }\n    });\n  }\n  // Metoda pro generování nového certifikátu\n  generateCertificate(instanceId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const confirmed = yield _this3.modalService.confirm('Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.', 'Generování certifikátu', 'Generovat', 'Zrušit', 'btn-success', 'btn-secondary');\n      if (!confirmed) {\n        return;\n      }\n      _this3.generatingCertificate = true;\n      _this3.generatedCertificate = null;\n      const url = `${environment.apiUrl}/certificates/instance/${instanceId}/generate`;\n      console.log('Generating certificate using URL:', url);\n      _this3.http.post(url, {}).subscribe({\n        next: response => {\n          console.log('Certificate generated:', response);\n          _this3.generatedCertificate = response;\n          _this3.generatingCertificate = false;\n          // Aktualizace informací o certifikátu\n          _this3.loadCertificateInfo(instanceId);\n          // Zobrazení modálu s informacemi o vygenerovaném certifikátu\n          _this3.modalService.open('certificateGeneratedModal');\n        },\n        error: err => {\n          console.error('Error generating certificate', err);\n          _this3.generatingCertificate = false;\n          _this3.modalService.alert(`Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`, 'Chyba', 'Zavřít', 'btn-danger');\n        }\n      });\n    })();\n  }\n  // Metoda pro stažení certifikátu\n  downloadCertificate() {\n    if (!this.generatedCertificate) {\n      return;\n    }\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n  // Pomocná metoda pro konverzi Base64 na Blob\n  base64ToBlob(base64, contentType) {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  // Metoda pro získání CSS třídy podle počtu dní do expirace certifikátu\n  getCertificateExpirationClass(daysToExpiration) {\n    if (daysToExpiration <= 7) {\n      return 'text-danger';\n    } else if (daysToExpiration <= 30) {\n      return 'text-warning';\n    } else {\n      return 'text-info';\n    }\n  }\n  editCustomer(customer) {\n    // Přesměrujeme na detailní stránku s parametrem edit=true\n    this.router.navigate(['/customers', customer.id], {\n      queryParams: {\n        edit: 'true'\n      }\n    });\n  }\n  deleteCustomer(customer) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      console.log('Delete customer', customer);\n      const confirmed = yield _this4.modalService.confirm(`Opravdu chcete smazat zákazníka ${customer.name}?`, 'Smazání zákazníka', 'Smazat', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this4.loading = true;\n        _this4.customerService.deleteCustomer(customer.id).subscribe({\n          next: () => {\n            console.log('Customer deleted successfully');\n            _this4.toastr.success(`Zákazník ${customer.name} byl úspěšně smazán`, 'Úspěch');\n            _this4.loadCustomers();\n          },\n          error: err => {\n            console.error('Chyba při mazání zákazníka', err);\n            _this4.modalService.alert(`Chyba při mazání zákazníka: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n            _this4.loading = false;\n          }\n        });\n      }\n    })();\n  }\n  ngOnInit() {\n    this.isAdmin = this.authService.isAdmin();\n    console.log('User is admin:', this.isAdmin);\n    // Načtení posledního použitého filtru z localStorage\n    try {\n      const lastFilterKey = `last_filter_customers`;\n      const lastFilterJson = localStorage.getItem(lastFilterKey);\n      if (lastFilterJson) {\n        this.currentFilters = JSON.parse(lastFilterJson);\n        console.log('Načten poslední filtr z localStorage:', this.currentFilters);\n      }\n    } catch (error) {\n      console.error('Chyba při načítání posledního filtru z localStorage', error);\n    }\n    this.loadCustomers();\n  }\n  /**\r\n   * Metody pro filtrování a řazení\r\n   */\n  onFilterChange(filters) {\n    this.currentFilters = filters;\n    this.currentPage = 1; // Reset na první stránku při změně filtru\n    // Uložení aktuálního filtru do localStorage\n    try {\n      const lastFilterKey = `last_filter_customers`;\n      if (Object.keys(filters).length > 0) {\n        localStorage.setItem(lastFilterKey, JSON.stringify(filters));\n        console.log('Uložen aktuální filtr do localStorage:', filters);\n      } else {\n        // Pokud je filtr prázdný, odstraníme ho z localStorage\n        localStorage.removeItem(lastFilterKey);\n        console.log('Odstraněn filtr z localStorage');\n      }\n    } catch (error) {\n      console.error('Chyba při ukládání filtru do localStorage', error);\n    }\n    // Načtení dat z backendu s filtrem\n    this.loadCustomers();\n  }\n  onSort(column) {\n    if (this.sortColumn === column) {\n      // Pokud klikneme na stejný sloupec, změníme směr řazení\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Pokud klikneme na jiný sloupec, nastavit výchozí směr řazení\n      this.sortColumn = column;\n      this.sortDirection = 'asc';\n    }\n    this.applyFiltersAndSort();\n    this.updatePaginatedCustomers();\n  }\n  applyFiltersAndSort() {\n    // Filtrování se provádí na backendu, zde pouze řadíme\n    this.filteredCustomers = [...this.customers];\n    // Řazení\n    this.filteredCustomers.sort((a, b) => {\n      const valueA = a[this.sortColumn]?.toString().toLowerCase() || '';\n      const valueB = b[this.sortColumn]?.toString().toLowerCase() || '';\n      if (this.sortDirection === 'asc') {\n        return valueA.localeCompare(valueB);\n      } else {\n        return valueB.localeCompare(valueA);\n      }\n    });\n  }\n  /**\r\n   * Metody pro stránkování\r\n   */\n  onPageChange(page) {\n    this.currentPage = page;\n    this.updatePaginatedCustomers();\n  }\n  updatePaginatedCustomers() {\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);\n  }\n  get totalPages() {\n    return Math.ceil(this.filteredCustomers.length / this.pageSize);\n  }\n  get pageRange() {\n    const range = [];\n    const maxPages = 5;\n    const startPage = Math.max(1, this.currentPage - Math.floor(maxPages / 2));\n    const endPage = Math.min(this.totalPages, startPage + maxPages - 1);\n    for (let i = startPage; i <= endPage; i++) {\n      range.push(i);\n    }\n    return range;\n  }\n  saveCustomer() {\n    console.log('saveCustomer() called');\n    if (!this.isAdmin) {\n      this.error = 'Pouze administrátor může vytvářet zákazníky';\n      console.log('User is not admin, returning');\n      return;\n    }\n    if (this.customerForm.invalid) {\n      this.error = 'Formulář obsahuje chyby. Opravte je prosím.';\n      console.log('Form is invalid, marking fields as touched');\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.customerForm.controls).forEach(key => {\n        const control = this.customerForm.get(key);\n        control?.markAsTouched();\n        if (control?.invalid) {\n          console.log(`Field ${key} is invalid:`, control.errors);\n        }\n      });\n      return;\n    }\n    const customerData = this.customerForm.value;\n    console.log('Form is valid, saving customer data:', customerData);\n    this.saving = true;\n    if (this.isEditMode && this.selectedCustomer) {\n      // Aktualizace existujícího zákazníka\n      console.log('Updating customer with ID:', this.selectedCustomer.id);\n      this.customerService.updateCustomer(this.selectedCustomer.id, customerData).subscribe({\n        next: updatedCustomer => {\n          console.log('Customer updated successfully:', updatedCustomer);\n          this.saving = false;\n          this.closeCustomerModal();\n          // Obnovit seznam\n          this.loadCustomers();\n        },\n        error: err => {\n          console.error('Chyba při aktualizaci zákazníka', err);\n          console.error('Status:', err.status);\n          console.error('Status text:', err.statusText);\n          console.error('Error details:', err.error);\n          this.error = `Chyba při aktualizaci zákazníka: ${err.status} ${err.statusText}`;\n          this.saving = false;\n        }\n      });\n    } else {\n      // Vytvoření nového zákazníka\n      this.customerService.createCustomer(customerData).subscribe({\n        next: createdCustomer => {\n          console.log('Customer created successfully:', createdCustomer);\n          this.saving = false;\n          // Zavřít modal\n          this.closeCustomerModal();\n          // Obnovit seznam\n          this.loadCustomers();\n        },\n        error: err => {\n          console.error('Chyba při ukládání zákazníka', err);\n          console.error('Status:', err.status);\n          console.error('Status text:', err.statusText);\n          console.error('Error details:', err.error);\n          this.error = `Chyba při ukládání zákazníka: ${err.status} ${err.statusText}`;\n          this.saving = false;\n        }\n      });\n    }\n  }\n  loadCustomers() {\n    this.loading = true;\n    // Použití filtrů při načítání dat\n    this.customerService.getCustomers(this.currentFilters).subscribe({\n      next: customers => {\n        console.log('Načteni zákazníci:', customers);\n        this.customers = customers || [];\n        // Kontakty jsou již načteny z backendu, není potřeba je načítat znovu\n        // Pouze logujeme počet kontaktů pro kontrolu\n        this.customers.forEach(customer => {\n          if (customer.contacts) {\n            console.log(`Zákazník ${customer.name} má ${customer.contacts.length} kontaktů`);\n          }\n        });\n        this.filteredCustomers = [...this.customers];\n        // Řazení stále provádíme na klientské straně\n        this.applyFiltersAndSort();\n        this.updatePaginatedCustomers();\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání zákazníků', err);\n        this.loading = false;\n      }\n    });\n  }\n  /**\r\n   * Kopírování API klíče do schránky\r\n   * @param inputElement Reference na input element s API klíčem\r\n   */\n  copyApiKey(inputElement) {\n    inputElement.select();\n    document.execCommand('copy');\n    this.modalService.alert('API klíč byl zkopírován do schránky.', 'Informace', 'OK', 'btn-success');\n  }\n  /**\r\n   * Regenerace API klíče pro instanci\r\n   * @param instanceId ID instance\r\n   */\n  regenerateApiKey(instanceId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const confirmed = yield _this5.modalService.confirm('Opravdu chcete regenerovat API klíč? Všechny aplikace používající stávající klíč budou muset být aktualizovány.', 'Regenerace API klíče', 'Regenerovat', 'Zrušit', 'btn-warning', 'btn-secondary');\n      if (confirmed) {\n        _this5.regeneratingApiKey = true;\n        _this5.instanceService.regenerateApiKey(instanceId).subscribe({\n          next: () => {\n            // Po úspěšné regeneraci načti aktualizovaná data instance\n            _this5.instanceService.getInstance(instanceId).subscribe({\n              next: updatedInstance => {\n                // Aktualizovat instanci v seznamu\n                const index = _this5.instances.findIndex(i => i.id === instanceId);\n                if (index !== -1) {\n                  _this5.instances[index] = updatedInstance;\n                }\n                // Aktualizovat vybranou instanci\n                if (_this5.selectedInstanceForVersion && _this5.selectedInstanceForVersion.id === instanceId) {\n                  _this5.selectedInstanceForVersion = updatedInstance;\n                }\n                _this5.regeneratingApiKey = false;\n                _this5.modalService.alert('API klíč byl úspěšně regenerován.', 'Informace', 'OK', 'btn-success');\n              },\n              error: err => {\n                console.error('Chyba při získávání aktualizované instance', err);\n                _this5.regeneratingApiKey = false;\n                _this5.modalService.alert(`Chyba při získávání aktualizované instance: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n              }\n            });\n          },\n          error: err => {\n            console.error('Chyba při regeneraci API klíče', err);\n            _this5.regeneratingApiKey = false;\n            _this5.modalService.alert(`Chyba při regeneraci API klíče: ${err.status} ${err.statusText}`, 'Chyba', 'Zavřít', 'btn-danger');\n          }\n        });\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function CustomersComponent_Factory(t) {\n      return new (t || CustomersComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContactService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.InstanceVersionService), i0.ɵɵdirectiveInject(i6.VersionService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i9.CertificateService), i0.ɵɵdirectiveInject(i10.ModalService), i0.ɵɵdirectiveInject(i11.HttpClient), i0.ɵɵdirectiveInject(i12.MonitoringService), i0.ɵɵdirectiveInject(i13.Router), i0.ɵɵdirectiveInject(i14.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomersComponent,\n      selectors: [[\"app-customers\"]],\n      decls: 299,\n      vars: 127,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"btn btn-success text-white\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"routerLink\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-body\"], [3, \"entityType\", \"fields\", \"filterChange\"], [\"class\", \"d-flex justify-content-center mt-4\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-4\", 4, \"ngIf\"], [\"class\", \"alert alert-info mt-4\", 4, \"ngIf\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-between align-items-center mt-3\", 4, \"ngIf\"], [\"id\", \"customerModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"customerModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"customerModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"name\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"abbreviation\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"abbreviation\", \"formControlName\", \"abbreviation\", 1, \"form-control\"], [\"for\", \"taxId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taxId\", \"formControlName\", \"taxId\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"website\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"website\", \"formControlName\", \"website\", 1, \"form-control\"], [1, \"col-md-12\", \"mb-3\"], [\"for\", \"street\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"street\", \"formControlName\", \"street\", 1, \"form-control\"], [1, \"col-md-4\", \"mb-3\"], [\"for\", \"city\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"city\", \"formControlName\", \"city\", 1, \"form-control\"], [\"for\", \"postalCode\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"postalCode\", \"formControlName\", \"postalCode\", 1, \"form-control\"], [\"for\", \"country\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"country\", \"formControlName\", \"country\", 1, \"form-control\"], [1, \"mb-3\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"class\", \"mt-4 mb-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"bi\", \"bi-x-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", 4, \"ngIf\"], [\"class\", \"bi bi-save me-1\", 4, \"ngIf\"], [\"id\", \"customerDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"customerDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"customerDetailModalLabel\", 1, \"modal-title\"], [\"class\", \"modal-body\", 4, \"ngIf\"], [\"id\", \"contactModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"contactModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [\"id\", \"contactModalLabel\", 1, \"modal-title\"], [\"for\", \"firstName\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\", 3, \"ngClass\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", 1, \"form-control\"], [\"for\", \"position\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"position\", \"formControlName\", \"position\", 1, \"form-control\"], [\"for\", \"contactEmail\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"contactEmail\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"contactPhone\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"contactPhone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"contactNote\", 1, \"form-label\"], [\"id\", \"contactNote\", \"formControlName\", \"notes\", \"rows\", \"2\", 1, \"form-control\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isPrimary\", \"formControlName\", \"isPrimary\", 1, \"form-check-input\"], [\"for\", \"isPrimary\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"id\", \"instanceModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"instanceName\", \"formControlName\", \"name\", 1, \"form-control\"], [\"for\", \"instanceServerUrl\", 1, \"form-label\", \"required-field\"], [\"type\", \"text\", \"id\", \"instanceServerUrl\", \"formControlName\", \"serverUrl\", 1, \"form-control\"], [\"for\", \"instanceExpirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"instanceExpirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"instanceStatus\", 1, \"form-label\"], [\"id\", \"instanceStatus\", \"formControlName\", \"status\", 1, \"form-select\"], [\"value\", \"Active\"], [\"value\", \"Blocked\"], [\"value\", \"Expired\"], [\"value\", \"Trial\"], [\"value\", \"Maintenance\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"form-label\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"for\", \"instanceNotes\", 1, \"form-label\"], [\"id\", \"instanceNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"id\", \"instanceVersionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceVersionModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\", 2, \"z-index\", \"1070 !important\"], [\"id\", \"instanceVersionModalLabel\", 1, \"modal-title\"], [\"for\", \"instanceName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceName\", \"disabled\", \"\", 1, \"form-control\", 3, \"value\"], [\"for\", \"versionId\", 1, \"form-label\", \"required-field\"], [\"id\", \"versionId\", \"formControlName\", \"versionId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"installedByUserId\", 1, \"form-label\", \"required-field\"], [\"id\", \"installedByUserId\", \"formControlName\", \"installedByUserId\", 1, \"form-select\"], [\"for\", \"instanceVersionNotes\", 1, \"form-label\"], [\"id\", \"instanceVersionNotes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\"], [\"id\", \"instanceDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"instanceDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"instanceDetailModalLabel\", 1, \"modal-title\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"mt-3\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"me-2\", 3, \"disabled\", \"routerLink\", \"click\"], [1, \"bi\", \"bi-graph-up\", \"me-1\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\", \"me-1\"], [\"id\", \"certificateGeneratedModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"certificateGeneratedModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [\"id\", \"certificateGeneratedModalLabel\", 1, \"modal-title\"], [1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"btn\", \"btn-success\", \"text-white\", 3, \"routerLink\"], [1, \"bi\", \"bi-magic\", \"me-2\"], [1, \"d-none\", \"d-md-inline\"], [1, \"d-inline\", \"d-md-none\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [1, \"bi\", \"bi-building-fill-add\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mt-4\"], [1, \"alert\", \"alert-info\", \"mt-4\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [1, \"sortable-header\", 3, \"click\"], [1, \"bi\"], [1, \"sortable-header\", \"d-none\", \"d-md-table-cell\", 3, \"click\"], [1, \"sortable-header\", \"d-none\", \"d-lg-table-cell\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-md-none\", \"small\", \"text-muted\"], [1, \"d-lg-none\", \"small\", \"text-muted\"], [1, \"d-none\", \"d-md-table-cell\"], [1, \"d-none\", \"d-lg-table-cell\"], [1, \"btn-group\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-eye-fill\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Upravit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-danger\", \"title\", \"Smazat\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-3\"], [1, \"pagination-info\"], [\"aria-label\", \"Str\\u00E1nkov\\u00E1n\\u00ED\"], [1, \"pagination\", \"mb-0\"], [1, \"page-item\"], [\"href\", \"javascript:void(0)\", 1, \"page-link\", 3, \"click\"], [1, \"bi\", \"bi-chevron-double-left\"], [1, \"bi\", \"bi-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-chevron-right\"], [1, \"bi\", \"bi-chevron-double-right\"], [1, \"alert\", \"alert-danger\", \"mb-3\"], [1, \"invalid-feedback\"], [1, \"mt-4\", \"mb-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-person-plus-fill\", \"me-1\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [\"class\", \"badge bg-primary ms-2\", 4, \"ngIf\"], [3, \"href\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"badge\", \"bg-primary\", \"ms-2\"], [3, \"href\"], [1, \"bi\", \"bi-plus-circle-fill\", \"me-1\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"spinner-border spinner-border-sm\", \"role\", \"status\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Zobrazit metriky\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"routerLink\"], [1, \"bi\", \"bi-graph-up\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\"], [\"type\", \"button\", \"title\", \"P\\u0159idat verzi\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"ms-2\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"], [1, \"bi\", \"bi-save\", \"me-1\"], [1, \"row\", \"mb-4\"], [1, \"col-md-6\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-light\"], [\"class\", \"row\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"mb-3\", \"mt-4\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"bg-primary\", \"text-white\"], [1, \"card-body\", \"text-center\"], [1, \"display-4\"], [1, \"card\", \"bg-success\", \"text-white\"], [1, \"card\", \"bg-info\", \"text-white\"], [1, \"card\", \"bg-warning\", \"text-dark\"], [1, \"card-title\"], [1, \"alert\", \"alert-danger\"], [1, \"text-danger\"], [\"for\", \"instanceBlockReason\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"instanceBlockReason\", \"formControlName\", \"blockReason\", 1, \"form-control\"], [3, \"value\"], [1, \"row\", \"mb-3\"], [1, \"col-md-12\"], [1, \"input-group\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"value\"], [\"apiKeyInput\", \"\"], [\"type\", \"button\", 1, \"btn\", \"input-group-button\", 3, \"click\"], [1, \"bi\", \"bi-clipboard\"], [\"type\", \"button\", 1, \"btn\", \"input-group-button\", 3, \"disabled\", \"click\"], [\"class\", \"bi bi-arrow-repeat\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mt-2\"], [1, \"text-muted\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"routerLink\", \"click\"], [1, \"bi\", \"bi-shield-lock\", \"me-1\"], [1, \"mb-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [\"class\", \"bi bi-shield-plus me-2\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\", \"me-2\"], [1, \"bi\", \"bi-arrow-repeat\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"bi\", \"bi-shield-plus\", \"me-2\"], [1, \"bi\", \"bi-info-circle\", \"me-2\"], [1, \"bi\", \"bi-gear\", \"me-1\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [\"class\", \"ms-2\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [1, \"ms-2\", 3, \"ngClass\"], [1, \"text-nowrap\"], [1, \"mt-3\"], [1, \"list-group\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-monospace\", \"font-weight-bold\"], [1, \"alert\", \"alert-warning\", \"mt-3\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-download\", \"me-2\"]],\n      template: function CustomersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Spr\\u00E1va z\\u00E1kazn\\u00EDk\\u016F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2);\n          i0.ɵɵtemplate(5, CustomersComponent_a_5_Template, 6, 2, \"a\", 3);\n          i0.ɵɵtemplate(6, CustomersComponent_a_6_Template, 6, 2, \"a\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"app-advanced-filter\", 7);\n          i0.ɵɵlistener(\"filterChange\", function CustomersComponent_Template_app_advanced_filter_filterChange_9_listener($event) {\n            return ctx.onFilterChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, CustomersComponent_div_10_Template, 4, 0, \"div\", 8);\n          i0.ɵɵtemplate(11, CustomersComponent_div_11_Template, 2, 1, \"div\", 9);\n          i0.ɵɵtemplate(12, CustomersComponent_div_12_Template, 2, 0, \"div\", 10);\n          i0.ɵɵtemplate(13, CustomersComponent_div_13_Template, 23, 31, \"div\", 11);\n          i0.ɵɵtemplate(14, CustomersComponent_div_14_Template, 18, 12, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\", 14)(17, \"div\", 15)(18, \"div\", 16)(19, \"h5\", 17);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 19);\n          i0.ɵɵtemplate(23, CustomersComponent_div_23_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementStart(24, \"form\", 21);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.saveCustomer();\n          });\n          i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"label\", 24);\n          i0.ɵɵtext(28, \"N\\u00E1zev\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 25);\n          i0.ɵɵtemplate(30, CustomersComponent_div_30_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 23)(32, \"label\", 27);\n          i0.ɵɵtext(33, \"Zkratka\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 28);\n          i0.ɵɵtemplate(35, CustomersComponent_div_35_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23)(38, \"label\", 29);\n          i0.ɵɵtext(39, \"DI\\u010C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"label\", 31);\n          i0.ɵɵtext(43, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 32);\n          i0.ɵɵtemplate(45, CustomersComponent_div_45_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 22)(47, \"div\", 23)(48, \"label\", 33);\n          i0.ɵɵtext(49, \"Telefon\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 23)(52, \"label\", 35);\n          i0.ɵɵtext(53, \"Webov\\u00E9 str\\u00E1nky\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"input\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 22)(56, \"div\", 37)(57, \"label\", 38);\n          i0.ɵɵtext(58, \"Ulice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"input\", 39);\n          i0.ɵɵtemplate(60, CustomersComponent_div_60_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 22)(62, \"div\", 40)(63, \"label\", 41);\n          i0.ɵɵtext(64, \"M\\u011Bsto\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 42);\n          i0.ɵɵtemplate(66, CustomersComponent_div_66_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 40)(68, \"label\", 43);\n          i0.ɵɵtext(69, \"PS\\u010C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 44);\n          i0.ɵɵtemplate(71, CustomersComponent_div_71_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 40)(73, \"label\", 45);\n          i0.ɵɵtext(74, \"Zem\\u011B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(75, \"input\", 46);\n          i0.ɵɵtemplate(76, CustomersComponent_div_76_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 47)(78, \"label\", 48);\n          i0.ɵɵtext(79, \"Pozn\\u00E1mka\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"textarea\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(81, CustomersComponent_div_81_Template, 10, 3, \"div\", 50);\n          i0.ɵɵtemplate(82, CustomersComponent_div_82_Template, 10, 3, \"div\", 50);\n          i0.ɵɵelementStart(83, \"div\", 51)(84, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_84_listener() {\n            return ctx.closeCustomerModal();\n          });\n          i0.ɵɵelement(85, \"i\", 53);\n          i0.ɵɵtext(86, \"Zav\\u0159\\u00EDt \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"button\", 54);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_87_listener() {\n            return ctx.saveCustomer();\n          });\n          i0.ɵɵtemplate(88, CustomersComponent_span_88_Template, 1, 0, \"span\", 55);\n          i0.ɵɵtemplate(89, CustomersComponent_i_89_Template, 1, 0, \"i\", 56);\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(91, \"div\", 57)(92, \"div\", 14)(93, \"div\", 15)(94, \"div\", 16)(95, \"h5\", 58);\n          i0.ɵɵtext(96, \"Detail z\\u00E1kazn\\u00EDka\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(97, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(98, CustomersComponent_div_98_Template, 59, 22, \"div\", 59);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"div\", 60)(100, \"div\", 61)(101, \"div\", 15)(102, \"div\", 16)(103, \"h5\", 62);\n          i0.ɵɵtext(104);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(105, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"div\", 19);\n          i0.ɵɵtemplate(107, CustomersComponent_div_107_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementStart(108, \"form\", 21);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_108_listener() {\n            return ctx.saveContact();\n          });\n          i0.ɵɵelementStart(109, \"div\", 22)(110, \"div\", 23)(111, \"label\", 63);\n          i0.ɵɵtext(112, \"Jm\\u00E9no\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(113, \"input\", 64);\n          i0.ɵɵtemplate(114, CustomersComponent_div_114_Template, 2, 0, \"div\", 65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"div\", 23)(116, \"label\", 66);\n          i0.ɵɵtext(117, \"P\\u0159\\u00EDjmen\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(118, \"input\", 67);\n          i0.ɵɵtemplate(119, CustomersComponent_div_119_Template, 2, 0, \"div\", 65);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(120, \"div\", 47)(121, \"label\", 68);\n          i0.ɵɵtext(122, \"Pozice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(123, \"input\", 69);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"div\", 47)(125, \"label\", 70);\n          i0.ɵɵtext(126, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(127, \"input\", 71);\n          i0.ɵɵtemplate(128, CustomersComponent_div_128_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"div\", 47)(130, \"label\", 72);\n          i0.ɵɵtext(131, \"Telefon\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(132, \"input\", 73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"div\", 47)(134, \"label\", 74);\n          i0.ɵɵtext(135, \"Pozn\\u00E1mka\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(136, \"textarea\", 75);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 76);\n          i0.ɵɵelement(138, \"input\", 77);\n          i0.ɵɵelementStart(139, \"label\", 78);\n          i0.ɵɵtext(140, \"Hlavn\\u00ED kontakt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 51)(142, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_142_listener() {\n            return ctx.closeContactModal();\n          });\n          i0.ɵɵelement(143, \"i\", 53);\n          i0.ɵɵtext(144, \"Zav\\u0159\\u00EDt \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(145, \"button\", 79);\n          i0.ɵɵtemplate(146, CustomersComponent_span_146_Template, 1, 0, \"span\", 55);\n          i0.ɵɵtemplate(147, CustomersComponent_i_147_Template, 1, 0, \"i\", 56);\n          i0.ɵɵtext(148);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(149, \"div\", 80)(150, \"div\", 61)(151, \"div\", 15)(152, \"div\", 16)(153, \"h5\", 81);\n          i0.ɵɵtext(154);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(155, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"div\", 19);\n          i0.ɵɵtemplate(157, CustomersComponent_div_157_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementStart(158, \"form\", 21);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_158_listener() {\n            return ctx.saveInstance();\n          });\n          i0.ɵɵelementStart(159, \"div\", 47)(160, \"label\", 82);\n          i0.ɵɵtext(161, \"N\\u00E1zev instance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(162, \"input\", 83);\n          i0.ɵɵtemplate(163, CustomersComponent_div_163_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(164, \"div\", 47)(165, \"label\", 84);\n          i0.ɵɵtext(166, \"URL serveru\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(167, \"input\", 85);\n          i0.ɵɵtemplate(168, CustomersComponent_div_168_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(169, \"div\", 47)(170, \"label\", 86);\n          i0.ɵɵtext(171, \"Datum expirace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(172, \"input\", 87);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"div\", 47)(174, \"label\", 88);\n          i0.ɵɵtext(175, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"select\", 89)(177, \"option\", 90);\n          i0.ɵɵtext(178, \"Aktivn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(179, \"option\", 91);\n          i0.ɵɵtext(180, \"Blokovan\\u00E1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(181, \"option\", 92);\n          i0.ɵɵtext(182, \"Expirovan\\u00E1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(183, \"option\", 93);\n          i0.ɵɵtext(184, \"Zku\\u0161ebn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(185, \"option\", 94);\n          i0.ɵɵtext(186, \"\\u00DAdr\\u017Eba\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(187, CustomersComponent_div_187_Template, 4, 0, \"div\", 95);\n          i0.ɵɵelementStart(188, \"div\", 47)(189, \"label\", 96);\n          i0.ɵɵtext(190, \"Povolen\\u00E9 moduly\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(191, \"div\", 97);\n          i0.ɵɵelement(192, \"input\", 98);\n          i0.ɵɵelementStart(193, \"label\", 99);\n          i0.ɵɵtext(194, \"Reportov\\u00E1n\\u00ED\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(195, \"div\", 97);\n          i0.ɵɵelement(196, \"input\", 100);\n          i0.ɵɵelementStart(197, \"label\", 101);\n          i0.ɵɵtext(198, \"Pokro\\u010Dil\\u00E1 bezpe\\u010Dnost\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(199, \"div\", 97);\n          i0.ɵɵelement(200, \"input\", 102);\n          i0.ɵɵelementStart(201, \"label\", 103);\n          i0.ɵɵtext(202, \"API integrace\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(203, \"div\", 97);\n          i0.ɵɵelement(204, \"input\", 104);\n          i0.ɵɵelementStart(205, \"label\", 105);\n          i0.ɵɵtext(206, \"Export dat\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(207, \"div\", 97);\n          i0.ɵɵelement(208, \"input\", 106);\n          i0.ɵɵelementStart(209, \"label\", 107);\n          i0.ɵɵtext(210, \"P\\u0159izp\\u016Fsoben\\u00ED\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(211, \"div\", 47)(212, \"label\", 108);\n          i0.ɵɵtext(213, \"Pozn\\u00E1mka\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(214, \"textarea\", 109);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(215, \"div\", 51)(216, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_216_listener() {\n            return ctx.closeInstanceModal();\n          });\n          i0.ɵɵelement(217, \"i\", 53);\n          i0.ɵɵtext(218, \"Zav\\u0159\\u00EDt \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(219, \"button\", 79);\n          i0.ɵɵtemplate(220, CustomersComponent_span_220_Template, 1, 0, \"span\", 55);\n          i0.ɵɵtemplate(221, CustomersComponent_i_221_Template, 1, 0, \"i\", 56);\n          i0.ɵɵtext(222);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(223, \"div\", 110)(224, \"div\", 61)(225, \"div\", 15)(226, \"div\", 16)(227, \"h5\", 111);\n          i0.ɵɵtext(228);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(229, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(230, \"div\", 19);\n          i0.ɵɵtemplate(231, CustomersComponent_div_231_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementStart(232, \"form\", 21);\n          i0.ɵɵlistener(\"ngSubmit\", function CustomersComponent_Template_form_ngSubmit_232_listener() {\n            return ctx.saveInstanceVersion();\n          });\n          i0.ɵɵelementStart(233, \"div\", 47)(234, \"label\", 112);\n          i0.ɵɵtext(235, \"Instance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(236, \"input\", 113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(237, \"div\", 47)(238, \"label\", 114);\n          i0.ɵɵtext(239, \"Verze:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(240, \"select\", 115)(241, \"option\", 116);\n          i0.ɵɵtext(242, \"-- Vyberte verzi --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(243, CustomersComponent_option_243_Template, 3, 6, \"option\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(244, CustomersComponent_div_244_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(245, \"div\", 47)(246, \"label\", 118);\n          i0.ɵɵtext(247, \"Instaloval:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(248, \"select\", 119)(249, \"option\", 116);\n          i0.ɵɵtext(250, \"-- Vyberte u\\u017Eivatele --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(251, CustomersComponent_option_251_Template, 2, 3, \"option\", 117);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(252, CustomersComponent_div_252_Template, 2, 0, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"div\", 47)(254, \"label\", 120);\n          i0.ɵɵtext(255, \"Pozn\\u00E1mka:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(256, \"textarea\", 121);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(257, \"div\", 51)(258, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_258_listener() {\n            return ctx.closeInstanceVersionModal();\n          });\n          i0.ɵɵelement(259, \"i\", 53);\n          i0.ɵɵtext(260, \"Zav\\u0159\\u00EDt \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(261, \"button\", 79);\n          i0.ɵɵtemplate(262, CustomersComponent_span_262_Template, 1, 0, \"span\", 55);\n          i0.ɵɵtemplate(263, CustomersComponent_i_263_Template, 1, 0, \"i\", 56);\n          i0.ɵɵtext(264);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(265, \"div\", 122)(266, \"div\", 14)(267, \"div\", 15)(268, \"div\", 16)(269, \"h5\", 123);\n          i0.ɵɵtext(270);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(271, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(272, \"div\", 19);\n          i0.ɵɵtemplate(273, CustomersComponent_div_273_Template, 82, 39, \"div\", 124);\n          i0.ɵɵelementStart(274, \"div\", 125)(275, \"button\", 126);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_275_listener() {\n            return ctx.closeInstanceDetailModal();\n          });\n          i0.ɵɵelement(276, \"i\", 127);\n          i0.ɵɵtext(277, \"Metriky \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(278, \"button\", 128);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_278_listener() {\n            return ctx.closeInstanceDetailModal();\n          });\n          i0.ɵɵelement(279, \"i\", 53);\n          i0.ɵɵtext(280, \"Zav\\u0159\\u00EDt \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(281, \"button\", 54);\n          i0.ɵɵlistener(\"click\", function CustomersComponent_Template_button_click_281_listener() {\n            return ctx.editInstance(ctx.selectedInstanceForVersion);\n          });\n          i0.ɵɵelement(282, \"i\", 129);\n          i0.ɵɵtext(283, \"Upravit \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(284, \"div\", 130)(285, \"div\", 61)(286, \"div\", 15)(287, \"div\", 131)(288, \"h5\", 132);\n          i0.ɵɵtext(289, \"Certifik\\u00E1t vygenerov\\u00E1n\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(290, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(291, \"div\", 19)(292, \"div\", 133);\n          i0.ɵɵelement(293, \"i\", 134);\n          i0.ɵɵtext(294, \" Certifik\\u00E1t byl \\u00FAsp\\u011B\\u0161n\\u011B vygenerov\\u00E1n. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(295, CustomersComponent_div_295_Template, 29, 6, \"div\", 124);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(296, \"div\", 51)(297, \"button\", 135);\n          i0.ɵɵtext(298, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_12_0;\n          let tmp_13_0;\n          let tmp_14_0;\n          let tmp_15_0;\n          let tmp_16_0;\n          let tmp_17_0;\n          let tmp_18_0;\n          let tmp_19_0;\n          let tmp_20_0;\n          let tmp_21_0;\n          let tmp_22_0;\n          let tmp_23_0;\n          let tmp_24_0;\n          let tmp_25_0;\n          let tmp_26_0;\n          let tmp_27_0;\n          let tmp_28_0;\n          let tmp_29_0;\n          let tmp_30_0;\n          let tmp_31_0;\n          let tmp_32_0;\n          let tmp_43_0;\n          let tmp_44_0;\n          let tmp_45_0;\n          let tmp_46_0;\n          let tmp_47_0;\n          let tmp_48_0;\n          let tmp_56_0;\n          let tmp_57_0;\n          let tmp_58_0;\n          let tmp_59_0;\n          let tmp_60_0;\n          let tmp_69_0;\n          let tmp_71_0;\n          let tmp_72_0;\n          let tmp_74_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"entityType\", \"customers\")(\"fields\", ctx.filterFields);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.filteredCustomers.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Upravit z\\u00E1kazn\\u00EDka\" : \"P\\u0159idat z\\u00E1kazn\\u00EDka\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.customerForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(98, _c6, (tmp_12_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_12_0.invalid, (tmp_12_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_12_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_13_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.customerForm.get(\"name\")) == null ? null : tmp_14_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(101, _c6, (tmp_15_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_15_0.invalid, (tmp_15_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_15_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_16_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_16_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.customerForm.get(\"abbreviation\")) == null ? null : tmp_17_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(104, _c6, (tmp_18_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_18_0.invalid, (tmp_18_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_18_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_19_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_19_0.invalid) && ((tmp_19_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_19_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx.customerForm.get(\"email\")) == null ? null : tmp_20_0.touched));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(107, _c6, (tmp_21_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_21_0.invalid, (tmp_21_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_21_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_22_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_22_0.invalid) && ((tmp_22_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_22_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_23_0.invalid) && ((tmp_23_0 = ctx.customerForm.get(\"street\")) == null ? null : tmp_23_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(110, _c6, (tmp_24_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_24_0.invalid, (tmp_24_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_24_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_25_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_25_0.invalid) && ((tmp_25_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_25_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_26_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_26_0.invalid) && ((tmp_26_0 = ctx.customerForm.get(\"city\")) == null ? null : tmp_26_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(113, _c6, (tmp_27_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_27_0.invalid, (tmp_27_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_27_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_28_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_28_0.invalid) && ((tmp_28_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_28_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_29_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_29_0.invalid) && ((tmp_29_0 = ctx.customerForm.get(\"postalCode\")) == null ? null : tmp_29_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(116, _c6, (tmp_30_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_30_0.invalid, (tmp_30_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_30_0.valid));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_31_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_31_0.invalid) && ((tmp_31_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_31_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_32_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_32_0.invalid) && ((tmp_32_0 = ctx.customerForm.get(\"country\")) == null ? null : tmp_32_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.customerForm.invalid || ctx.saving);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.saving);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.saving);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.isEditMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedCustomer);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditContactMode ? \"Upravit kontakt\" : \"P\\u0159idat kontakt\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.contactError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.contactForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(119, _c6, (tmp_43_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_43_0.invalid, (tmp_43_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_43_0.valid));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_44_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_44_0.invalid) && ((tmp_44_0 = ctx.contactForm.get(\"firstName\")) == null ? null : tmp_44_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(122, _c6, (tmp_45_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_45_0.invalid, (tmp_45_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_45_0.valid));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_46_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_46_0.invalid) && ((tmp_46_0 = ctx.contactForm.get(\"lastName\")) == null ? null : tmp_46_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_47_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_47_0.invalid) && ((tmp_47_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_47_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_48_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_48_0.invalid) && ((tmp_48_0 = ctx.contactForm.get(\"email\")) == null ? null : tmp_48_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"disabled\", ctx.contactForm.invalid || ctx.savingContact);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingContact);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.savingContact);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.isEditContactMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditInstanceMode ? \"Upravit instanci DIS\" : \"P\\u0159idat instanci DIS\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.instanceError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.instanceForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_56_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_56_0.invalid) && ((tmp_56_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_56_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_57_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_57_0.invalid) && ((tmp_57_0 = ctx.instanceForm.get(\"name\")) == null ? null : tmp_57_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_58_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_58_0.invalid) && ((tmp_58_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_58_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_59_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_59_0.invalid) && ((tmp_59_0 = ctx.instanceForm.get(\"serverUrl\")) == null ? null : tmp_59_0.touched));\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_60_0 = ctx.instanceForm.get(\"status\")) == null ? null : tmp_60_0.value) === \"Blocked\");\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"disabled\", ctx.instanceForm.invalid || ctx.savingInstance);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingInstance);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.savingInstance);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.isEditInstanceMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.isEditInstanceVersionMode ? \"Upravit verzi instance\" : \"P\\u0159idat verzi instance\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.instanceVersionError);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.instanceVersionForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_69_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_69_0.invalid) && ((tmp_69_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_69_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableVersions);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_71_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_71_0.invalid) && ((tmp_71_0 = ctx.instanceVersionForm.get(\"versionId\")) == null ? null : tmp_71_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_72_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_72_0.invalid) && ((tmp_72_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_72_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableUsers);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_74_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_74_0.invalid) && ((tmp_74_0 = ctx.instanceVersionForm.get(\"installedByUserId\")) == null ? null : tmp_74_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", ctx.instanceVersionForm.invalid || ctx.savingInstanceVersion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.savingInstanceVersion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.savingInstanceVersion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.isEditInstanceVersionMode ? \"Aktualizovat\" : \"Ulo\\u017Eit\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate2(\"Detail instance \", ctx.selectedCustomer == null ? null : ctx.selectedCustomer.abbreviation, \" - \", ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.name, \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedInstanceForVersion);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedInstanceForVersion)(\"routerLink\", i0.ɵɵpureFunction1(125, _c3, ctx.selectedInstanceForVersion == null ? null : ctx.selectedInstanceForVersion.id));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedInstanceForVersion);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.generatedCertificate);\n        }\n      },\n      dependencies: [i15.NgClass, i15.NgForOf, i15.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i13.RouterLink, i16.AdvancedFilterComponent, i15.DecimalPipe, i15.DatePipe],\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAEA,SAAiCA,UAAU,QAAwD,gBAAgB;AACnH,SAASC,WAAW,QAAQ,gCAAgC;AAe5D,SAA2FC,cAAc,QAAQ,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;ICdrIC,8BAA0F;IACxFA,yBAAgC;IAAAA,iCAAiC;IAAAA,2DAA4B;IAAAA,iBAAO;IAAAA,iCAAiC;IAAAA,6BAAQ;IAAAA,iBAAO;;;IADnJA,uDAAmC;;;;;;;;IAGtCA,8BAA6E;IAC3EA,yBAA4C;IAAAA,iCAAiC;IAAAA,+CAAgB;IAAAA,iBAAO;IAAAA,iCAAiC;IAAAA,2BAAM;IAAAA,iBAAO;;;IADjJA,uDAAiC;;;;;IAgBpCA,gCAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAEAA,gCAAgG;IAC9FA,2EACF;IAAAA,iBAAM;;;;;;IA8DMA,mCAAgH;IAAjEA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAAsB;IAAA,EAAC;IAC7EA,yBAAiC;IACnCA,iBAAS;;;;;;IACTA,mCAAgH;IAAlEA;MAAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC9EA,yBAAgC;IAClCA,iBAAS;;;;;;IAtBfA,0BAAgD;IAEvCA,YAAmB;IAAAA,iBAAM;IAC9BA,gCAAwC;IAAAA,YAAoC;IAAAA,iBAAM;IAClFA,gCAAwC;IAAAA,YAA4B;IAAAA,iBAAM;IAC1EA,gCAAwC;IAAAA,YAA2C;IAAAA,iBAAM;IACzFA,iCAAwC;IAAAA,aAA4C;IAAAA,iBAAM;IAE5FA,gCAAmC;IAAAA,aAA2B;IAAAA,iBAAK;IACnEA,gCAAmC;IAAAA,aAAwB;IAAAA,iBAAK;IAChEA,gCAAmC;IAAAA,aAAiC;IAAAA,iBAAK;IACzEA,gCAAmC;IAAAA,aAAkC;IAAAA,iBAAK;IAC1EA,2BAAI;IAE4CA;MAAA;MAAA;MAAA;MAAA,OAASA,uDAA4B;IAAA,EAAC;IAChFA,0BAA8B;IAChCA,iBAAS;IACTA,0FAES;IACTA,0FAES;IACXA,iBAAM;;;;;IArBDA,eAAmB;IAAnBA,uCAAmB;IACgBA,eAAoC;IAApCA,iEAAoC;IACpCA,eAA4B;IAA5BA,8DAA4B;IAC5BA,eAA2C;IAA3CA,wEAA2C;IAC3CA,eAA4C;IAA5CA,yEAA4C;IAEnDA,eAA2B;IAA3BA,+CAA2B;IAC3BA,eAAwB;IAAxBA,4CAAwB;IACxBA,eAAiC;IAAjCA,qDAAiC;IACjCA,eAAkC;IAAlCA,sDAAkC;IAMgBA,eAAa;IAAbA,sCAAa;IAGZA,eAAa;IAAbA,sCAAa;;;;;;IA/D3GA,gCAA8F;IAIlFA;MAAAA;MAAA;MAAA,OAASA,8BAAO,MAAM,CAAC;IAAA,EAAC;IAC1BA,4BACA;IAAAA,yBAG8C;IAChDA,iBAAK;IACLA,+BAAoF;IAAhFA;MAAAA;MAAA;MAAA,OAASA,8BAAO,cAAc,CAAC;IAAA,EAAC;IAClCA,yBACA;IAAAA,yBAGsD;IACxDA,iBAAK;IACLA,gCAAiF;IAA7EA;MAAAA;MAAA;MAAA,OAASA,8BAAO,WAAW,CAAC;IAAA,EAAC;IAC/BA,0BACA;IAAAA,0BAGmD;IACrDA,iBAAK;IACLA,gCAAqF;IAAjFA;MAAAA;MAAA;MAAA,OAASA,8BAAO,eAAe,CAAC;IAAA,EAAC;IACnCA,2BACA;IAAAA,0BAGuD;IACzDA,iBAAK;IACLA,gCAAsF;IAAlFA;MAAAA;MAAA;MAAA,OAASA,8BAAO,gBAAgB,CAAC;IAAA,EAAC;IACpCA,2BACA;IAAAA,0BAGwD;IAC1DA,iBAAK;IACLA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,8EAyBK;IACPA,iBAAQ;;;;IA9DAA,eAAqE;IAArEA,4FAAqE;IAOrEA,eAA6E;IAA7EA,oGAA6E;IAO7EA,eAA0E;IAA1EA,iGAA0E;IAO1EA,eAA8E;IAA9EA,qGAA8E;IAO9EA,eAA+E;IAA/EA,sGAA+E;IAQ5DA,eAAqB;IAArBA,mDAAqB;;;;;;IA+C9CA,+BAA2F;IAC1CA;MAAA;MAAA;MAAA;MAAA,OAASA,6CAAkB;IAAA,EAAC;IAACA,YAAU;IAAAA,iBAAI;;;;;IADvCA,0DAAqC;IACZA,eAAU;IAAVA,8BAAU;;;;;;IAjB9FA,gCAA+H;IAE3HA,YACF;IAAAA,iBAAM;IACNA,gCAA8B;IAGuBA;MAAAA;MAAA;MAAA,OAASA,oCAAa,CAAC,CAAC;IAAA,EAAC;IACtEA,yBAAyC;IAC3CA,iBAAI;IAENA,+BAA2D;IACVA;MAAAA;MAAA;MAAA,OAASA,0DAA2B,CAAC,CAAC;IAAA,EAAC;IACpFA,0BAAkC;IACpCA,iBAAI;IAENA,4EAEK;IACLA,gCAAoE;IACnBA;MAAAA;MAAA;MAAA,OAASA,0DAA2B,CAAC,CAAC;IAAA,EAAC;IACpFA,0BAAmC;IACrCA,iBAAI;IAENA,gCAAoE;IACnBA;MAAAA;MAAA;MAAA,OAASA,uDAAwB;IAAA,EAAC;IAC/EA,0BAA0C;IAC5CA,iBAAI;;;;IAzBRA,eACF;IADEA,kPACF;IAG0BA,eAAoC;IAApCA,oDAAoC;IAKpCA,eAAoC;IAApCA,oDAAoC;IAKrCA,eAAY;IAAZA,0CAAY;IAGXA,eAA6C;IAA7CA,oEAA6C;IAK7CA,eAA6C;IAA7CA,oEAA6C;;;;;IAqBvEA,gCAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAMMA,gCAA6G;IAC3GA,4CACF;IAAAA,iBAAM;;;;;IAMNA,gCAA6H;IAC3HA,sGACF;IAAAA,iBAAM;;;;;IAcJA,4BAA2D;IAAAA,gDAAsB;IAAAA,iBAAO;;;;;IAD1FA,gCAA+G;IAC7GA,8EAAwF;IAC1FA,iBAAM;;;;;IADGA,eAAkD;IAAlDA,6IAAkD;;;;;IAqB3DA,gCAAiH;IAC/GA,uCACF;IAAAA,iBAAM;;;;;IAQNA,gCAA6G;IAC3GA,4CACF;IAAAA,iBAAM;;;;;IAMNA,gCAAyH;IACvHA,0CACF;IAAAA,iBAAM;;;;;IAMNA,gCAAmH;IACjHA,2CACF;IAAAA,iBAAM;;;;;IAkBRA,gCAAmE;IAEjCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAgF;IAC9EA,4EACF;IAAAA,iBAAM;;;;;IAiBIA,iCAA8D;IAAAA,kCAAQ;IAAAA,iBAAO;;;;;IAI7EA,8BAA2D;IAAAA,YAAmB;IAAAA,iBAAI;;;;IAAzDA,qFAAiC;IAACA,eAAmB;IAAnBA,uCAAmB;;;;;IAC9EA,4BAA6B;IAAAA,iBAAC;IAAAA,iBAAO;;;;;IAGrCA,8BAAwD;IAAAA,YAAmB;IAAAA,iBAAI;;;;IAAtDA,kFAA8B;IAACA,eAAmB;IAAnBA,uCAAmB;;;;;IAC3EA,4BAA6B;IAAAA,iBAAC;IAAAA,iBAAO;;;;;;IAZzCA,0BAAqC;IAESA,YAA8C;IAAAA,iBAAO;IAC/FA,0FAA6E;IAC/EA,iBAAK;IACLA,0BAAI;IAAAA,YAA6B;IAAAA,iBAAK;IACtCA,0BAAI;IACFA,oFAAkF;IAClFA,0FAAqC;IACvCA,iBAAK;IACLA,2BAAI;IACFA,sFAA+E;IAC/EA,4FAAqC;IACvCA,iBAAK;IACLA,2BAAI;IAE6DA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACzFA,0BAAiC;IACnCA,iBAAS;IACTA,oCAA4G;IAAhDA;MAAA;MAAA;MAAA;MAAA,OAASA,iDAAsB;IAAA,EAAC;IAC1FA,0BAAgC;IAClCA,iBAAS;;;;IAnBLA,eAAmC;IAAnCA,gDAAmC;IAACA,eAA8C;IAA9CA,+EAA8C;IACjFA,eAAuB;IAAvBA,4CAAuB;IAE5BA,eAA6B;IAA7BA,iDAA6B;IAE3BA,eAAmB;IAAnBA,wCAAmB;IAChBA,eAAoB;IAApBA,yCAAoB;IAGvBA,eAAmB;IAAnBA,wCAAmB;IAChBA,eAAoB;IAApBA,yCAAoB;;;;;IAxBrCA,gCAA8E;IAIlEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,qBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,wBAAO;IAAAA,iBAAK;IAChBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,oFAwBK;IACPA,iBAAQ;;;;IAzBkBA,gBAAW;IAAXA,0CAAW;;;;;;IA9B3CA,gCAA0C;IAErBA,oCAAe;IAAAA,iBAAK;IACrCA,mCAA6F;IAAhCA;MAAAA;MAAA;MAAA,OAASA,4CAAqB;IAAA,EAAC;IAC1FA,yBAA2C;IAAAA,oCAC7C;IAAAA,iBAAS;IAGXA,4EAIM;IAENA,4EAEM;IAENA,6EAuCM;IACRA,iBAAM;;;;IAlDEA,eAAqB;IAArBA,8CAAqB;IAMrBA,eAA+C;IAA/CA,gFAA+C;IAI/CA,eAA6C;IAA7CA,8EAA6C;;;;;IAmDnDA,gCAAoE;IAElCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAkF;IAChFA,oEACF;IAAAA,iBAAM;;;;;IAiCIA,iCAA0G;IAC1EA,+CAAW;IAAAA,iBAAO;;;;;;IAElDA,4BAAoD;IAClDA,YACA;IAAAA,mCAE6B;IADrBA;MAAAA;MAAA;MAAA;MAAA,OAASA,gEAAqC;IAAA,EAAC;IAErDA,yBAAsC;IACxCA,iBAAS;;;;;IALTA,eACA;IADAA,0EACA;;;;;;;;;;;;;;;;;;IArBNA,0BAAuC;IACJA;MAAA;MAAA;MAAA;MAAA,OAASA,uDAA4B;IAAA,EAAC;IAACA,YAAmB;IAAAA,iBAAI;IAC/FA,0BAAI;IAAAA,YAAwB;IAAAA,iBAAK;IACjCA,qBAAS;IACTA,0BAAI;IAQAA,YACF;IAAAA,iBAAO;IAETA,2BAAI;IACFA,4FAEO;IACPA,4FAOO;IACTA,iBAAK;IACLA,2BAAI;IAAAA,aAAmD;;IAAAA,iBAAK;IAC5DA,2BAAI;IAAAA,aAAkF;;IAAAA,iBAAK;IAC3FA,2BAAI;IAE6DA;MAAA;MAAA;MAAA;MAAA,OAASA,iDAAsB;IAAA,EAAC;IAC3FA,0BAAiC;IACnCA,iBAAS;IACTA,oCAAqI;IACnIA,0BAA8B;IAChCA,iBAAS;IACTA,oCAA8G;IAAlDA;MAAA;MAAA;MAAA;MAAA,OAASA,oDAAwB;IAAA,EAAC;IAC5FA,0BAAgC;IAClCA,iBAAS;;;;;IAvC2DA,eAAmB;IAAnBA,uCAAmB;IACvFA,eAAwB;IAAxBA,4CAAwB;IAGNA,eAMlB;IANkBA,8UAMlB;IACAA,eACF;IADEA,mFACF;IAGOA,eAA0C;IAA1CA,uEAA0C;IAG1CA,eAA2C;IAA3CA,wEAA2C;IAShDA,eAAmD;IAAnDA,wFAAmD;IACnDA,eAAkF;IAAlFA,0HAAkF;IAMxBA,eAAiD;IAAjDA,yEAAiD;;;;;IAlDvHA,gCAAgF;IAIpEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,6BAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,uBAAM;IAAAA,iBAAK;IACfA,2BAAI;IAAAA,yCAAc;IAAAA,iBAAK;IACvBA,2BAAI;IAAAA,gCAAe;IAAAA,iBAAK;IACxBA,2BAAI;IAAAA,+BAAc;IAAAA,iBAAK;IACvBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,oFA2CK;IACPA,iBAAQ;;;;IA5CmBA,gBAAY;IAAZA,2CAAY;;;;;;IAjC7CA,gCAA0C;IAErBA,4BAAY;IAAAA,iBAAK;IAClCA,mCAA8F;IAAjCA;MAAAA;MAAA;MAAA,OAASA,8CAAsB;IAAA,EAAC;IAC3FA,yBAA2C;IAAAA,qCAC7C;IAAAA,iBAAS;IAGXA,4EAIM;IAENA,4EAEM;IAENA,6EA6DM;IACRA,iBAAM;;;;IAxEEA,eAAsB;IAAtBA,+CAAsB;IAMtBA,eAAiD;IAAjDA,kFAAiD;IAIjDA,eAA+C;IAA/CA,gFAA+C;;;;;IAqEnDA,4BAA0E;;;;;IAC1EA,yBAA+C;;;;;IAsBjDA,yBAAsC;IAAQA,wBAAG;IAAAA,iBAAS;IAACA,YAAgC;IAAAA,iBAAI;;;;IAApCA,eAAgC;IAAhCA,mEAAgC;;;;;IAC3FA,yBAAkC;IAAQA,yBAAI;IAAAA,iBAAS;IAACA,YAA4B;IAAAA,iBAAI;;;;IAAhCA,eAA4B;IAA5BA,+DAA4B;;;;;IAOpFA,yBAAkC;IAAQA,sBAAM;IAAAA,iBAAS;IAACA,YAA4B;IAAAA,iBAAI;;;;IAAhCA,eAA4B;IAA5BA,+DAA4B;;;;;IACtFA,yBAAkC;IAAQA,wBAAQ;IAAAA,iBAAS;IAACA,YAA4B;IAAAA,iBAAI;;;;IAAhCA,eAA4B;IAA5BA,+DAA4B;;;;;IACxFA,yBAAoC;IAAQA,oBAAI;IAAAA,iBAAS;IAACA,YAA8B;IAAAA,iBAAI;;;;IAAlCA,eAA8B;IAA9BA,iEAA8B;;;;;IACxFA,yBAAkC;IAAQA,8BAAS;IAAAA,iBAAS;IAACA,YAA4B;IAAAA,iBAAI;;;;IAAhCA,eAA4B;IAA5BA,+DAA4B;;;;;IAUzFA,gCAAqE;IAEnCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAAkE;IAIpCA,YAAuC;IAAAA,iBAAK;IAClEA,8BAAgB;IAAAA,oCAAe;IAAAA,iBAAI;IAIzCA,gCAA2B;IAGCA,aAA6C;IAAAA,iBAAK;IACxEA,+BAAgB;IAAAA,6CAAkB;IAAAA,iBAAI;IAI5CA,iCAA2B;IAGCA,aAAwC;IAAAA,iBAAK;IACnEA,+BAAgB;IAAAA,2CAAgB;IAAAA,iBAAI;IAI1CA,iCAA2B;IAGCA,aAAkD;IAAAA,iBAAK;IAC7EA,+BAAgB;IAAAA,sDAAsB;IAAAA,iBAAI;IAIhDA,gCAA2B;IAGEA,+BAAS;IAAAA,iBAAK;IACrCA,+BAAgB;IAAQA,qDAAqB;IAAAA,iBAAS;IAACA,aAA+D;;IAAAA,iBAAI;IAC1HA,+BAAgB;IAAQA,4DAA4B;IAAAA,iBAAS;IAACA,aAA4C;IAAAA,iBAAI;IAIpHA,gCAA2B;IAGEA,+DAAqB;IAAAA,iBAAK;IACjDA,+BAAgB;IAAQA,uBAAM;IAAAA,iBAAS;IAACA,aAA8D;IAAAA,iBAAI;IAC1GA,+BAAgB;IAAQA,0CAAe;IAAAA,iBAAS;IAACA,aAAkD;IAAAA,iBAAI;;;;IA3CjFA,eAAuC;IAAvCA,gEAAuC;IAQvCA,eAA6C;IAA7CA,sEAA6C;IAQ7CA,eAAwC;IAAxCA,iEAAwC;IAQxCA,eAAkD;IAAlDA,2EAAkD;IASjBA,gBAA+D;IAA/DA,iHAA+D;IACxDA,eAA4C;IAA5CA,+EAA4C;IAQlEA,eAA8D;IAA9DA,sGAA8D;IACrDA,eAAkD;IAAlDA,qFAAkD;;;;;IAM3GA,gCAA8E;IAC5EA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,yDACF;;;;;IAQJA,gCAAmE;IAEjCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAgF;IAC9EA,4EACF;IAAAA,iBAAM;;;;;IAgBIA,iCAA8D;IAAAA,kCAAQ;IAAAA,iBAAO;;;;;IAI7EA,8BAA2D;IAAAA,YAAmB;IAAAA,iBAAI;;;;IAAzDA,sFAAiC;IAACA,eAAmB;IAAnBA,wCAAmB;;;;;IAG9EA,8BAAwD;IAAAA,YAAmB;IAAAA,iBAAI;;;;IAAtDA,mFAA8B;IAACA,eAAmB;IAAnBA,wCAAmB;;;;;IAV/EA,0BAAqC;IAESA,YAA8C;IAAAA,iBAAO;IAC/FA,2FAA6E;IAC/EA,iBAAK;IACLA,0BAAI;IAAAA,YAAsB;IAAAA,iBAAK;IAC/BA,0BAAI;IACFA,qFAAkF;IACpFA,iBAAK;IACLA,0BAAI;IACFA,uFAA+E;IACjFA,iBAAK;;;;IATGA,eAAmC;IAAnCA,iDAAmC;IAACA,eAA8C;IAA9CA,iFAA8C;IACjFA,eAAuB;IAAvBA,6CAAuB;IAE5BA,eAAsB;IAAtBA,2CAAsB;IAEpBA,eAAmB;IAAnBA,yCAAmB;IAGnBA,eAAmB;IAAnBA,yCAAmB;;;;;IArBjCA,gCAA8E;IAIlEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,qBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,wBAAO;IAAAA,iBAAK;IAGpBA,8BAAO;IACLA,oFAYK;IACPA,iBAAQ;;;;IAbkBA,gBAAW;IAAXA,2CAAW;;;;;IAqBzCA,gCAAoE;IAElCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAkF;IAChFA,oEACF;IAAAA,iBAAM;;;;;IAgCIA,iCAA0G;IAC1EA,+CAAW;IAAAA,iBAAO;;;;;IAElDA,4BAAoD;IAAAA,YAAmC;IAAAA,iBAAO;;;;;IAA1CA,eAAmC;IAAnCA,iEAAmC;;;;;;IAnB3FA,0BAAuC;IACJA;MAAA;MAAA;MAAA;MAAA,OAASA,yDAA4B;IAAA,EAAC;IAACA,YAAmB;IAAAA,iBAAI;IAC/FA,0BAAI;IAAAA,YAAwB;IAAAA,iBAAK;IACjCA,qBAAS;IACTA,0BAAI;IAQAA,YACF;IAAAA,iBAAO;IAETA,2BAAI;IACFA,6FAEO;IACPA,6FAA8F;IAChGA,iBAAK;IACLA,2BAAI;IAAAA,aAAmD;;IAAAA,iBAAK;IAC5DA,2BAAI;IAAAA,aAAkF;;IAAAA,iBAAK;;;;;IArBnBA,eAAmB;IAAnBA,wCAAmB;IACvFA,eAAwB;IAAxBA,6CAAwB;IAGNA,eAMlB;IANkBA,wVAMlB;IACAA,eACF;IADEA,qFACF;IAGOA,eAA0C;IAA1CA,yEAA0C;IAG1CA,eAA2C;IAA3CA,0EAA2C;IAEhDA,eAAmD;IAAnDA,yFAAmD;IACnDA,eAAkF;IAAlFA,4HAAkF;;;;;IApC9FA,gCAAgF;IAIpEA,0BAAK;IAAAA,iBAAK;IACdA,0BAAI;IAAAA,sBAAM;IAAAA,iBAAK;IACfA,0BAAI;IAAAA,6BAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,uBAAM;IAAAA,iBAAK;IACfA,2BAAI;IAAAA,yCAAc;IAAAA,iBAAK;IACvBA,2BAAI;IAAAA,gCAAe;IAAAA,iBAAK;IACxBA,2BAAI;IAAAA,+BAAc;IAAAA,iBAAK;IAG3BA,8BAAO;IACLA,qFAuBK;IACPA,iBAAQ;;;;IAxBmBA,gBAAY;IAAZA,4CAAY;;;;;;IAjK7CA,+BAAiD;IAGvCA,YAA2B;IAAAA,iBAAK;IACpCA,yBAAG;IAAQA,wBAAQ;IAAAA,iBAAS;IAACA,YAAmC;IAAAA,iBAAI;IACpEA,wEAA+F;IAC/FA,0EAAwF;IACxFA,0BAAG;IAAQA,uBAAM;IAAAA,iBAAS;IAACA,aAA6B;IAAAA,iBAAI;IAC5DA,0BAAG;IAAQA,4BAAM;IAAAA,iBAAS;IAACA,aAA2B;IAAAA,iBAAI;IAC1DA,0BAAG;IAAQA,0BAAI;IAAAA,iBAAS;IAACA,aAAiC;IAAAA,iBAAI;IAC9DA,0BAAG;IAAQA,2BAAK;IAAAA,iBAAS;IAACA,aAA8B;IAAAA,iBAAI;IAE9DA,iCAAsB;IACpBA,0EAA0F;IAC1FA,0EAA4F;IAC5FA,0EAA4F;IAC5FA,0EAA6F;IAC/FA,iBAAM;IAIRA,iCAAuB;IAEFA,+CAAoB;IAAAA,iBAAK;IAE5CA,+BAAuB;IACrBA,8EAIM;IAENA,gFAmDM;IAENA,8EAEM;IACRA,iBAAM;IAGRA,gCAAkB;IACCA,qCAAe;IAAAA,iBAAK;IAGvCA,8EAIM;IAENA,8EAEM;IAENA,+EA0BM;IAENA,iCAAuB;IACJA,6BAAY;IAAAA,iBAAK;IAGpCA,8EAIM;IAENA,8EAEM;IAENA,+EAwCM;IAENA,iCAA6C;IACkCA;MAAAA;MAAA;MAAA,OAASA,kDAA0B;IAAA,EAAC;IAC/GA,yBAAmC;IAAAA,kCACrC;IAAAA,iBAAS;IACTA,mCAA8G;IAAjCA;MAAAA;MAAA;MAAA,OAASA,8CAAsB;IAAA,EAAC;IAC3GA,0BAAsC;IAAAA,yBACxC;IAAAA,iBAAS;;;;IAhMHA,eAA2B;IAA3BA,mDAA2B;IACFA,eAAmC;IAAnCA,qEAAmC;IAC5DA,eAAgC;IAAhCA,yDAAgC;IAChCA,eAA4B;IAA5BA,qDAA4B;IACLA,eAA6B;IAA7BA,+DAA6B;IAC7BA,eAA2B;IAA3BA,6DAA2B;IAC7BA,eAAiC;IAAjCA,mEAAiC;IAChCA,eAA8B;IAA9BA,gEAA8B;IAGpDA,eAA4B;IAA5BA,qDAA4B;IAC5BA,eAA4B;IAA5BA,qDAA4B;IAC5BA,eAA8B;IAA9BA,uDAA8B;IAC9BA,eAA4B;IAA5BA,qDAA4B;IAU1BA,eAAuB;IAAvBA,gDAAuB;IAMvBA,eAA8C;IAA9CA,+EAA8C;IAqD9CA,eAA2C;IAA3CA,4EAA2C;IAU/CA,eAAqB;IAArBA,8CAAqB;IAMrBA,eAA+C;IAA/CA,gFAA+C;IAI/CA,eAA6C;IAA7CA,8EAA6C;IAgC7CA,eAAsB;IAAtBA,+CAAsB;IAMtBA,eAAiD;IAAjDA,kFAAiD;IAIjDA,eAA+C;IAA/CA,gFAA+C;IA8CLA,eAA8B;IAA9BA,oDAA8B;;;;;IAkB9EA,gCAA0D;IACxDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,qDACF;;;;;IAMMA,gCAAgH;IAC9GA,4CACF;IAAAA,iBAAM;;;;;IAMNA,gCAA8G;IAC5GA,yDACF;IAAAA,iBAAM;;;;;IAaNA,4BAA0D;IAAAA,gDAAsB;IAAAA,iBAAO;;;;;IADzFA,gCAA6G;IAC3GA,+EAAuF;IACzFA,iBAAM;;;;;IADGA,eAAiD;IAAjDA,4IAAiD;;;;;IAwBxDA,4BAAiF;;;;;IACjFA,yBAAsD;;;;;IAkB5DA,gCAA2D;IACzDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,sDACF;;;;;IAKIA,gCAA6G;IAC3GA,qDACF;IAAAA,iBAAM;;;;;IAMNA,gCAAuH;IACrHA,6CACF;IAAAA,iBAAM;;;;;IAmBRA,+BAA0E;IACpBA,kCAAa;IAAAA,iBAAQ;IACzEA,6BAA+F;IACjGA,iBAAM;;;;;IAoCFA,4BAAkF;;;;;IAClFA,yBAAuD;;;;;IAkB7DA,gCAAkE;IAChEA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6DACF;;;;;IAWMA,mCAAuE;IAAAA,YAA2E;;IAAAA,iBAAS;;;;IAAzGA,uCAAoB;IAACA,eAA2E;IAA3EA,8HAA2E;;;;;IAEpJA,gCAAqI;IACnIA,uCACF;IAAAA,iBAAM;;;;;IAOJA,mCAA8D;IAAAA,YAAwC;IAAAA,iBAAS;;;;IAAnEA,oCAAiB;IAACA,eAAwC;IAAxCA,2EAAwC;;;;;IAExGA,gCAAqJ;IACnJA,+CACF;IAAAA,iBAAM;;;;;IAaJA,4BAAyF;;;;;IACzFA,yBAA8D;;;;;IA4D1DA,4BAA+F;;;;;IAC/FA,yBAA8D;;;;;IAiChEA,4BAAuG;;;;;IACvGA,yBAAqE;;;;;IAIzEA,gCAA0E;IAExCA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAkF;IAChFA,yBAAsC;IACtCA,8EACF;IAAAA,iBAAM;;;;;IAoBIA,iCAA+D;IAAAA,2BAAM;IAAAA,iBAAO;;;;;IAC5EA,iCAA+D;IAAAA,6BAAQ;IAAAA,iBAAO;;;;;IAC9EA,iCAA4I;IAC1IA,YACF;IAAAA,iBAAO;;;;IAFyDA,2GAA2E;IACzIA,eACF;IADEA,iGACF;;;;;;;;;IAtBVA,8BAAqE;IAIlDA,2BAAW;IAAAA,iBAAS;IAACA,YAAgC;IAAAA,iBAAI;IACpEA,yBAAG;IAAQA,yBAAQ;IAAAA,iBAAS;IAACA,aAA6B;IAAAA,iBAAI;IAC9DA,0BAAG;IAAQA,wBAAO;IAAAA,iBAAS;IAACA,aAA4B;IAAAA,iBAAI;IAE9DA,iCAAsB;IACTA,6BAAY;IAAAA,iBAAS;IAACA,aAA8D;;IAAAA,iBAAI;IACnGA,0BAAG;IAAQA,wCAAkB;IAAAA,iBAAS;IAACA,aAAqE;;IAAAA,iBAAI;IAChHA,0BAAG;IAC0HA;MAAAA;MAAA;MAAA,OAASA,kDAA0B;IAAA,EAAC;IAC7JA,0BAA+B;IAAAA,wDACjC;IAAAA,iBAAI;IAENA,0BAAG;IACOA,wBAAO;IAAAA,iBAAS;IACxBA,wFAA4E;IAC5EA,wFAA8E;IAC9EA,wFAEO;IACTA,iBAAI;;;;IAnB4BA,eAAgC;IAAhCA,mEAAgC;IACnCA,eAA6B;IAA7BA,gEAA6B;IAC9BA,eAA4B;IAA5BA,+DAA4B;IAGvBA,eAA8D;IAA9DA,kHAA8D;IACxDA,eAAqE;IAArEA,0HAAqE;IAEvGA,eAAgF;IAAhFA,gGAAgF;IAM5EA,eAA6B;IAA7BA,uDAA6B;IAC7BA,eAA8B;IAA9BA,wDAA8B;IAC9BA,eAA0C;IAA1CA,oEAA0C;;;;;IAkB3DA,gCAA0G;IAExEA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,gCAAoN;IAClNA,6DACF;IAAAA,iBAAM;;;;;IAaAA,0BAA4E;IACtEA,YAA2B;IAAAA,iBAAK;IACpCA,0BAAI;IAAAA,YAAmD;;IAAAA,iBAAK;IAC5DA,0BAAI;IAAAA,YAAiC;IAAAA,iBAAK;IAC1CA,0BAAI;IAAAA,YAA0B;IAAAA,iBAAK;;;;IAH/BA,eAA2B;IAA3BA,gDAA2B;IAC3BA,eAAmD;IAAnDA,wFAAmD;IACnDA,eAAiC;IAAjCA,sDAAiC;IACjCA,eAA0B;IAA1BA,+CAA0B;;;;;IAftCA,gCAA+M;IAInMA,qBAAK;IAAAA,iBAAK;IACdA,+BAAwB;IAAAA,+BAAe;IAAAA,iBAAK;IAC5CA,0BAAI;IAAAA,0BAAU;IAAAA,iBAAK;IACnBA,2BAAI;IAAAA,8BAAQ;IAAAA,iBAAK;IAGrBA,8BAAO;IACLA,qFAKK;IACPA,iBAAQ;;;;IANkBA,gBAAkD;IAAlDA,2FAAkD;;;;;;;;;IAvJpFA,2BAAwC;IAG9BA,2BAAM;IAAAA,iBAAK;IACfA,yBAAG;IAAAA,YAAqC;IAAAA,iBAAI;IAE9CA,gCAAsB;IAChBA,uBAAO;IAAAA,iBAAK;IAChBA,0BAAG;IAQCA,aACF;IAAAA,iBAAO;IAKbA,iCAAsB;IAEdA,6BAAY;IAAAA,iBAAK;IACrBA,0BAAG;IAAAA,aAAiD;IAAAA,iBAAI;IAE1DA,iCAAsB;IAChBA,gDAA0B;IAAAA,iBAAK;IACnCA,0BAAG;IAAAA,aAAqI;;IAAAA,iBAAI;IAIhJA,iCAAsB;IAEdA,oCAAS;IAAAA,iBAAK;IAClBA,iCAAyB;IACvBA,mCAAiH;IACjHA,oCAAuF;IAAlCA;MAAAA;MAAA;MAAA;MAAA,OAASA,yCAAuB;IAAA,EAAC;IACpFA,0BAA+B;IACjCA,iBAAS;IACTA,oCAA+I;IAA1FA;MAAAA;MAAA;MAAA,OAASA,gFAA+C;IAAA,EAAC;IAC5GA,iFAA+F;IAC/FA,2EAA8D;IAChEA,iBAAS;IAEXA,iCAAoE;IACxCA,uGAAuE;IAAAA,iBAAQ;IACzGA,+BAAkJ;IAArCA;MAAAA;MAAA;MAAA,OAASA,kDAA0B;IAAA,EAAC;IAC/IA,0BAAsC;IAAAA,iCACxC;IAAAA,iBAAI;IAKVA,iCAAsB;IAEdA,iCAAgB;IAAAA,iBAAK;IACzBA,0BAAG;IAAAA,aAAqE;;IAAAA,iBAAI;IAE9EA,iCAAsB;IAChBA,gCAAe;IAAAA,iBAAK;IACxBA,0BAAG;IAAAA,aAAuH;;IAAAA,iBAAI;IAIlIA,gCAAkB;IACZA,+BAAS;IAAAA,iBAAK;IAClBA,0BAAG;IAAAA,aAA6C;IAAAA,iBAAI;IAItDA,iCAAkB;IAEGA,gCAAU;IAAAA,iBAAK;IAChCA,oCAAqJ;IAAhGA;MAAAA;MAAA;MAAA,OAASA,mFAAkD;IAAA,EAAC;IAC/GA,iFAAuG;IACvGA,2EAAqE;IAAAA,uDACvE;IAAAA,iBAAS;IAGXA,+EAIM;IAENA,+EAGM;IAENA,iFA2BM;IACRA,iBAAM;IAENA,gCAAkB;IAEGA,oCAAc;IAAAA,iBAAK;IACpCA,oCAAuH;IAAlEA;MAAAA;MAAA;MAAA,OAASA,wFAAuD;IAAA,EAAC;IACpHA,0BAA2C;IAAAA,mCAC7C;IAAAA,iBAAS;IAGXA,+EAIM;IAENA,+EAEM;IAENA,gFAmBM;IACRA,iBAAM;;;;IA5JCA,eAAqC;IAArCA,6DAAqC;IAKlBA,eAMlB;IANkBA,4bAMlB;IACAA,eACF;IADEA,yGACF;IAQCA,eAAiD;IAAjDA,yEAAiD;IAIjDA,eAAqI;IAArIA,qLAAqI;IAQ9FA,eAAkD;IAAlDA,wEAAkD;IAIqBA,eAA+B;IAA/BA,qDAA+B;IACrIA,eAAwB;IAAxBA,iDAAwB;IAC3BA,eAAyB;IAAzBA,kDAAyB;IAK5BA,eAAkE;IAAlEA,+FAAkE;IAUpEA,eAAqE;IAArEA,+GAAqE;IAIrEA,eAAuH;IAAvHA,uKAAuH;IAMzHA,eAA6C;IAA7CA,qEAA6C;IAOoEA,eAAkC;IAAlCA,wDAAkC;IAC3IA,eAA2B;IAA3BA,oDAA2B;IAC9BA,eAA4B;IAA5BA,qDAA4B;IAI9BA,eAA4B;IAA5BA,qDAA4B;IAM5BA,eAAiD;IAAjDA,kFAAiD;IAKjDA,eAAgD;IAAhDA,iFAAgD;IAsChDA,eAA4D;IAA5DA,6FAA4D;IAM5DA,eAAmL;IAAnLA,oPAAmL;IAInLA,eAA8K;IAA9KA,+OAA8K;;;;;;IAqDxLA,2BAAkC;IACfA,6CAAwB;IAAAA,iBAAK;IAC9CA,+BAAuB;IAEbA,2BAAW;IAAAA,iBAAO;IACxBA,iCAAyB;IAAAA,YAAqC;IAAAA,iBAAO;IAEvEA,+BAA8E;IACtEA,6BAAY;IAAAA,iBAAO;IACzBA,kCAAyB;IAAAA,aAAmE;;IAAAA,iBAAO;IAErGA,gCAA8E;IACtEA,0CAAoB;IAAAA,iBAAO;IACjCA,kCAA8C;IAAAA,aAAmC;IAAAA,iBAAO;IAI5FA,iCAAsC;IACpCA,0BAAoD;IACpDA,+BAAQ;IAAAA,yCAAS;IAAAA,iBAAS;IAACA,+OAE7B;IAAAA,iBAAM;IAENA,iCAAkB;IACgBA;MAAAA;MAAA;MAAA,OAASA,6CAAqB;IAAA,EAAC;IAC7DA,0BAAmC;IAAAA,+CACrC;IAAAA,iBAAS;;;;IArBkBA,eAAqC;IAArCA,6DAAqC;IAIrCA,eAAmE;IAAnEA,4GAAmE;IAI9CA,eAAmC;IAAnCA,2DAAmC;;;;;;;;;ADtgC/F,OAAM,MAAOC,kBAAkB;EA4C7B;;;;EAIAC,UAAU,CAACC,OAAe;IACxB,IAAI,CAACC,YAAY,CAACC,KAAK,CAACF,OAAO,CAAC;EAClC;EAEA;;;;EAIAG,SAAS,CAACH,OAAe;IACvB,IAAI,CAACC,YAAY,CAACG,IAAI,CAACJ,OAAO,CAAC;EACjC;EAEAK,kBAAkB;IAChB,IAAI,CAACN,UAAU,CAAC,eAAe,CAAC;EAClC;EAEAO,iBAAiB;IACf,IAAI,CAACP,UAAU,CAAC,cAAc,CAAC;EACjC;EAEAQ,wBAAwB;IACtB,IAAI,CAACR,UAAU,CAAC,qBAAqB,CAAC;EACxC;EAEAS,oBAAoB;IAClB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,YAAY,CAAC,IAAI,CAACD,gBAAgB,CAAC;;EAE5C;EAEAE,oBAAoB;IAClBC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IAEtC,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;MAC1BG,OAAO,CAACE,KAAK,CAAC,sBAAsB,CAAC;MACrC;;IAGF;IACA,IAAI,CAACC,YAAY,CAACC,KAAK,CAAC;MACtBC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE;KACR,CAAC;IACF,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAAClB,SAAS,CAAC,eAAe,CAAC;EACjC;EAEAmB,YAAY,CAACC,QAAqB;IAChCX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEU,QAAQ,CAAC;IAEtC;IACA,IAAI,CAACR,YAAY,CAACS,UAAU,CAAC;MAC3BP,IAAI,EAAEM,QAAQ,CAACN,IAAI;MACnBC,SAAS,EAAEK,QAAQ,CAACL,SAAS,IAAI,EAAE;MACnCO,cAAc,EAAEF,QAAQ,CAACE,cAAc,GAAG,IAAIC,IAAI,CAACH,QAAQ,CAACE,cAAc,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MAC5GC,MAAM,EAAEN,QAAQ,CAACM,MAAM,IAAI,QAAQ;MACnCC,WAAW,EAAEP,QAAQ,CAACO,WAAW,IAAI,EAAE;MACvCC,eAAe,EAAER,QAAQ,CAACQ,eAAe;MACzCC,sBAAsB,EAAET,QAAQ,CAACS,sBAAsB;MACvDC,oBAAoB,EAAEV,QAAQ,CAACU,oBAAoB;MACnDC,gBAAgB,EAAEX,QAAQ,CAACW,gBAAgB;MAC3CC,mBAAmB,EAAEZ,QAAQ,CAACY,mBAAmB;MACjDhB,KAAK,EAAEI,QAAQ,CAACJ,KAAK,IAAI;KAC1B,CAAC;IACF,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACgB,gBAAgB,GAAGb,QAAQ;IAChC,IAAI,CAACF,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAAClB,SAAS,CAAC,eAAe,CAAC;EACjC;EAEMkC,cAAc,CAACd,QAAqB;IAAA;IAAA;MACxCX,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEU,QAAQ,CAAC;MAExC,MAAMe,SAAS,SAAS,KAAI,CAACrC,YAAY,CAACsC,OAAO,CAC/C,kCAAkChB,QAAQ,CAACN,IAAI,GAAG,EAClD,kBAAkB,EAClB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAIqB,SAAS,EAAE;QACb,KAAI,CAACE,eAAe,CAACH,cAAc,CAACd,QAAQ,CAACkB,EAAE,CAAC,CAACC,SAAS,CAAC;UACzDC,IAAI,EAAE,MAAK;YACT/B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;YAC5C,KAAI,CAAC+B,MAAM,CAACC,OAAO,CAAC,YAAYtB,QAAQ,CAACN,IAAI,uBAAuB,EAAE,QAAQ,CAAC;YAC/E;YACA,KAAI,CAAC6B,SAAS,GAAG,KAAI,CAACA,SAAS,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,EAAE,KAAKlB,QAAQ,CAACkB,EAAE,CAAC;UACnE,CAAC;UACD3B,KAAK,EAAGmC,GAAG,IAAI;YACbrC,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEmC,GAAG,CAAC;YAC/C,KAAI,CAAChD,YAAY,CAACiD,KAAK,CACrB,8BAA8BD,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE,EAC5D,OAAO,EACP,QAAQ,EACR,YAAY,CACb;UACH;SACD,CAAC;;IACH;EACH;EAEAC,YAAY;IACVxC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACE,YAAY,CAACsC,KAAK,CAAC;IAErD,IAAI,IAAI,CAACtC,YAAY,CAACuC,OAAO,EAAE;MAC7B,IAAI,CAACjC,aAAa,GAAG,6CAA6C;MAClE;MACAkC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzC,YAAY,CAAC0C,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACpD,MAAMC,OAAO,GAAG,IAAI,CAAC7C,YAAY,CAAC8C,GAAG,CAACF,GAAG,CAAC;QAC1CC,OAAO,EAAEE,aAAa,EAAE;MAC1B,CAAC,CAAC;MACF;;IAGF,IAAI,CAAC,IAAI,CAACrD,gBAAgB,EAAE;MAC1B,IAAI,CAACY,aAAa,GAAG,6BAA6B;MAClD;;IAGF,IAAI,CAAC0C,cAAc,GAAG,IAAI;IAE1B,MAAMC,YAAY,GAAG,IAAI,CAACjD,YAAY,CAACsC,KAAK;IAE5C,IAAI,IAAI,CAACjC,kBAAkB,IAAI,IAAI,CAACgB,gBAAgB,EAAE;MACpD;MACA,MAAM6B,eAAe,GAA6B;QAChDhD,IAAI,EAAE+C,YAAY,CAAC/C,IAAI,IAAI,EAAE;QAC7BC,SAAS,EAAE8C,YAAY,CAAC9C,SAAS,IAAI,EAAE;QACvCO,cAAc,EAAEuC,YAAY,CAACvC,cAAc,GAAG,IAAIC,IAAI,CAACsC,YAAY,CAACvC,cAAc,CAAC,GAAGyC,SAAS;QAC/FrC,MAAM,EAAEmC,YAAY,CAACnC,MAAM;QAC3BC,WAAW,EAAEkC,YAAY,CAAClC,WAAW;QACrCC,eAAe,EAAEiC,YAAY,CAACjC,eAAe;QAC7CC,sBAAsB,EAAEgC,YAAY,CAAChC,sBAAsB;QAC3DC,oBAAoB,EAAE+B,YAAY,CAAC/B,oBAAoB;QACvDC,gBAAgB,EAAE8B,YAAY,CAAC9B,gBAAgB;QAC/CC,mBAAmB,EAAE6B,YAAY,CAAC7B,mBAAmB;QACrDhB,KAAK,EAAE6C,YAAY,CAAC7C,KAAK,IAAI;OAC9B;MAEDP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoD,eAAe,CAAC;MAC9D,IAAI,CAACzB,eAAe,CAAC2B,cAAc,CAAC,IAAI,CAAC/B,gBAAgB,CAACK,EAAE,EAAEwB,eAAe,CAAC,CAACvB,SAAS,CAAC;QACvFC,IAAI,EAAGsB,eAAe,IAAI;UACxBrD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoD,eAAe,CAAC;UAC9D,IAAI,CAACF,cAAc,GAAG,KAAK;UAE3B;UACA,MAAMK,KAAK,GAAG,IAAI,CAACtB,SAAS,CAACuB,SAAS,CAACrB,CAAC,IAAIA,CAAC,CAACP,EAAE,KAAKwB,eAAe,CAACxB,EAAE,CAAC;UACxE,IAAI2B,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACtB,SAAS,CAACsB,KAAK,CAAC,GAAGH,eAAe;;UAGzC;UACA,IAAI,CAACK,kBAAkB,EAAE;QAC3B,CAAC;QACDxD,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEmC,GAAG,CAAC;UACpD,IAAI,CAAC5B,aAAa,GAAG,mCAAmC4B,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UACtF,IAAI,CAACY,cAAc,GAAG,KAAK;QAC7B;OACD,CAAC;KACH,MAAM;MACL;MACA,MAAMQ,WAAW,GAA6B;QAC5CC,UAAU,EAAE,IAAI,CAAC/D,gBAAgB,CAACgC,EAAE;QACpCxB,IAAI,EAAE+C,YAAY,CAAC/C,IAAI,IAAI,EAAE;QAC7BC,SAAS,EAAE8C,YAAY,CAAC9C,SAAS,IAAI,EAAE;QACvCO,cAAc,EAAEuC,YAAY,CAACvC,cAAc,GAAG,IAAIC,IAAI,CAACsC,YAAY,CAACvC,cAAc,CAAC,GAAGyC,SAAS;QAC/FrC,MAAM,EAAEmC,YAAY,CAACnC,MAAM;QAC3BE,eAAe,EAAEiC,YAAY,CAACjC,eAAe;QAC7CC,sBAAsB,EAAEgC,YAAY,CAAChC,sBAAsB;QAC3DC,oBAAoB,EAAE+B,YAAY,CAAC/B,oBAAoB;QACvDC,gBAAgB,EAAE8B,YAAY,CAAC9B,gBAAgB;QAC/CC,mBAAmB,EAAE6B,YAAY,CAAC7B,mBAAmB;QACrDhB,KAAK,EAAE6C,YAAY,CAAC7C,KAAK,IAAI;OAC9B;MAEDP,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE0D,WAAW,CAAC;MACtD,IAAI,CAAC/B,eAAe,CAACiC,cAAc,CAACF,WAAW,CAAC,CAAC7B,SAAS,CAAC;QACzDC,IAAI,EAAG+B,eAAe,IAAI;UACxB9D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6D,eAAe,CAAC;UAC9D,IAAI,CAACX,cAAc,GAAG,KAAK;UAE3B;UACA,IAAI,CAACjB,SAAS,CAAC6B,IAAI,CAACD,eAAe,CAAC;UAEpC;UACA,IAAI,CAACJ,kBAAkB,EAAE;QAC3B,CAAC;QACDxD,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;UAClD,IAAI,CAAC5B,aAAa,GAAG,iCAAiC4B,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UACpF,IAAI,CAACY,cAAc,GAAG,KAAK;QAC7B;OACD,CAAC;;EAEN;EAEAO,kBAAkB;IAChB,IAAI,CAACvE,UAAU,CAAC,eAAe,CAAC;EAClC;EAEA6E,qBAAqB,CAAC/C,MAA+B;IACnD,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,SAAS;QAC/B,KAAK,SAAS;UAAE,OAAO,WAAW;QAClC,KAAK,SAAS;UAAE,OAAO,YAAY;QACnC,KAAK,OAAO;UAAE,OAAO,UAAU;QAC/B,KAAK,aAAa;UAAE,OAAO,QAAQ;QACnC;UAAS,OAAOA,MAAM;MAAC;KAE1B,MAAM;MACL,QAAQA,MAAM;QACZ,KAAKjC,cAAc,CAACiF,MAAM;UAAE,OAAO,SAAS;QAC5C,KAAKjF,cAAc,CAACkF,OAAO;UAAE,OAAO,WAAW;QAC/C,KAAKlF,cAAc,CAACmF,OAAO;UAAE,OAAO,YAAY;QAChD,KAAKnF,cAAc,CAACoF,KAAK;UAAE,OAAO,UAAU;QAC5C,KAAKpF,cAAc,CAACqF,WAAW;UAAE,OAAO,QAAQ;QAChD;UAAS,OAAOC,MAAM,CAACrD,MAAM,CAAC;MAAC;;EAGrC;EAEAsD,mBAAmB;IACjBvE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;MAC1BG,OAAO,CAACE,KAAK,CAAC,sBAAsB,CAAC;MACrC;;IAGF;IACA,IAAI,CAACsE,WAAW,CAACpE,KAAK,CAAC;MACrBqE,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTtE,KAAK,EAAE,EAAE;MACTuE,SAAS,EAAE;KACZ,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACzF,SAAS,CAAC,cAAc,CAAC;EAChC;EAEA0F,WAAW,CAACC,OAAgB;IAC1BlF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiF,OAAO,CAAC;IAEpC;IACA,IAAI,CAACV,WAAW,CAACW,QAAQ,CAAC;MACxBV,SAAS,EAAES,OAAO,CAACT,SAAS;MAC5BC,QAAQ,EAAEQ,OAAO,CAACR,QAAQ;MAC1BC,QAAQ,EAAEO,OAAO,CAACP,QAAQ,IAAI,EAAE;MAChCC,KAAK,EAAEM,OAAO,CAACN,KAAK,IAAI,EAAE;MAC1BC,KAAK,EAAEK,OAAO,CAACL,KAAK,IAAI,EAAE;MAC1BtE,KAAK,EAAE2E,OAAO,CAAC3E,KAAK,IAAI,EAAE;MAC1BuE,SAAS,EAAEI,OAAO,CAACJ;KACpB,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACK,eAAe,GAAGF,OAAO;IAC9B,IAAI,CAACF,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACzF,SAAS,CAAC,cAAc,CAAC;EAChC;EAEM8F,aAAa,CAACH,OAAgB;IAAA;IAAA;MAClClF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiF,OAAO,CAAC;MAEtC,MAAMxD,SAAS,SAAS,MAAI,CAACrC,YAAY,CAACsC,OAAO,CAC/C,iCAAiCuD,OAAO,CAACT,SAAS,IAAIS,OAAO,CAACR,QAAQ,GAAG,EACzE,kBAAkB,EAClB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAIhD,SAAS,EAAE;QACb,MAAI,CAAC4D,cAAc,CAACD,aAAa,CAACH,OAAO,CAACrD,EAAE,CAAC,CAACC,SAAS,CAAC;UACtDC,IAAI,EAAE,MAAK;YACT/B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;YAC3C,MAAI,CAAC+B,MAAM,CAACC,OAAO,CAAC,WAAWiD,OAAO,CAACT,SAAS,IAAIS,OAAO,CAACR,QAAQ,qBAAqB,EAAE,QAAQ,CAAC;YACpG;YACA,MAAI,CAACa,QAAQ,GAAG,MAAI,CAACA,QAAQ,CAACpD,MAAM,CAACqD,CAAC,IAAIA,CAAC,CAAC3D,EAAE,KAAKqD,OAAO,CAACrD,EAAE,CAAC;UAChE,CAAC;UACD3B,KAAK,EAAGmC,GAAG,IAAI;YACbrC,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEmC,GAAG,CAAC;YAC/C,MAAI,CAAChD,YAAY,CAACiD,KAAK,CACrB,8BAA8BD,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE,EAC5D,OAAO,EACP,QAAQ,EACR,YAAY,CACb;UACH;SACD,CAAC;;IACH;EACH;EAEAkD,WAAW;IACTzF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACuE,WAAW,CAAC/B,KAAK,CAAC;IAEnD,IAAI,IAAI,CAAC+B,WAAW,CAAC9B,OAAO,EAAE;MAC5B,IAAI,CAACsC,YAAY,GAAG,6CAA6C;MACjE;MACArC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC4B,WAAW,CAAC3B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACnD,MAAMC,OAAO,GAAG,IAAI,CAACwB,WAAW,CAACvB,GAAG,CAACF,GAAG,CAAC;QACzCC,OAAO,EAAEE,aAAa,EAAE;MAC1B,CAAC,CAAC;MACF;;IAGF,IAAI,CAAC,IAAI,CAACrD,gBAAgB,EAAE;MAC1B,IAAI,CAACmF,YAAY,GAAG,6BAA6B;MACjD;;IAGF,IAAI,CAACU,aAAa,GAAG,IAAI;IAEzB,MAAMC,WAAW,GAAG,IAAI,CAACnB,WAAW,CAAC/B,KAAK;IAE1C,IAAI,IAAI,CAACsC,iBAAiB,IAAI,IAAI,CAACK,eAAe,EAAE;MAClD;MACA,MAAMQ,cAAc,GAAG;QACrBnB,SAAS,EAAEkB,WAAW,CAAClB,SAAS,IAAI,EAAE;QACtCC,QAAQ,EAAEiB,WAAW,CAACjB,QAAQ,IAAI,EAAE;QACpCC,QAAQ,EAAEgB,WAAW,CAAChB,QAAQ,IAAI,EAAE;QACpCC,KAAK,EAAEe,WAAW,CAACf,KAAK,IAAI,EAAE;QAC9BC,KAAK,EAAEc,WAAW,CAACd,KAAK,IAAI,EAAE;QAC9BtE,KAAK,EAAEoF,WAAW,CAACpF,KAAK,IAAI,EAAE;QAC9BuE,SAAS,EAAEa,WAAW,CAACb,SAAS,IAAI;OACrC;MAED9E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2F,cAAc,CAAC;MAC5D,IAAI,CAACN,cAAc,CAACO,aAAa,CAAC,IAAI,CAACT,eAAe,CAACvD,EAAE,EAAE+D,cAAc,CAAC,CAAC9D,SAAS,CAAC;QACnFC,IAAI,EAAG6D,cAAc,IAAI;UACvB5F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2F,cAAc,CAAC;UAC5D,IAAI,CAACF,aAAa,GAAG,KAAK;UAE1B;UACA,MAAMlC,KAAK,GAAG,IAAI,CAAC+B,QAAQ,CAAC9B,SAAS,CAAC+B,CAAC,IAAIA,CAAC,CAAC3D,EAAE,KAAK+D,cAAc,CAAC/D,EAAE,CAAC;UACtE,IAAI2B,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC+B,QAAQ,CAAC/B,KAAK,CAAC,GAAGoC,cAAc;;UAGvC;UACA,IAAI,CAACzG,UAAU,CAAC,cAAc,CAAC;QACjC,CAAC;QACDe,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEmC,GAAG,CAAC;UACpD,IAAI,CAAC2C,YAAY,GAAG,mCAAmC3C,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UACrF,IAAI,CAACmD,aAAa,GAAG,KAAK;QAC5B;OACD,CAAC;KACH,MAAM;MACL;MACA,MAAMI,UAAU,GAAyB;QACvClC,UAAU,EAAE,IAAI,CAAC/D,gBAAgB,CAACgC,EAAE;QACpC4C,SAAS,EAAEkB,WAAW,CAAClB,SAAS,IAAI,EAAE;QACtCC,QAAQ,EAAEiB,WAAW,CAACjB,QAAQ,IAAI,EAAE;QACpCC,QAAQ,EAAEgB,WAAW,CAAChB,QAAQ,IAAI,EAAE;QACpCC,KAAK,EAAEe,WAAW,CAACf,KAAK,IAAI,EAAE;QAC9BC,KAAK,EAAEc,WAAW,CAACd,KAAK,IAAI,EAAE;QAC9BtE,KAAK,EAAEoF,WAAW,CAACpF,KAAK,IAAI,EAAE;QAC9BuE,SAAS,EAAEa,WAAW,CAACb,SAAS,IAAI;OACrC;MAED9E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6F,UAAU,CAAC;MAChD,IAAI,CAACR,cAAc,CAACS,aAAa,CAACD,UAAU,CAAC,CAAChE,SAAS,CAAC;QACtDC,IAAI,EAAGiE,cAAc,IAAI;UACvBhG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+F,cAAc,CAAC;UAC5D,IAAI,CAACN,aAAa,GAAG,KAAK;UAE1B;UACA,IAAI,CAACH,QAAQ,CAACxB,IAAI,CAACiC,cAAc,CAAC;UAElC,IAAI,CAACtG,iBAAiB,EAAE;QAC1B,CAAC;QACDQ,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;UAClDrC,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEmC,GAAG,CAACnC,KAAK,CAAC;UAE1C;UACA,IAAImC,GAAG,CAACnC,KAAK,IAAImC,GAAG,CAACnC,KAAK,CAAC+F,MAAM,EAAE;YACjC,MAAMC,aAAa,GAAG,EAAE;YACxB,KAAK,MAAMnD,GAAG,IAAIV,GAAG,CAACnC,KAAK,CAAC+F,MAAM,EAAE;cAClC,IAAI5D,GAAG,CAACnC,KAAK,CAAC+F,MAAM,CAACE,cAAc,CAACpD,GAAG,CAAC,EAAE;gBACxCmD,aAAa,CAACnC,IAAI,CAAC,GAAGhB,GAAG,KAAKV,GAAG,CAACnC,KAAK,CAAC+F,MAAM,CAAClD,GAAG,CAAC,CAACqD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;;;YAGrE,IAAI,CAACpB,YAAY,GAAG,iCAAiCkB,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE;WAChF,MAAM;YACL,IAAI,CAACpB,YAAY,GAAG,iCAAiC3C,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;;UAGrF,IAAI,CAACmD,aAAa,GAAG,KAAK;QAC5B;OACD,CAAC;;EAEN;EAOA;EACAW,cAAc;IACZ,OAAQrD,OAAwB,IAA6B;MAC3D,MAAMP,KAAK,GAAGO,OAAO,CAACP,KAAK;MAC3B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC6D,MAAM,KAAK,CAAC,EAAE;QAChC,OAAO,IAAI,CAAC,CAAC;;MAEf;MACA,OAAOxH,UAAU,CAAC8F,KAAK,CAAC5B,OAAO,CAAC;IAClC,CAAC;EACH;EA+BAuD,YACUC,EAAe,EACfC,eAAgC,EAChCnB,cAA8B,EAC9B1D,eAAgC,EAChC8E,sBAA8C,EAC9CC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,kBAAsC,EACtCzH,YAA0B,EAC1B0H,IAAgB,EAChBC,iBAAoC,EACpCC,MAAc,EACdjF,MAAqB;IAbrB,OAAE,GAAFwE,EAAE;IACF,oBAAe,GAAfC,eAAe;IACf,mBAAc,GAAdnB,cAAc;IACd,oBAAe,GAAf1D,eAAe;IACf,2BAAsB,GAAtB8E,sBAAsB;IACtB,mBAAc,GAAdC,cAAc;IACd,gBAAW,GAAXC,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,uBAAkB,GAAlBC,kBAAkB;IAClB,iBAAY,GAAZzH,YAAY;IACZ,SAAI,GAAJ0H,IAAI;IACJ,sBAAiB,GAAjBC,iBAAiB;IACjB,WAAM,GAANC,MAAM;IACN,WAAM,GAANjF,MAAM;IAvgBhB;IACA,mBAAc,GAAGhD,cAAc;IAC/B,YAAO,GAAY,KAAK;IACxB,UAAK,GAAkB,IAAI;IAC3B,cAAS,GAAU,EAAE;IACrB,sBAAiB,GAAU,EAAE;IAC7B,uBAAkB,GAAU,EAAE;IAC9B,qBAAgB,GAAQ,IAAI;IAC5B,aAAQ,GAAU,EAAE;IACpB,cAAS,GAAkB,EAAE;IAC7B,oBAAe,GAAY,KAAK;IAChC,qBAAgB,GAAY,KAAK;IACjC,kBAAa,GAAY,KAAK;IAC9B,mBAAc,GAAY,KAAK;IAG/B,sBAAiB,GAAY,KAAK;IAClC,uBAAkB,GAAY,KAAK;IACnC,iBAAY,GAAkB,IAAI;IAClC,kBAAa,GAAkB,IAAI;IACnC,oBAAe,GAAmB,IAAI;IACtC,qBAAgB,GAAuB,IAAI;IAE3C;IACA,iBAAY,GAAU,CACpB;MAAEqB,IAAI,EAAE,MAAM;MAAE6G,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAE,EAC9C;MAAE9G,IAAI,EAAE,SAAS;MAAE6G,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACpE;IACD,mBAAc,GAAQ,EAAE;IACxB,eAAU,GAAW,MAAM;IAC3B,kBAAa,GAAmB,KAAK;IAErC;IACA,gBAAW,GAAW,CAAC;IACvB,aAAQ,GAAW,EAAE;IACrB,SAAI,GAAGC,IAAI,CAAC,CAAC;IAwab,eAAU,GAAY,KAAK;IAC3B,WAAM,GAAY,KAAK;IAEvB,YAAO,GAAY,KAAK;IAcxB;IACA,qBAAgB,GAAgD,EAAE;IAClE,4BAAuB,GAAsC,EAAE;IAC/D,sBAAiB,GAAiB,EAAE;IACpC,6BAAwB,GAAY,KAAK;IACzC,mBAAc,GAAW,EAAE;IAC3B,0BAAqB,GAAY,KAAK;IAEtC,4BAAuB,GAA2B,IAAI;IACtD,8BAAyB,GAAY,KAAK;IAC1C,yBAAoB,GAAkB,IAAI;IAC1C,0BAAqB,GAAY,KAAK;IAEtC,+BAA0B,GAAuB,IAAI;IAErD;IACA,2BAAsB,GAAY,KAAK;IACvC,oBAAe,GAAmC,IAAI;IACtD,0BAAqB,GAAY,KAAK;IACtC,yBAAoB,GAAyC,IAAI;IAEjE;IACA,uBAAkB,GAAY,KAAK;IAEnC;IACA,sBAAiB,GAAY,KAAK;IAClC,uBAAkB,GAAQ,IAAI;IAC9B,oBAAe,GAAkB,IAAI;IAkBnC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAChClH,IAAI,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC5I,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACnEE,SAAS,EAAE,CAAC,EAAE,EAAE7I,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC;MACzCG,KAAK,EAAE,CAAC,EAAE,EAAE9I,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC;MACrC7C,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9F,UAAU,CAAC8F,KAAK,EAAE9F,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1D5C,KAAK,EAAE,CAAC,EAAE,EAAE/F,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC;MACrCI,OAAO,EAAE,CAAC,EAAE,EAAE/I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC;MACxCK,MAAM,EAAE,CAAC,EAAE,EAAE,CAAChJ,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9DM,IAAI,EAAE,CAAC,EAAE,EAAE,CAACjJ,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DO,UAAU,EAAE,CAAC,EAAE,EAAE,CAAClJ,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACjEQ,OAAO,EAAE,CAAC,EAAE,EAAE,CAACnJ,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/DlH,KAAK,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAA2B;KAClE,CAAC;;IACF,IAAI,CAACjD,WAAW,GAAG,IAAI,CAACgC,EAAE,CAACe,KAAK,CAAC;MAC/B9C,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjE/C,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5F,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAChE9C,QAAQ,EAAE,CAAC,EAAE,EAAE7F,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC;MACzC7C,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAACyB,cAAc,EAAE,EAAEvH,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/D5C,KAAK,EAAE,CAAC,EAAE,EAAE/F,UAAU,CAAC2I,SAAS,CAAC,EAAE,CAAC,CAAC;MACrClH,KAAK,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC;MACtC3C,SAAS,EAAE,CAAC,KAAK;KAClB,CAAC;IACF,IAAI,CAAC3E,YAAY,GAAG,IAAI,CAACqG,EAAE,CAACe,KAAK,CAAC;MAChClH,IAAI,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DnH,SAAS,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAAC0I,QAAQ,EAAE1I,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjE5G,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBI,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClBC,WAAW,EAAE,CAAC,EAAE,EAAEpC,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC,CAAC;MAC5CtG,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,oBAAoB,EAAE,CAAC,KAAK,CAAC;MAC7BC,gBAAgB,EAAE,CAAC,KAAK,CAAC;MACzBC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BhB,KAAK,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC;KACtC,CAAC;IAEF,IAAI,CAACS,mBAAmB,GAAG,IAAI,CAAC1B,EAAE,CAACe,KAAK,CAAC;MACvCY,SAAS,EAAE,CAAC,EAAE,EAAErJ,UAAU,CAAC0I,QAAQ,CAAC;MACpCY,iBAAiB,EAAE,CAAC,EAAE,EAAEtJ,UAAU,CAAC0I,QAAQ,CAAC;MAC5CjH,KAAK,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAAC2I,SAAS,CAAC,GAAG,CAAC;KACtC,CAAC;IACF,IAAI,CAACY,aAAa,EAAE;EACtB;EAEAC,oBAAoB;IAClB;IACA,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAC,kBAAkB,CAACC,QAAkB;IACnC;IACA,IAAI,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,EAAEE,QAAQ,CAAC5G,EAAE,CAAC,CAAC;EACnD;EAEA6G,oBAAoB,CAACC,UAAkB;IACrC,IAAI,CAACC,uBAAuB,CAACD,UAAU,CAAC,GAAG,IAAI;IAC/C,IAAI,CAACE,gBAAgB,CAACF,UAAU,CAAC,GAAG,EAAE;IAEtC,IAAI,CAACjC,sBAAsB,CAACoC,mBAAmB,CAACH,UAAU,CAAC,CAAC7G,SAAS,CAAC;MACpEC,IAAI,EAAGgH,QAAQ,IAAI;QACjB/I,OAAO,CAACC,GAAG,CAAC,gCAAgC0I,UAAU,GAAG,EAAEI,QAAQ,CAAC;QACpE,IAAI,CAACF,gBAAgB,CAACF,UAAU,CAAC,GAAGI,QAAQ;QAC5C,IAAI,CAACH,uBAAuB,CAACD,UAAU,CAAC,GAAG,KAAK;MAClD,CAAC;MACDzI,KAAK,EAAGmC,GAAG,IAAI;QACbrC,OAAO,CAACE,KAAK,CAAC,qCAAqCyI,UAAU,EAAE,EAAEtG,GAAG,CAAC;QACrE,IAAI,CAACuG,uBAAuB,CAACD,UAAU,CAAC,GAAG,KAAK;MAClD;KACD,CAAC;EACJ;EAEAK,gBAAgB,CAACL,UAAkB;IACjC,IAAI,CAAC,IAAI,CAACE,gBAAgB,CAACF,UAAU,CAAC,IAAI,IAAI,CAACE,gBAAgB,CAACF,UAAU,CAAC,CAACrC,MAAM,KAAK,CAAC,EAAE;MACxF,OAAO,GAAG;;IAGZ;IACA,OAAO,IAAI,CAACuC,gBAAgB,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,CAACM,aAAa;EAC3D;EAEAC,2BAA2B,CAACvI,QAAqB;IAC/CX,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEU,QAAQ,CAAC;IACxD,IAAI,CAACwI,0BAA0B,GAAGxI,QAAQ;IAC1C,IAAI,CAACyI,yBAAyB,GAAG,KAAK;IACtC,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAACnB,mBAAmB,CAAC9H,KAAK,CAAC;MAC7B+H,SAAS,EAAE,EAAE;MACbC,iBAAiB,EAAE,EAAE;MACrB7H,KAAK,EAAE;KACR,CAAC;IAEF;IACA,IAAI,CAAC+I,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACC,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAAChK,SAAS,CAAC,sBAAsB,CAAC;EACxC;EAEA+J,qBAAqB;IACnB,IAAI,CAACE,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B,IAAI,CAAC9C,cAAc,CAAC+C,WAAW,EAAE,CAAC5H,SAAS,CAAC;MAC1CC,IAAI,EAAGgH,QAAQ,IAAI;QACjB/I,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE8I,QAAQ,CAAC;QACnD,IAAI,CAACU,iBAAiB,GAAGV,QAAQ;QACjC,IAAI,CAACS,wBAAwB,GAAG,KAAK;MACvC,CAAC;MACDtJ,KAAK,EAAGmC,GAAG,IAAI;QACbrC,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEmC,GAAG,CAAC;QACzD,IAAI,CAACmH,wBAAwB,GAAG,KAAK;QACrC,IAAI,CAACH,oBAAoB,GAAG,wCAAwChH,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;MACpG;KACD,CAAC;EACJ;EAEAgH,kBAAkB;IAChB,IAAI,CAACI,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,cAAc,GAAG,EAAE;IAExB,IAAI,CAAChD,WAAW,CAACiD,QAAQ,EAAE,CAAC/H,SAAS,CAAC;MACpCC,IAAI,EAAG+H,KAAK,IAAI;QACd9J,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6J,KAAK,CAAC;QAC7C,IAAI,CAACF,cAAc,GAAGE,KAAK;QAC3B,IAAI,CAACH,qBAAqB,GAAG,KAAK;MACpC,CAAC;MACDzJ,KAAK,EAAGmC,GAAG,IAAI;QACbrC,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEmC,GAAG,CAAC;QAC7D,IAAI,CAACsH,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACN,oBAAoB,GAAG,4CAA4ChH,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;MACxG;KACD,CAAC;EACJ;EAEAwH,mBAAmB;IACjB,IAAI,IAAI,CAAC7B,mBAAmB,CAACxF,OAAO,EAAE;MACpC,IAAI,CAAC2G,oBAAoB,GAAG,6CAA6C;MACzE;MACA1G,MAAM,CAACC,IAAI,CAAC,IAAI,CAACsF,mBAAmB,CAACrF,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QAC3D,MAAMC,OAAO,GAAG,IAAI,CAACkF,mBAAmB,CAACjF,GAAG,CAACF,GAAG,CAAC;QACjDC,OAAO,EAAEE,aAAa,EAAE;MAC1B,CAAC,CAAC;MACF;;IAGF,IAAI,CAAC,IAAI,CAACiG,0BAA0B,EAAE;MACpC,IAAI,CAACE,oBAAoB,GAAG,8BAA8B;MAC1D;;IAGF,IAAI,CAACW,qBAAqB,GAAG,IAAI;IAEjC,MAAMC,mBAAmB,GAAG,IAAI,CAAC/B,mBAAmB,CAACzF,KAAK;IAE1D,IAAI,IAAI,CAAC2G,yBAAyB,IAAI,IAAI,CAACc,uBAAuB,EAAE;MAClE;MACA,MAAMC,sBAAsB,GAAG;QAC7B5J,KAAK,EAAE0J,mBAAmB,CAAC1J,KAAK,IAAI;OACrC;MAEDP,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkK,sBAAsB,CAAC;MAC7E,IAAI,CAACzD,sBAAsB,CAAC0D,qBAAqB,CAC/C,IAAI,CAACjB,0BAA0B,CAACtH,EAAE,EAClC,IAAI,CAACqI,uBAAuB,CAACrI,EAAE,EAC/BsI,sBAAsB,CACvB,CAACrI,SAAS,CAAC;QACVC,IAAI,EAAGoI,sBAAsB,IAAI;UAC/BnK,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkK,sBAAsB,CAAC;UAC7E,IAAI,CAACH,qBAAqB,GAAG,KAAK;UAElC;UACA,IAAI,CAACtB,oBAAoB,CAAC,IAAI,CAACS,0BAA2B,CAACtH,EAAE,CAAC;UAE9D;UACA,IAAI,CAACwI,yBAAyB,EAAE;QAClC,CAAC;QACDnK,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,sCAAsC,EAAEmC,GAAG,CAAC;UAC1D,IAAI,CAACgH,oBAAoB,GAAG,yCAAyChH,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UACnG,IAAI,CAACyH,qBAAqB,GAAG,KAAK;QACpC;OACD,CAAC;KACH,MAAM;MACL;MACA,MAAMM,kBAAkB,GAAiC;QACvDnC,SAAS,EAAEoC,QAAQ,CAACN,mBAAmB,CAAC9B,SAAS,CAAC;QAClDC,iBAAiB,EAAEmC,QAAQ,CAACN,mBAAmB,CAAC7B,iBAAiB,CAAC;QAClE7H,KAAK,EAAE0J,mBAAmB,CAAC1J,KAAK,IAAI;OACrC;MAEDP,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqK,kBAAkB,CAAC;MACrE,IAAI,CAAC5D,sBAAsB,CAAC8D,kBAAkB,CAC5C,IAAI,CAACrB,0BAA0B,CAACtH,EAAE,EAClCyI,kBAAkB,CACnB,CAACxI,SAAS,CAAC;QACVC,IAAI,EAAG0I,sBAAsB,IAAI;UAC/BzK,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEwK,sBAAsB,CAAC;UAC7E,IAAI,CAACT,qBAAqB,GAAG,KAAK;UAElC;UACA,IAAI,CAACtB,oBAAoB,CAAC,IAAI,CAACS,0BAA2B,CAACtH,EAAE,CAAC;UAE9D;UACA,IAAI,CAACwI,yBAAyB,EAAE;QAClC,CAAC;QACDnK,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEmC,GAAG,CAAC;UACxD,IAAI,CAACgH,oBAAoB,GAAG,uCAAuChH,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UACjG,IAAI,CAACyH,qBAAqB,GAAG,KAAK;QACpC;OACD,CAAC;;EAEN;EAEAK,yBAAyB;IACvB,IAAI,CAAClL,UAAU,CAAC,sBAAsB,CAAC;EACzC;EAEAuL,kBAAkB,CAAC/J,QAAqB;IACtCX,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,QAAQ,CAAC;IAC7C,IAAI,CAACwI,0BAA0B,GAAGxI,QAAQ;IAE1C;IACA,IAAI,CAAC+H,oBAAoB,CAAC/H,QAAQ,CAACkB,EAAE,CAAC;IAEtC;IACA7B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEU,QAAQ,CAACkB,EAAE,CAAC;IACrE,IAAI,CAAC8I,mBAAmB,CAAChK,QAAQ,CAACkB,EAAE,CAAC;IAErC;IACA,IAAI,CAACtC,SAAS,CAAC,qBAAqB,CAAC;EACvC;EAEAqL,wBAAwB;IACtB,IAAI,CAACzL,UAAU,CAAC,qBAAqB,CAAC;EACxC;EAEA;EACAwL,mBAAmB,CAAChC,UAAkB;IACpC3I,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0I,UAAU,CAAC;IACtE,IAAI,CAACkC,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACC,eAAe,GAAG,IAAI;IAE3B,MAAMC,GAAG,GAAG,GAAGhM,WAAW,CAACiM,MAAM,0BAA0BrC,UAAU,EAAE;IACvE3I,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE8K,GAAG,CAAC;IAEzC;IACA,IAAI,CAAChE,IAAI,CAAC9D,GAAG,CAA0B8H,GAAG,CAAC,CAACjJ,SAAS,CAAC;MACpDC,IAAI,EAAGkJ,QAAQ,IAAI;QACjBjL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgL,QAAQ,CAAC;QACjD,IAAI,CAACH,eAAe,GAAGG,QAAQ;QAC/B,IAAI,CAACJ,sBAAsB,GAAG,KAAK;MACrC,CAAC;MACD3K,KAAK,EAAGmC,GAAG,IAAI;QACbrC,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEmC,GAAG,CAAC;QACpDrC,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEmC,GAAG,CAACpB,MAAM,EAAEoB,GAAG,CAACE,UAAU,CAAC;QAC3D,IAAIF,GAAG,CAACnC,KAAK,EAAE;UACbF,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEmC,GAAG,CAACnC,KAAK,CAACgL,OAAO,IAAI7I,GAAG,CAACnC,KAAK,CAAC;;QAEjE,IAAI,CAAC2K,sBAAsB,GAAG,KAAK;QACnC;QACA,IAAIxI,GAAG,CAACpB,MAAM,KAAK,GAAG,EAAE;UACtBjB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C,IAAI,CAAC6K,eAAe,GAAG,IAAI;SAC5B,MAAM;UACL;UACA9K,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnE,IAAI,CAAC6K,eAAe,GAAG;YACrBnC,UAAU,EAAE,IAAI,CAACQ,0BAA0B,EAAEtH,EAAE,IAAI,CAAC;YACpDsJ,YAAY,EAAE,IAAI,CAAChC,0BAA0B,EAAE9I,IAAI,IAAI,EAAE;YACzD+K,YAAY,EAAE,EAAE;YAChBC,UAAU,EAAE,YAAY;YACxBC,OAAO,EAAE,YAAY;YACrBC,MAAM,EAAE,YAAY;YACpBC,OAAO,EAAE,KAAK;YACdC,gBAAgB,EAAE;WACnB;;MAEL;KACD,CAAC;EACJ;EAEA;EACMC,mBAAmB,CAAC/C,UAAkB;IAAA;IAAA;MAC1C,MAAMjH,SAAS,SAAS,MAAI,CAACrC,YAAY,CAACsC,OAAO,CAC/C,kHAAkH,EAClH,wBAAwB,EACxB,WAAW,EACX,QAAQ,EACR,aAAa,EACb,eAAe,CAChB;MAED,IAAI,CAACD,SAAS,EAAE;QACd;;MAGF,MAAI,CAACiK,qBAAqB,GAAG,IAAI;MACjC,MAAI,CAACC,oBAAoB,GAAG,IAAI;MAEhC,MAAMb,GAAG,GAAG,GAAGhM,WAAW,CAACiM,MAAM,0BAA0BrC,UAAU,WAAW;MAChF3I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8K,GAAG,CAAC;MAErD,MAAI,CAAChE,IAAI,CAAC8E,IAAI,CAAgCd,GAAG,EAAE,EAAE,CAAC,CAACjJ,SAAS,CAAC;QAC/DC,IAAI,EAAGkJ,QAAQ,IAAI;UACjBjL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgL,QAAQ,CAAC;UAC/C,MAAI,CAACW,oBAAoB,GAAGX,QAAQ;UACpC,MAAI,CAACU,qBAAqB,GAAG,KAAK;UAElC;UACA,MAAI,CAAChB,mBAAmB,CAAChC,UAAU,CAAC;UAEpC;UACA,MAAI,CAACtJ,YAAY,CAACG,IAAI,CAAC,2BAA2B,CAAC;QACrD,CAAC;QACDU,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;UAClD,MAAI,CAACsJ,qBAAqB,GAAG,KAAK;UAClC,MAAI,CAACtM,YAAY,CAACiD,KAAK,CACrB,qCAAqCD,GAAG,CAACnC,KAAK,EAAEgL,OAAO,IAAI7I,GAAG,CAAC6I,OAAO,IAAI,eAAe,EAAE,EAC3F,OAAO,EACP,QAAQ,EACR,YAAY,CACb;QACH;OACD,CAAC;IAAC;EACL;EAEA;EACAY,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAACF,oBAAoB,EAAE;MAC9B;;IAGF;IACA,MAAMG,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAACJ,oBAAoB,CAACK,UAAU,EAAE,sBAAsB,CAAC;IAC5F,MAAMlB,GAAG,GAAGmB,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGzB,GAAG;IACZsB,CAAC,CAACI,QAAQ,GAAG,eAAe,IAAI,CAACtD,0BAA0B,EAAE9I,IAAI,IAAI,UAAU,MAAM;IACrFiM,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,EAAE;IACTV,MAAM,CAACC,GAAG,CAACU,eAAe,CAAC9B,GAAG,CAAC;IAC/BuB,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,CAAC,CAAC;EAC9B;EAEA;EACQL,YAAY,CAACe,MAAc,EAAEC,WAAmB;IACtD,MAAMC,cAAc,GAAGC,IAAI,CAACH,MAAM,CAAC;IACnC,MAAMI,UAAU,GAAG,EAAE;IAErB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,cAAc,CAAC3G,MAAM,EAAE8G,MAAM,IAAI,GAAG,EAAE;MAClE,MAAMC,KAAK,GAAGJ,cAAc,CAACI,KAAK,CAACD,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MAExD,MAAME,WAAW,GAAG,IAAIC,KAAK,CAACF,KAAK,CAAC/G,MAAM,CAAC;MAC3C,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,KAAK,CAAC/G,MAAM,EAAElE,CAAC,EAAE,EAAE;QACrCkL,WAAW,CAAClL,CAAC,CAAC,GAAGiL,KAAK,CAACG,UAAU,CAACpL,CAAC,CAAC;;MAGtC,MAAMqL,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7CH,UAAU,CAACpJ,IAAI,CAAC0J,SAAS,CAAC;;IAG5B,OAAO,IAAIE,IAAI,CAACR,UAAU,EAAE;MAAEhG,IAAI,EAAE6F;IAAW,CAAE,CAAC;EACpD;EAEA;EACAY,6BAA6B,CAACnC,gBAAwB;IACpD,IAAIA,gBAAgB,IAAI,CAAC,EAAE;MACzB,OAAO,aAAa;KACrB,MAAM,IAAIA,gBAAgB,IAAI,EAAE,EAAE;MACjC,OAAO,cAAc;KACtB,MAAM;MACL,OAAO,WAAW;;EAEtB;EAEA3L,YAAY,CAAC2I,QAAa;IACxB;IACA,IAAI,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAAC,YAAY,EAAEE,QAAQ,CAAC5G,EAAE,CAAC,EAAE;MAAEgM,WAAW,EAAE;QAAEC,IAAI,EAAE;MAAM;IAAE,CAAE,CAAC;EACtF;EAEMC,cAAc,CAACtF,QAAa;IAAA;IAAA;MAChCzI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwI,QAAQ,CAAC;MAExC,MAAM/G,SAAS,SAAS,MAAI,CAACrC,YAAY,CAACsC,OAAO,CAC/C,mCAAmC8G,QAAQ,CAACpI,IAAI,GAAG,EACnD,mBAAmB,EACnB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAIqB,SAAS,EAAE;QACb,MAAI,CAACsM,OAAO,GAAG,IAAI;QAEnB,MAAI,CAACvH,eAAe,CAACsH,cAAc,CAACtF,QAAQ,CAAC5G,EAAE,CAAC,CAACC,SAAS,CAAC;UACzDC,IAAI,EAAE,MAAK;YACT/B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;YAC5C,MAAI,CAAC+B,MAAM,CAACC,OAAO,CAAC,YAAYwG,QAAQ,CAACpI,IAAI,qBAAqB,EAAE,QAAQ,CAAC;YAC7E,MAAI,CAACgI,aAAa,EAAE;UACtB,CAAC;UACDnI,KAAK,EAAGmC,GAAG,IAAI;YACbrC,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEmC,GAAG,CAAC;YAChD,MAAI,CAAChD,YAAY,CAACiD,KAAK,CACrB,+BAA+BD,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE,EAC7D,OAAO,EACP,QAAQ,EACR,YAAY,CACb;YACD,MAAI,CAACyL,OAAO,GAAG,KAAK;UACtB;SACD,CAAC;;IACH;EACH;EAEAC,QAAQ;IACN,IAAI,CAACC,OAAO,GAAG,IAAI,CAACrH,WAAW,CAACqH,OAAO,EAAE;IACzClO,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACiO,OAAO,CAAC;IAE3C;IACA,IAAI;MACF,MAAMC,aAAa,GAAG,uBAAuB;MAC7C,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAACH,aAAa,CAAC;MAE1D,IAAIC,cAAc,EAAE;QAClB,IAAI,CAACG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAChDpO,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACsO,cAAc,CAAC;;KAE5E,CAAC,OAAOrO,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;;IAG7E,IAAI,CAACmI,aAAa,EAAE;EACtB;EAEA;;;EAGAqG,cAAc,CAACC,OAAY;IACzB,IAAI,CAACJ,cAAc,GAAGI,OAAO;IAC7B,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC;IAEtB;IACA,IAAI;MACF,MAAMT,aAAa,GAAG,uBAAuB;MAC7C,IAAIxL,MAAM,CAACC,IAAI,CAAC+L,OAAO,CAAC,CAACrI,MAAM,GAAG,CAAC,EAAE;QACnC+H,YAAY,CAACQ,OAAO,CAACV,aAAa,EAAEK,IAAI,CAACM,SAAS,CAACH,OAAO,CAAC,CAAC;QAC5D3O,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0O,OAAO,CAAC;OAC/D,MAAM;QACL;QACAN,YAAY,CAACU,UAAU,CAACZ,aAAa,CAAC;QACtCnO,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;KAEhD,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;IAGnE;IACA,IAAI,CAACmI,aAAa,EAAE;EACtB;EAEA2G,MAAM,CAACC,MAAc;IACnB,IAAI,IAAI,CAACC,UAAU,KAAKD,MAAM,EAAE;MAC9B;MACA,IAAI,CAACE,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KACnE,MAAM;MACL;MACA,IAAI,CAACD,UAAU,GAAGD,MAAM;MACxB,IAAI,CAACE,aAAa,GAAG,KAAK;;IAG5B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAD,mBAAmB;IACjB;IACA,IAAI,CAACE,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC;IAE5C;IACA,IAAI,CAACD,iBAAiB,CAACE,IAAI,CAAC,CAACnD,CAAC,EAAEoD,CAAC,KAAI;MACnC,MAAMC,MAAM,GAAGrD,CAAC,CAAC,IAAI,CAAC6C,UAAU,CAAC,EAAES,QAAQ,EAAE,CAACC,WAAW,EAAE,IAAI,EAAE;MACjE,MAAMC,MAAM,GAAGJ,CAAC,CAAC,IAAI,CAACP,UAAU,CAAC,EAAES,QAAQ,EAAE,CAACC,WAAW,EAAE,IAAI,EAAE;MAEjE,IAAI,IAAI,CAACT,aAAa,KAAK,KAAK,EAAE;QAChC,OAAOO,MAAM,CAACI,aAAa,CAACD,MAAM,CAAC;OACpC,MAAM;QACL,OAAOA,MAAM,CAACC,aAAa,CAACJ,MAAM,CAAC;;IAEvC,CAAC,CAAC;EACJ;EAEA;;;EAGAK,YAAY,CAACC,IAAY;IACvB,IAAI,CAACpB,WAAW,GAAGoB,IAAI;IACvB,IAAI,CAACX,wBAAwB,EAAE;EACjC;EAEAA,wBAAwB;IACtB,MAAMY,UAAU,GAAG,CAAC,IAAI,CAACrB,WAAW,GAAG,CAAC,IAAI,IAAI,CAACsB,QAAQ;IACzD,MAAMC,QAAQ,GAAGF,UAAU,GAAG,IAAI,CAACC,QAAQ;IAC3C,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACd,iBAAiB,CAACjC,KAAK,CAAC4C,UAAU,EAAEE,QAAQ,CAAC;EAC9E;EAEA,IAAIE,UAAU;IACZ,OAAOhJ,IAAI,CAACiJ,IAAI,CAAC,IAAI,CAAChB,iBAAiB,CAAChJ,MAAM,GAAG,IAAI,CAAC4J,QAAQ,CAAC;EACjE;EAEA,IAAIK,SAAS;IACX,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,QAAQ,GAAG,CAAC;IAClB,MAAMC,SAAS,GAAGrJ,IAAI,CAACsJ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/B,WAAW,GAAGvH,IAAI,CAACuJ,KAAK,CAACH,QAAQ,GAAG,CAAC,CAAC,CAAC;IAC1E,MAAMI,OAAO,GAAGxJ,IAAI,CAACyJ,GAAG,CAAC,IAAI,CAACT,UAAU,EAAEK,SAAS,GAAGD,QAAQ,GAAG,CAAC,CAAC;IAEnE,KAAK,IAAIrO,CAAC,GAAGsO,SAAS,EAAEtO,CAAC,IAAIyO,OAAO,EAAEzO,CAAC,EAAE,EAAE;MACzCoO,KAAK,CAACzM,IAAI,CAAC3B,CAAC,CAAC;;IAGf,OAAOoO,KAAK;EACd;EAEAO,YAAY;IACV/Q,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,IAAI,CAAC,IAAI,CAACiO,OAAO,EAAE;MACjB,IAAI,CAAChO,KAAK,GAAG,6CAA6C;MAC1DF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;;IAGF,IAAI,IAAI,CAACqH,YAAY,CAAC5E,OAAO,EAAE;MAC7B,IAAI,CAACxC,KAAK,GAAG,6CAA6C;MAC1DF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD;MACA0C,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC0E,YAAY,CAACzE,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QACpD,MAAMC,OAAO,GAAG,IAAI,CAACsE,YAAY,CAACrE,GAAG,CAACF,GAAG,CAAC;QAC1CC,OAAO,EAAEE,aAAa,EAAE;QACxB,IAAIF,OAAO,EAAEN,OAAO,EAAE;UACpB1C,OAAO,CAACC,GAAG,CAAC,SAAS8C,GAAG,cAAc,EAAEC,OAAO,CAACiD,MAAM,CAAC;;MAE3D,CAAC,CAAC;MACF;;IAGF,MAAM+K,YAAY,GAAG,IAAI,CAAC1J,YAAY,CAAC7E,KAAK;IAC5CzC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+Q,YAAY,CAAC;IACjE,IAAI,CAACC,MAAM,GAAG,IAAI;IAElB,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACrR,gBAAgB,EAAE;MAC5C;MACAG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACJ,gBAAgB,CAACgC,EAAE,CAAC;MACnE,IAAI,CAAC4E,eAAe,CAAC0K,cAAc,CAAC,IAAI,CAACtR,gBAAgB,CAACgC,EAAE,EAAEmP,YAAY,CAAC,CAAClP,SAAS,CAAC;QACpFC,IAAI,EAAGqP,eAAe,IAAI;UACxBpR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmR,eAAe,CAAC;UAC9D,IAAI,CAACH,MAAM,GAAG,KAAK;UACnB,IAAI,CAACxR,kBAAkB,EAAE;UACzB;UACA,IAAI,CAAC4I,aAAa,EAAE;QACtB,CAAC;QACDnI,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEmC,GAAG,CAAC;UACrDrC,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEmC,GAAG,CAACpB,MAAM,CAAC;UACpCjB,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmC,GAAG,CAACE,UAAU,CAAC;UAC7CvC,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEmC,GAAG,CAACnC,KAAK,CAAC;UAC1C,IAAI,CAACA,KAAK,GAAG,oCAAoCmC,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UAC/E,IAAI,CAAC0O,MAAM,GAAG,KAAK;QACrB;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACxK,eAAe,CAAC4K,cAAc,CAACL,YAAY,CAAC,CAAClP,SAAS,CAAC;QAC1DC,IAAI,EAAGuP,eAAe,IAAI;UACxBtR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqR,eAAe,CAAC;UAC9D,IAAI,CAACL,MAAM,GAAG,KAAK;UACnB;UACA,IAAI,CAACxR,kBAAkB,EAAE;UACzB;UACA,IAAI,CAAC4I,aAAa,EAAE;QACtB,CAAC;QACDnI,KAAK,EAAGmC,GAAG,IAAI;UACbrC,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;UAClDrC,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEmC,GAAG,CAACpB,MAAM,CAAC;UACpCjB,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmC,GAAG,CAACE,UAAU,CAAC;UAC7CvC,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEmC,GAAG,CAACnC,KAAK,CAAC;UAC1C,IAAI,CAACA,KAAK,GAAG,iCAAiCmC,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE;UAC5E,IAAI,CAAC0O,MAAM,GAAG,KAAK;QACrB;OACD,CAAC;;EAEN;EAEA5I,aAAa;IACX,IAAI,CAAC2F,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,CAACvH,eAAe,CAAC8K,YAAY,CAAC,IAAI,CAAChD,cAAc,CAAC,CAACzM,SAAS,CAAC;MAC/DC,IAAI,EAAGwN,SAAS,IAAI;QAClBvP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsP,SAAS,CAAC;QAC5C,IAAI,CAACA,SAAS,GAAGA,SAAS,IAAI,EAAE;QAEhC;QACA;QACA,IAAI,CAACA,SAAS,CAACzM,OAAO,CAAC2F,QAAQ,IAAG;UAChC,IAAIA,QAAQ,CAAClD,QAAQ,EAAE;YACrBvF,OAAO,CAACC,GAAG,CAAC,YAAYwI,QAAQ,CAACpI,IAAI,OAAOoI,QAAQ,CAAClD,QAAQ,CAACe,MAAM,WAAW,CAAC;;QAEpF,CAAC,CAAC;QAEF,IAAI,CAACgJ,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC;QAC5C;QACA,IAAI,CAACH,mBAAmB,EAAE;QAC1B,IAAI,CAACC,wBAAwB,EAAE;QAC/B,IAAI,CAACrB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD9N,KAAK,EAAGmC,GAAG,IAAI;QACbrC,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;QAClD,IAAI,CAAC2L,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;;;;EAIAwD,UAAU,CAACC,YAA8B;IACvCA,YAAY,CAACC,MAAM,EAAE;IACrBpF,QAAQ,CAACqF,WAAW,CAAC,MAAM,CAAC;IAC5B,IAAI,CAACtS,YAAY,CAACiD,KAAK,CACrB,sCAAsC,EACtC,WAAW,EACX,IAAI,EACJ,aAAa,CACd;EACH;EAEA;;;;EAIMsP,gBAAgB,CAACjJ,UAAkB;IAAA;IAAA;MACvC,MAAMjH,SAAS,SAAS,MAAI,CAACrC,YAAY,CAACsC,OAAO,CAC/C,iHAAiH,EACjH,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,aAAa,EACb,eAAe,CAChB;MAED,IAAID,SAAS,EAAE;QACb,MAAI,CAACmQ,kBAAkB,GAAG,IAAI;QAE9B,MAAI,CAACjQ,eAAe,CAACgQ,gBAAgB,CAACjJ,UAAU,CAAC,CAAC7G,SAAS,CAAC;UAC1DC,IAAI,EAAE,MAAK;YACT;YACA,MAAI,CAACH,eAAe,CAACkQ,WAAW,CAACnJ,UAAU,CAAC,CAAC7G,SAAS,CAAC;cACrDC,IAAI,EAAGsB,eAAe,IAAI;gBACxB;gBACA,MAAMG,KAAK,GAAG,MAAI,CAACtB,SAAS,CAACuB,SAAS,CAACrB,CAAC,IAAIA,CAAC,CAACP,EAAE,KAAK8G,UAAU,CAAC;gBAChE,IAAInF,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChB,MAAI,CAACtB,SAAS,CAACsB,KAAK,CAAC,GAAGH,eAAe;;gBAGzC;gBACA,IAAI,MAAI,CAAC8F,0BAA0B,IAAI,MAAI,CAACA,0BAA0B,CAACtH,EAAE,KAAK8G,UAAU,EAAE;kBACxF,MAAI,CAACQ,0BAA0B,GAAG9F,eAAe;;gBAGnD,MAAI,CAACwO,kBAAkB,GAAG,KAAK;gBAC/B,MAAI,CAACxS,YAAY,CAACiD,KAAK,CACrB,mCAAmC,EACnC,WAAW,EACX,IAAI,EACJ,aAAa,CACd;cACH,CAAC;cACDpC,KAAK,EAAGmC,GAAG,IAAI;gBACbrC,OAAO,CAACE,KAAK,CAAC,4CAA4C,EAAEmC,GAAG,CAAC;gBAChE,MAAI,CAACwP,kBAAkB,GAAG,KAAK;gBAC/B,MAAI,CAACxS,YAAY,CAACiD,KAAK,CACrB,+CAA+CD,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE,EAC7E,OAAO,EACP,QAAQ,EACR,YAAY,CACb;cACH;aACD,CAAC;UACJ,CAAC;UACDrC,KAAK,EAAGmC,GAAG,IAAI;YACbrC,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEmC,GAAG,CAAC;YACpD,MAAI,CAACwP,kBAAkB,GAAG,KAAK;YAC/B,MAAI,CAACxS,YAAY,CAACiD,KAAK,CACrB,mCAAmCD,GAAG,CAACpB,MAAM,IAAIoB,GAAG,CAACE,UAAU,EAAE,EACjE,OAAO,EACP,QAAQ,EACR,YAAY,CACb;UACH;SACD,CAAC;;IACH;EACH;;;uBAhtCWrD,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAA6S;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UC5B/BlT,8BAAuB;UAEfA,oDAAgB;UAAAA,iBAAK;UACzBA,8BAAoC;UAClCA,+DAEI;UACJA,+DAEI;UAENA,iBAAM;UAGRA,8BAAkB;UAMZA;YAAA,OAAgBmT,0BAAsB;UAAA,EAAC;UACzCnT,iBAAsB;UAEtBA,qEAIM;UAENA,qEAEM;UAENA,sEAEM;UAENA,wEAuEM;UAGNA,wEA+BM;UACRA,iBAAM;UAKVA,gCAAiH;UAIzDA,aAA2D;UAAAA,iBAAK;UAChHA,8BAA6G;UAC/GA,iBAAM;UACNA,gCAAwB;UACtBA,sEAEM;UACNA,iCAA6D;UAA5BA;YAAA,OAAYmT,kBAAc;UAAA,EAAC;UAC1DnT,gCAAiB;UAE0IA,2BAAK;UAAAA,iBAAQ;UACpKA,6BAAqK;UACrKA,sEAEM;UACRA,iBAAM;UAENA,gCAA2B;UACsJA,wBAAO;UAAAA,iBAAQ;UAC9LA,6BAAqM;UACrMA,sEAEM;UACRA,iBAAM;UAGRA,gCAAiB;UAEyBA,yBAAG;UAAAA,iBAAQ;UACjDA,6BAA2E;UAC7EA,iBAAM;UAENA,gCAA2B;UACiIA,sBAAK;UAAAA,iBAAQ;UACvKA,6BAA0K;UAC1KA,sEAEM;UACRA,iBAAM;UAGRA,gCAAiB;UAEyBA,wBAAO;UAAAA,iBAAQ;UACrDA,6BAA2E;UAC7EA,iBAAM;UAENA,gCAA2B;UACeA,yCAAc;UAAAA,iBAAQ;UAC9DA,6BAA+E;UACjFA,iBAAM;UAGRA,gCAAiB;UAEgJA,sBAAK;UAAAA,iBAAQ;UAC1KA,6BAA6K;UAC7KA,sEAEM;UACRA,iBAAM;UAGRA,gCAAiB;UAE0IA,2BAAK;UAAAA,iBAAQ;UACpKA,6BAAqK;UACrKA,sEAEM;UACRA,iBAAM;UAENA,gCAA2B;UACgJA,yBAAG;UAAAA,iBAAQ;UACpLA,6BAA6L;UAC7LA,sEAEM;UACRA,iBAAM;UAENA,gCAA2B;UACuIA,0BAAI;UAAAA,iBAAQ;UAC5KA,6BAAiL;UACjLA,sEAEM;UACRA,iBAAM;UAGRA,gCAAkB;UACsBA,8BAAQ;UAAAA,iBAAQ;UACtDA,gCAAsF;UACxFA,iBAAM;UAGNA,uEA0DM;UAGNA,uEAgFM;UAENA,gCAA0B;UACgDA;YAAA,OAASmT,wBAAoB;UAAA,EAAC;UACpGnT,yBAAmC;UAAAA,kCACrC;UAAAA,iBAAS;UACTA,mCAAmH;UAAzBA;YAAA,OAASmT,kBAAc;UAAA,EAAC;UAChHnT,wEAA0E;UAC1EA,kEAA+C;UAAAA,aACjD;UAAAA,iBAAS;UASrBA,gCAA6H;UAI/DA,2CAAgB;UAAAA,iBAAK;UAC3EA,8BAA6G;UAC/GA,iBAAM;UACNA,wEAqMM;UACRA,iBAAM;UAKVA,gCAA+G;UAIxDA,cAA8D;UAAAA,iBAAK;UAClHA,+BAA6G;UAC/GA,iBAAM;UACNA,iCAAwB;UACtBA,wEAEM;UACNA,kCAA2D;UAA3BA;YAAA,OAAYmT,iBAAa;UAAA,EAAC;UACxDnT,iCAAiB;UAEuJA,4BAAK;UAAAA,iBAAQ;UACjLA,8BAAmF;UACnFA,wEAEM;UACRA,iBAAM;UAENA,iCAA2B;UACwIA,yCAAQ;UAAAA,iBAAQ;UACjLA,8BAAiF;UACjFA,wEAEM;UACRA,iBAAM;UAGRA,iCAAkB;UACyBA,wBAAM;UAAAA,iBAAQ;UACvDA,8BAAiF;UACnFA,iBAAM;UAENA,iCAAkB;UAC6BA,uBAAK;UAAAA,iBAAQ;UAC1DA,8BAA+K;UAC/KA,wEAEM;UACRA,iBAAM;UAENA,iCAAkB;UAC6BA,yBAAO;UAAAA,iBAAQ;UAC5DA,8BAAkF;UACpFA,iBAAM;UAENA,iCAAkB;UAC4BA,+BAAQ;UAAAA,iBAAQ;UAC5DA,iCAA4F;UAC9FA,iBAAM;UAENA,iCAA6B;UAC3BA,8BAA2F;UAC3FA,mCAAgD;UAAAA,qCAAc;UAAAA,iBAAQ;UAGxEA,iCAA0B;UACgDA;YAAA,OAASmT,uBAAmB;UAAA,EAAC;UACnGnT,0BAAmC;UAAAA,mCACrC;UAAAA,iBAAS;UACTA,oCAAgG;UAC9FA,0EAAiF;UACjFA,oEAAsD;UAAAA,cACxD;UAAAA,iBAAS;UASrBA,iCAAiH;UAIzDA,cAAyE;UAAAA,iBAAK;UAC9HA,+BAA6G;UAC/GA,iBAAM;UACNA,iCAAwB;UACtBA,wEAEM;UACNA,kCAA6D;UAA5BA;YAAA,OAAYmT,kBAAc;UAAA,EAAC;UAC1DnT,iCAAkB;UAC4CA,qCAAc;UAAAA,iBAAQ;UAClFA,8BAA6K;UAC7KA,wEAEM;UACRA,iBAAM;UAENA,iCAAkB;UACiDA,6BAAW;UAAAA,iBAAQ;UACpFA,8BAAiM;UACjMA,wEAEM;UACRA,iBAAM;UAENA,iCAAkB;UACuCA,gCAAc;UAAAA,iBAAQ;UAC7EA,8BAAqG;UACvGA,iBAAM;UAENA,iCAAkB;UAC+BA,wBAAM;UAAAA,iBAAQ;UAC7DA,oCAAyE;UAChDA,8BAAO;UAAAA,iBAAS;UACvCA,oCAAwB;UAAAA,gCAAS;UAAAA,iBAAS;UAC1CA,oCAAwB;UAAAA,iCAAU;UAAAA,iBAAS;UAC3CA,oCAAsB;UAAAA,oCAAQ;UAAAA,iBAAS;UACvCA,oCAA4B;UAAAA,kCAAM;UAAAA,iBAAS;UAI/CA,wEAGM;UAENA,iCAAkB;UACUA,sCAAe;UAAAA,iBAAQ;UACjDA,iCAAwB;UACtBA,8BAAuG;UACvGA,mCAAsD;UAAAA,uCAAW;UAAAA,iBAAQ;UAE3EA,iCAAwB;UACtBA,+BAAqH;UACrHA,oCAA6D;UAAAA,qDAAoB;UAAAA,iBAAQ;UAE3FA,iCAAwB;UACtBA,+BAAiH;UACjHA,oCAA2D;UAAAA,+BAAa;UAAAA,iBAAQ;UAElFA,iCAAwB;UACtBA,+BAAyG;UACzGA,oCAAuD;UAAAA,4BAAU;UAAAA,iBAAQ;UAE3EA,iCAAwB;UACtBA,+BAA+G;UAC/GA,oCAA0D;UAAAA,6CAAY;UAAAA,iBAAQ;UAIlFA,iCAAkB;UAC8BA,+BAAQ;UAAAA,iBAAQ;UAC9DA,kCAA8F;UAChGA,iBAAM;UAENA,iCAA0B;UACgDA;YAAA,OAASmT,wBAAoB;UAAA,EAAC;UACpGnT,0BAAmC;UAAAA,mCACrC;UAAAA,iBAAS;UACTA,oCAAkG;UAChGA,0EAAkF;UAClFA,oEAAuD;UAAAA,cACzD;UAAAA,iBAAS;UASrBA,kCAA0L;UAI3HA,cAAoF;UAAAA,iBAAK;UAChJA,+BAA6G;UAC/GA,iBAAM;UACNA,iCAAwB;UACtBA,wEAEM;UACNA,kCAA2E;UAAnCA;YAAA,OAAYmT,yBAAqB;UAAA,EAAC;UACxEnT,iCAAkB;UAC6BA,2BAAS;UAAAA,iBAAQ;UAC9DA,+BAA8G;UAChHA,iBAAM;UAENA,iCAAkB;UACyCA,wBAAM;UAAAA,iBAAQ;UACvEA,qCAA2L;UACxKA,qCAAmB;UAAAA,iBAAS;UAC7CA,+EAA2J;UAC7JA,iBAAS;UACTA,wEAEM;UACRA,iBAAM;UAENA,iCAAkB;UACiDA,6BAAW;UAAAA,iBAAQ;UACpFA,qCAA2N;UACxMA,8CAAuB;UAAAA,iBAAS;UACjDA,+EAA+G;UACjHA,iBAAS;UACTA,wEAEM;UACRA,iBAAM;UAENA,iCAAkB;UACqCA,gCAAS;UAAAA,iBAAQ;UACtEA,kCAAqG;UACvGA,iBAAM;UAENA,iCAA0B;UACgDA;YAAA,OAASmT,+BAA2B;UAAA,EAAC;UAC3GnT,0BAAmC;UAAAA,mCACrC;UAAAA,iBAAS;UACTA,oCAAgH;UAC9GA,0EAAyF;UACzFA,oEAA8D;UAAAA,cAChE;UAAAA,iBAAS;UASrBA,kCAA6H;UAI/DA,cAA6F;UAAAA,iBAAK;UACxJA,+BAA6G;UAC/GA,iBAAM;UACNA,iCAAwB;UACtBA,2EAiKM;UAENA,kCAA6C;UACmHA;YAAA,OAASmT,8BAA0B;UAAA,EAAC;UAChMnT,2BAAmC;UAAAA,0BACrC;UAAAA,iBAAS;UACTA,qCAAkH;UAArCA;YAAA,OAASmT,8BAA0B;UAAA,EAAC;UAC/GnT,0BAAmC;UAAAA,mCACrC;UAAAA,iBAAS;UACTA,oCAA2I;UAApDA;YAAA,OAASmT,gDAAyC;UAAA,EAAC;UACxInT,2BAAsC;UAAAA,0BACxC;UAAAA,iBAAS;UAQnBA,kCAAyI;UAIrEA,kDAAsB;UAAAA,iBAAK;UACvFA,+BAA6G;UAC/GA,iBAAM;UACNA,iCAAwB;UAEpBA,2BAA4C;UAC5CA,qFACF;UAAAA,iBAAM;UAENA,0EA4BM;UACRA,iBAAM;UACNA,iCAA0B;UACgDA,kCAAM;UAAAA,iBAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAhjCdA,eAAa;UAAbA,kCAAa;UAG1BA,eAAa;UAAbA,kCAAa;UAWzEA,eAA0B;UAA1BA,wCAA0B;UAKtBA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAIXA,eAA0D;UAA1DA,uFAA0D;UAI1DA,eAAwD;UAAxDA,qFAAwD;UA0ExDA,eAAwD;UAAxDA,qFAAwD;UAyCZA,eAA2D;UAA3DA,wGAA2D;UAIrGA,eAAW;UAAXA,gCAAW;UAGXA,eAA0B;UAA1BA,4CAA0B;UAGWA,eAAiH;UAAjHA,6MAAiH;UAC7EA,eAA2F;UAA3FA,4LAA2F;UAC9JA,eAA4E;UAA5EA,qLAA4E;UAMrCA,eAAiI;UAAjIA,8NAAiI;UACrFA,eAA2G;UAA3GA,4MAA2G;UAC9LA,eAA4F;UAA5FA,qMAA4F;UAa5DA,eAAmH;UAAnHA,gNAAmH;UAC7EA,eAA6F;UAA7FA,8LAA6F;UACnKA,eAA8E;UAA9EA,uLAA8E;UAoB7CA,gBAAqH;UAArHA,kNAAqH;UAC/EA,eAA+F;UAA/FA,gMAA+F;UACtKA,eAAgF;UAAhFA,yLAAgF;UAQjDA,eAAiH;UAAjHA,8MAAiH;UAC7EA,eAA2F;UAA3FA,4LAA2F;UAC9JA,eAA4E;UAA5EA,qLAA4E;UAMvCA,eAA6H;UAA7HA,0NAA6H;UACnFA,eAAuG;UAAvGA,wMAAuG;UACtLA,eAAwF;UAAxFA,iMAAwF;UAMtDA,eAAuH;UAAvHA,oNAAuH;UAChFA,eAAiG;UAAjGA,kMAAiG;UAC1KA,eAAkF;UAAlFA,2LAAkF;UAYtFA,eAAgB;UAAhBA,qCAAgB;UA6DhBA,eAAgB;UAAhBA,qCAAgB;UAsF0BA,eAA2C;UAA3CA,iEAA2C;UAChFA,eAAY;UAAZA,iCAAY;UACfA,eAAa;UAAbA,kCAAa;UAA8BA,eACjD;UADiDA,+EACjD;UAgBmBA,eAAsB;UAAtBA,2CAAsB;UA+MEA,eAA8D;UAA9DA,uFAA8D;UAIvGA,eAAkB;UAAlBA,uCAAkB;UAGlBA,eAAyB;UAAzBA,2CAAyB;UAGiBA,eAAyH;UAAzHA,sNAAyH;UAE7JA,eAAoF;UAApFA,6LAAoF;UAMjDA,eAAuH;UAAvHA,oNAAuH;UAE1JA,eAAkF;UAAlFA,2LAAkF;UAaPA,eAA2F;UAA3FA,4LAA2F;UACxKA,eAA4E;UAA5EA,qLAA4E;UAwBpCA,gBAAiD;UAAjDA,uEAAiD;UACtFA,eAAmB;UAAnBA,wCAAmB;UACtBA,eAAoB;UAApBA,yCAAoB;UAA8BA,eACxD;UADwDA,sFACxD;UAa4CA,eAAyE;UAAzEA,kGAAyE;UAInHA,eAAmB;UAAnBA,wCAAmB;UAGnBA,eAA0B;UAA1BA,4CAA0B;UAGqDA,eAA2F;UAA3FA,4LAA2F;UACtKA,eAA4E;UAA5EA,qLAA4E;UAOSA,eAAqG;UAArGA,sMAAqG;UAC1LA,eAAsF;UAAtFA,+LAAsF;UAqB3EA,gBAAqD;UAArDA,kHAAqD;UAsCxBA,gBAAmD;UAAnDA,yEAAmD;UACxFA,eAAoB;UAApBA,yCAAoB;UACvBA,eAAqB;UAArBA,0CAAqB;UAA8BA,eACzD;UADyDA,uFACzD;UAamDA,eAAoF;UAApFA,6GAAoF;UAIrIA,eAA0B;UAA1BA,+CAA0B;UAG1BA,eAAiC;UAAjCA,mDAAiC;UAGuBA,eAA0C;UAA1CA,2GAA0C;UAK7BA,eAAmH;UAAnHA,oNAAmH;UAE5JA,eAAoB;UAApBA,+CAAoB;UAE5CA,eAAoG;UAApGA,6MAAoG;UAOnBA,eAAmI;UAAnIA,oOAAmI;UAE/LA,eAAiB;UAAjBA,4CAAiB;UAEtCA,eAAoH;UAApHA,6NAAoH;UAc5EA,eAAiE;UAAjEA,uFAAiE;UACtGA,eAA2B;UAA3BA,gDAA2B;UAC9BA,eAA4B;UAA5BA,iDAA4B;UAA8BA,eAChE;UADgEA,8FAChE;UAakDA,eAA6F;UAA7FA,kNAA6F;UAI7IA,eAAgC;UAAhCA,qDAAgC;UAoKYA,eAAwC;UAAxCA,0DAAwC;UAM1CA,eAAwC;UAAxCA,0DAAwC;UAuBlFA,gBAA0B;UAA1BA,+CAA0B", "names": ["Validators", "environment", "InstanceStatus", "i0", "CustomersComponent", "closeModal", "modalId", "modalService", "close", "openModal", "open", "closeCustomerModal", "closeContactModal", "closeCustomerDetailModal", "editSelectedCustomer", "selectedCustomer", "editCustomer", "openAddInstanceModal", "console", "log", "error", "instanceForm", "reset", "name", "serverUrl", "notes", "isEditInstanceMode", "instanceError", "editInstance", "instance", "patchValue", "expirationDate", "Date", "toISOString", "split", "status", "blockReason", "moduleReporting", "moduleAdvancedSecurity", "moduleApiIntegration", "moduleDataExport", "moduleCustomization", "selectedInstance", "deleteInstance", "confirmed", "confirm", "instanceService", "id", "subscribe", "next", "toastr", "success", "instances", "filter", "i", "err", "alert", "statusText", "saveInstance", "value", "invalid", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "savingInstance", "instanceData", "updatedInstance", "undefined", "updateInstance", "index", "findIndex", "closeInstanceModal", "newInstance", "customerId", "createInstance", "createdInstance", "push", "getInstanceStatusText", "Active", "Blocked", "Expired", "Trial", "Maintenance", "String", "openAddContactModal", "contactForm", "firstName", "lastName", "position", "email", "phone", "isPrimary", "isEditContactMode", "contactError", "editContact", "contact", "setValue", "selectedContact", "deleteContact", "contactService", "contacts", "c", "saveContact", "savingContact", "contactData", "updatedContact", "updateContact", "newContact", "createContact", "createdContact", "errors", "errorMessages", "hasOwnProperty", "join", "emailValidator", "length", "constructor", "fb", "customerService", "instanceVersionService", "versionService", "userService", "authService", "certificateService", "http", "monitoringService", "router", "label", "type", "isCustom", "Math", "customerForm", "group", "required", "max<PERSON><PERSON><PERSON>", "abbreviation", "companyId", "taxId", "website", "street", "city", "postalCode", "country", "instanceVersionForm", "versionId", "installedByUserId", "loadCustomers", "openAddCustomerModal", "navigate", "viewCustomerDetail", "customer", "loadInstanceVersions", "instanceId", "loadingInstanceVersions", "instanceVersions", "getInstanceVersions", "versions", "getLatestVersion", "versionNumber", "openAddInstanceVersionModal", "selectedInstanceForVersion", "isEditInstanceVersionMode", "instanceVersionError", "loadAvailableVersions", "loadAvailableUsers", "loadingAvailableVersions", "availableVersions", "getVersions", "loadingAvailableUsers", "availableUsers", "getUsers", "users", "saveInstanceVersion", "savingInstanceVersion", "instanceVersionData", "selectedInstanceVersion", "updatedInstanceVersion", "updateInstanceVersion", "closeInstanceVersionModal", "newInstanceVersion", "parseInt", "addInstanceVersion", "createdInstanceVersion", "viewInstanceDetail", "loadCertificateInfo", "closeInstanceDetailModal", "loadingCertificateInfo", "certificateInfo", "url", "apiUrl", "response", "message", "instanceName", "customerName", "thumbprint", "subject", "issuer", "<PERSON><PERSON><PERSON><PERSON>", "daysToExpiration", "generateCertificate", "generatingCertificate", "generatedCertificate", "post", "downloadCertificate", "blob", "base64ToBlob", "privateKey", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "base64", "contentType", "byteCharacters", "atob", "byteArrays", "offset", "slice", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "Blob", "getCertificateExpirationClass", "queryParams", "edit", "deleteCustomer", "loading", "ngOnInit", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "localStorage", "getItem", "currentFilters", "JSON", "parse", "onFilterChange", "filters", "currentPage", "setItem", "stringify", "removeItem", "onSort", "column", "sortColumn", "sortDirection", "applyFiltersAndSort", "updatePaginatedCustomers", "filteredCustomers", "customers", "sort", "b", "valueA", "toString", "toLowerCase", "valueB", "localeCompare", "onPageChange", "page", "startIndex", "pageSize", "endIndex", "paginatedCustomers", "totalPages", "ceil", "pageRange", "range", "maxPages", "startPage", "max", "floor", "endPage", "min", "saveCustomer", "customerData", "saving", "isEditMode", "updateCustomer", "updatedCustomer", "createCustomer", "createdCustomer", "getCustomers", "copyApiKey", "inputElement", "select", "execCommand", "regenerateApiKey", "regeneratingApiKey", "getInstance", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\customers\\customers.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\customers\\customers.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators, ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';\nimport { environment } from '../../environments/environment';\nimport * as bootstrap from 'bootstrap';\nimport { CustomerService } from '../services/customer.service';\nimport { ContactService } from '../services/contact.service';\nimport { InstanceService } from '../services/instance.service';\nimport { HttpClient } from '@angular/common/http';\nimport { InstanceVersionService, CreateInstanceVersionRequest } from '../services/instance-version.service';\nimport { VersionService } from '../services/version.service';\nimport { UserService } from '../services/user.service';\nimport { AuthService } from '../services/auth.service';\nimport { CertificateService } from '../services/certificate.service';\nimport { ModalService } from '../services/modal.service';\nimport { MonitoringService } from '../services/monitoring.service';\nimport { Customer } from '../models/customer.model';\nimport { Contact, CreateContactRequest } from '../models/contact.model';\nimport { DISInstance, CreateDISInstanceRequest, UpdateDISInstanceRequest, InstanceVersion, InstanceStatus } from '../models/instance.model';\nimport { DISVersion } from '../models/version.model';\nimport { User } from '../models/user.model';\nimport { CertificateInfoResponse, CertificateGenerationResponse } from '../models/certificate.model';\nimport { ToastrService } from 'ngx-toastr';\n@Component({\n  selector: 'app-customers',\n  templateUrl: './customers.component.html',\n  styleUrls: ['./customers.component.css']\n})\nexport class CustomersComponent implements OnInit {\n  // Zpřístupnění enumu InstanceStatus pro šablonu\n  InstanceStatus = InstanceStatus;\n  loading: boolean = false;\n  error: string | null = null;\n  customers: any[] = [];\n  filteredCustomers: any[] = [];\n  paginatedCustomers: any[] = [];\n  selectedCustomer: any = null;\n  contacts: any[] = [];\n  instances: DISInstance[] = [];\n  loadingContacts: boolean = false;\n  loadingInstances: boolean = false;\n  savingContact: boolean = false;\n  savingInstance: boolean = false;\n  contactForm: FormGroup;\n  instanceForm: FormGroup;\n  isEditContactMode: boolean = false;\n  isEditInstanceMode: boolean = false;\n  contactError: string | null = null;\n  instanceError: string | null = null;\n  selectedContact: Contact | null = null;\n  selectedInstance: DISInstance | null = null;\n\n  // Filtrování a řazení\n  filterFields: any[] = [\n    { name: 'name', label: 'Název', type: 'text' },\n    { name: 'contact', label: 'Kontakt', type: 'text', isCustom: true }\n  ];\n  currentFilters: any = {};\n  sortColumn: string = 'name';\n  sortDirection: 'asc' | 'desc' = 'asc';\n\n  // Stránkování\n  currentPage: number = 1;\n  pageSize: number = 10;\n  Math = Math; // Pro použití v šabloně\n\n  // Reference na modaly\n  customerModal: any;\n  contactModal: any;\n  instanceModal: any;\n  customerDetailModal: any;\n\n  /**\n   * Pomocná metoda pro správné zavření modálu\n   * @param modalId ID modálu, který chceme zavřít\n   */\n  closeModal(modalId: string): void {\n    this.modalService.close(modalId);\n  }\n\n  /**\n   * Pomocná metoda pro otevření modálu\n   * @param modalId ID modálu, který chceme otevřít\n   */\n  openModal(modalId: string): void {\n    this.modalService.open(modalId);\n  }\n\n  closeCustomerModal(): void {\n    this.closeModal('customerModal');\n  }\n\n  closeContactModal(): void {\n    this.closeModal('contactModal');\n  }\n\n  closeCustomerDetailModal(): void {\n    this.closeModal('customerDetailModal');\n  }\n\n  editSelectedCustomer(): void {\n    if (this.selectedCustomer) {\n      this.editCustomer(this.selectedCustomer);\n    }\n  }\n\n  openAddInstanceModal(): void {\n    console.log('Open add instance modal');\n\n    if (!this.selectedCustomer) {\n      console.error('No customer selected');\n      return;\n    }\n\n    // Reset formuláře a chybové zprávy\n    this.instanceForm.reset({\n      name: '',\n      serverUrl: '',\n      notes: ''\n    });\n    this.isEditInstanceMode = false;\n    this.instanceError = null;\n\n    // Otevřít modal\n    this.openModal('instanceModal');\n  }\n\n  editInstance(instance: DISInstance): void {\n    console.log('Edit instance', instance);\n\n    // Naplnění formuláře daty instance\n    this.instanceForm.patchValue({\n      name: instance.name,\n      serverUrl: instance.serverUrl || '',\n      expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',\n      status: instance.status || 'Active',\n      blockReason: instance.blockReason || '',\n      moduleReporting: instance.moduleReporting,\n      moduleAdvancedSecurity: instance.moduleAdvancedSecurity,\n      moduleApiIntegration: instance.moduleApiIntegration,\n      moduleDataExport: instance.moduleDataExport,\n      moduleCustomization: instance.moduleCustomization,\n      notes: instance.notes || ''\n    });\n    this.isEditInstanceMode = true;\n    this.selectedInstance = instance;\n    this.instanceError = null;\n\n    // Otevřít modal\n    this.openModal('instanceModal');\n  }\n\n  async deleteInstance(instance: DISInstance): Promise<void> {\n    console.log('Delete instance', instance);\n\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat instanci ${instance.name}?`,\n      'Smazání instance',\n      'Smazat',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.instanceService.deleteInstance(instance.id).subscribe({\n        next: () => {\n          console.log('Instance deleted successfully');\n          this.toastr.success(`Instance ${instance.name} byla úspěšně smazána`, 'Úspěch');\n          // Odstranit instanci ze seznamu\n          this.instances = this.instances.filter(i => i.id !== instance.id);\n        },\n        error: (err) => {\n          console.error('Chyba při mazání instance', err);\n          this.modalService.alert(\n            `Chyba při mazání instance: ${err.status} ${err.statusText}`,\n            'Chyba',\n            'Zavřít',\n            'btn-danger'\n          );\n        }\n      });\n    }\n  }\n\n  saveInstance(): void {\n    console.log('Save instance', this.instanceForm.value);\n\n    if (this.instanceForm.invalid) {\n      this.instanceError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceForm.controls).forEach(key => {\n        const control = this.instanceForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n\n    if (!this.selectedCustomer) {\n      this.instanceError = 'Není vybrán žádný zákazník.';\n      return;\n    }\n\n    this.savingInstance = true;\n\n    const instanceData = this.instanceForm.value;\n\n    if (this.isEditInstanceMode && this.selectedInstance) {\n      // Aktualizace existující instance\n      const updatedInstance: UpdateDISInstanceRequest = {\n        name: instanceData.name || '',\n        serverUrl: instanceData.serverUrl || '',\n        expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n        status: instanceData.status,\n        blockReason: instanceData.blockReason,\n        moduleReporting: instanceData.moduleReporting,\n        moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n        moduleApiIntegration: instanceData.moduleApiIntegration,\n        moduleDataExport: instanceData.moduleDataExport,\n        moduleCustomization: instanceData.moduleCustomization,\n        notes: instanceData.notes || ''\n      };\n\n      console.log('Sending updated instance data:', updatedInstance);\n      this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance).subscribe({\n        next: (updatedInstance) => {\n          console.log('Instance updated successfully:', updatedInstance);\n          this.savingInstance = false;\n\n          // Aktualizovat instanci v seznamu\n          const index = this.instances.findIndex(i => i.id === updatedInstance.id);\n          if (index !== -1) {\n            this.instances[index] = updatedInstance;\n          }\n\n          // Zavřít modal\n          this.closeInstanceModal();\n        },\n        error: (err) => {\n          console.error('Chyba při aktualizaci instance', err);\n          this.instanceError = `Chyba při aktualizaci instance: ${err.status} ${err.statusText}`;\n          this.savingInstance = false;\n        }\n      });\n    } else {\n      // Vytvoření nové instance\n      const newInstance: CreateDISInstanceRequest = {\n        customerId: this.selectedCustomer.id,\n        name: instanceData.name || '',\n        serverUrl: instanceData.serverUrl || '',\n        expirationDate: instanceData.expirationDate ? new Date(instanceData.expirationDate) : undefined,\n        status: instanceData.status,\n        moduleReporting: instanceData.moduleReporting,\n        moduleAdvancedSecurity: instanceData.moduleAdvancedSecurity,\n        moduleApiIntegration: instanceData.moduleApiIntegration,\n        moduleDataExport: instanceData.moduleDataExport,\n        moduleCustomization: instanceData.moduleCustomization,\n        notes: instanceData.notes || ''\n      };\n\n      console.log('Sending new instance data:', newInstance);\n      this.instanceService.createInstance(newInstance).subscribe({\n        next: (createdInstance) => {\n          console.log('Instance created successfully:', createdInstance);\n          this.savingInstance = false;\n\n          // Přidat novou instanci do seznamu\n          this.instances.push(createdInstance);\n\n          // Zavřít modal\n          this.closeInstanceModal();\n        },\n        error: (err) => {\n          console.error('Chyba při vytváření instance', err);\n          this.instanceError = `Chyba při vytváření instance: ${err.status} ${err.statusText}`;\n          this.savingInstance = false;\n        }\n      });\n    }\n  }\n\n  closeInstanceModal(): void {\n    this.closeModal('instanceModal');\n  }\n\n  getInstanceStatusText(status: InstanceStatus | string): string {\n    if (typeof status === 'string') {\n      switch (status) {\n        case 'Active': return 'Aktivní';\n        case 'Blocked': return 'Blokovaná';\n        case 'Expired': return 'Expirovaná';\n        case 'Trial': return 'Zkušební';\n        case 'Maintenance': return 'Údržba';\n        default: return status;\n      }\n    } else {\n      switch (status) {\n        case InstanceStatus.Active: return 'Aktivní';\n        case InstanceStatus.Blocked: return 'Blokovaná';\n        case InstanceStatus.Expired: return 'Expirovaná';\n        case InstanceStatus.Trial: return 'Zkušební';\n        case InstanceStatus.Maintenance: return 'Údržba';\n        default: return String(status);\n      }\n    }\n  }\n\n  openAddContactModal(): void {\n    console.log('Open add contact modal');\n\n    if (!this.selectedCustomer) {\n      console.error('No customer selected');\n      return;\n    }\n\n    // Reset formuláře a chybové zprávy\n    this.contactForm.reset({\n      firstName: '',\n      lastName: '',\n      position: '',\n      email: '',\n      phone: '',\n      notes: '',\n      isPrimary: false\n    });\n    this.isEditContactMode = false;\n    this.contactError = null;\n\n    // Otevřít modal\n    this.openModal('contactModal');\n  }\n\n  editContact(contact: Contact): void {\n    console.log('Edit contact', contact);\n\n    // Naplnění formuláře daty kontaktu\n    this.contactForm.setValue({\n      firstName: contact.firstName,\n      lastName: contact.lastName,\n      position: contact.position || '',\n      email: contact.email || '',\n      phone: contact.phone || '',\n      notes: contact.notes || '',\n      isPrimary: contact.isPrimary\n    });\n    this.isEditContactMode = true;\n    this.selectedContact = contact;\n    this.contactError = null;\n\n    // Otevřít modal\n    this.openModal('contactModal');\n  }\n\n  async deleteContact(contact: Contact): Promise<void> {\n    console.log('Delete contact', contact);\n\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`,\n      'Smazání kontaktu',\n      'Smazat',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.contactService.deleteContact(contact.id).subscribe({\n        next: () => {\n          console.log('Contact deleted successfully');\n          this.toastr.success(`Kontakt ${contact.firstName} ${contact.lastName} byl úspěšně smazán`, 'Úspěch');\n          // Odstranit kontakt ze seznamu\n          this.contacts = this.contacts.filter(c => c.id !== contact.id);\n        },\n        error: (err) => {\n          console.error('Chyba při mazání kontaktu', err);\n          this.modalService.alert(\n            `Chyba při mazání kontaktu: ${err.status} ${err.statusText}`,\n            'Chyba',\n            'Zavřít',\n            'btn-danger'\n          );\n        }\n      });\n    }\n  }\n\n  saveContact(): void {\n    console.log('Save contact', this.contactForm.value);\n\n    if (this.contactForm.invalid) {\n      this.contactError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.contactForm.controls).forEach(key => {\n        const control = this.contactForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n\n    if (!this.selectedCustomer) {\n      this.contactError = 'Není vybrán žádný zákazník.';\n      return;\n    }\n\n    this.savingContact = true;\n\n    const contactData = this.contactForm.value;\n\n    if (this.isEditContactMode && this.selectedContact) {\n      // Aktualizace existujícího kontaktu\n      const updatedContact = {\n        firstName: contactData.firstName || '',\n        lastName: contactData.lastName || '',\n        position: contactData.position || '',\n        email: contactData.email || '',\n        phone: contactData.phone || '',\n        notes: contactData.notes || '',\n        isPrimary: contactData.isPrimary || false\n      };\n\n      console.log('Sending updated contact data:', updatedContact);\n      this.contactService.updateContact(this.selectedContact.id, updatedContact).subscribe({\n        next: (updatedContact) => {\n          console.log('Contact updated successfully:', updatedContact);\n          this.savingContact = false;\n\n          // Aktualizovat kontakt v seznamu\n          const index = this.contacts.findIndex(c => c.id === updatedContact.id);\n          if (index !== -1) {\n            this.contacts[index] = updatedContact;\n          }\n\n          // Zavřít modal\n          this.closeModal('contactModal');\n        },\n        error: (err) => {\n          console.error('Chyba při aktualizaci kontaktu', err);\n          this.contactError = `Chyba při aktualizaci kontaktu: ${err.status} ${err.statusText}`;\n          this.savingContact = false;\n        }\n      });\n    } else {\n      // Vytvoření nového kontaktu\n      const newContact: CreateContactRequest = {\n        customerId: this.selectedCustomer.id,\n        firstName: contactData.firstName || '',\n        lastName: contactData.lastName || '',\n        position: contactData.position || '',\n        email: contactData.email || '',\n        phone: contactData.phone || '',\n        notes: contactData.notes || '',\n        isPrimary: contactData.isPrimary || false\n      };\n\n      console.log('Sending contact data:', newContact);\n      this.contactService.createContact(newContact).subscribe({\n        next: (createdContact) => {\n          console.log('Contact created successfully:', createdContact);\n          this.savingContact = false;\n\n          // Přidat kontakt do seznamu\n          this.contacts.push(createdContact);\n\n          this.closeContactModal();\n        },\n        error: (err) => {\n          console.error('Chyba při vytváření kontaktu', err);\n          console.error('Error details:', err.error);\n\n          // Zobrazit detailnější chybovou zprávu\n          if (err.error && err.error.errors) {\n            const errorMessages = [];\n            for (const key in err.error.errors) {\n              if (err.error.errors.hasOwnProperty(key)) {\n                errorMessages.push(`${key}: ${err.error.errors[key].join(', ')}`);\n              }\n            }\n            this.contactError = `Chyba při vytváření kontaktu: ${errorMessages.join('; ')}`;\n          } else {\n            this.contactError = `Chyba při vytváření kontaktu: ${err.status} ${err.statusText}`;\n          }\n\n          this.savingContact = false;\n        }\n      });\n    }\n  }\n  customerForm: FormGroup;\n  isEditMode: boolean = false;\n  saving: boolean = false;\n\n  isAdmin: boolean = false;\n\n  // Vlastní validátor pro email - kontroluje formát pouze pokud je pole vyplněno\n  emailValidator(): ValidatorFn {\n    return (control: AbstractControl): ValidationErrors | null => {\n      const value = control.value;\n      if (!value || value.length === 0) {\n        return null; // Pokud je pole prázdné, validace projde\n      }\n      // Použijeme standardní validátor emailu\n      return Validators.email(control);\n    };\n  }\n\n  // Proměnné pro správu verzí instancí\n  instanceVersions: { [instanceId: number]: InstanceVersion[] } = {};\n  loadingInstanceVersions: { [instanceId: number]: boolean } = {};\n  availableVersions: DISVersion[] = [];\n  loadingAvailableVersions: boolean = false;\n  availableUsers: User[] = [];\n  loadingAvailableUsers: boolean = false;\n  instanceVersionForm: FormGroup;\n  selectedInstanceVersion: InstanceVersion | null = null;\n  isEditInstanceVersionMode: boolean = false;\n  instanceVersionError: string | null = null;\n  savingInstanceVersion: boolean = false;\n  instanceVersionModal: any;\n  selectedInstanceForVersion: DISInstance | null = null;\n\n  // Proměnné pro práci s certifikáty\n  loadingCertificateInfo: boolean = false;\n  certificateInfo: CertificateInfoResponse | null = null;\n  generatingCertificate: boolean = false;\n  generatedCertificate: CertificateGenerationResponse | null = null;\n\n  // Proměnné pro práci s API klíčem\n  regeneratingApiKey: boolean = false;\n\n  // Proměnné pro statistiky\n  loadingStatistics: boolean = false;\n  customerStatistics: any = null;\n  statisticsError: string | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private customerService: CustomerService,\n    private contactService: ContactService,\n    private instanceService: InstanceService,\n    private instanceVersionService: InstanceVersionService,\n    private versionService: VersionService,\n    private userService: UserService,\n    private authService: AuthService,\n    private certificateService: CertificateService,\n    private modalService: ModalService,\n    private http: HttpClient,\n    private monitoringService: MonitoringService,\n    private router: Router,\n    private toastr: ToastrService\n  ) {\n    this.customerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      abbreviation: ['', [Validators.required, Validators.maxLength(50)]],\n      companyId: ['', Validators.maxLength(20)],                      // Changed from ico to companyId to match API model\n      taxId: ['', Validators.maxLength(20)],                          // Changed from dic to taxId to match API model\n      email: ['', [Validators.email, Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      website: ['', Validators.maxLength(255)],\n      street: ['', [Validators.required, Validators.maxLength(255)]],\n      city: ['', [Validators.required, Validators.maxLength(255)]],      // Added missing fields from API model\n      postalCode: ['', [Validators.required, Validators.maxLength(20)]],\n      country: ['', [Validators.required, Validators.maxLength(100)]],\n      notes: ['', Validators.maxLength(500)]                           // Changed from note to notes to match API model\n    });\n    this.contactForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.maxLength(100)]],\n      lastName: ['', [Validators.required, Validators.maxLength(100)]],\n      position: ['', Validators.maxLength(100)],\n      email: ['', [this.emailValidator(), Validators.maxLength(255)]],\n      phone: ['', Validators.maxLength(50)],\n      notes: ['', Validators.maxLength(100)],\n      isPrimary: [false]\n    });\n    this.instanceForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      status: ['Active'],\n      blockReason: ['', Validators.maxLength(500)],\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false],\n      notes: ['', Validators.maxLength(500)]\n    });\n\n    this.instanceVersionForm = this.fb.group({\n      versionId: ['', Validators.required],\n      installedByUserId: ['', Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n    this.loadCustomers();\n  }\n\n  openAddCustomerModal(): void {\n    // Přesměrujeme na stránku pro vytvoření nového zákazníka\n    this.router.navigate(['/customers/add']);\n  }\n\n  viewCustomerDetail(customer: Customer): void {\n    // Místo otevření modálu přesměrujeme na detailní stránku\n    this.router.navigate(['/customers', customer.id]);\n  }\n\n  loadInstanceVersions(instanceId: number): void {\n    this.loadingInstanceVersions[instanceId] = true;\n    this.instanceVersions[instanceId] = [];\n\n    this.instanceVersionService.getInstanceVersions(instanceId).subscribe({\n      next: (versions) => {\n        console.log(`Loaded versions for instance ${instanceId}:`, versions);\n        this.instanceVersions[instanceId] = versions;\n        this.loadingInstanceVersions[instanceId] = false;\n      },\n      error: (err) => {\n        console.error(`Chyba při načítání verzí instance ${instanceId}`, err);\n        this.loadingInstanceVersions[instanceId] = false;\n      }\n    });\n  }\n\n  getLatestVersion(instanceId: number): string {\n    if (!this.instanceVersions[instanceId] || this.instanceVersions[instanceId].length === 0) {\n      return '-';\n    }\n\n    // Verze jsou seřazeny podle data instalace sestupně, takže první je nejnovější\n    return this.instanceVersions[instanceId][0].versionNumber;\n  }\n\n  openAddInstanceVersionModal(instance: DISInstance): void {\n    console.log('Open add instance version modal', instance);\n    this.selectedInstanceForVersion = instance;\n    this.isEditInstanceVersionMode = false;\n    this.instanceVersionError = null;\n\n    // Reset formuláře\n    this.instanceVersionForm.reset({\n      versionId: '',\n      installedByUserId: '',\n      notes: ''\n    });\n\n    // Načtení dostupných verzí\n    this.loadAvailableVersions();\n\n    // Načtení dostupných uživatelů\n    this.loadAvailableUsers();\n\n    // Otevření modalu\n    this.openModal('instanceVersionModal');\n  }\n\n  loadAvailableVersions(): void {\n    this.loadingAvailableVersions = true;\n    this.availableVersions = [];\n\n    this.versionService.getVersions().subscribe({\n      next: (versions) => {\n        console.log('Loaded available versions:', versions);\n        this.availableVersions = versions;\n        this.loadingAvailableVersions = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dostupných verzí', err);\n        this.loadingAvailableVersions = false;\n        this.instanceVersionError = `Chyba při načítání dostupných verzí: ${err.status} ${err.statusText}`;\n      }\n    });\n  }\n\n  loadAvailableUsers(): void {\n    this.loadingAvailableUsers = true;\n    this.availableUsers = [];\n\n    this.userService.getUsers().subscribe({\n      next: (users) => {\n        console.log('Loaded available users:', users);\n        this.availableUsers = users;\n        this.loadingAvailableUsers = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání dostupných uživatelů', err);\n        this.loadingAvailableUsers = false;\n        this.instanceVersionError = `Chyba při načítání dostupných uživatelů: ${err.status} ${err.statusText}`;\n      }\n    });\n  }\n\n  saveInstanceVersion(): void {\n    if (this.instanceVersionForm.invalid) {\n      this.instanceVersionError = 'Formulář obsahuje chyby. Opravte je prosím.';\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.instanceVersionForm.controls).forEach(key => {\n        const control = this.instanceVersionForm.get(key);\n        control?.markAsTouched();\n      });\n      return;\n    }\n\n    if (!this.selectedInstanceForVersion) {\n      this.instanceVersionError = 'Není vybrána žádná instance.';\n      return;\n    }\n\n    this.savingInstanceVersion = true;\n\n    const instanceVersionData = this.instanceVersionForm.value;\n\n    if (this.isEditInstanceVersionMode && this.selectedInstanceVersion) {\n      // Aktualizace existující verze instance\n      const updatedInstanceVersion = {\n        notes: instanceVersionData.notes || ''\n      };\n\n      console.log('Sending updated instance version data:', updatedInstanceVersion);\n      this.instanceVersionService.updateInstanceVersion(\n        this.selectedInstanceForVersion.id,\n        this.selectedInstanceVersion.id,\n        updatedInstanceVersion\n      ).subscribe({\n        next: (updatedInstanceVersion) => {\n          console.log('Instance version updated successfully:', updatedInstanceVersion);\n          this.savingInstanceVersion = false;\n\n          // Aktualizovat verzi v seznamu\n          this.loadInstanceVersions(this.selectedInstanceForVersion!.id);\n\n          // Zavřít modal\n          this.closeInstanceVersionModal();\n        },\n        error: (err) => {\n          console.error('Chyba při aktualizaci verze instance', err);\n          this.instanceVersionError = `Chyba při aktualizaci verze instance: ${err.status} ${err.statusText}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n    } else {\n      // Vytvoření nové verze instance\n      const newInstanceVersion: CreateInstanceVersionRequest = {\n        versionId: parseInt(instanceVersionData.versionId),\n        installedByUserId: parseInt(instanceVersionData.installedByUserId),\n        notes: instanceVersionData.notes || ''\n      };\n\n      console.log('Sending new instance version data:', newInstanceVersion);\n      this.instanceVersionService.addInstanceVersion(\n        this.selectedInstanceForVersion.id,\n        newInstanceVersion\n      ).subscribe({\n        next: (createdInstanceVersion) => {\n          console.log('Instance version created successfully:', createdInstanceVersion);\n          this.savingInstanceVersion = false;\n\n          // Aktualizovat seznam verzí\n          this.loadInstanceVersions(this.selectedInstanceForVersion!.id);\n\n          // Zavřít modal\n          this.closeInstanceVersionModal();\n        },\n        error: (err) => {\n          console.error('Chyba při vytváření verze instance', err);\n          this.instanceVersionError = `Chyba při vytváření verze instance: ${err.status} ${err.statusText}`;\n          this.savingInstanceVersion = false;\n        }\n      });\n    }\n  }\n\n  closeInstanceVersionModal(): void {\n    this.closeModal('instanceVersionModal');\n  }\n\n  viewInstanceDetail(instance: DISInstance): void {\n    console.log('View instance detail', instance);\n    this.selectedInstanceForVersion = instance;\n\n    // Načtení verzí instance\n    this.loadInstanceVersions(instance.id);\n\n    // Načtení informací o certifikátu\n    console.log('Loading certificate info for instance ID:', instance.id);\n    this.loadCertificateInfo(instance.id);\n\n    // Otevřít modal\n    this.openModal('instanceDetailModal');\n  }\n\n  closeInstanceDetailModal(): void {\n    this.closeModal('instanceDetailModal');\n  }\n\n  // Metoda pro načtení informací o certifikátu\n  loadCertificateInfo(instanceId: number): void {\n    console.log('loadCertificateInfo called with instanceId:', instanceId);\n    this.loadingCertificateInfo = true;\n    this.certificateInfo = null;\n\n    const url = `${environment.apiUrl}/certificates/instance/${instanceId}`;\n    console.log('Calling API endpoint:', url);\n\n    // Použijeme přímo HttpClient místo service\n    this.http.get<CertificateInfoResponse>(url).subscribe({\n      next: (response) => {\n        console.log('Certificate info loaded:', response);\n        this.certificateInfo = response;\n        this.loadingCertificateInfo = false;\n      },\n      error: (err) => {\n        console.error('Error loading certificate info', err);\n        console.error('Error details:', err.status, err.statusText);\n        if (err.error) {\n          console.error('Error message:', err.error.message || err.error);\n        }\n        this.loadingCertificateInfo = false;\n        // Pokud je chyba 404, znamená to, že instance nemá certifikát\n        if (err.status === 404) {\n          console.log('Instance nemá certifikát (404)');\n          this.certificateInfo = null;\n        } else {\n          // Pro ostatní chyby zkusíme vytvořit prázdný objekt, aby se zobrazila sekce\n          console.log('Vytvářím prázdný objekt certifikátu pro zobrazení UI');\n          this.certificateInfo = {\n            instanceId: this.selectedInstanceForVersion?.id || 0,\n            instanceName: this.selectedInstanceForVersion?.name || '',\n            customerName: '',\n            thumbprint: 'Nedostupné',\n            subject: 'Nedostupné',\n            issuer: 'Nedostupné',\n            isValid: false,\n            daysToExpiration: 0\n          };\n        }\n      }\n    });\n  }\n\n  // Metoda pro generování nového certifikátu\n  async generateCertificate(instanceId: number): Promise<void> {\n    const confirmed = await this.modalService.confirm(\n      'Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.',\n      'Generování certifikátu',\n      'Generovat',\n      'Zrušit',\n      'btn-success',\n      'btn-secondary'\n    );\n\n    if (!confirmed) {\n      return;\n    }\n\n    this.generatingCertificate = true;\n    this.generatedCertificate = null;\n\n    const url = `${environment.apiUrl}/certificates/instance/${instanceId}/generate`;\n    console.log('Generating certificate using URL:', url);\n\n    this.http.post<CertificateGenerationResponse>(url, {}).subscribe({\n      next: (response) => {\n        console.log('Certificate generated:', response);\n        this.generatedCertificate = response;\n        this.generatingCertificate = false;\n\n        // Aktualizace informací o certifikátu\n        this.loadCertificateInfo(instanceId);\n\n        // Zobrazení modálu s informacemi o vygenerovaném certifikátu\n        this.modalService.open('certificateGeneratedModal');\n      },\n      error: (err) => {\n        console.error('Error generating certificate', err);\n        this.generatingCertificate = false;\n        this.modalService.alert(\n          `Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`,\n          'Chyba',\n          'Zavřít',\n          'btn-danger'\n        );\n      }\n    });\n  }\n\n  // Metoda pro stažení certifikátu\n  downloadCertificate(): void {\n    if (!this.generatedCertificate) {\n      return;\n    }\n\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n\n  // Pomocná metoda pro konverzi Base64 na Blob\n  private base64ToBlob(base64: string, contentType: string): Blob {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n\n    return new Blob(byteArrays, { type: contentType });\n  }\n\n  // Metoda pro získání CSS třídy podle počtu dní do expirace certifikátu\n  getCertificateExpirationClass(daysToExpiration: number): string {\n    if (daysToExpiration <= 7) {\n      return 'text-danger';\n    } else if (daysToExpiration <= 30) {\n      return 'text-warning';\n    } else {\n      return 'text-info';\n    }\n  }\n\n  editCustomer(customer: any): void {\n    // Přesměrujeme na detailní stránku s parametrem edit=true\n    this.router.navigate(['/customers', customer.id], { queryParams: { edit: 'true' } });\n  }\n\n  async deleteCustomer(customer: any): Promise<void> {\n    console.log('Delete customer', customer);\n\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat zákazníka ${customer.name}?`,\n      'Smazání zákazníka',\n      'Smazat',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.loading = true;\n\n      this.customerService.deleteCustomer(customer.id).subscribe({\n        next: () => {\n          console.log('Customer deleted successfully');\n          this.toastr.success(`Zákazník ${customer.name} byl úspěšně smazán`, 'Úspěch');\n          this.loadCustomers();\n        },\n        error: (err) => {\n          console.error('Chyba při mazání zákazníka', err);\n          this.modalService.alert(\n            `Chyba při mazání zákazníka: ${err.status} ${err.statusText}`,\n            'Chyba',\n            'Zavřít',\n            'btn-danger'\n          );\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  ngOnInit(): void {\n    this.isAdmin = this.authService.isAdmin();\n    console.log('User is admin:', this.isAdmin);\n\n    // Načtení posledního použitého filtru z localStorage\n    try {\n      const lastFilterKey = `last_filter_customers`;\n      const lastFilterJson = localStorage.getItem(lastFilterKey);\n\n      if (lastFilterJson) {\n        this.currentFilters = JSON.parse(lastFilterJson);\n        console.log('Načten poslední filtr z localStorage:', this.currentFilters);\n      }\n    } catch (error) {\n      console.error('Chyba při načítání posledního filtru z localStorage', error);\n    }\n\n    this.loadCustomers();\n  }\n\n  /**\n   * Metody pro filtrování a řazení\n   */\n  onFilterChange(filters: any): void {\n    this.currentFilters = filters;\n    this.currentPage = 1; // Reset na první stránku při změně filtru\n\n    // Uložení aktuálního filtru do localStorage\n    try {\n      const lastFilterKey = `last_filter_customers`;\n      if (Object.keys(filters).length > 0) {\n        localStorage.setItem(lastFilterKey, JSON.stringify(filters));\n        console.log('Uložen aktuální filtr do localStorage:', filters);\n      } else {\n        // Pokud je filtr prázdný, odstraníme ho z localStorage\n        localStorage.removeItem(lastFilterKey);\n        console.log('Odstraněn filtr z localStorage');\n      }\n    } catch (error) {\n      console.error('Chyba při ukládání filtru do localStorage', error);\n    }\n\n    // Načtení dat z backendu s filtrem\n    this.loadCustomers();\n  }\n\n  onSort(column: string): void {\n    if (this.sortColumn === column) {\n      // Pokud klikneme na stejný sloupec, změníme směr řazení\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Pokud klikneme na jiný sloupec, nastavit výchozí směr řazení\n      this.sortColumn = column;\n      this.sortDirection = 'asc';\n    }\n\n    this.applyFiltersAndSort();\n    this.updatePaginatedCustomers();\n  }\n\n  applyFiltersAndSort(): void {\n    // Filtrování se provádí na backendu, zde pouze řadíme\n    this.filteredCustomers = [...this.customers];\n\n    // Řazení\n    this.filteredCustomers.sort((a, b) => {\n      const valueA = a[this.sortColumn]?.toString().toLowerCase() || '';\n      const valueB = b[this.sortColumn]?.toString().toLowerCase() || '';\n\n      if (this.sortDirection === 'asc') {\n        return valueA.localeCompare(valueB);\n      } else {\n        return valueB.localeCompare(valueA);\n      }\n    });\n  }\n\n  /**\n   * Metody pro stránkování\n   */\n  onPageChange(page: number): void {\n    this.currentPage = page;\n    this.updatePaginatedCustomers();\n  }\n\n  updatePaginatedCustomers(): void {\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);\n  }\n\n  get totalPages(): number {\n    return Math.ceil(this.filteredCustomers.length / this.pageSize);\n  }\n\n  get pageRange(): number[] {\n    const range = [];\n    const maxPages = 5;\n    const startPage = Math.max(1, this.currentPage - Math.floor(maxPages / 2));\n    const endPage = Math.min(this.totalPages, startPage + maxPages - 1);\n\n    for (let i = startPage; i <= endPage; i++) {\n      range.push(i);\n    }\n\n    return range;\n  }\n\n  saveCustomer(): void {\n    console.log('saveCustomer() called');\n    if (!this.isAdmin) {\n      this.error = 'Pouze administrátor může vytvářet zákazníky';\n      console.log('User is not admin, returning');\n      return;\n    }\n\n    if (this.customerForm.invalid) {\n      this.error = 'Formulář obsahuje chyby. Opravte je prosím.';\n      console.log('Form is invalid, marking fields as touched');\n      // Označit všechna pole jako touched, aby se zobrazily chyby\n      Object.keys(this.customerForm.controls).forEach(key => {\n        const control = this.customerForm.get(key);\n        control?.markAsTouched();\n        if (control?.invalid) {\n          console.log(`Field ${key} is invalid:`, control.errors);\n        }\n      });\n      return;\n    }\n\n    const customerData = this.customerForm.value;\n    console.log('Form is valid, saving customer data:', customerData);\n    this.saving = true;\n\n    if (this.isEditMode && this.selectedCustomer) {\n      // Aktualizace existujícího zákazníka\n      console.log('Updating customer with ID:', this.selectedCustomer.id);\n      this.customerService.updateCustomer(this.selectedCustomer.id, customerData).subscribe({\n        next: (updatedCustomer) => {\n          console.log('Customer updated successfully:', updatedCustomer);\n          this.saving = false;\n          this.closeCustomerModal();\n          // Obnovit seznam\n          this.loadCustomers();\n        },\n        error: (err) => {\n          console.error('Chyba při aktualizaci zákazníka', err);\n          console.error('Status:', err.status);\n          console.error('Status text:', err.statusText);\n          console.error('Error details:', err.error);\n          this.error = `Chyba při aktualizaci zákazníka: ${err.status} ${err.statusText}`;\n          this.saving = false;\n        }\n      });\n    } else {\n      // Vytvoření nového zákazníka\n      this.customerService.createCustomer(customerData).subscribe({\n        next: (createdCustomer) => {\n          console.log('Customer created successfully:', createdCustomer);\n          this.saving = false;\n          // Zavřít modal\n          this.closeCustomerModal();\n          // Obnovit seznam\n          this.loadCustomers();\n        },\n        error: (err) => {\n          console.error('Chyba při ukládání zákazníka', err);\n          console.error('Status:', err.status);\n          console.error('Status text:', err.statusText);\n          console.error('Error details:', err.error);\n          this.error = `Chyba při ukládání zákazníka: ${err.status} ${err.statusText}`;\n          this.saving = false;\n        }\n      });\n    }\n  }\n\n  loadCustomers(): void {\n    this.loading = true;\n\n    // Použití filtrů při načítání dat\n    this.customerService.getCustomers(this.currentFilters).subscribe({\n      next: (customers) => {\n        console.log('Načteni zákazníci:', customers);\n        this.customers = customers || [];\n\n        // Kontakty jsou již načteny z backendu, není potřeba je načítat znovu\n        // Pouze logujeme počet kontaktů pro kontrolu\n        this.customers.forEach(customer => {\n          if (customer.contacts) {\n            console.log(`Zákazník ${customer.name} má ${customer.contacts.length} kontaktů`);\n          }\n        });\n\n        this.filteredCustomers = [...this.customers];\n        // Řazení stále provádíme na klientské straně\n        this.applyFiltersAndSort();\n        this.updatePaginatedCustomers();\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání zákazníků', err);\n        this.loading = false;\n      }\n    });\n  }\n\n  /**\n   * Kopírování API klíče do schránky\n   * @param inputElement Reference na input element s API klíčem\n   */\n  copyApiKey(inputElement: HTMLInputElement): void {\n    inputElement.select();\n    document.execCommand('copy');\n    this.modalService.alert(\n      'API klíč byl zkopírován do schránky.',\n      'Informace',\n      'OK',\n      'btn-success'\n    );\n  }\n\n  /**\n   * Regenerace API klíče pro instanci\n   * @param instanceId ID instance\n   */\n  async regenerateApiKey(instanceId: number): Promise<void> {\n    const confirmed = await this.modalService.confirm(\n      'Opravdu chcete regenerovat API klíč? Všechny aplikace používající stávající klíč budou muset být aktualizovány.',\n      'Regenerace API klíče',\n      'Regenerovat',\n      'Zrušit',\n      'btn-warning',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.regeneratingApiKey = true;\n\n      this.instanceService.regenerateApiKey(instanceId).subscribe({\n        next: () => {\n          // Po úspěšné regeneraci načti aktualizovaná data instance\n          this.instanceService.getInstance(instanceId).subscribe({\n            next: (updatedInstance) => {\n              // Aktualizovat instanci v seznamu\n              const index = this.instances.findIndex(i => i.id === instanceId);\n              if (index !== -1) {\n                this.instances[index] = updatedInstance;\n              }\n\n              // Aktualizovat vybranou instanci\n              if (this.selectedInstanceForVersion && this.selectedInstanceForVersion.id === instanceId) {\n                this.selectedInstanceForVersion = updatedInstance;\n              }\n\n              this.regeneratingApiKey = false;\n              this.modalService.alert(\n                'API klíč byl úspěšně regenerován.',\n                'Informace',\n                'OK',\n                'btn-success'\n              );\n            },\n            error: (err) => {\n              console.error('Chyba při získávání aktualizované instance', err);\n              this.regeneratingApiKey = false;\n              this.modalService.alert(\n                `Chyba při získávání aktualizované instance: ${err.status} ${err.statusText}`,\n                'Chyba',\n                'Zavřít',\n                'btn-danger'\n              );\n            }\n          });\n        },\n        error: (err) => {\n          console.error('Chyba při regeneraci API klíče', err);\n          this.regeneratingApiKey = false;\n          this.modalService.alert(\n            `Chyba při regeneraci API klíče: ${err.status} ${err.statusText}`,\n            'Chyba',\n            'Zavřít',\n            'btn-danger'\n          );\n        }\n      });\n    }\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2><PERSON><PERSON><PERSON><PERSON><PERSON>ík<PERSON></h2>\n    <div class=\"d-flex flex-wrap gap-2\">\n      <a [routerLink]=\"['/instance-wizard']\" class=\"btn btn-success text-white\" *ngIf=\"isAdmin\">\n        <i class=\"bi bi-magic me-2\"></i><span class=\"d-none d-md-inline\">Průvodce vytvořením instance</span><span class=\"d-inline d-md-none\">Průvodce</span>\n      </a>\n      <a [routerLink]=\"['/customers/add']\" class=\"btn btn-primary\" *ngIf=\"isAdmin\">\n        <i class=\"bi bi-building-fill-add me-2\"></i><span class=\"d-none d-md-inline\">Přidat zákazníka</span><span class=\"d-inline d-md-none\">Přidat</span>\n      </a>\n\n    </div>\n  </div>\n\n  <div class=\"card\">\n    <div class=\"card-body\">\n      <!-- Pokročilé filtrování a vyhledávání -->\n      <app-advanced-filter\n        [entityType]=\"'customers'\"\n        [fields]=\"filterFields\"\n        (filterChange)=\"onFilterChange($event)\">\n      </app-advanced-filter>\n\n      <div *ngIf=\"loading\" class=\"d-flex justify-content-center mt-4\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Načítání...</span>\n        </div>\n      </div>\n\n      <div *ngIf=\"error\" class=\"alert alert-danger mt-4\">\n        {{ error }}\n      </div>\n\n      <div *ngIf=\"!loading && !error && filteredCustomers.length === 0\" class=\"alert alert-info mt-4\">\n        Žádní zákazníci nebyli nalezeni.\n      </div>\n\n      <div *ngIf=\"!loading && !error && filteredCustomers.length > 0\" class=\"table-responsive mt-4\">\n        <table class=\"table table-striped table-hover\">\n          <thead class=\"dark-header table-header-override\">\n            <tr class=\"dark-header-row\">\n              <th (click)=\"onSort('name')\" class=\"sortable-header\">\n                Název\n                <i class=\"bi\"\n                  [class.bi-sort-up]=\"sortColumn === 'name' && sortDirection === 'asc'\"\n                  [class.bi-sort-down]=\"sortColumn === 'name' && sortDirection === 'desc'\"\n                  [class.bi-sort]=\"sortColumn !== 'name'\"></i>\n              </th>\n              <th (click)=\"onSort('abbreviation')\" class=\"sortable-header d-none d-md-table-cell\">\n                Zkratka\n                <i class=\"bi\"\n                  [class.bi-sort-up]=\"sortColumn === 'abbreviation' && sortDirection === 'asc'\"\n                  [class.bi-sort-down]=\"sortColumn === 'abbreviation' && sortDirection === 'desc'\"\n                  [class.bi-sort]=\"sortColumn !== 'abbreviation'\"></i>\n              </th>\n              <th (click)=\"onSort('companyId')\" class=\"sortable-header d-none d-md-table-cell\">\n                IČ\n                <i class=\"bi\"\n                  [class.bi-sort-up]=\"sortColumn === 'companyId' && sortDirection === 'asc'\"\n                  [class.bi-sort-down]=\"sortColumn === 'companyId' && sortDirection === 'desc'\"\n                  [class.bi-sort]=\"sortColumn !== 'companyId'\"></i>\n              </th>\n              <th (click)=\"onSort('contactsCount')\" class=\"sortable-header d-none d-md-table-cell\">\n                Kontakty\n                <i class=\"bi\"\n                  [class.bi-sort-up]=\"sortColumn === 'contactsCount' && sortDirection === 'asc'\"\n                  [class.bi-sort-down]=\"sortColumn === 'contactsCount' && sortDirection === 'desc'\"\n                  [class.bi-sort]=\"sortColumn !== 'contactsCount'\"></i>\n              </th>\n              <th (click)=\"onSort('instancesCount')\" class=\"sortable-header d-none d-lg-table-cell\">\n                Instance\n                <i class=\"bi\"\n                  [class.bi-sort-up]=\"sortColumn === 'instancesCount' && sortDirection === 'asc'\"\n                  [class.bi-sort-down]=\"sortColumn === 'instancesCount' && sortDirection === 'desc'\"\n                  [class.bi-sort]=\"sortColumn !== 'instancesCount'\"></i>\n              </th>\n              <th>Akce</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let customer of paginatedCustomers\">\n              <td>\n                <div>{{ customer.name }}</div>\n                <div class=\"d-md-none small text-muted\">Zkratka: {{ customer.abbreviation }}</div>\n                <div class=\"d-md-none small text-muted\">IČ: {{ customer.companyId }}</div>\n                <div class=\"d-md-none small text-muted\">Kontakty: {{ customer.contactsCount || 0 }}</div>\n                <div class=\"d-lg-none small text-muted\">Instance: {{ customer.instancesCount || 0 }}</div>\n              </td>\n              <td class=\"d-none d-md-table-cell\">{{ customer.abbreviation }}</td>\n              <td class=\"d-none d-md-table-cell\">{{ customer.companyId }}</td>\n              <td class=\"d-none d-md-table-cell\">{{ customer.contactsCount || 0 }}</td>\n              <td class=\"d-none d-lg-table-cell\">{{ customer.instancesCount || 0 }}</td>\n              <td>\n                <div class=\"btn-group\">\n                  <button class=\"btn btn-sm btn-outline-info\" (click)=\"viewCustomerDetail(customer)\" title=\"Zobrazit detail\">\n                    <i class=\"bi bi-eye-fill\"></i>\n                  </button>\n                  <button class=\"btn btn-sm btn-outline-primary\" (click)=\"editCustomer(customer)\" *ngIf=\"isAdmin\" title=\"Upravit\">\n                    <i class=\"bi bi-pencil-fill\"></i>\n                  </button>\n                  <button class=\"btn btn-sm btn-outline-danger\" (click)=\"deleteCustomer(customer)\" *ngIf=\"isAdmin\" title=\"Smazat\">\n                    <i class=\"bi bi-trash-fill\"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <!-- Stránkování -->\n      <div *ngIf=\"!loading && !error && filteredCustomers.length > 0\" class=\"d-flex justify-content-between align-items-center mt-3\">\n        <div class=\"pagination-info\">\n          Zobrazeno {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredCustomers.length) }} z {{ filteredCustomers.length }} záznamů\n        </div>\n        <nav aria-label=\"Stránkování\">\n          <ul class=\"pagination mb-0\">\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\n              <a class=\"page-link\" href=\"javascript:void(0)\" (click)=\"onPageChange(1)\">\n                <i class=\"bi bi-chevron-double-left\"></i>\n              </a>\n            </li>\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\n              <a class=\"page-link\" href=\"javascript:void(0)\" (click)=\"onPageChange(currentPage - 1)\">\n                <i class=\"bi bi-chevron-left\"></i>\n              </a>\n            </li>\n            <li *ngFor=\"let page of pageRange\" class=\"page-item\" [class.active]=\"page === currentPage\">\n              <a class=\"page-link\" href=\"javascript:void(0)\" (click)=\"onPageChange(page)\">{{ page }}</a>\n            </li>\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\n              <a class=\"page-link\" href=\"javascript:void(0)\" (click)=\"onPageChange(currentPage + 1)\">\n                <i class=\"bi bi-chevron-right\"></i>\n              </a>\n            </li>\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\n              <a class=\"page-link\" href=\"javascript:void(0)\" (click)=\"onPageChange(totalPages)\">\n                <i class=\"bi bi-chevron-double-right\"></i>\n              </a>\n            </li>\n          </ul>\n        </nav>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu zákazníka -->\n<div class=\"modal fade\" id=\"customerModal\" tabindex=\"-1\" aria-labelledby=\"customerModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"customerModalLabel\">{{ isEditMode ? 'Upravit zákazníka' : 'Přidat zákazníka' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"error\" class=\"alert alert-danger mb-3\">\n          {{ error }}\n        </div>\n        <form [formGroup]=\"customerForm\" (ngSubmit)=\"saveCustomer()\">\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"name\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('name')?.invalid, 'valid-field': customerForm.get('name')?.valid}\">Název</label>\n              <input type=\"text\" class=\"form-control\" id=\"name\" formControlName=\"name\" [class.is-invalid]=\"customerForm.get('name')?.invalid && customerForm.get('name')?.touched\">\n              <div *ngIf=\"customerForm.get('name')?.invalid && customerForm.get('name')?.touched\" class=\"invalid-feedback\">\n                Název je povinný\n              </div>\n            </div>\n\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"abbreviation\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('abbreviation')?.invalid, 'valid-field': customerForm.get('abbreviation')?.valid}\">Zkratka</label>\n              <input type=\"text\" class=\"form-control\" id=\"abbreviation\" formControlName=\"abbreviation\" [class.is-invalid]=\"customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched\">\n              <div *ngIf=\"customerForm.get('abbreviation')?.invalid && customerForm.get('abbreviation')?.touched\" class=\"invalid-feedback\">\n                Zkratka je povinná a nesmí být delší než 50 znaků\n              </div>\n            </div>\n          </div>\n\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"taxId\" class=\"form-label\">DIČ</label>\n              <input type=\"text\" class=\"form-control\" id=\"taxId\" formControlName=\"taxId\">\n            </div>\n\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"email\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('email')?.invalid, 'valid-field': customerForm.get('email')?.valid}\">Email</label>\n              <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\" [class.is-invalid]=\"customerForm.get('email')?.invalid && customerForm.get('email')?.touched\">\n              <div *ngIf=\"customerForm.get('email')?.invalid && customerForm.get('email')?.touched\" class=\"invalid-feedback\">\n                <span *ngIf=\"customerForm.get('email')?.errors?.['email']\">Neplatný formát emailu</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"phone\" class=\"form-label\">Telefon</label>\n              <input type=\"text\" class=\"form-control\" id=\"phone\" formControlName=\"phone\">\n            </div>\n\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"website\" class=\"form-label\">Webové stránky</label>\n              <input type=\"text\" class=\"form-control\" id=\"website\" formControlName=\"website\">\n            </div>\n          </div>\n\n          <div class=\"row\">\n            <div class=\"col-md-12 mb-3\">\n              <label for=\"street\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('street')?.invalid, 'valid-field': customerForm.get('street')?.valid}\">Ulice</label>\n              <input type=\"text\" class=\"form-control\" id=\"street\" formControlName=\"street\" [class.is-invalid]=\"customerForm.get('street')?.invalid && customerForm.get('street')?.touched\">\n              <div *ngIf=\"customerForm.get('street')?.invalid && customerForm.get('street')?.touched\" class=\"invalid-feedback\">\n                Ulice je povinná\n              </div>\n            </div>\n          </div>\n\n          <div class=\"row\">\n            <div class=\"col-md-4 mb-3\">\n              <label for=\"city\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('city')?.invalid, 'valid-field': customerForm.get('city')?.valid}\">Město</label>\n              <input type=\"text\" class=\"form-control\" id=\"city\" formControlName=\"city\" [class.is-invalid]=\"customerForm.get('city')?.invalid && customerForm.get('city')?.touched\">\n              <div *ngIf=\"customerForm.get('city')?.invalid && customerForm.get('city')?.touched\" class=\"invalid-feedback\">\n                Město je povinné\n              </div>\n            </div>\n\n            <div class=\"col-md-4 mb-3\">\n              <label for=\"postalCode\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('postalCode')?.invalid, 'valid-field': customerForm.get('postalCode')?.valid}\">PSČ</label>\n              <input type=\"text\" class=\"form-control\" id=\"postalCode\" formControlName=\"postalCode\" [class.is-invalid]=\"customerForm.get('postalCode')?.invalid && customerForm.get('postalCode')?.touched\">\n              <div *ngIf=\"customerForm.get('postalCode')?.invalid && customerForm.get('postalCode')?.touched\" class=\"invalid-feedback\">\n                PSČ je povinné\n              </div>\n            </div>\n\n            <div class=\"col-md-4 mb-3\">\n              <label for=\"country\" class=\"form-label\" [ngClass]=\"{'required-field': customerForm.get('country')?.invalid, 'valid-field': customerForm.get('country')?.valid}\">Země</label>\n              <input type=\"text\" class=\"form-control\" id=\"country\" formControlName=\"country\" [class.is-invalid]=\"customerForm.get('country')?.invalid && customerForm.get('country')?.touched\">\n              <div *ngIf=\"customerForm.get('country')?.invalid && customerForm.get('country')?.touched\" class=\"invalid-feedback\">\n                Země je povinná\n              </div>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"notes\" class=\"form-label\">Poznámka</label>\n            <textarea class=\"form-control\" id=\"notes\" formControlName=\"notes\" rows=\"3\"></textarea>\n          </div>\n\n          <!-- Sekce pro kontaktní osoby (pouze v režimu editace) -->\n          <div *ngIf=\"isEditMode\" class=\"mt-4 mb-3\">\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\n              <h5 class=\"mb-0\">Kontaktní osoby</h5>\n              <button type=\"button\" class=\"btn btn-outline-primary btn-sm\" (click)=\"openAddContactModal()\">\n                <i class=\"bi bi-person-plus-fill me-1\"></i>Přidat kontakt\n              </button>\n            </div>\n\n            <div *ngIf=\"loadingContacts\" class=\"d-flex justify-content-center\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingContacts && contacts.length === 0\" class=\"alert alert-info\">\n              Žádné kontaktní osoby nebyly nalezeny.\n            </div>\n\n            <div *ngIf=\"!loadingContacts && contacts.length > 0\" class=\"table-responsive\">\n              <table class=\"table table-striped table-hover\">\n                <thead class=\"dark-header table-header-override\">\n                  <tr class=\"dark-header-row\">\n                    <th>Jméno</th>\n                    <th>Pozice</th>\n                    <th>Email</th>\n                    <th>Telefon</th>\n                    <th>Akce</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let contact of contacts\">\n                    <td>\n                      <span [class.fw-bold]=\"contact.isPrimary\">{{ contact.firstName }} {{ contact.lastName }}</span>\n                      <span *ngIf=\"contact.isPrimary\" class=\"badge bg-primary ms-2\">Primární</span>\n                    </td>\n                    <td>{{ contact.position || '-' }}</td>\n                    <td>\n                      <a *ngIf=\"contact.email\" href=\"mailto:{{ contact.email }}\">{{ contact.email }}</a>\n                      <span *ngIf=\"!contact.email\">-</span>\n                    </td>\n                    <td>\n                      <a *ngIf=\"contact.phone\" href=\"tel:{{ contact.phone }}\">{{ contact.phone }}</a>\n                      <span *ngIf=\"!contact.phone\">-</span>\n                    </td>\n                    <td>\n                      <div class=\"btn-group\">\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-primary\" (click)=\"editContact(contact)\" title=\"Upravit\">\n                          <i class=\"bi bi-pencil-fill\"></i>\n                        </button>\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" (click)=\"deleteContact(contact)\" title=\"Smazat\">\n                          <i class=\"bi bi-trash-fill\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <!-- Sekce pro instance DIS (pouze v režimu editace) -->\n          <div *ngIf=\"isEditMode\" class=\"mt-4 mb-3\">\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\n              <h5 class=\"mb-0\">Instance DIS</h5>\n              <button type=\"button\" class=\"btn btn-outline-primary btn-sm\" (click)=\"openAddInstanceModal()\">\n                <i class=\"bi bi-plus-circle-fill me-1\"></i>Přidat instanci\n              </button>\n            </div>\n\n            <div *ngIf=\"loadingInstances\" class=\"d-flex justify-content-center\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingInstances && instances.length === 0\" class=\"alert alert-info\">\n              Žádné instance DIS nebyly nalezeny.\n            </div>\n\n            <div *ngIf=\"!loadingInstances && instances.length > 0\" class=\"table-responsive\">\n              <table class=\"table table-striped table-hover\">\n                <thead class=\"dark-header table-header-override\">\n                  <tr class=\"dark-header-row\">\n                    <th>Název</th>\n                    <th>Server</th>\n                    <th>Databáze</th>\n                    <th>Status</th>\n                    <th>Aktuální verze</th>\n                    <th>Datum instalace</th>\n                    <th>Datum expirace</th>\n                    <th>Akce</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let instance of instances\">\n                    <td><a href=\"javascript:void(0)\" (click)=\"viewInstanceDetail(instance)\">{{ instance.name }}</a></td>\n                    <td>{{ instance.serverUrl }}</td>\n                    <td></td>\n                    <td>\n                      <span class=\"badge\" [ngClass]=\"{\n                        'bg-success': instance.status === InstanceStatus.Active,\n                        'bg-danger': instance.status === InstanceStatus.Blocked,\n                        'bg-warning': instance.status === InstanceStatus.Trial,\n                        'bg-secondary': instance.status === InstanceStatus.Maintenance,\n                        'bg-dark': instance.status === InstanceStatus.Expired\n                      }\">\n                        {{ getInstanceStatusText(instance.status) }}\n                      </span>\n                    </td>\n                    <td>\n                      <span *ngIf=\"loadingInstanceVersions[instance.id]\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                        <span class=\"visually-hidden\">Načítání...</span>\n                      </span>\n                      <span *ngIf=\"!loadingInstanceVersions[instance.id]\">\n                        {{ getLatestVersion(instance.id) }}\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-primary ms-2\"\n                                (click)=\"openAddInstanceVersionModal(instance)\"\n                                title=\"Přidat verzi\">\n                          <i class=\"bi bi-plus-circle-fill\"></i>\n                        </button>\n                      </span>\n                    </td>\n                    <td>{{ instance.installationDate | date:'dd.MM.yyyy' }}</td>\n                    <td>{{ instance.expirationDate ? (instance.expirationDate | date:'dd.MM.yyyy') : '' }}</td>\n                    <td>\n                      <div class=\"btn-group\">\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-primary\" (click)=\"editInstance(instance)\" title=\"Upravit\">\n                          <i class=\"bi bi-pencil-fill\"></i>\n                        </button>\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-info\" [routerLink]=\"['/instance-metrics', instance.id]\" title=\"Zobrazit metriky\">\n                          <i class=\"bi bi-graph-up\"></i>\n                        </button>\n                        <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" (click)=\"deleteInstance(instance)\" title=\"Smazat\">\n                          <i class=\"bi bi-trash-fill\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\" (click)=\"closeCustomerModal()\">\n              <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n            </button>\n            <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"customerForm.invalid || saving\" (click)=\"saveCustomer()\">\n              <span *ngIf=\"saving\" class=\"spinner-border spinner-border-sm me-1\"></span>\n              <i *ngIf=\"!saving\" class=\"bi bi-save me-1\"></i>{{ isEditMode ? 'Aktualizovat' : 'Uložit' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro detail zákazníka -->\n<div class=\"modal fade\" id=\"customerDetailModal\" tabindex=\"-1\" aria-labelledby=\"customerDetailModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"customerDetailModalLabel\">Detail zákazníka</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\" *ngIf=\"selectedCustomer\">\n        <div class=\"row mb-4\">\n          <div class=\"col-md-6\">\n            <h4>{{ selectedCustomer.name }}</h4>\n            <p><strong>Zkratka:</strong> {{ selectedCustomer.abbreviation }}</p>\n            <p *ngIf=\"selectedCustomer.companyId\"><strong>IČ:</strong> {{ selectedCustomer.companyId }}</p>\n            <p *ngIf=\"selectedCustomer.taxId\"><strong>DIČ:</strong> {{ selectedCustomer.taxId }}</p>\n            <p><strong>Ulice:</strong> {{ selectedCustomer.street }}</p>\n            <p><strong>Město:</strong> {{ selectedCustomer.city }}</p>\n            <p><strong>PSČ:</strong> {{ selectedCustomer.postalCode }}</p>\n            <p><strong>Země:</strong> {{ selectedCustomer.country }}</p>\n          </div>\n          <div class=\"col-md-6\">\n            <p *ngIf=\"selectedCustomer.email\"><strong>Email:</strong> {{ selectedCustomer.email }}</p>\n            <p *ngIf=\"selectedCustomer.phone\"><strong>Telefon:</strong> {{ selectedCustomer.phone }}</p>\n            <p *ngIf=\"selectedCustomer.website\"><strong>Web:</strong> {{ selectedCustomer.website }}</p>\n            <p *ngIf=\"selectedCustomer.notes\"><strong>Poznámka:</strong> {{ selectedCustomer.notes }}</p>\n          </div>\n        </div>\n\n        <!-- Statistiky zákazníka -->\n        <div class=\"card mb-4\">\n          <div class=\"card-header bg-light\">\n            <h5 class=\"mb-0\">Statistiky zákazníka</h5>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"loadingStatistics\" class=\"d-flex justify-content-center\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingStatistics && customerStatistics\" class=\"row\">\n              <div class=\"col-md-3 mb-3\">\n                <div class=\"card bg-primary text-white\">\n                  <div class=\"card-body text-center\">\n                    <h3 class=\"display-4\">{{ customerStatistics.instancesCount }}</h3>\n                    <p class=\"mb-0\">Celkem instancí</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-3 mb-3\">\n                <div class=\"card bg-success text-white\">\n                  <div class=\"card-body text-center\">\n                    <h3 class=\"display-4\">{{ customerStatistics.activeInstancesCount }}</h3>\n                    <p class=\"mb-0\">Aktivních instancí</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-3 mb-3\">\n                <div class=\"card bg-info text-white\">\n                  <div class=\"card-body text-center\">\n                    <h3 class=\"display-4\">{{ customerStatistics.apiCallsLast24h }}</h3>\n                    <p class=\"mb-0\">API volání (24h)</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-3 mb-3\">\n                <div class=\"card bg-warning text-dark\">\n                  <div class=\"card-body text-center\">\n                    <h3 class=\"display-4\">{{ customerStatistics.expiringCertificatesCount }}</h3>\n                    <p class=\"mb-0\">Expirující certifikáty</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                  <div class=\"card-body\">\n                    <h5 class=\"card-title\">Výkon API</h5>\n                    <p class=\"mb-0\"><strong>Průměrná doba odezvy:</strong> {{ customerStatistics.avgApiResponseTime | number:'1.2-2' }} ms</p>\n                    <p class=\"mb-0\"><strong>Bezpečnostní události (24h):</strong> {{ customerStatistics.securityEventsCount }}</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                  <div class=\"card-body\">\n                    <h5 class=\"card-title\">Nejpoužívanější verze</h5>\n                    <p class=\"mb-0\"><strong>Verze:</strong> {{ customerStatistics.mostUsedVersion || 'Není k dispozici' }}</p>\n                    <p class=\"mb-0\"><strong>Počet instancí:</strong> {{ customerStatistics.mostUsedVersionCount || 0 }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingStatistics && statisticsError\" class=\"alert alert-danger\">\n              {{ statisticsError }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"mb-3\">\n          <h5 class=\"mb-0\">Kontaktní osoby</h5>\n        </div>\n\n        <div *ngIf=\"loadingContacts\" class=\"d-flex justify-content-center\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Načítání...</span>\n          </div>\n        </div>\n\n        <div *ngIf=\"!loadingContacts && contacts.length === 0\" class=\"alert alert-info\">\n          Žádné kontaktní osoby nebyly nalezeny.\n        </div>\n\n        <div *ngIf=\"!loadingContacts && contacts.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-striped table-hover\">\n            <thead class=\"dark-header table-header-override\">\n              <tr class=\"dark-header-row\">\n                <th>Jméno</th>\n                <th>Pozice</th>\n                <th>Email</th>\n                <th>Telefon</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let contact of contacts\">\n                <td>\n                  <span [class.fw-bold]=\"contact.isPrimary\">{{ contact.firstName }} {{ contact.lastName }}</span>\n                  <span *ngIf=\"contact.isPrimary\" class=\"badge bg-primary ms-2\">Primární</span>\n                </td>\n                <td>{{ contact.position }}</td>\n                <td>\n                  <a *ngIf=\"contact.email\" href=\"mailto:{{ contact.email }}\">{{ contact.email }}</a>\n                </td>\n                <td>\n                  <a *ngIf=\"contact.phone\" href=\"tel:{{ contact.phone }}\">{{ contact.phone }}</a>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <div class=\"mb-3 mt-4\">\n          <h5 class=\"mb-0\">Instance DIS</h5>\n        </div>\n\n        <div *ngIf=\"loadingInstances\" class=\"d-flex justify-content-center\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Načítání...</span>\n          </div>\n        </div>\n\n        <div *ngIf=\"!loadingInstances && instances.length === 0\" class=\"alert alert-info\">\n          Žádné instance DIS nebyly nalezeny.\n        </div>\n\n        <div *ngIf=\"!loadingInstances && instances.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-striped table-hover\">\n            <thead class=\"dark-header table-header-override\">\n              <tr class=\"dark-header-row\">\n                <th>Název</th>\n                <th>Server</th>\n                <th>Databáze</th>\n                <th>Status</th>\n                <th>Aktuální verze</th>\n                <th>Datum instalace</th>\n                <th>Datum expirace</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let instance of instances\">\n                <td><a href=\"javascript:void(0)\" (click)=\"viewInstanceDetail(instance)\">{{ instance.name }}</a></td>\n                <td>{{ instance.serverUrl }}</td>\n                <td></td>\n                <td>\n                  <span class=\"badge\" [ngClass]=\"{\n                    'bg-success': instance.status === InstanceStatus.Active,\n                    'bg-danger': instance.status === InstanceStatus.Blocked,\n                    'bg-warning': instance.status === InstanceStatus.Trial,\n                    'bg-secondary': instance.status === InstanceStatus.Maintenance,\n                    'bg-dark': instance.status === InstanceStatus.Expired\n                  }\">\n                    {{ getInstanceStatusText(instance.status) }}\n                  </span>\n                </td>\n                <td>\n                  <span *ngIf=\"loadingInstanceVersions[instance.id]\" class=\"spinner-border spinner-border-sm\" role=\"status\">\n                    <span class=\"visually-hidden\">Načítání...</span>\n                  </span>\n                  <span *ngIf=\"!loadingInstanceVersions[instance.id]\">{{ getLatestVersion(instance.id) }}</span>\n                </td>\n                <td>{{ instance.installationDate | date:'dd.MM.yyyy' }}</td>\n                <td>{{ instance.expirationDate ? (instance.expirationDate | date:'dd.MM.yyyy') : '' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <div class=\"d-flex justify-content-end mt-3\">\n          <button type=\"button\" class=\"btn btn-secondary me-2\" data-bs-dismiss=\"modal\" (click)=\"closeCustomerDetailModal()\">\n            <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n          </button>\n          <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"!selectedCustomer\" (click)=\"editSelectedCustomer()\">\n            <i class=\"bi bi-pencil-fill me-1\"></i>Upravit\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu kontaktu -->\n<div class=\"modal fade\" id=\"contactModal\" tabindex=\"-1\" aria-labelledby=\"contactModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"contactModalLabel\">{{ isEditContactMode ? 'Upravit kontakt' : 'Přidat kontakt' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"contactError\" class=\"alert alert-danger mb-3\">\n          {{ contactError }}\n        </div>\n        <form [formGroup]=\"contactForm\" (ngSubmit)=\"saveContact()\">\n          <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"firstName\" class=\"form-label\" [ngClass]=\"{'required-field': contactForm.get('firstName')?.invalid, 'valid-field': contactForm.get('firstName')?.valid}\">Jméno</label>\n              <input type=\"text\" class=\"form-control\" id=\"firstName\" formControlName=\"firstName\">\n              <div *ngIf=\"contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched\" class=\"text-danger\">\n                Jméno je povinné\n              </div>\n            </div>\n\n            <div class=\"col-md-6 mb-3\">\n              <label for=\"lastName\" class=\"form-label\" [ngClass]=\"{'required-field': contactForm.get('lastName')?.invalid, 'valid-field': contactForm.get('lastName')?.valid}\">Příjmení</label>\n              <input type=\"text\" class=\"form-control\" id=\"lastName\" formControlName=\"lastName\">\n              <div *ngIf=\"contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched\" class=\"text-danger\">\n                Příjmení je povinné\n              </div>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"position\" class=\"form-label\">Pozice</label>\n            <input type=\"text\" class=\"form-control\" id=\"position\" formControlName=\"position\">\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"contactEmail\" class=\"form-label\">Email</label>\n            <input type=\"email\" class=\"form-control\" id=\"contactEmail\" formControlName=\"email\" [class.is-invalid]=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\">\n            <div *ngIf=\"contactForm.get('email')?.invalid && contactForm.get('email')?.touched\" class=\"invalid-feedback\">\n              <span *ngIf=\"contactForm.get('email')?.errors?.['email']\">Neplatný formát emailu</span>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"contactPhone\" class=\"form-label\">Telefon</label>\n            <input type=\"text\" class=\"form-control\" id=\"contactPhone\" formControlName=\"phone\">\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"contactNote\" class=\"form-label\">Poznámka</label>\n            <textarea class=\"form-control\" id=\"contactNote\" formControlName=\"notes\" rows=\"2\"></textarea>\n          </div>\n\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" class=\"form-check-input\" id=\"isPrimary\" formControlName=\"isPrimary\">\n            <label class=\"form-check-label\" for=\"isPrimary\">Hlavní kontakt</label>\n          </div>\n\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\" (click)=\"closeContactModal()\">\n              <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n            </button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"contactForm.invalid || savingContact\">\n              <span *ngIf=\"savingContact\" class=\"spinner-border spinner-border-sm me-1\"></span>\n              <i *ngIf=\"!savingContact\" class=\"bi bi-save me-1\"></i>{{ isEditContactMode ? 'Aktualizovat' : 'Uložit' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu instance DIS -->\n<div class=\"modal fade\" id=\"instanceModal\" tabindex=\"-1\" aria-labelledby=\"instanceModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"instanceModalLabel\">{{ isEditInstanceMode ? 'Upravit instanci DIS' : 'Přidat instanci DIS' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"instanceError\" class=\"alert alert-danger mb-3\">\n          {{ instanceError }}\n        </div>\n        <form [formGroup]=\"instanceForm\" (ngSubmit)=\"saveInstance()\">\n          <div class=\"mb-3\">\n            <label for=\"instanceName\" class=\"form-label required-field\">Název instance</label>\n            <input type=\"text\" class=\"form-control\" id=\"instanceName\" formControlName=\"name\" [class.is-invalid]=\"instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched\">\n            <div *ngIf=\"instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched\" class=\"invalid-feedback\">\n              Název instance je povinný\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"instanceServerUrl\" class=\"form-label required-field\">URL serveru</label>\n            <input type=\"text\" class=\"form-control\" id=\"instanceServerUrl\" formControlName=\"serverUrl\" [class.is-invalid]=\"instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched\">\n            <div *ngIf=\"instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched\" class=\"invalid-feedback\">\n              URL serveru je povinná\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"instanceExpirationDate\" class=\"form-label\">Datum expirace</label>\n            <input type=\"date\" class=\"form-control\" id=\"instanceExpirationDate\" formControlName=\"expirationDate\">\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"instanceStatus\" class=\"form-label\">Status</label>\n            <select class=\"form-select\" id=\"instanceStatus\" formControlName=\"status\">\n              <option value=\"Active\">Aktivní</option>\n              <option value=\"Blocked\">Blokovaná</option>\n              <option value=\"Expired\">Expirovaná</option>\n              <option value=\"Trial\">Zkušební</option>\n              <option value=\"Maintenance\">Údržba</option>\n            </select>\n          </div>\n\n          <div class=\"mb-3\" *ngIf=\"instanceForm.get('status')?.value === 'Blocked'\">\n            <label for=\"instanceBlockReason\" class=\"form-label\">Důvod blokace</label>\n            <input type=\"text\" class=\"form-control\" id=\"instanceBlockReason\" formControlName=\"blockReason\">\n          </div>\n\n          <div class=\"mb-3\">\n            <label class=\"form-label\">Povolené moduly</label>\n            <div class=\"form-check\">\n              <input class=\"form-check-input\" type=\"checkbox\" id=\"moduleReporting\" formControlName=\"moduleReporting\">\n              <label class=\"form-check-label\" for=\"moduleReporting\">Reportování</label>\n            </div>\n            <div class=\"form-check\">\n              <input class=\"form-check-input\" type=\"checkbox\" id=\"moduleAdvancedSecurity\" formControlName=\"moduleAdvancedSecurity\">\n              <label class=\"form-check-label\" for=\"moduleAdvancedSecurity\">Pokročilá bezpečnost</label>\n            </div>\n            <div class=\"form-check\">\n              <input class=\"form-check-input\" type=\"checkbox\" id=\"moduleApiIntegration\" formControlName=\"moduleApiIntegration\">\n              <label class=\"form-check-label\" for=\"moduleApiIntegration\">API integrace</label>\n            </div>\n            <div class=\"form-check\">\n              <input class=\"form-check-input\" type=\"checkbox\" id=\"moduleDataExport\" formControlName=\"moduleDataExport\">\n              <label class=\"form-check-label\" for=\"moduleDataExport\">Export dat</label>\n            </div>\n            <div class=\"form-check\">\n              <input class=\"form-check-input\" type=\"checkbox\" id=\"moduleCustomization\" formControlName=\"moduleCustomization\">\n              <label class=\"form-check-label\" for=\"moduleCustomization\">Přizpůsobení</label>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"instanceNotes\" class=\"form-label\">Poznámka</label>\n            <textarea class=\"form-control\" id=\"instanceNotes\" formControlName=\"notes\" rows=\"3\"></textarea>\n          </div>\n\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\" (click)=\"closeInstanceModal()\">\n              <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n            </button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"instanceForm.invalid || savingInstance\">\n              <span *ngIf=\"savingInstance\" class=\"spinner-border spinner-border-sm me-1\"></span>\n              <i *ngIf=\"!savingInstance\" class=\"bi bi-save me-1\"></i>{{ isEditInstanceMode ? 'Aktualizovat' : 'Uložit' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro přidání/úpravu verze instance -->\n<div class=\"modal fade\" id=\"instanceVersionModal\" tabindex=\"-1\" aria-labelledby=\"instanceVersionModalLabel\" aria-hidden=\"true\" data-bs-backdrop=\"false\" style=\"z-index: 1070 !important;\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"instanceVersionModalLabel\">{{ isEditInstanceVersionMode ? 'Upravit verzi instance' : 'Přidat verzi instance' }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"instanceVersionError\" class=\"alert alert-danger mb-3\">\n          {{ instanceVersionError }}\n        </div>\n        <form [formGroup]=\"instanceVersionForm\" (ngSubmit)=\"saveInstanceVersion()\">\n          <div class=\"mb-3\">\n            <label for=\"instanceName\" class=\"form-label\">Instance:</label>\n            <input type=\"text\" class=\"form-control\" id=\"instanceName\" [value]=\"selectedInstanceForVersion?.name\" disabled>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"versionId\" class=\"form-label required-field\">Verze:</label>\n            <select class=\"form-select\" id=\"versionId\" formControlName=\"versionId\" [class.is-invalid]=\"instanceVersionForm.get('versionId')?.invalid && instanceVersionForm.get('versionId')?.touched\">\n              <option value=\"\">-- Vyberte verzi --</option>\n              <option *ngFor=\"let version of availableVersions\" [value]=\"version.id\">{{ version.versionNumber }} ({{ version.releaseDate | date:'dd.MM.yyyy' }})</option>\n            </select>\n            <div *ngIf=\"instanceVersionForm.get('versionId')?.invalid && instanceVersionForm.get('versionId')?.touched\" class=\"invalid-feedback\">\n              Verze je povinná\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"installedByUserId\" class=\"form-label required-field\">Instaloval:</label>\n            <select class=\"form-select\" id=\"installedByUserId\" formControlName=\"installedByUserId\" [class.is-invalid]=\"instanceVersionForm.get('installedByUserId')?.invalid && instanceVersionForm.get('installedByUserId')?.touched\">\n              <option value=\"\">-- Vyberte uživatele --</option>\n              <option *ngFor=\"let user of availableUsers\" [value]=\"user.id\">{{ user.firstName }} {{ user.lastName }}</option>\n            </select>\n            <div *ngIf=\"instanceVersionForm.get('installedByUserId')?.invalid && instanceVersionForm.get('installedByUserId')?.touched\" class=\"invalid-feedback\">\n              Uživatel je povinný\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"instanceVersionNotes\" class=\"form-label\">Poznámka:</label>\n            <textarea class=\"form-control\" id=\"instanceVersionNotes\" formControlName=\"notes\" rows=\"3\"></textarea>\n          </div>\n\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\" (click)=\"closeInstanceVersionModal()\">\n              <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n            </button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"instanceVersionForm.invalid || savingInstanceVersion\">\n              <span *ngIf=\"savingInstanceVersion\" class=\"spinner-border spinner-border-sm me-1\"></span>\n              <i *ngIf=\"!savingInstanceVersion\" class=\"bi bi-save me-1\"></i>{{ isEditInstanceVersionMode ? 'Aktualizovat' : 'Uložit' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro detail instance -->\n<div class=\"modal fade\" id=\"instanceDetailModal\" tabindex=\"-1\" aria-labelledby=\"instanceDetailModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"instanceDetailModalLabel\">Detail instance {{ selectedCustomer?.abbreviation }} - {{ selectedInstanceForVersion?.name }}</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"selectedInstanceForVersion\">\n          <div class=\"row mb-3\">\n            <div class=\"col-md-6\">\n              <h6>Název:</h6>\n              <p>{{ selectedInstanceForVersion.name }}</p>\n            </div>\n            <div class=\"col-md-6\">\n              <h6>Status:</h6>\n              <p>\n                <span class=\"badge\" [ngClass]=\"{\n                  'bg-success': selectedInstanceForVersion.status === InstanceStatus.Active,\n                  'bg-danger': selectedInstanceForVersion.status === InstanceStatus.Blocked,\n                  'bg-warning': selectedInstanceForVersion.status === InstanceStatus.Trial,\n                  'bg-secondary': selectedInstanceForVersion.status === InstanceStatus.Maintenance,\n                  'bg-dark': selectedInstanceForVersion.status === InstanceStatus.Expired\n                }\">\n                  {{ getInstanceStatusText(selectedInstanceForVersion.status) }}\n                </span>\n              </p>\n            </div>\n          </div>\n\n          <div class=\"row mb-3\">\n            <div class=\"col-md-6\">\n              <h6>URL serveru:</h6>\n              <p>{{ selectedInstanceForVersion.serverUrl || '-' }}</p>\n            </div>\n            <div class=\"col-md-6\">\n              <h6>Datum poslední komunikace:</h6>\n              <p>{{ selectedInstanceForVersion.lastConnectionDate ? (selectedInstanceForVersion.lastConnectionDate | date:'dd.MM.yyyy HH:mm') : '-' }}</p>\n            </div>\n          </div>\n\n          <div class=\"row mb-3\">\n            <div class=\"col-md-12\">\n              <h6>API klíč:</h6>\n              <div class=\"input-group\">\n                <input type=\"text\" class=\"form-control\" [value]=\"selectedInstanceForVersion.apiKey || '-'\" readonly #apiKeyInput>\n                <button class=\"btn input-group-button\" type=\"button\" (click)=\"copyApiKey(apiKeyInput)\">\n                  <i class=\"bi bi-clipboard\"></i>\n                </button>\n                <button class=\"btn input-group-button\" type=\"button\" (click)=\"regenerateApiKey(selectedInstanceForVersion.id)\" [disabled]=\"regeneratingApiKey\">\n                  <span *ngIf=\"regeneratingApiKey\" class=\"spinner-border spinner-border-sm\" role=\"status\"></span>\n                  <i *ngIf=\"!regeneratingApiKey\" class=\"bi bi-arrow-repeat\"></i>\n                </button>\n              </div>\n              <div class=\"d-flex justify-content-between align-items-center mt-2\">\n                <small class=\"text-muted\">Tento klíč je potřeba zadat do aplikace DIS pro komunikaci s DIS Admin.</small>\n                <a [routerLink]=\"['/ip-whitelisting', selectedInstanceForVersion.id]\" class=\"btn btn-sm btn-outline-primary\" (click)=\"closeInstanceDetailModal()\">\n                  <i class=\"bi bi-shield-lock me-1\"></i>IP Whitelisting\n                </a>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"row mb-3\">\n            <div class=\"col-md-6\">\n              <h6>Datum instalace:</h6>\n              <p>{{ selectedInstanceForVersion.installationDate | date:'dd.MM.yyyy' }}</p>\n            </div>\n            <div class=\"col-md-6\">\n              <h6>Datum expirace:</h6>\n              <p>{{ selectedInstanceForVersion.expirationDate ? (selectedInstanceForVersion.expirationDate | date:'dd.MM.yyyy') : '-' }}</p>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <h6>Poznámka:</h6>\n            <p>{{ selectedInstanceForVersion.notes || '-' }}</p>\n          </div>\n\n          <!-- Sekce s informacemi o certifikátu -->\n          <div class=\"mb-4\">\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\n              <h5 class=\"mb-0\">Certifikát</h5>\n              <button type=\"button\" class=\"btn btn-sm btn-success\" (click)=\"generateCertificate(selectedInstanceForVersion.id)\" [disabled]=\"generatingCertificate\">\n                <span *ngIf=\"generatingCertificate\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n                <i *ngIf=\"!generatingCertificate\" class=\"bi bi-shield-plus me-2\"></i>Vygenerovat nový certifikát\n              </button>\n            </div>\n\n            <div *ngIf=\"loadingCertificateInfo\" class=\"d-flex justify-content-center\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingCertificateInfo && !certificateInfo\" class=\"alert alert-info\">\n              <i class=\"bi bi-info-circle me-2\"></i>\n              Instance nemá přiřazený certifikát.\n            </div>\n\n            <div *ngIf=\"!loadingCertificateInfo && certificateInfo\" class=\"card\">\n              <div class=\"card-body\">\n                <div class=\"row\">\n                  <div class=\"col-md-6\">\n                    <p><strong>Thumbprint:</strong> {{ certificateInfo.thumbprint }}</p>\n                    <p><strong>Subject:</strong> {{ certificateInfo.subject }}</p>\n                    <p><strong>Issuer:</strong> {{ certificateInfo.issuer }}</p>\n                  </div>\n                  <div class=\"col-md-6\">\n                    <p><strong>Platnost do:</strong> {{ certificateInfo.expirationDate | date:'dd.MM.yyyy HH:mm' }}</p>\n                    <p><strong>Poslední validace:</strong> {{ certificateInfo.lastValidation | date:'dd.MM.yyyy HH:mm' || '-' }}</p>\n                    <p>\n                      <a [routerLink]=\"['/certificate-rotation/instance', selectedInstanceForVersion.id]\" class=\"btn btn-sm btn-outline-primary\" (click)=\"closeInstanceDetailModal()\">\n                        <i class=\"bi bi-gear me-1\"></i>Nastavení automatické rotace\n                      </a>\n                    </p>\n                    <p>\n                      <strong>Status:</strong>\n                      <span *ngIf=\"certificateInfo.isValid\" class=\"badge bg-success\">Platný</span>\n                      <span *ngIf=\"!certificateInfo.isValid\" class=\"badge bg-danger\">Neplatný</span>\n                      <span *ngIf=\"certificateInfo.daysToExpiration > 0\" class=\"ms-2\" [ngClass]=\"getCertificateExpirationClass(certificateInfo.daysToExpiration)\">\n                        ({{ certificateInfo.daysToExpiration }} dní do expirace)\n                      </span>\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\n              <h5 class=\"mb-0\">Historie verzí</h5>\n              <button type=\"button\" class=\"btn btn-sm btn-primary\" (click)=\"openAddInstanceVersionModal(selectedInstanceForVersion)\">\n                <i class=\"bi bi-plus-circle-fill me-2\"></i>Přidat verzi\n              </button>\n            </div>\n\n            <div *ngIf=\"loadingInstanceVersions[selectedInstanceForVersion.id]\" class=\"d-flex justify-content-center\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"!loadingInstanceVersions[selectedInstanceForVersion.id] && (!instanceVersions[selectedInstanceForVersion.id] || instanceVersions[selectedInstanceForVersion.id].length === 0)\" class=\"alert alert-info\">\n              Žádné verze nebyly nalezeny.\n            </div>\n\n            <div *ngIf=\"!loadingInstanceVersions[selectedInstanceForVersion.id] && instanceVersions[selectedInstanceForVersion.id] && instanceVersions[selectedInstanceForVersion.id].length > 0\" class=\"table-responsive\">\n              <table class=\"table table-striped table-hover\">\n                <thead class=\"dark-header table-header-override\">\n                  <tr class=\"dark-header-row\">\n                    <th>Verze</th>\n                    <th class=\"text-nowrap\">Datum instalace</th>\n                    <th>Instaloval</th>\n                    <th>Poznámka</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let version of instanceVersions[selectedInstanceForVersion.id]\">\n                    <td>{{ version.versionNumber }}</td>\n                    <td>{{ version.installedAt | date:'dd.MM.yyyy HH:mm' }}</td>\n                    <td>{{ version.installedByUserName }}</td>\n                    <td>{{ version.notes || '-' }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"d-flex justify-content-end mt-3\">\n          <button type=\"button\" class=\"btn btn-info me-2\" [disabled]=\"!selectedInstanceForVersion\" [routerLink]=\"['/instance-metrics', selectedInstanceForVersion?.id]\" (click)=\"closeInstanceDetailModal()\">\n            <i class=\"bi bi-graph-up me-1\"></i>Metriky\n          </button>\n          <button type=\"button\" class=\"btn btn-secondary me-2\" data-bs-dismiss=\"modal\" (click)=\"closeInstanceDetailModal()\">\n            <i class=\"bi bi-x-circle me-1\"></i>Zavřít\n          </button>\n          <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"!selectedInstanceForVersion\" (click)=\"editInstance(selectedInstanceForVersion!)\">\n            <i class=\"bi bi-pencil-fill me-1\"></i>Upravit\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro zobrazení informací o vygenerovaném certifikátu -->\n<div class=\"modal fade\" id=\"certificateGeneratedModal\" tabindex=\"-1\" aria-labelledby=\"certificateGeneratedModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-success text-white\">\n        <h5 class=\"modal-title\" id=\"certificateGeneratedModalLabel\">Certifikát vygenerován</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div class=\"alert alert-success\">\n          <i class=\"bi bi-check-circle-fill me-2\"></i>\n          Certifikát byl úspěšně vygenerován.\n        </div>\n\n        <div *ngIf=\"generatedCertificate\">\n          <h6 class=\"mt-3\">Informace o certifikátu:</h6>\n          <ul class=\"list-group\">\n            <li class=\"list-group-item d-flex justify-content-between align-items-center\">\n              <span>Thumbprint:</span>\n              <span class=\"text-muted\">{{ generatedCertificate.thumbprint }}</span>\n            </li>\n            <li class=\"list-group-item d-flex justify-content-between align-items-center\">\n              <span>Platnost do:</span>\n              <span class=\"text-muted\">{{ generatedCertificate.expirationDate | date:'dd.MM.yyyy HH:mm' }}</span>\n            </li>\n            <li class=\"list-group-item d-flex justify-content-between align-items-center\">\n              <span>Heslo k certifikátu:</span>\n              <span class=\"text-monospace font-weight-bold\">{{ generatedCertificate.password }}</span>\n            </li>\n          </ul>\n\n          <div class=\"alert alert-warning mt-3\">\n            <i class=\"bi bi-exclamation-triangle-fill me-2\"></i>\n            <strong>Důležité:</strong> Toto heslo si poznamenejte. Budete ho potřebovat při instalaci certifikátu.\n            Z bezpečnostních důvodů není heslo nikde uloženo a nebude možné ho později zobrazit.\n          </div>\n\n          <div class=\"mt-3\">\n            <button class=\"btn btn-primary\" (click)=\"downloadCertificate()\">\n              <i class=\"bi bi-download me-2\"></i>Stáhnout certifikát\n            </button>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}