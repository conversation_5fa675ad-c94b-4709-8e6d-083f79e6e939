{"ast": null, "code": "import { HomeComponent } from './home/<USER>';\nimport { LoginComponent } from './login/login.component';\nimport { UsersComponent } from './users/users.component';\nimport { UserDetailComponent } from './users/user-detail/user-detail.component';\nimport { CustomersComponent } from './customers/customers.component';\nimport { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';\nimport { VersionsComponent } from './versions/versions.component';\nimport { VersionFormComponent } from './versions/version-form/version-form.component';\nimport { ProfileComponent } from './profile/profile.component';\nimport { SecurityComponent } from './security/security.component';\nimport { CertificatesComponent } from './certificates/certificates.component';\nimport { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { AlertsComponent } from './alerts/alerts.component';\nimport { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';\nimport { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';\nimport { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';\nimport { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';\nimport { AuthGuard } from './services/auth.guard';\nexport const routes = [{\n  path: '',\n  component: HomeComponent,\n  pathMatch: 'full',\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Domů',\n    icon: 'house-fill'\n  }\n}, {\n  path: 'login',\n  component: LoginComponent,\n  data: {\n    breadcrumb: 'Přihlášení',\n    icon: 'box-arrow-in-right'\n  }\n}, {\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Dashboard',\n    icon: 'grid-fill'\n  }\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Uživatelé',\n    icon: 'people-fill'\n  }\n}, {\n  path: 'users/add',\n  component: UserDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat uživatele',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'users/:id',\n  component: UserDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Detail uživatele',\n    icon: 'person-fill'\n  }\n}, {\n  path: 'customers',\n  component: CustomersComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Zákazníci',\n    icon: 'building-fill'\n  }\n}, {\n  path: 'customers/add',\n  component: CustomerDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat zákazníka',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'customers/:id',\n  component: CustomerDetailComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Detail zákazníka',\n    icon: 'info-circle-fill'\n  }\n}, {\n  path: 'versions',\n  component: VersionsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Verze DIS',\n    icon: 'tag-fill'\n  }\n}, {\n  path: 'versions/add',\n  component: VersionFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Přidat verzi',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'versions/:id',\n  component: VersionFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Detail verze',\n    icon: 'info-circle-fill'\n  }\n}, {\n  path: 'profile',\n  component: ProfileComponent,\n  canActivate: [AuthGuard],\n  data: {\n    breadcrumb: 'Profil',\n    icon: 'person-fill'\n  }\n}, {\n  path: 'security',\n  component: SecurityComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Bezpečnostní události',\n    icon: 'shield-exclamation-fill'\n  }\n}, {\n  path: 'certificates',\n  component: CertificatesComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Certifikáty',\n    icon: 'file-earmark-lock-fill'\n  }\n}, {\n  path: 'instance-wizard',\n  component: InstanceWizardComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Průvodce instancí',\n    icon: 'magic'\n  }\n}, {\n  path: 'monitoring',\n  loadChildren: () => import('./monitoring/monitoring.module').then(m => m.MonitoringModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Monitoring',\n    icon: 'graph-up'\n  }\n}, {\n  path: 'performance',\n  loadChildren: () => import('./performance/performance.module').then(m => m.PerformanceModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Výkon DIS',\n    icon: 'speedometer'\n  }\n}, {\n  path: 'instance-metrics/:id',\n  loadChildren: () => import('./instance-metrics/instance-metrics.module').then(m => m.InstanceMetricsModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Metriky instance',\n    icon: 'speedometer2'\n  }\n}, {\n  path: 'alerts',\n  component: AlertsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Upozornění',\n    icon: 'bell-fill'\n  }\n}, {\n  path: 'alerts/rules/add',\n  component: AlertRuleFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Přidat pravidlo',\n    icon: 'plus-circle-fill'\n  }\n}, {\n  path: 'alerts/rules/:id',\n  component: AlertRuleFormComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Upravit pravidlo',\n    icon: 'pencil-fill'\n  }\n}, {\n  path: 'certificate-rotation',\n  component: CertificateRotationSettingsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Nastavení automatické rotace DIS certifikátů',\n    icon: 'arrow-repeat'\n  }\n}, {\n  path: 'certificate-rotation/instance/:id',\n  component: InstanceCertificateSettingsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Nastavení certifikátů instance',\n    icon: 'gear-fill'\n  }\n}, {\n  path: 'ip-whitelisting/:id',\n  component: IpWhitelistingComponent,\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'IP Whitelist',\n    icon: 'list-check'\n  }\n}, {\n  path: 'logs',\n  loadChildren: () => import('./logs/logs.module').then(m => m.LogsModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Logy systému',\n    icon: 'journal-text'\n  }\n}, {\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),\n  canActivate: [AuthGuard],\n  data: {\n    adminOnly: true,\n    breadcrumb: 'Administrace',\n    icon: 'gear-fill'\n  }\n},\n// Fallback route\n{\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "mappings": "AACA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,uBAAuB,QAAQ,uDAAuD;AAC/F,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,iCAAiC;AAGpE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,oCAAoC,QAAQ,gEAAgE;AACrH,SAASC,oCAAoC,QAAQ,gEAAgE;AACrH,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,SAAS,QAAQ,uBAAuB;AAEjD,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAErB,aAAa;EAAEsB,SAAS,EAAE,MAAM;EAAEC,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAY;AAAE,CAAE,EACrI;EAAEN,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEpB,cAAc;EAAEuB,IAAI,EAAE;IAAEC,UAAU,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAoB;AAAE,CAAE,EAC5G;EAAEN,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAET,kBAAkB;EAAEW,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAW;AAAE,CAAE,EACrJ;EAAEN,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEnB,cAAc;EAAEqB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EAC/I;EAAEN,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAElB,mBAAmB;EAAEoB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EACpK;EAAEN,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAElB,mBAAmB;EAAEoB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EAC/J;EAAEN,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEjB,kBAAkB;EAAEmB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAe;AAAE,CAAE,EACxI;EAAEN,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEhB,uBAAuB;EAAEkB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EAC5K;EAAEN,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEhB,uBAAuB;EAAEkB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EAC3J;EAAEN,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEf,iBAAiB;EAAEiB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAU;AAAE,CAAE,EACjI;EAAEN,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEd,oBAAoB;EAAEgB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EACnJ;EAAEN,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEd,oBAAoB;EAAEgB,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EACnJ;EAAEN,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEb,gBAAgB;EAAEe,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EAC/H;EAAEN,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEZ,iBAAiB;EAAEc,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,uBAAuB;IAAEC,IAAI,EAAE;EAAyB;AAAE,CAAE,EAC7K;EAAEN,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEX,qBAAqB;EAAEa,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAwB;AAAE,CAAE,EAC1K;EAAEN,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEV,uBAAuB;EAAEY,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,mBAAmB;IAAEC,IAAI,EAAE;EAAO;AAAE,CAAE,EACpK;EAAEN,IAAI,EAAE,YAAY;EAAEQ,YAAY,EAAE,MAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;EAAER,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAU;AAAE,CAAE,EACnN;EAAEN,IAAI,EAAE,aAAa;EAAEQ,YAAY,EAAE,MAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB,CAAC;EAAET,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACzN;EAAEN,IAAI,EAAE,sBAAsB;EAAEQ,YAAY,EAAE,MAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,qBAAqB,CAAC;EAAEV,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAc;AAAE,CAAE,EACxP;EAAEN,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAER,eAAe;EAAEU,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAW;AAAE,CAAE,EAChJ;EAAEN,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEP,sBAAsB;EAAES,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAkB;AAAE,CAAE,EAC7K;EAAEN,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEP,sBAAsB;EAAES,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,kBAAkB;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACzK;EAAEN,IAAI,EAAE,sBAAsB;EAAEC,SAAS,EAAEN,oCAAoC;EAAEQ,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,8CAA8C;IAAEC,IAAI,EAAE;EAAc;AAAE,CAAE,EACxN;EAAEN,IAAI,EAAE,mCAAmC;EAAEC,SAAS,EAAEL,oCAAoC;EAAEO,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,gCAAgC;IAAEC,IAAI,EAAE;EAAW;AAAE,CAAE,EACpN;EAAEN,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEJ,uBAAuB;EAAEM,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAY;AAAE,CAAE,EACxK;EAAEN,IAAI,EAAE,MAAM;EAAEQ,YAAY,EAAE,MAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,UAAU,CAAC;EAAEX,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAc;AAAE,CAAE,EACjM;EAAEN,IAAI,EAAE,OAAO;EAAEQ,YAAY,EAAE,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,WAAW,CAAC;EAAEZ,WAAW,EAAE,CAACL,SAAS,CAAC;EAAEM,IAAI,EAAE;IAAEG,SAAS,EAAE,IAAI;IAAEF,UAAU,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAW;AAAE,CAAE;AAClM;AACA;EAAEN,IAAI,EAAE,IAAI;EAAEgB,UAAU,EAAE;AAAE,CAAE,CAC/B", "names": ["HomeComponent", "LoginComponent", "UsersComponent", "UserDetailComponent", "CustomersComponent", "CustomerDetailComponent", "VersionsComponent", "VersionFormComponent", "ProfileComponent", "SecurityComponent", "CertificatesComponent", "InstanceWizardComponent", "DashboardComponent", "AlertsComponent", "AlertRuleFormComponent", "CertificateRotationSettingsComponent", "InstanceCertificateSettingsComponent", "IpWhitelistingComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "pathMatch", "canActivate", "data", "breadcrumb", "icon", "adminOnly", "loadChildren", "then", "m", "MonitoringModule", "PerformanceModule", "InstanceMetricsModule", "LogsModule", "AdminModule", "redirectTo"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { HomeComponent } from './home/<USER>';\nimport { LoginComponent } from './login/login.component';\nimport { UsersComponent } from './users/users.component';\nimport { UserDetailComponent } from './users/user-detail/user-detail.component';\nimport { CustomersComponent } from './customers/customers.component';\nimport { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';\nimport { VersionsComponent } from './versions/versions.component';\nimport { VersionFormComponent } from './versions/version-form/version-form.component';\nimport { ProfileComponent } from './profile/profile.component';\nimport { SecurityComponent } from './security/security.component';\nimport { CertificatesComponent } from './certificates/certificates.component';\nimport { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { MonitoringComponent } from './monitoring/monitoring.component';\nimport { InstanceMetricsComponent } from './instance-metrics/instance-metrics.component';\nimport { AlertsComponent } from './alerts/alerts.component';\nimport { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';\nimport { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';\nimport { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';\nimport { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';\nimport { AuthGuard } from './services/auth.guard';\n\nexport const routes: Routes = [\n  { path: '', component: HomeComponent, pathMatch: 'full', canActivate: [AuthGuard], data: { breadcrumb: 'Domů', icon: 'house-fill' } },\n  { path: 'login', component: LoginComponent, data: { breadcrumb: 'Přihlášení', icon: 'box-arrow-in-right' } },\n  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Dashboard', icon: 'grid-fill' } },\n  { path: 'users', component: UsersComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Uživatelé', icon: 'people-fill' } },\n  { path: 'users/add', component: UserDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat uživatele', icon: 'plus-circle-fill' } },\n  { path: 'users/:id', component: UserDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Detail uživatele', icon: 'person-fill' } },\n  { path: 'customers', component: CustomersComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Zákazníci', icon: 'building-fill' } },\n  { path: 'customers/add', component: CustomerDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat zákazníka', icon: 'plus-circle-fill' } },\n  { path: 'customers/:id', component: CustomerDetailComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Detail zákazníka', icon: 'info-circle-fill' } },\n  { path: 'versions', component: VersionsComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Verze DIS', icon: 'tag-fill' } },\n  { path: 'versions/add', component: VersionFormComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Přidat verzi', icon: 'plus-circle-fill' } },\n  { path: 'versions/:id', component: VersionFormComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Detail verze', icon: 'info-circle-fill' } },\n  { path: 'profile', component: ProfileComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Profil', icon: 'person-fill' } },\n  { path: 'security', component: SecurityComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Bezpečnostní události', icon: 'shield-exclamation-fill' } },\n  { path: 'certificates', component: CertificatesComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Certifikáty', icon: 'file-earmark-lock-fill' } },\n  { path: 'instance-wizard', component: InstanceWizardComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Průvodce instancí', icon: 'magic' } },\n  { path: 'monitoring', loadChildren: () => import('./monitoring/monitoring.module').then(m => m.MonitoringModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Monitoring', icon: 'graph-up' } },\n  { path: 'performance', loadChildren: () => import('./performance/performance.module').then(m => m.PerformanceModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Výkon DIS', icon: 'speedometer' } },\n  { path: 'instance-metrics/:id', loadChildren: () => import('./instance-metrics/instance-metrics.module').then(m => m.InstanceMetricsModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Metriky instance', icon: 'speedometer2' } },\n  { path: 'alerts', component: AlertsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Upozornění', icon: 'bell-fill' } },\n  { path: 'alerts/rules/add', component: AlertRuleFormComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat pravidlo', icon: 'plus-circle-fill' } },\n  { path: 'alerts/rules/:id', component: AlertRuleFormComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Upravit pravidlo', icon: 'pencil-fill' } },\n  { path: 'certificate-rotation', component: CertificateRotationSettingsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Nastavení automatické rotace DIS certifikátů', icon: 'arrow-repeat' } },\n  { path: 'certificate-rotation/instance/:id', component: InstanceCertificateSettingsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Nastavení certifikátů instance', icon: 'gear-fill' } },\n  { path: 'ip-whitelisting/:id', component: IpWhitelistingComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'IP Whitelist', icon: 'list-check' } },\n  { path: 'logs', loadChildren: () => import('./logs/logs.module').then(m => m.LogsModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Logy systému', icon: 'journal-text' } },\n  { path: 'admin', loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Administrace', icon: 'gear-fill' } },\n  // Fallback route\n  { path: '**', redirectTo: '' }\n];\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}