{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/security.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../services/modal.service\";\nimport * as i4 from \"@angular/common\";\nfunction SecurityComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"span\", 20);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction SecurityComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E1 aktivn\\u00ED upozorn\\u011Bn\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_8_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\", 35);\n    i0.ɵɵelement(3, \"i\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 37)(6, \"span\", 36);\n    i0.ɵɵelement(7, \"i\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_8_tr_19_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const alert_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r12.openResolveAlertModal(alert_r11));\n    });\n    i0.ɵɵelement(20, \"i\", 17);\n    i0.ɵɵtext(21, \"Vy\\u0159e\\u0161it \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const alert_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getSeverityClass(alert_r11.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getSeverityIcon(alert_r11.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getSeverityText(alert_r11.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getAlertTypeClass(alert_r11.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getAlertTypeIcon(alert_r11.alertType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.getAlertTypeText(alert_r11.alertType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, alert_r11.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(alert_r11.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.instanceName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.customerName || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"table\", 33)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_8_tr_19_Template, 22, 13, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.activeAlerts);\n  }\n}\nfunction SecurityComponent_div_9_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 ne\\u00FAsp\\u011B\\u0161n\\u00E9 pokusy o p\\u0159ipojen\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_16_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 36);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r15.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", stat_r15.failedCertificateValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r15.failedCertificateValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 8, stat_r15.lastFailedCertificateValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", stat_r15.failedApiKeyValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r15.failedApiKeyValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 11, stat_r15.lastFailedApiKeyValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r15.lastKnownIpAddress || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"table\", 33)(2, \"thead\")(3, \"tr\")(4, \"th\", 37);\n    i0.ɵɵtext(5, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 37);\n    i0.ɵɵtext(7, \"Ne\\u00FAsp. validace cert.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 37);\n    i0.ɵɵtext(9, \"Posledn\\u00ED ne\\u00FAsp. validace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 37);\n    i0.ɵɵtext(11, \"Ne\\u00FAsp. validace API kl\\u00ED\\u010De\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 37);\n    i0.ɵɵtext(13, \"Posledn\\u00ED ne\\u00FAsp. validace API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 37);\n    i0.ɵɵtext(15, \"Posledn\\u00ED IP\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_16_tr_17_Template, 17, 14, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.failedConnectionStats);\n  }\n}\nfunction SecurityComponent_div_9_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_24_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"span\", 36);\n    i0.ɵɵelement(3, \"i\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 37)(6, \"span\", 36);\n    i0.ɵɵelement(7, \"i\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r17 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.getSeverityClass(event_r17.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.getSeverityIcon(event_r17.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getSeverityText(event_r17.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.getEventTypeClass(event_r17.eventType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.getEventTypeIcon(event_r17.eventType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r16.getEventTypeText(event_r17.eventType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 10, event_r17.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r17.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r17.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r17.username || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"table\", 33)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 37);\n    i0.ɵɵtext(13, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_24_tr_17_Template, 18, 13, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.securityEvents);\n  }\n}\nfunction SecurityComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 23)(3, \"h5\", 24);\n    i0.ɵɵelement(4, \"i\", 25);\n    i0.ɵɵtext(5, \"Aktivn\\u00ED upozorn\\u011Bn\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 26);\n    i0.ɵɵtemplate(7, SecurityComponent_div_9_div_7_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(8, SecurityComponent_div_9_div_8_Template, 20, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 22)(10, \"div\", 23)(11, \"h5\", 24);\n    i0.ɵɵelement(12, \"i\", 29);\n    i0.ɵɵtext(13, \"Statistiky ne\\u00FAsp\\u011B\\u0161n\\u00FDch p\\u0159ipojen\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 26);\n    i0.ɵɵtemplate(15, SecurityComponent_div_9_div_15_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(16, SecurityComponent_div_9_div_16_Template, 18, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 22)(18, \"div\", 23)(19, \"h5\", 24);\n    i0.ɵɵelement(20, \"i\", 30);\n    i0.ɵɵtext(21, \"Ned\\u00E1vn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 26);\n    i0.ɵɵtemplate(23, SecurityComponent_div_9_div_23_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(24, SecurityComponent_div_9_div_24_Template, 18, 1, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.activeAlerts.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length > 0);\n  }\n}\nfunction SecurityComponent_div_18_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \" \\u0158e\\u0161en\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 39);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"form\", 40);\n    i0.ɵɵlistener(\"ngSubmit\", function SecurityComponent_div_18_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.resolveAlert());\n    });\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"label\", 42);\n    i0.ɵɵtext(9, \"\\u0158e\\u0161en\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"textarea\", 43);\n    i0.ɵɵtemplate(11, SecurityComponent_div_18_div_11_Template, 2, 0, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_5_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeClass(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getAlertTypeIcon(ctx_r3.selectedAlert.alertType) + \" me-1\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getAlertTypeText(ctx_r3.selectedAlert.alertType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\": \", ctx_r3.selectedAlert.description, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.resolveAlertForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.resolveAlertForm.get(\"resolution\")) == null ? null : tmp_5_0.touched));\n  }\n}\nexport class SecurityComponent {\n  constructor(securityService, fb, modalService) {\n    this.securityService = securityService;\n    this.fb = fb;\n    this.modalService = modalService;\n    this.loading = false;\n    this.error = null;\n    this.securityEvents = [];\n    this.activeAlerts = [];\n    this.failedConnectionStats = [];\n    this.selectedAlert = null;\n    this.resolveAlertForm = this.fb.group({\n      resolution: ['', Validators.required]\n    });\n  }\n  ngOnInit() {\n    this.loadSecurityDashboard();\n  }\n  loadSecurityDashboard() {\n    this.loading = true;\n    this.error = null;\n    this.securityService.getSecurityDashboard().subscribe({\n      next: response => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n        this.activeAlerts = response.activeAlerts.map(alert => ({\n          ...alert,\n          timestamp: new Date(alert.timestamp),\n          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined\n        }));\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n  openResolveAlertModal(alert) {\n    this.selectedAlert = alert;\n    this.resolveAlertForm.reset({\n      resolution: ''\n    });\n    this.modalService.open('resolveAlertModal');\n  }\n  resolveAlert() {\n    if (this.resolveAlertForm.invalid || !this.selectedAlert) {\n      return;\n    }\n    const formData = this.resolveAlertForm.value;\n    this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({\n      next: () => {\n        // Zavření modálu\n        this.modalService.close('resolveAlertModal');\n        // Aktualizace dat\n        this.loadSecurityDashboard();\n      },\n      error: err => {\n        console.error('Error resolving alert', err);\n        this.error = 'Chyba při řešení upozornění';\n      }\n    });\n  }\n  getSeverityClass(severity) {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n  getSeverityIcon(severity) {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n  getSeverityText(severity) {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n  getEventTypeClass(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n  getEventTypeIcon(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n  getEventTypeText(eventType) {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n  getAlertTypeClass(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'text-warning';\n      case 'FailedConnectionAttempts':\n        return 'text-danger';\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'ApiKeyMisuse':\n        return 'text-danger';\n      case 'SystemError':\n        return 'text-danger';\n      case 'Error':\n        return 'text-danger';\n      case 'Warning':\n        return 'text-warning';\n      case 'Information':\n        return 'text-info';\n      case 'Other':\n        return 'text-secondary';\n      default:\n        return 'text-info';\n    }\n  }\n  getAlertTypeIcon(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'bi-clock-history';\n      case 'FailedConnectionAttempts':\n        return 'bi-x-circle-fill';\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'SystemError':\n        return 'bi-exclamation-octagon-fill';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      case 'Information':\n        return 'bi-info-circle-fill';\n      case 'Warning':\n        return 'bi-exclamation-triangle-fill';\n      case 'Error':\n        return 'bi-x-octagon-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n  getAlertTypeText(alertType) {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'Expirující certifikát';\n      case 'FailedConnectionAttempts':\n        return 'Neúspěšné připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'ApiKeyMisuse':\n        return 'Cyh použití API klíče';\n      case 'SystemError':\n        return 'Systémová chyba';\n      case 'Other':\n        return 'Ostatní';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      default:\n        return alertType;\n    }\n  }\n  static {\n    this.ɵfac = function SecurityComponent_Factory(t) {\n      return new (t || SecurityComponent)(i0.ɵɵdirectiveInject(i1.SecurityService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ModalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SecurityComponent,\n      selectors: [[\"app-security\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 5,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"resolveAlertModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"resolveAlertModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-primary\", \"text-white\"], [\"id\", \"resolveAlertModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-1\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"bi\", \"bi-bell-fill\", \"me-2\"], [1, \"card-body\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-nowrap\", 3, \"ngClass\"], [3, \"ngClass\"], [1, \"text-nowrap\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"alert\", 3, \"ngClass\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"resolution\", 1, \"form-label\"], [\"id\", \"resolution\", \"formControlName\", \"resolution\", \"rows\", \"3\", \"placeholder\", \"Popi\\u0161te, jak bylo upozorn\\u011Bn\\u00ED vy\\u0159e\\u0161eno\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"]],\n      template: function SecurityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_4_listener() {\n            return ctx.loadSecurityDashboard();\n          });\n          i0.ɵɵelement(5, \"i\", 3);\n          i0.ɵɵtext(6, \"Aktualizovat \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, SecurityComponent_div_7_Template, 4, 0, \"div\", 4);\n          i0.ɵɵtemplate(8, SecurityComponent_div_8_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(9, SecurityComponent_div_9_Template, 25, 6, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"h5\", 11);\n          i0.ɵɵtext(15, \"Vy\\u0159e\\u0161it upozorn\\u011Bn\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"button\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtemplate(18, SecurityComponent_div_18_Template, 12, 6, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 14)(20, \"button\", 15);\n          i0.ɵɵtext(21, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_22_listener() {\n            return ctx.resolveAlert();\n          });\n          i0.ɵɵelement(23, \"i\", 17);\n          i0.ɵɵtext(24, \"Vy\\u0159e\\u0161it \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedAlert);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.resolveAlertForm.invalid);\n        }\n      },\n      dependencies: [LocalDatePipe, CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, ReactiveFormsModule, i2.FormGroupDirective, i2.FormControlName],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  overflow: hidden;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  vertical-align: middle;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  border-radius: 0.25rem;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #198754 !important;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .card[_ngcontent-%COMP%] {\\n    background-color: #2b3035;\\n    border-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-bottom-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-top-color: #373b3e;\\n  }\\n  \\n  .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(255, 255, 255, 0.075);\\n  }\\n  \\n  .alert-info[_ngcontent-%COMP%] {\\n    background-color: #0d3b66;\\n    border-color: #0d3b66;\\n    color: #e9ecef;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAGA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;;;;;;;ICA/DC,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAWMA,+BAAgE;IAC9DA,yEACF;IAAAA,iBAAM;;;;;;IAeAA,0BAAuC;IAGjCA,wBAA6D;IAC7DA,YACF;IAAAA,iBAAO;IAETA,8BAAwB;IAEpBA,wBAA+D;IAAAA,YACjE;IAAAA,iBAAO;IAETA,8BAAwB;IAAAA,aAAoD;;IAAAA,iBAAK;IACjFA,2BAAI;IAAAA,aAAuB;IAAAA,iBAAK;IAChCA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IAAAA,aAA+B;IAAAA,iBAAK;IACxCA,2BAAI;IACqCA;MAAA;MAAA;MAAA;MAAA,OAASA,uDAA4B;IAAA,EAAC;IAC3EA,yBAA4C;IAAAA,mCAC9C;IAAAA,iBAAS;;;;;IAjBiBA,eAA4C;IAA5CA,sEAA4C;IACjEA,eAAqD;IAArDA,+EAAqD;IACxDA,eACF;IADEA,4EACF;IAGMA,eAA8C;IAA9CA,wEAA8C;IAC/CA,eAAuD;IAAvDA,iFAAuD;IAAKA,eACjE;IADiEA,6EACjE;IAEsBA,eAAoD;IAApDA,qFAAoD;IACxEA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAA+B;IAA/BA,mDAA+B;IAC/BA,eAA+B;IAA/BA,mDAA+B;;;;;IA7B3CA,+BAA8D;IAIlDA,mCAAS;IAAAA,iBAAK;IAClBA,0BAAI;IAAAA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,wBAAG;IAAAA,iBAAK;IACZA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,2BAAI;IAAAA,yBAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,mCAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,iFAqBK;IACPA,iBAAQ;;;;IAtBgBA,gBAAe;IAAfA,6CAAe;;;;;IAoC3CA,+BAAyE;IACvEA,kGACF;IAAAA,iBAAM;;;;;IAcAA,0BAA+C;IACzCA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAEAA,YACF;IAAAA,iBAAO;IAETA,0BAAI;IAAAA,YAAgF;;IAAAA,iBAAK;IACzFA,0BAAI;IAEAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAAAA,aAA2E;;IAAAA,iBAAK;IACpFA,2BAAI;IAAAA,aAAoC;IAAAA,iBAAK;;;;IAbzCA,eAAuB;IAAvBA,2CAAuB;IAEnBA,eAAuF;IAAvFA,yGAAuF;IAC3FA,eACF;IADEA,0EACF;IAEEA,eAAgF;IAAhFA,+GAAgF;IAE5EA,eAAkF;IAAlFA,oGAAkF;IACtFA,eACF;IADEA,qEACF;IAEEA,eAA2E;IAA3EA,4GAA2E;IAC3EA,eAAoC;IAApCA,wDAAoC;;;;;IA3BhDA,+BAAuE;IAIvCA,wBAAQ;IAAAA,iBAAK;IACrCA,8BAAwB;IAAAA,0CAAqB;IAAAA,iBAAK;IAClDA,8BAAwB;IAAAA,kDAAwB;IAAAA,iBAAK;IACrDA,+BAAwB;IAAAA,yDAAyB;IAAAA,iBAAK;IACtDA,+BAAwB;IAAAA,uDAA4B;IAAAA,iBAAK;IACzDA,+BAAwB;IAAAA,iCAAW;IAAAA,iBAAK;IAG5CA,8BAAO;IACLA,kFAeK;IACPA,iBAAQ;;;;IAhBeA,gBAAwB;IAAxBA,sDAAwB;;;;;IA8BnDA,+BAAkE;IAChEA,4EACF;IAAAA,iBAAM;;;;;IAcAA,0BAAyC;IAGnCA,wBAA6D;IAC7DA,YACF;IAAAA,iBAAO;IAETA,8BAAwB;IAEpBA,wBAA+D;IAAAA,YACjE;IAAAA,iBAAO;IAETA,8BAAwB;IAAAA,aAAoD;;IAAAA,iBAAK;IACjFA,2BAAI;IAAAA,aAAuB;IAAAA,iBAAK;IAChCA,2BAAI;IAAAA,aAAqB;IAAAA,iBAAK;IAC9BA,2BAAI;IAAAA,aAA2B;IAAAA,iBAAK;;;;;IAb5BA,eAA4C;IAA5CA,sEAA4C;IAC7CA,eAAqD;IAArDA,+EAAqD;IACxDA,eACF;IADEA,4EACF;IAGMA,eAA8C;IAA9CA,wEAA8C;IAC/CA,eAAuD;IAAvDA,iFAAuD;IAAKA,eACjE;IADiEA,6EACjE;IAEsBA,eAAoD;IAApDA,qFAAoD;IACxEA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAqB;IAArBA,yCAAqB;IACrBA,eAA2B;IAA3BA,+CAA2B;;;;;IA5BvCA,+BAAgE;IAIpDA,mCAAS;IAAAA,iBAAK;IAClBA,0BAAI;IAAAA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,wBAAG;IAAAA,iBAAK;IACZA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,+BAAwB;IAAAA,0BAAS;IAAAA,iBAAK;IACtCA,2BAAI;IAAAA,8BAAQ;IAAAA,iBAAK;IAGrBA,8BAAO;IACLA,kFAgBK;IACPA,iBAAQ;;;;IAjBgBA,gBAAiB;IAAjBA,+CAAiB;;;;;IA5HnDA,2BAAgC;IAKxBA,wBAAoC;IAAAA,kDACtC;IAAAA,iBAAK;IAEPA,+BAAuB;IACrBA,yEAEM;IACNA,0EAsCM;IACRA,iBAAM;IAIRA,+BAAuB;IAGjBA,yBAA6C;IAAAA,gFAC/C;IAAAA,iBAAK;IAEPA,gCAAuB;IACrBA,2EAEM;IACNA,4EA+BM;IACRA,iBAAM;IAIRA,gCAAuB;IAGjBA,yBAAuC;IAAAA,wEACzC;IAAAA,iBAAK;IAEPA,gCAAuB;IACrBA,2EAEM;IACNA,4EAgCM;IACRA,iBAAM;;;;IAvIEA,eAA+B;IAA/BA,uDAA+B;IAG/BA,eAA6B;IAA7BA,qDAA6B;IAkD7BA,eAAwC;IAAxCA,gEAAwC;IAGxCA,eAAsC;IAAtCA,8DAAsC;IA2CtCA,eAAiC;IAAjCA,yDAAiC;IAGjCA,eAA+B;IAA/BA,uDAA+B;;;;;IA+D/BA,+BAA4H;IAC1HA,uDACF;IAAAA,iBAAM;;;;;;IAlBZA,2BAA2B;IAEvBA,wBAAuE;IACvEA,8BAAQ;IAAAA,YAA+C;IAAAA,iBAAS;IAAAA,YAClE;IAAAA,iBAAM;IAENA,gCAAiE;IAA5BA;MAAAA;MAAA;MAAA,OAAYA,qCAAc;IAAA,EAAC;IAC9DA,+BAAkB;IAC2BA,qCAAM;IAAAA,iBAAQ;IACzDA,gCAMY;IACZA,4EAEM;IACRA,iBAAM;;;;;IAlBWA,eAAsD;IAAtDA,kFAAsD;IACpEA,eAA+D;IAA/DA,2FAA+D;IAC1DA,eAA+C;IAA/CA,6EAA+C;IAASA,eAClE;IADkEA,kEAClE;IAEMA,eAA8B;IAA9BA,mDAA8B;IAU1BA,eAAgG;IAAhGA,2MAAgG;;;AD/KpH,OAAM,MAAOC,iBAAiB;EAW5BC,YACUC,eAAgC,EAChCC,EAAe,EACfC,YAA0B;IAF1B,oBAAe,GAAfF,eAAe;IACf,OAAE,GAAFC,EAAE;IACF,iBAAY,GAAZC,YAAY;IAbtB,YAAO,GAAY,KAAK;IACxB,UAAK,GAAkB,IAAI;IAE3B,mBAAc,GAA4B,EAAE;IAC5C,iBAAY,GAAoB,EAAE;IAClC,0BAAqB,GAAoC,EAAE;IAE3D,kBAAa,GAAyB,IAAI;IAQxC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAEb,UAAU,CAACc,QAAQ;KACrC,CAAC;EACJ;EAEAC,QAAQ;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAA,qBAAqB;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACV,eAAe,CAACW,oBAAoB,EAAE,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACA,IAAI,CAACC,cAAc,GAAGD,QAAQ,CAACE,oBAAoB,CAACC,GAAG,CAACC,KAAK,KAAK;UAChE,GAAGA,KAAK;UACRC,SAAS,EAAE,IAAIC,IAAI,CAACF,KAAK,CAACC,SAAS;SACpC,CAAC,CAAC;QAEH,IAAI,CAACE,YAAY,GAAGP,QAAQ,CAACO,YAAY,CAACJ,GAAG,CAACK,KAAK,KAAK;UACtD,GAAGA,KAAK;UACRH,SAAS,EAAE,IAAIC,IAAI,CAACE,KAAK,CAACH,SAAS,CAAC;UACpCI,UAAU,EAAED,KAAK,CAACC,UAAU,GAAG,IAAIH,IAAI,CAACE,KAAK,CAACC,UAAU,CAAC,GAAGC;SAC7D,CAAC,CAAC;QAEH,IAAI,CAACC,qBAAqB,GAAGX,QAAQ,CAACW,qBAAqB,CAACR,GAAG,CAACS,IAAI,KAAK;UACvE,GAAGA,IAAI;UACPC,+BAA+B,EAAED,IAAI,CAACC,+BAA+B,GAAG,IAAIP,IAAI,CAACM,IAAI,CAACC,+BAA+B,CAAC,GAAGH,SAAS;UAClII,0BAA0B,EAAEF,IAAI,CAACE,0BAA0B,GAAG,IAAIR,IAAI,CAACM,IAAI,CAACE,0BAA0B,CAAC,GAAGJ;SAC3G,CAAC,CAAC;QAEH,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGmB,GAAG,IAAI;QACbC,OAAO,CAACpB,KAAK,CAAC,kCAAkC,EAAEmB,GAAG,CAAC;QACtD,IAAI,CAACnB,KAAK,GAAG,8CAA8C;QAC3D,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAsB,qBAAqB,CAACT,KAAoB;IACxC,IAAI,CAACU,aAAa,GAAGV,KAAK;IAC1B,IAAI,CAACnB,gBAAgB,CAAC8B,KAAK,CAAC;MAC1B5B,UAAU,EAAE;KACb,CAAC;IAEF,IAAI,CAACH,YAAY,CAACgC,IAAI,CAAC,mBAAmB,CAAC;EAC7C;EAEAC,YAAY;IACV,IAAI,IAAI,CAAChC,gBAAgB,CAACiC,OAAO,IAAI,CAAC,IAAI,CAACJ,aAAa,EAAE;MACxD;;IAGF,MAAMK,QAAQ,GAAG,IAAI,CAAClC,gBAAgB,CAACmC,KAAK;IAE5C,IAAI,CAACtC,eAAe,CAACmC,YAAY,CAAC,IAAI,CAACH,aAAa,CAACO,EAAE,EAAEF,QAAQ,CAAChC,UAAU,CAAC,CAACO,SAAS,CAAC;MACtFC,IAAI,EAAE,MAAK;QACT;QACA,IAAI,CAACX,YAAY,CAACsC,KAAK,CAAC,mBAAmB,CAAC;QAE5C;QACA,IAAI,CAAChC,qBAAqB,EAAE;MAC9B,CAAC;MACDE,KAAK,EAAGmB,GAAG,IAAI;QACbC,OAAO,CAACpB,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;QAC3C,IAAI,CAACnB,KAAK,GAAG,6BAA6B;MAC5C;KACD,CAAC;EACJ;EAEA+B,gBAAgB,CAACC,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,aAAa;MACtB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB,KAAK,CAAC;QACJ,OAAO,WAAW;MACpB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;IAAC;EAE1B;EAEAC,eAAe,CAACD,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAO,8BAA8B;MACvC,KAAK,CAAC;QACJ,OAAO,4BAA4B;MACrC,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,sBAAsB;IAAC;EAEpC;EAEAE,eAAe,CAACF,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,UAAU;MACnB,KAAK,CAAC;QACJ,OAAO,QAAQ;MACjB,KAAK,CAAC;QACJ,OAAO,SAAS;MAClB,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,YAAY;MACrB;QACE,OAAO,SAAS;IAAC;EAEvB;EAEAG,iBAAiB,CAACC,SAAiB;IACjC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,8BAA8B;QACjC,OAAO,cAAc;MACvB,KAAK,WAAW;QACd,OAAO,aAAa;MACtB,KAAK,qBAAqB;QACxB,OAAO,cAAc;MACvB;QACE,OAAO,WAAW;IAAC;EAEzB;EAEAC,gBAAgB,CAACD,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,8BAA8B;MACvC,KAAK,8BAA8B;QACjC,OAAO,uBAAuB;MAChC,KAAK,WAAW;QACd,OAAO,sBAAsB;MAC/B,KAAK,qBAAqB;QACxB,OAAO,kBAAkB;MAC3B,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC;QACE,OAAO,qBAAqB;IAAC;EAEnC;EAEAE,gBAAgB,CAACF,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,2BAA2B;MACpC,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,8BAA8B;QACjC,OAAO,8BAA8B;MACvC,KAAK,cAAc;QACjB,OAAO,6BAA6B;MACtC,KAAK,oBAAoB;QACvB,OAAO,wBAAwB;MACjC,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEAG,iBAAiB,CAACC,SAAiB;IACjC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,cAAc;MACvB,KAAK,0BAA0B;QAC7B,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,aAAa;MACtB,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,aAAa;QAChB,OAAO,WAAW;MACpB,KAAK,OAAO;QACV,OAAO,gBAAgB;MACzB;QACE,OAAO,WAAW;IAAC;EAEzB;EAEAC,gBAAgB,CAACD,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,kBAAkB;MAC3B,KAAK,0BAA0B;QAC7B,OAAO,kBAAkB;MAC3B,KAAK,oBAAoB;QACvB,OAAO,8BAA8B;MACvC,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,6BAA6B;MACtC,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC,KAAK,aAAa;QAChB,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,8BAA8B;MACvC,KAAK,OAAO;QACV,OAAO,mBAAmB;MAC5B;QACE,OAAO,qBAAqB;IAAC;EAEnC;EAEAE,gBAAgB,CAACF,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,uBAAuB;MAChC,KAAK,0BAA0B;QAC7B,OAAO,qBAAqB;MAC9B,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,cAAc;QACjB,OAAO,uBAAuB;MAChC,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,aAAa;QAChB,OAAO,WAAW;MACpB,KAAK,SAAS;QACZ,OAAO,UAAU;MACnB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAOA,SAAS;IAAC;EAEvB;;;uBA5QWpD,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAuD;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCjB9B9D,8BAAuB;UAEfA,oDAAqB;UAAAA,iBAAK;UAC9BA,iCAAkE;UAAlCA;YAAA,OAAS+D,2BAAuB;UAAA,EAAC;UAC/D/D,uBAA0C;UAAAA,6BAC5C;UAAAA,iBAAS;UAGXA,kEAIM;UAENA,kEAEM;UAENA,mEAkJM;UACRA,iBAAM;UAGNA,+BAAyH;UAI7DA,uDAAkB;UAAAA,iBAAK;UAC3EA,8BAA6G;UAC/GA,iBAAM;UACNA,gCAAwB;UACtBA,qEAqBM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,iCAAM;UAAAA,iBAAS;UACvFA,mCAA6G;UAAzBA;YAAA,OAAS+D,kBAAc;UAAA,EAAC;UAC1G/D,yBAA4C;UAAAA,mCAC9C;UAAAA,iBAAS;;;UAnMTA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAIXA,eAAwB;UAAxBA,iDAAwB;UA8JlBA,eAAmB;UAAnBA,wCAAmB;UAyBqBA,eAAqC;UAArCA,uDAAqC;;;qBD3L/EJ,aAAa,EAAEC,YAAY,mCAAEC,WAAW,0FAAEC,mBAAmB;MAAAiE;IAAA;EAAA", "names": ["Validators", "LocalDatePipe", "CommonModule", "FormsModule", "ReactiveFormsModule", "i0", "SecurityComponent", "constructor", "securityService", "fb", "modalService", "resolveAlertForm", "group", "resolution", "required", "ngOnInit", "loadSecurityDashboard", "loading", "error", "getSecurityDashboard", "subscribe", "next", "response", "securityEvents", "recentSecurityEvents", "map", "event", "timestamp", "Date", "active<PERSON>lerts", "alert", "resolvedAt", "undefined", "failedConnectionStats", "stat", "lastFailedCertificateValidation", "lastFailedApiKeyValidation", "err", "console", "openResolveAlertModal", "<PERSON><PERSON><PERSON><PERSON>", "reset", "open", "<PERSON><PERSON><PERSON><PERSON>", "invalid", "formData", "value", "id", "close", "getSeverityClass", "severity", "getSeverityIcon", "getSeverityText", "getEventTypeClass", "eventType", "getEventTypeIcon", "getEventTypeText", "getAlertTypeClass", "alertType", "getAlertTypeIcon", "getAlertTypeText", "selectors", "standalone", "features", "decls", "vars", "consts", "template", "ctx", "styles"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { SecurityService } from '../services/security.service';\nimport { AlertResponse, SecurityEventResponse, FailedConnectionStatsResponse } from '../models/security.model';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ModalService } from '../services/modal.service';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-security',\n  templateUrl: './security.component.html',\n  styleUrls: ['./security.component.css'],\n  imports: [LocalDatePipe, CommonModule, FormsModule, ReactiveFormsModule],\n  standalone: true\n})\nexport class SecurityComponent implements OnInit {\n  loading: boolean = false;\n  error: string | null = null;\n\n  securityEvents: SecurityEventResponse[] = [];\n  activeAlerts: AlertResponse[] = [];\n  failedConnectionStats: FailedConnectionStatsResponse[] = [];\n\n  selectedAlert: AlertResponse | null = null;\n  resolveAlertForm: FormGroup;\n\n  constructor(\n    private securityService: SecurityService,\n    private fb: FormBuilder,\n    private modalService: ModalService\n  ) {\n    this.resolveAlertForm = this.fb.group({\n      resolution: ['', Validators.required]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadSecurityDashboard();\n  }\n\n  loadSecurityDashboard(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.securityService.getSecurityDashboard().subscribe({\n      next: (response) => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n\n        this.activeAlerts = response.activeAlerts.map(alert => ({\n          ...alert,\n          timestamp: new Date(alert.timestamp),\n          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined\n        }));\n\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n\n  openResolveAlertModal(alert: AlertResponse): void {\n    this.selectedAlert = alert;\n    this.resolveAlertForm.reset({\n      resolution: ''\n    });\n\n    this.modalService.open('resolveAlertModal');\n  }\n\n  resolveAlert(): void {\n    if (this.resolveAlertForm.invalid || !this.selectedAlert) {\n      return;\n    }\n\n    const formData = this.resolveAlertForm.value;\n\n    this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({\n      next: () => {\n        // Zavření modálu\n        this.modalService.close('resolveAlertModal');\n\n        // Aktualizace dat\n        this.loadSecurityDashboard();\n      },\n      error: (err) => {\n        console.error('Error resolving alert', err);\n        this.error = 'Chyba při řešení upozornění';\n      }\n    });\n  }\n\n  getSeverityClass(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n\n  getSeverityIcon(severity: number): string {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n\n  getSeverityText(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n\n  getEventTypeClass(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n\n  getEventTypeIcon(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n\n  getEventTypeText(eventType: string): string {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n\n  getAlertTypeClass(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'text-warning';\n      case 'FailedConnectionAttempts':\n        return 'text-danger';\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'ApiKeyMisuse':\n        return 'text-danger';\n      case 'SystemError':\n        return 'text-danger';\n      case 'Error':\n        return 'text-danger';\n      case 'Warning':\n        return 'text-warning';\n      case 'Information':\n        return 'text-info';\n      case 'Other':\n        return 'text-secondary';\n      default:\n        return 'text-info';\n    }\n  }\n\n  getAlertTypeIcon(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'bi-clock-history';\n      case 'FailedConnectionAttempts':\n        return 'bi-x-circle-fill';\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'SystemError':\n        return 'bi-exclamation-octagon-fill';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      case 'Information':\n        return 'bi-info-circle-fill';\n      case 'Warning':\n        return 'bi-exclamation-triangle-fill';\n      case 'Error':\n        return 'bi-x-octagon-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n\n  getAlertTypeText(alertType: string): string {\n    switch (alertType) {\n      case 'CertificateExpiring':\n        return 'Expirující certifikát';\n      case 'FailedConnectionAttempts':\n        return 'Neúspěšné připojení';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'ApiKeyMisuse':\n        return 'Cyh použití API klíče';\n      case 'SystemError':\n        return 'Systémová chyba';\n      case 'Other':\n        return 'Ostatní';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      default:\n        return alertType;\n    }\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Bezpečnostní u<PERSON></h2>\n    <button class=\"btn btn-primary\" (click)=\"loadSecurityDashboard()\">\n      <i class=\"bi bi-arrow-clockwise me-2\"></i>Aktualizovat\n    </button>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger mb-4\">\n    {{ error }}\n  </div>\n\n  <div *ngIf=\"!loading && !error\">\n    <!-- Aktivní upozornění -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-bell-fill me-2\"></i>Aktivní upozornění\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"activeAlerts.length === 0\" class=\"alert alert-info\">\n          Žádná aktivní upozornění.\n        </div>\n        <div *ngIf=\"activeAlerts.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Závažnost</th>\n                <th>Typ</th>\n                <th>Čas</th>\n                <th>Popis</th>\n                <th>Instance</th>\n                <th>Zákazník</th>\n                <th>Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let alert of activeAlerts\">\n                <td>\n                  <span class=\"text-nowrap\" [ngClass]=\"getSeverityClass(alert.severity)\">\n                    <i [ngClass]=\"getSeverityIcon(alert.severity) + ' me-1'\"></i>\n                    {{ getSeverityText(alert.severity) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getAlertTypeClass(alert.alertType)\">\n                    <i [ngClass]=\"getAlertTypeIcon(alert.alertType) + ' me-1'\"></i>{{ getAlertTypeText(alert.alertType) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">{{ alert.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ alert.description }}</td>\n                <td>{{ alert.instanceName || '-' }}</td>\n                <td>{{ alert.customerName || '-' }}</td>\n                <td>\n                  <button class=\"btn btn-sm btn-primary\" (click)=\"openResolveAlertModal(alert)\">\n                    <i class=\"bi bi-check-circle-fill me-1\"></i>Vyřešit\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiky neúspěšných připojení -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-shield-exclamation me-2\"></i>Statistiky neúspěšných připojení\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"failedConnectionStats.length === 0\" class=\"alert alert-info\">\n          Žádné neúspěšné pokusy o připojení.\n        </div>\n        <div *ngIf=\"failedConnectionStats.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th class=\"text-nowrap\">Instance</th>\n                <th class=\"text-nowrap\">Neúsp. validace cert.</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace</th>\n                <th class=\"text-nowrap\">Neúsp. validace API klíče</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace API</th>\n                <th class=\"text-nowrap\">Poslední IP</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let stat of failedConnectionStats\">\n                <td>{{ stat.instanceName }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedCertificateValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedCertificateValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedCertificateValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedApiKeyValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedApiKeyValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedApiKeyValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>{{ stat.lastKnownIpAddress || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Nedávné bezpečnostní události -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header bg-primary text-white\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-journal-text me-2\"></i>Nedávné bezpečnostní události\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"securityEvents.length === 0\" class=\"alert alert-info\">\n          Žádné bezpečnostní události.\n        </div>\n        <div *ngIf=\"securityEvents.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Závažnost</th>\n                <th>Typ</th>\n                <th>Čas</th>\n                <th>Popis</th>\n                <th class=\"text-nowrap\">IP adresa</th>\n                <th>Uživatel</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let event of securityEvents\">\n                <td>\n                  <span [ngClass]=\"getSeverityClass(event.severity)\">\n                    <i [ngClass]=\"getSeverityIcon(event.severity) + ' me-1'\"></i>\n                    {{ getSeverityText(event.severity) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getEventTypeClass(event.eventType)\">\n                    <i [ngClass]=\"getEventTypeIcon(event.eventType) + ' me-1'\"></i>{{ getEventTypeText(event.eventType) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">{{ event.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ event.description }}</td>\n                <td>{{ event.ipAddress }}</td>\n                <td>{{ event.username || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro řešení upozornění -->\n<div class=\"modal fade\" id=\"resolveAlertModal\" tabindex=\"-1\" aria-labelledby=\"resolveAlertModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-primary text-white\">\n        <h5 class=\"modal-title\" id=\"resolveAlertModalLabel\">Vyřešit upozornění</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"selectedAlert\">\n          <div class=\"alert\" [ngClass]=\"getAlertTypeClass(selectedAlert.alertType)\">\n            <i [ngClass]=\"getAlertTypeIcon(selectedAlert.alertType) + ' me-1'\"></i>\n            <strong>{{ getAlertTypeText(selectedAlert.alertType) }}</strong>: {{ selectedAlert.description }}\n          </div>\n\n          <form [formGroup]=\"resolveAlertForm\" (ngSubmit)=\"resolveAlert()\">\n            <div class=\"mb-3\">\n              <label for=\"resolution\" class=\"form-label\">Řešení</label>\n              <textarea\n                id=\"resolution\"\n                formControlName=\"resolution\"\n                class=\"form-control\"\n                rows=\"3\"\n                placeholder=\"Popište, jak bylo upozornění vyřešeno\"\n              ></textarea>\n              <div *ngIf=\"resolveAlertForm.get('resolution')?.invalid && resolveAlertForm.get('resolution')?.touched\" class=\"text-danger\">\n                Řešení je povinné\n              </div>\n            </div>\n          </form>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zavřít</button>\n        <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"resolveAlertForm.invalid\" (click)=\"resolveAlert()\">\n          <i class=\"bi bi-check-circle-fill me-1\"></i>Vyřešit\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}