2025-06-02 10:46:12.111 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 10:46:12.308 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-06-02 10:46:15.511 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 10:46:15.527 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-02 10:46:15.566 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-06-02 10:46:15.660 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-06-02 10:46:15.672 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 10:46:15.677 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-02 10:46:15.706 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-02 10:46:15.718 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-06-02 10:46:15.727 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-06-02 10:46:16.016 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 10:46:16.083 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-06-02 10:46:16.289 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (34ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:16.319 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:16.327 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:16.342 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:16.347 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-06-02 10:46:16.417 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel: Overriding address(es) 'https://localhost:7177, http://localhost:5177'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-02 10:46:16.433 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-06-02 10:46:16.483 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-06-02 10:46:16.486 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5177
2025-06-02 10:46:16.487 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7177
2025-06-02 10:46:16.489 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-06-02 10:46:16.492 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-06-02 10:46:16.495 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.DISApi
2025-06-02 10:46:51.917 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-06-02 10:46:52.593 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (26ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:52.614 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:52.667 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:52.694 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 10:46:52.702 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-06-02 10:47:53.263 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5177/api/health - null null
2025-06-02 10:47:53.313 +02:00 [INF] DISAdmin.DISApi.Authentication.ApiKeyAuthenticationHandler: Přeskočení autentizace pro endpoint s atributem AllowAnonymous
2025-06-02 10:47:53.328 +02:00 [INF] Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware: Request:
Protocol: HTTP/1.1
Method: GET
Scheme: http
PathBase: 
Path: /api/health
Connection: keep-alive
Host: localhost:5177
User-Agent: Mozilla/5.0 (Windows NT; Windows NT 10.0; cs-CZ) WindowsPowerShell/5.1.26100.4061
2025-06-02 10:47:53.336 +02:00 [INF] Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware: Response:
StatusCode: 307
Location: https://localhost:7177/api/health
2025-06-02 10:47:53.344 +02:00 [INF] Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware: Duration: 15.7468ms
2025-06-02 10:47:53.367 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5177/api/health - 307 0 null 108.7073ms
2025-06-02 11:29:44.584 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 11:29:44.730 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-06-02 11:29:46.710 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 11:29:46.739 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-02 11:29:46.782 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-06-02 11:29:46.917 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-06-02 11:29:46.934 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 11:29:46.938 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-02 11:29:46.953 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-02 11:29:46.967 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-06-02 11:29:46.976 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-06-02 11:29:47.338 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 11:29:47.393 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-06-02 11:29:47.551 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (24ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 11:29:47.572 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 11:29:47.578 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 11:29:47.587 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-06-02 11:29:47.591 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-06-02 11:29:47.638 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel: Overriding address(es) 'https://localhost:7177, http://localhost:5177'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-02 11:29:47.653 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-06-02 11:29:47.704 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-06-02 11:29:47.709 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5177
2025-06-02 11:29:47.711 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7177
2025-06-02 11:29:47.713 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-06-02 11:29:47.715 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-06-02 11:29:47.717 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.DISApi
