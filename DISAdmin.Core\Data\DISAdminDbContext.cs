using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace DISAdmin.Core.Data;

public class DISAdminDbContext : DbContext
{
    public DISAdminDbContext(DbContextOptions<DISAdminDbContext> options) : base(options)
    {
    }

    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    // {
    //     base.OnConfiguring(optionsBuilder);

    //     // Ignorování varování o nevyřešených změnách modelu
    //     optionsBuilder.ConfigureWarnings(warnings =>
    //         warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
    // }

    public DbSet<User> Users { get; set; } = null!;
    public DbSet<Customer> Customers { get; set; } = null!;
    public DbSet<CustomerContact> CustomerContacts { get; set; } = null!;
    public DbSet<DISInstance> DISInstances { get; set; } = null!;
    public DbSet<DISVersion> DISVersions { get; set; } = null!;
    public DbSet<VersionChangeLog> VersionChangeLogs { get; set; } = null!;
    public DbSet<InstanceVersion> InstanceVersions { get; set; } = null!;
    public DbSet<DiagnosticLog> DiagnosticLogs { get; set; } = null!;
    public DbSet<PerformanceMetric> PerformanceMetrics { get; set; } = null!;
    public DbSet<ActivityLog> ActivityLogs { get; set; } = null!;
    public DbSet<ErrorLog> ErrorLogs { get; set; } = null!;
    public DbSet<DashboardConfig> DashboardConfigs { get; set; } = null!;
    public DbSet<SavedFilter> SavedFilters { get; set; } = null!;
    public DbSet<CertificateHistory> CertificateHistory { get; set; } = null!;
    public DbSet<SecurityEvent> SecurityEvents { get; set; } = null!;
    public DbSet<SecurityEventFilter> SecurityEventFilters { get; set; } = null!;
    public DbSet<Alert> Alerts { get; set; } = null!;
    public DbSet<AlertRule> AlertRules { get; set; } = null!;
    public DbSet<CertificateRotationSettings> CertificateRotationSettings { get; set; } = null!;
    public DbSet<InstanceCertificateSettings> InstanceCertificateSettings { get; set; } = null!;
    public DbSet<AllowedIpAddress> AllowedIpAddresses { get; set; } = null!;

    public DbSet<LoginAttempt> LoginAttempts { get; set; } = null!;
    public DbSet<AppSetting> AppSettings { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // User
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Username)
            .IsUnique();

        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique();

        // Customer
        modelBuilder.Entity<Customer>()
            .HasIndex(c => c.CompanyId)
            .IsUnique();

        // CustomerContact
        modelBuilder.Entity<CustomerContact>()
            .HasOne(cc => cc.Customer)
            .WithMany(c => c.Contacts)
            .HasForeignKey(cc => cc.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        // DISInstance
        modelBuilder.Entity<DISInstance>()
            .HasOne(di => di.Customer)
            .WithMany(c => c.DISInstances)
            .HasForeignKey(di => di.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<DISInstance>()
            .HasIndex(di => di.ApiKey)
            .IsUnique();

        // DISVersion
        modelBuilder.Entity<DISVersion>()
            .HasOne(dv => dv.CreatedByUser)
            .WithMany(u => u.CreatedVersions)
            .HasForeignKey(dv => dv.CreatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Konverze System.Version na string a zpět
        var versionConverter = new ValueConverter<Version, string>(
            v => v.ToString(),
            s => Version.Parse(s)
        );

        modelBuilder.Entity<DISVersion>()
            .Property(dv => dv.VersionNumber)
            .HasConversion(versionConverter);

        modelBuilder.Entity<DISVersion>()
            .HasIndex(dv => dv.VersionNumber)
            .IsUnique();

        // VersionChangeLog
        modelBuilder.Entity<VersionChangeLog>()
            .HasOne(vcl => vcl.Version)
            .WithMany(dv => dv.ChangeLogs)
            .HasForeignKey(vcl => vcl.VersionId)
            .OnDelete(DeleteBehavior.Cascade);

        // InstanceVersion
        modelBuilder.Entity<InstanceVersion>()
            .HasOne(iv => iv.Instance)
            .WithMany(di => di.InstanceVersions)
            .HasForeignKey(iv => iv.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<InstanceVersion>()
            .HasOne(iv => iv.Version)
            .WithMany(dv => dv.InstanceVersions)
            .HasForeignKey(iv => iv.VersionId)
            .OnDelete(DeleteBehavior.Restrict);

        // DiagnosticLog
        modelBuilder.Entity<DiagnosticLog>()
            .HasOne(dl => dl.Instance)
            .WithMany(di => di.DiagnosticLogs)
            .HasForeignKey(dl => dl.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        // PerformanceMetric
        modelBuilder.Entity<PerformanceMetric>()
            .HasOne(pm => pm.Instance)
            .WithMany(di => di.PerformanceMetrics)
            .HasForeignKey(pm => pm.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        // ActivityLog
        modelBuilder.Entity<ActivityLog>()
            .HasOne(al => al.User)
            .WithMany()
            .HasForeignKey(al => al.UserId)
            .OnDelete(DeleteBehavior.SetNull);

        // ErrorLog
        modelBuilder.Entity<ErrorLog>()
            .HasOne(el => el.User)
            .WithMany()
            .HasForeignKey(el => el.UserId)
            .OnDelete(DeleteBehavior.SetNull);

        // InstanceCertificateSettings
        modelBuilder.Entity<InstanceCertificateSettings>()
            .HasOne(ics => ics.Instance)
            .WithOne()
            .HasForeignKey<InstanceCertificateSettings>(ics => ics.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        // SecurityEvent
        modelBuilder.Entity<SecurityEvent>()
            .HasOne(se => se.Instance)
            .WithMany()
            .HasForeignKey(se => se.InstanceId)
            .OnDelete(DeleteBehavior.SetNull);

        // SecurityEventFilter
        modelBuilder.Entity<SecurityEventFilter>()
            .Property(sef => sef.EventType)
            .HasConversion<int>();

        // AllowedIpAddress
        modelBuilder.Entity<AllowedIpAddress>()
            .HasOne(ip => ip.Instance)
            .WithMany()
            .HasForeignKey(ip => ip.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<AllowedIpAddress>()
            .HasIndex(ip => new { ip.InstanceId, ip.IpAddress })
            .IsUnique();

        // AppSetting
        modelBuilder.Entity<AppSetting>()
            .HasIndex(s => s.Key)
            .IsUnique();

        modelBuilder.Entity<AppSetting>()
            .Property(s => s.Type)
            .HasConversion<int>();
    }
}
