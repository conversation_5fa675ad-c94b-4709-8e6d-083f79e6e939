using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DISAdmin.Core.Logging;

/// <summary>
/// Provider pro DatabaseLogger
/// </summary>
[ProviderAlias("Database")]
public class DatabaseLoggerProvider : ILoggerProvider
{
    private readonly ConcurrentDictionary<string, DatabaseLogger> _loggers = new ConcurrentDictionary<string, DatabaseLogger>();
    private readonly IServiceProvider _serviceProvider;
    private readonly DatabaseLoggerOptions _options;
    private readonly string _source;

    public DatabaseLoggerProvider(IServiceProvider serviceProvider, IOptions<DatabaseLoggerOptions> options, string source = "DISAdmin")
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _source = source;
    }

    public ILogger CreateLogger(string categoryName)
    {
        return _loggers.GetOrAdd(categoryName, name => new DatabaseLogger(
            name,
            _serviceProvider,
            (category, logLevel) => _options.IsEnabled(category, logLevel),
            _source));
    }

    public void Dispose()
    {
        _loggers.Clear();
    }
}

/// <summary>
/// Konfigurace pro DatabaseLogger
/// </summary>
public class DatabaseLoggerOptions
{
    public LogLevel LogLevel { get; set; } = LogLevel.Error;
    public ConcurrentDictionary<string, LogLevel> CategoryLogLevels { get; } = new ConcurrentDictionary<string, LogLevel>();

    /// <summary>
    /// Filtry pro database logging - klíč je namespace/kategorie, hodnota je minimální LogLevel
    /// </summary>
    public Dictionary<string, string> Filters { get; set; } = new Dictionary<string, string>();

    public bool IsEnabled(string category, LogLevel logLevel)
    {
        // Nejdříve zkontrolujeme filtry z konfigurace
        foreach (var filter in Filters)
        {
            if (category.StartsWith(filter.Key, StringComparison.OrdinalIgnoreCase))
            {
                if (Enum.TryParse<LogLevel>(filter.Value, out var filterLogLevel))
                {
                    return logLevel >= filterLogLevel;
                }
            }
        }

        // Pak zkontrolujeme CategoryLogLevels (pro zpětnou kompatibilitu)
        if (CategoryLogLevels.TryGetValue(category, out var categoryLogLevel))
        {
            return logLevel >= categoryLogLevel;
        }

        return logLevel >= LogLevel;
    }
}
