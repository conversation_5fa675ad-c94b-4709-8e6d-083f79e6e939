{"ast": null, "code": "import { isDevMode } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as bootstrap from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/modal.service\";\nimport * as i4 from \"@angular/common\";\nfunction NavMenuComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggle());\n    });\n    i0.ɵɵelement(1, \"span\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-expanded\", ctx_r0.isExpanded);\n  }\n}\nconst _c0 = function () {\n  return [\"link-active\"];\n};\nconst _c1 = function () {\n  return [\"/dashboard\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 30)(1, \"a\", 31);\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵtext(3, \"Dashboard\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nconst _c2 = function () {\n  return [\"/users\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3, \"U\\u017Eivatel\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nconst _c3 = function () {\n  return [\"/certificates\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_21_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵtext(3, \"Spr\\u00E1va certifik\\u00E1t\\u016F\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nconst _c4 = function () {\n  return [\"/performance\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_22_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.collapse());\n    });\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \"V\\u00FDkon DIS\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nconst _c5 = function () {\n  return [\"/security\"];\n};\nconst _c6 = function () {\n  return [\"/monitoring\"];\n};\nconst _c7 = function () {\n  return [\"/alerts\"];\n};\nconst _c8 = function () {\n  return [\"/logs\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18)(1, \"a\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵtext(3, \"Bezpe\\u010Dnost \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 38)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 39);\n    i0.ɵɵtext(8, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\")(10, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.collapse());\n    });\n    i0.ɵɵelement(11, \"i\", 40);\n    i0.ɵɵtext(12, \"Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.collapse());\n    });\n    i0.ɵɵelement(15, \"i\", 41);\n    i0.ɵɵtext(16, \"Alerty\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\")(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_23_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.collapse());\n    });\n    i0.ɵɵelement(19, \"i\", 42);\n    i0.ɵɵtext(20, \"Logy syst\\u00E9mu\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(5, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c5));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c6));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c7));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(9, _c8));\n  }\n}\nconst _c9 = function () {\n  return [\"/admin/server-certificate\"];\n};\nconst _c10 = function () {\n  return [\"/admin/disapi-status\"];\n};\nconst _c11 = function () {\n  return [\"/certificate-rotation\"];\n};\nfunction NavMenuComponent_div_9_ul_1_li_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18)(1, \"a\", 43);\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵtext(3, \"Administrace \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 45)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 46);\n    i0.ɵɵtext(8, \"Serverov\\u00FD certifik\\u00E1t\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\")(10, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.collapse());\n    });\n    i0.ɵɵelement(11, \"i\", 47);\n    i0.ɵɵtext(12, \"Stav DIS API\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\")(14, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_li_24_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.collapse());\n    });\n    i0.ɵɵelement(15, \"i\", 48);\n    i0.ɵɵtext(16, \"Rotace DIS certifik\\u00E1t\\u016F\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(4, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c9));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c10));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c11));\n  }\n}\nconst _c12 = function () {\n  return [\"/customers\"];\n};\nconst _c13 = function () {\n  return [\"/versions\"];\n};\nfunction NavMenuComponent_div_9_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 16);\n    i0.ɵɵtemplate(1, NavMenuComponent_div_9_ul_1_li_1_Template, 4, 4, \"li\", 17);\n    i0.ɵɵelementStart(2, \"li\", 18)(3, \"a\", 19);\n    i0.ɵɵelement(4, \"i\", 20);\n    i0.ɵɵtext(5, \"Spr\\u00E1va \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 21);\n    i0.ɵɵtemplate(7, NavMenuComponent_div_9_ul_1_li_7_Template, 4, 2, \"li\", 22);\n    i0.ɵɵelementStart(8, \"li\")(9, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.collapse());\n    });\n    i0.ɵɵelement(10, \"i\", 24);\n    i0.ɵɵtext(11, \"Z\\u00E1kazn\\u00EDci\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"li\", 18)(13, \"a\", 25);\n    i0.ɵɵelement(14, \"i\", 26);\n    i0.ɵɵtext(15, \"DIS \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ul\", 27)(17, \"li\")(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_ul_1_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.collapse());\n    });\n    i0.ɵɵelement(19, \"i\", 28);\n    i0.ɵɵtext(20, \"Verze DIS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, NavMenuComponent_div_9_ul_1_li_21_Template, 4, 2, \"li\", 22);\n    i0.ɵɵtemplate(22, NavMenuComponent_div_9_ul_1_li_22_Template, 4, 2, \"li\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, NavMenuComponent_div_9_ul_1_li_23_Template, 21, 10, \"li\", 29);\n    i0.ɵɵtemplate(24, NavMenuComponent_div_9_ul_1_li_24_Template, 17, 8, \"li\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(10, _c0));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c12));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLinkActive\", i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c13));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isAdmin);\n  }\n}\nfunction NavMenuComponent_div_9_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.toggleTheme());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r6.isDarkMode ? \"P\\u0159epnout na sv\\u011Btl\\u00FD re\\u017Eim\" : \"P\\u0159epnout na tmav\\u00FD re\\u017Eim\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.isDarkMode ? \"bi-sun-fill\" : \"bi-moon-stars-fill\");\n  }\n}\nconst _c14 = function () {\n  return [\"/profile\"];\n};\nfunction NavMenuComponent_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 53);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 55)(5, \"li\")(6, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_4_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.collapse());\n    });\n    i0.ɵɵelement(7, \"i\", 56);\n    i0.ɵɵtext(8, \"M\\u016Fj profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\");\n    i0.ɵɵelement(10, \"hr\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"li\")(12, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_9_li_4_Template_a_click_12_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.logout());\n    });\n    i0.ɵɵelement(13, \"i\", 59);\n    i0.ɵɵtext(14, \"Odhl\\u00E1sit se\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.currentUser == null ? null : ctx_r7.currentUser.firstName, \" \", ctx_r7.currentUser == null ? null : ctx_r7.currentUser.lastName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c14));\n  }\n}\nconst _c15 = function () {\n  return [\"/login\"];\n};\nfunction NavMenuComponent_div_9_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 60)(1, \"a\", 31);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵtext(3, \"P\\u0159ihl\\u00E1sit se\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c15));\n  }\n}\nconst _c16 = function (a0) {\n  return {\n    show: a0\n  };\n};\nfunction NavMenuComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, NavMenuComponent_div_9_ul_1_Template, 25, 14, \"ul\", 11);\n    i0.ɵɵelementStart(2, \"ul\", 12);\n    i0.ɵɵtemplate(3, NavMenuComponent_div_9_li_3_Template, 3, 2, \"li\", 13);\n    i0.ɵɵtemplate(4, NavMenuComponent_div_9_li_4_Template, 15, 4, \"li\", 14);\n    i0.ɵɵtemplate(5, NavMenuComponent_div_9_li_5_Template, 4, 2, \"li\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c16, ctx_r1.isExpanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoggedIn);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoginPage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoggedIn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoggedIn && !ctx_r1.isLoginPage);\n  }\n}\nconst _c17 = function (a0) {\n  return {\n    \"login-right-aligned\": a0\n  };\n};\nfunction NavMenuComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function NavMenuComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.toggleTheme());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c17, ctx_r2.isLoginPage));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r2.isDarkMode ? \"P\\u0159epnout na sv\\u011Btl\\u00FD re\\u017Eim\" : \"P\\u0159epnout na tmav\\u00FD re\\u017Eim\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.isDarkMode ? \"bi-sun-fill\" : \"bi-moon-stars-fill\");\n  }\n}\nconst _c18 = function (a0) {\n  return {\n    \"login-container\": a0\n  };\n};\nconst _c19 = function (a0) {\n  return {\n    \"login-left-aligned\": a0\n  };\n};\nconst _c20 = function () {\n  return [\"/\"];\n};\nexport let NavMenuComponent = /*#__PURE__*/(() => {\n  class NavMenuComponent {\n    constructor(authService, router, modalService, renderer) {\n      this.authService = authService;\n      this.router = router;\n      this.modalService = modalService;\n      this.renderer = renderer;\n      this.isExpanded = false;\n      this.currentUser = null;\n      this.isLoggedIn = false;\n      this.isAdmin = false;\n      this.isDarkMode = false;\n      this.isDevMode = isDevMode();\n      this.isLoginPage = false;\n      this.authService.currentUser.subscribe(user => {\n        this.currentUser = user;\n        this.isLoggedIn = !!user;\n        this.isAdmin = user?.isAdmin || false;\n        // Reinicializace dropdown po změně přihlášení\n        setTimeout(() => this.initializeDropdowns(), 100);\n      });\n      // Zavřít rozbalovací menu při navigaci\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        this.collapse();\n      });\n    }\n    ngOnInit() {\n      // Kontrola, zda je nastaven tmavý režim v localStorage\n      this.isDarkMode = localStorage.getItem('darkMode') === 'true';\n      this.applyTheme();\n      // Kontrola, zda jsme na přihlašovací stránce - použijeme startsWith pro detekci i s parametry\n      this.isLoginPage = this.router.url.startsWith('/login');\n      console.log('Initial isLoginPage:', this.isLoginPage, 'URL:', this.router.url);\n      // Sledování změn URL\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        this.isLoginPage = event.url.startsWith('/login');\n        console.log('Navigation isLoginPage:', this.isLoginPage, 'URL:', event.url);\n      });\n    }\n    ngAfterViewInit() {\n      // Inicializace Bootstrap dropdown po načtení view\n      this.initializeDropdowns();\n    }\n    initializeDropdowns() {\n      // Počkáme na dokončení renderování\n      setTimeout(() => {\n        const dropdownElements = document.querySelectorAll('[data-bs-toggle=\"dropdown\"]');\n        dropdownElements.forEach(element => {\n          try {\n            new bootstrap.Dropdown(element);\n          } catch (error) {\n            console.warn('Chyba při inicializaci dropdown:', error);\n          }\n        });\n      }, 100);\n    }\n    collapse() {\n      this.isExpanded = false;\n    }\n    toggle() {\n      this.isExpanded = !this.isExpanded;\n    }\n    logout() {\n      this.authService.logout();\n      this.router.navigate(['/login']);\n    }\n    toggleTheme() {\n      this.isDarkMode = !this.isDarkMode;\n      localStorage.setItem('darkMode', this.isDarkMode.toString());\n      this.applyTheme();\n    }\n    applyTheme() {\n      if (this.isDarkMode) {\n        document.body.classList.add('dark-theme');\n      } else {\n        document.body.classList.remove('dark-theme');\n      }\n    }\n    static {\n      this.ɵfac = function NavMenuComponent_Factory(t) {\n        return new (t || NavMenuComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ModalService), i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavMenuComponent,\n        selectors: [[\"app-nav-menu\"]],\n        decls: 11,\n        vars: 11,\n        consts: [[1, \"navbar\", \"navbar-expand-sm\", \"navbar-toggleable-sm\", \"navbar-dark\", \"bg-primary\", \"box-shadow\", \"mb-3\"], [1, \"container\", 3, \"ngClass\"], [1, \"login-left\", 3, \"ngClass\"], [1, \"navbar-brand\", 3, \"routerLink\"], [1, \"bi\", \"bi-database-gear\", \"me-2\"], [\"class\", \"navbar-toggler\", \"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \".navbar-collapse\", \"aria-label\", \"Toggle navigation\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"navbar-collapse collapse d-sm-inline-flex justify-content-between\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"login-right\", 3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \".navbar-collapse\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"navbar-collapse\", \"collapse\", \"d-sm-inline-flex\", \"justify-content-between\", 3, \"ngClass\"], [\"class\", \"navbar-nav flex-grow-1\", 4, \"ngIf\"], [1, \"navbar-nav\", \"align-items-center\"], [\"class\", \"nav-item me-2\", 4, \"ngIf\"], [\"class\", \"nav-item dropdown\", 4, \"ngIf\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"navbar-nav\", \"flex-grow-1\"], [\"class\", \"nav-item\", 3, \"routerLinkActive\", 4, \"ngIf\"], [1, \"nav-item\", \"dropdown\", 3, \"routerLinkActive\"], [\"href\", \"#\", \"id\", \"usersDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-people\", \"me-1\"], [\"aria-labelledby\", \"usersDropdown\", 1, \"dropdown-menu\"], [4, \"ngIf\"], [1, \"dropdown-item\", 3, \"routerLink\", \"click\"], [1, \"bi\", \"bi-people\", \"me-2\"], [\"href\", \"#\", \"id\", \"disDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-code-square\", \"me-1\"], [\"aria-labelledby\", \"disDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-code-square\", \"me-2\"], [\"class\", \"nav-item dropdown\", 3, \"routerLinkActive\", 4, \"ngIf\"], [1, \"nav-item\", 3, \"routerLinkActive\"], [1, \"nav-link\", 3, \"routerLink\"], [1, \"bi\", \"bi-speedometer2\", \"me-1\"], [1, \"bi\", \"bi-person-lock\", \"me-2\"], [1, \"bi\", \"bi-card-checklist\", \"me-2\"], [1, \"bi\", \"bi-speedometer\", \"me-2\"], [\"href\", \"#\", \"id\", \"securityDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-shield-lock\", \"me-1\"], [\"aria-labelledby\", \"securityDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"bi\", \"bi-graph-up\", \"me-2\"], [1, \"bi\", \"bi-bell\", \"me-2\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"href\", \"#\", \"id\", \"adminDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"bi\", \"bi-gear\", \"me-1\"], [\"aria-labelledby\", \"adminDropdown\", 1, \"dropdown-menu\"], [1, \"bi\", \"bi-shield-lock\", \"me-2\"], [1, \"bi\", \"bi-hdd-network\", \"me-2\"], [1, \"bi\", \"bi-arrow-repeat\", \"me-2\"], [1, \"nav-item\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"theme-toggle-btn\", 3, \"title\", \"click\"], [1, \"bi\", 3, \"ngClass\"], [1, \"nav-item\", \"dropdown\"], [\"href\", \"#\", \"id\", \"navbarDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-person-circle\", \"me-1\"], [\"aria-labelledby\", \"navbarDropdown\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"bi\", \"bi-person\", \"me-2\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-box-arrow-right\", \"me-2\"], [1, \"nav-item\"], [1, \"bi\", \"bi-box-arrow-in-right\", \"me-1\"], [1, \"login-right\", 3, \"ngClass\"]],\n        template: function NavMenuComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"header\")(1, \"nav\", 0)(2, \"div\", 1)(3, \"div\", 2)(4, \"a\", 3);\n            i0.ɵɵelement(5, \"i\", 4);\n            i0.ɵɵelementStart(6, \"span\");\n            i0.ɵɵtext(7, \"DIS Admin\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(8, NavMenuComponent_button_8_Template, 2, 1, \"button\", 5);\n            i0.ɵɵtemplate(9, NavMenuComponent_div_9_Template, 6, 7, \"div\", 6);\n            i0.ɵɵtemplate(10, NavMenuComponent_div_10_Template, 3, 5, \"div\", 7);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c18, ctx.isLoginPage));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c19, ctx.isLoginPage));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(10, _c20));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoginPage);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i2.RouterLink, i2.RouterLinkActive],\n        styles: [\"a.navbar-brand[_ngcontent-%COMP%]{white-space:normal;text-align:center;word-break:break-all}html[_ngcontent-%COMP%]{font-size:14px}@media (min-width: 768px){html[_ngcontent-%COMP%]{font-size:16px}}.box-shadow[_ngcontent-%COMP%]{box-shadow:0 .25rem .75rem #0000000d}.btn-link[_ngcontent-%COMP%]{text-decoration:none;padding:.5rem 1rem;color:#ffffffd9;background:transparent;border:none;text-align:left}.btn-link[_ngcontent-%COMP%]:hover, .btn-link[_ngcontent-%COMP%]:focus{color:#fff;background-color:#ffffff1a}.nav-link.dropdown-toggle.btn-link[_ngcontent-%COMP%]{padding:.5rem 1rem;display:flex;align-items:center}.dropdown-menu[_ngcontent-%COMP%]{border-radius:.25rem;box-shadow:0 .5rem 1rem #00000026;border:none;padding:.5rem 0;display:none}.dropdown-menu.show[_ngcontent-%COMP%]{display:block}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;font-size:.9rem;display:flex;align-items:center}.dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:rgba(var(--bs-primary-rgb),.1)}.dropdown-toggle[_ngcontent-%COMP%]:after{margin-left:.5rem;vertical-align:middle}@media (max-width: 767.98px){.dropdown-menu[_ngcontent-%COMP%]{border:none;box-shadow:none;padding-left:1rem;background-color:transparent}.dropdown-item[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]:hover{color:#fff;background-color:transparent}.dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}}.dark-theme[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]{background-color:#343a40;color:#fff}.dark-theme[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]{color:#f8f9fa}.dark-theme[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.dark-theme[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}.login-container[_ngcontent-%COMP%]{max-width:1140px;margin:0 auto;padding:0 15px;display:flex;justify-content:space-between;width:100%}.login-left[_ngcontent-%COMP%], .login-right[_ngcontent-%COMP%]{display:flex;align-items:center}.login-left-aligned[_ngcontent-%COMP%]{margin-left:calc(50% - 360px)}.login-right-aligned[_ngcontent-%COMP%]{margin-right:calc(50% - 360px)}.login-container[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]{margin-right:0;padding-left:0}@media (max-width: 767px){.login-left-aligned[_ngcontent-%COMP%], .login-right-aligned[_ngcontent-%COMP%]{margin-left:0;margin-right:0}}@media (min-width: 768px) and (max-width: 991px){.login-left-aligned[_ngcontent-%COMP%]{margin-left:calc(50% - 270px)}.login-right-aligned[_ngcontent-%COMP%]{margin-right:calc(50% - 270px)}}@media (min-width: 992px) and (max-width: 1199px){.login-left-aligned[_ngcontent-%COMP%]{margin-left:calc(50% - 320px)}.login-right-aligned[_ngcontent-%COMP%]{margin-right:calc(50% - 320px)}}@media (min-width: 1200px){.login-left-aligned[_ngcontent-%COMP%]{margin-left:calc(50% - 360px)}.login-right-aligned[_ngcontent-%COMP%]{margin-right:calc(50% - 360px)}}\"]\n      });\n    }\n  }\n  return NavMenuComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}