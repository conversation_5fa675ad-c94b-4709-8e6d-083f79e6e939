{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let SecurityService = /*#__PURE__*/(() => {\n  class SecurityService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/dashboard`;\n    }\n    getSecurityDashboard() {\n      return this.http.get(`${this.apiUrl}/security`);\n    }\n    getAlerts(includeResolved = false) {\n      return this.http.get(`${this.apiUrl}/alerts?includeResolved=${includeResolved}`);\n    }\n    resolveAlert(alertId, resolution) {\n      const request = {\n        resolution\n      };\n      return this.http.post(`${this.apiUrl}/alerts/${alertId}/resolve`, request);\n    }\n    // Security Event Filters\n    getSecurityEventFilters() {\n      return this.http.get(`${environment.apiUrl}/security-event-filters`);\n    }\n    createSecurityEventFilter(request) {\n      return this.http.post(`${environment.apiUrl}/security-event-filters`, request);\n    }\n    deleteSecurityEventFilter(filterId) {\n      return this.http.delete(`${environment.apiUrl}/security-event-filters/${filterId}`);\n    }\n    static {\n      this.ɵfac = function SecurityService_Factory(t) {\n        return new (t || SecurityService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SecurityService,\n        factory: SecurityService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SecurityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}