{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BrowserModule } from '@angular/platform-browser';\nimport { NgModule, LOCALE_ID } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { routes } from './app.routes';\nimport { CommonModule, registerLocaleData } from '@angular/common';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgChartsModule } from 'ng2-charts';\nimport { ToastrModule } from 'ngx-toastr';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { SharedModule } from './shared/shared.module';\n// Registrace lokalizačních dat pro češtinu\nimport localeCz from '@angular/common/locales/cs';\nregisterLocaleData(localeCz);\nimport { AppComponent } from './app.component';\nimport { NavMenuComponent } from './nav-menu/nav-menu.component';\nimport { HomeComponent } from './home/<USER>';\nimport { LoginComponent } from './login/login.component';\nimport { UsersComponent } from './users/users.component';\nimport { UserDetailComponent } from './users/user-detail/user-detail.component';\nimport { CustomersComponent } from './customers/customers.component';\nimport { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';\nimport { VersionsComponent } from './versions/versions.component';\nimport { VersionFormComponent } from './versions/version-form/version-form.component';\nimport { ProfileComponent } from './profile/profile.component';\nimport { SecurityComponent } from './security/security.component';\nimport { CertificatesComponent } from './certificates/certificates.component';\nimport { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { TwoFactorSetupComponent } from './profile/two-factor-setup/two-factor-setup.component';\nimport { AlertsComponent } from './alerts/alerts.component';\nimport { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';\nimport { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';\nimport { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';\nimport { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';\nimport { BreadcrumbsComponent } from './shared/breadcrumbs/breadcrumbs.component';\nimport { AuthService } from './services/auth.service';\nimport { UserService } from './services/user.service';\nimport { CustomerService } from './services/customer.service';\nimport { VersionService } from './services/version.service';\nimport { SecurityService } from './services/security.service';\nimport { CertificateService } from './services/certificate.service';\nimport { CertificateRotationService } from './services/certificate-rotation.service';\nimport { ServerCertificateService } from './services/server-certificate.service';\nimport { InstanceService } from './services/instance.service';\nimport { ModalService } from './services/modal.service';\nimport { ChartService } from './services/chart.service';\nimport { SystemStatusService } from './services/system-status.service';\nimport { AlertService } from './services/alert.service';\nimport { SignalRService } from './services/signalr.service';\nimport { MonitoringService } from './services/monitoring.service';\nimport { DashboardConfigService } from './services/dashboard-config.service';\nimport { ChartModalService } from './services/chart-modal.service';\nimport { JwtInterceptor } from './services/jwt.interceptor';\nimport { ErrorInterceptor } from './interceptors/error.interceptor';\nimport { AuthGuard } from './services/auth.guard';\nimport { ChangelogModalComponent } from './versions/changelog-modal/changelog-modal.component';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, NavMenuComponent, HomeComponent, LoginComponent, UsersComponent, UserDetailComponent, CustomersComponent, CustomerDetailComponent, VersionsComponent, VersionFormComponent, ProfileComponent,\n  // SecurityComponent, // Nyní je standalone komponenta\n  // CertificatesComponent, // Nyní je standalone komponenta\n  InstanceWizardComponent, DashboardComponent, TwoFactorSetupComponent, AlertsComponent, AlertRuleFormComponent, CertificateRotationSettingsComponent, InstanceCertificateSettingsComponent, IpWhitelistingComponent, ChangelogModalComponent],\n  imports: [BrowserModule.withServerTransition({\n    appId: 'ng-cli-universal'\n  }), CommonModule, HttpClientModule, FormsModule, ReactiveFormsModule, RouterModule.forRoot(routes), BrowserAnimationsModule, NgbModule, NgChartsModule, ToastrModule.forRoot({\n    timeOut: 3000,\n    positionClass: 'toast-top-right',\n    preventDuplicates: false,\n    progressBar: true,\n    closeButton: false,\n    tapToDismiss: true,\n    newestOnTop: true,\n    maxOpened: 5,\n    autoDismiss: true,\n    toastClass: 'ngx-toastr',\n    iconClasses: {\n      error: 'toast-error',\n      info: 'toast-info',\n      success: 'toast-success',\n      warning: 'toast-warning'\n    }\n  }), DragDropModule, BreadcrumbsComponent, SharedModule, SecurityComponent, CertificatesComponent // Přidáme standalone komponentu\n  ],\n\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: JwtInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: ErrorInterceptor,\n    multi: true\n  }, {\n    provide: LOCALE_ID,\n    useValue: 'cs-CZ'\n  }, AuthService, UserService, CustomerService, VersionService, SecurityService, CertificateService, CertificateRotationService, ServerCertificateService, InstanceService, ModalService, ChartService, AlertService, SignalRService, MonitoringService, DashboardConfigService, ChartModalService, SystemStatusService, AuthGuard],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "mappings": ";AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,eAAe;AACnD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,iBAAiB;AAClE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,wBAAwB;AAErD;AACA,OAAOC,QAAQ,MAAM,4BAA4B;AACjDP,kBAAkB,CAACO,QAAQ,CAAC;AAE5B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,uBAAuB,QAAQ,uDAAuD;AAC/F,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,uBAAuB,QAAQ,uDAAuD;AAE/F,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,oCAAoC,QAAQ,gEAAgE;AACrH,SAASC,oCAAoC,QAAQ,gEAAgE;AACrH,SAASC,uBAAuB,QAAQ,6CAA6C;AAGrF,SAASC,oBAAoB,QAAQ,4CAA4C;AAEjF,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,0BAA0B,QAAQ,yCAAyC;AACpF,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,uBAAuB,QAAQ,sDAAsD;AAsFvF,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,eApFrB5D,QAAQ,CAAC;EACR6D,YAAY,EAAE,CACZ5C,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,uBAAuB,EACvBC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAgB;EAChB;EACA;EACAG,uBAAuB,EACvBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,sBAAsB,EACtBC,oCAAoC,EACpCC,oCAAoC,EACpCC,uBAAuB,EACvBsB,uBAAuB,CACxB;EACDG,OAAO,EAAE,CACP/D,aAAa,CAACgE,oBAAoB,CAAC;IAAEC,KAAK,EAAE;EAAkB,CAAE,CAAC,EACjExD,YAAY,EACZJ,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EACnBG,YAAY,CAAC2D,OAAO,CAAC1D,MAAM,CAAC,EAC5BG,uBAAuB,EACvBC,SAAS,EACTC,cAAc,EACdC,YAAY,CAACoD,OAAO,CAAC;IACnBC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,KAAK;IACxBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,KAAK;IAClBC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE;MACXC,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE;;GAEZ,CAAC,EACFlE,cAAc,EACdwB,oBAAoB,EACpBvB,YAAY,EACZa,iBAAiB,EACjBC,qBAAqB,CAAC;EAAA,CACvB;;EACDoD,SAAS,EAAE,CACT;IAAEC,OAAO,EAAE7E,iBAAiB;IAAE8E,QAAQ,EAAE3B,cAAc;IAAE4B,KAAK,EAAE;EAAI,CAAE,EACrE;IAAEF,OAAO,EAAE7E,iBAAiB;IAAE8E,QAAQ,EAAE1B,gBAAgB;IAAE2B,KAAK,EAAE;EAAI,CAAE,EACvE;IAAEF,OAAO,EAAEjF,SAAS;IAAEoF,QAAQ,EAAE;EAAO,CAAE,EACzC9C,WAAW,EACXC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZE,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,sBAAsB,EACtBC,iBAAiB,EACjBL,mBAAmB,EACnBQ,SAAS,CACV;EACD4B,SAAS,EAAE,CAACrE,YAAY;CACzB,CAAC,GACW2C,SAAS,CAAI;SAAbA,SAAS", "names": ["BrowserModule", "NgModule", "LOCALE_ID", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "RouterModule", "routes", "CommonModule", "registerLocaleData", "BrowserAnimationsModule", "NgbModule", "NgChartsModule", "ToastrModule", "DragDropModule", "SharedModule", "localeCz", "AppComponent", "NavMenuComponent", "HomeComponent", "LoginComponent", "UsersComponent", "UserDetailComponent", "CustomersComponent", "CustomerDetailComponent", "VersionsComponent", "VersionFormComponent", "ProfileComponent", "SecurityComponent", "CertificatesComponent", "InstanceWizardComponent", "DashboardComponent", "TwoFactorSetupComponent", "AlertsComponent", "AlertRuleFormComponent", "CertificateRotationSettingsComponent", "InstanceCertificateSettingsComponent", "IpWhitelistingComponent", "BreadcrumbsComponent", "AuthService", "UserService", "CustomerService", "VersionService", "SecurityService", "CertificateService", "CertificateRotationService", "ServerCertificateService", "InstanceService", "ModalService", "ChartService", "SystemStatusService", "AlertService", "SignalRService", "MonitoringService", "DashboardConfigService", "ChartModalService", "JwtInterceptor", "ErrorInterceptor", "<PERSON><PERSON><PERSON><PERSON>", "ChangelogModalComponent", "AppModule", "declarations", "imports", "withServerTransition", "appId", "forRoot", "timeOut", "positionClass", "preventDuplicates", "progressBar", "closeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newestOnTop", "maxOpened", "autoDismiss", "toastClass", "iconClasses", "error", "info", "success", "warning", "providers", "provide", "useClass", "multi", "useValue", "bootstrap"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\app.module.ts"], "sourcesContent": ["import { BrowserModule } from '@angular/platform-browser';\r\nimport { NgModule, LOCALE_ID } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { RouterModule } from '@angular/router';\r\nimport { routes } from './app.routes';\r\nimport { CommonModule, registerLocaleData } from '@angular/common';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgChartsModule } from 'ng2-charts';\r\nimport { ToastrModule } from 'ngx-toastr';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { SharedModule } from './shared/shared.module';\r\n\r\n// Registrace lokalizačních dat pro češtinu\r\nimport localeCz from '@angular/common/locales/cs';\r\nregisterLocaleData(localeCz);\r\n\r\nimport { AppComponent } from './app.component';\r\nimport { NavMenuComponent } from './nav-menu/nav-menu.component';\r\nimport { HomeComponent } from './home/<USER>';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { UsersComponent } from './users/users.component';\r\nimport { UserDetailComponent } from './users/user-detail/user-detail.component';\r\nimport { CustomersComponent } from './customers/customers.component';\r\nimport { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';\r\nimport { VersionsComponent } from './versions/versions.component';\r\nimport { VersionFormComponent } from './versions/version-form/version-form.component';\r\nimport { ProfileComponent } from './profile/profile.component';\r\nimport { SecurityComponent } from './security/security.component';\r\nimport { CertificatesComponent } from './certificates/certificates.component';\r\nimport { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { TwoFactorSetupComponent } from './profile/two-factor-setup/two-factor-setup.component';\r\nimport { MonitoringComponent } from './monitoring/monitoring.component';\r\nimport { AlertsComponent } from './alerts/alerts.component';\r\nimport { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';\r\nimport { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';\r\nimport { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';\r\nimport { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';\r\nimport { AdvancedFilterComponent } from './shared/advanced-filter/advanced-filter.component';\r\nimport { AdvancedTableComponent } from './shared/advanced-table/advanced-table.component';\r\nimport { BreadcrumbsComponent } from './shared/breadcrumbs/breadcrumbs.component';\r\n\r\nimport { AuthService } from './services/auth.service';\r\nimport { UserService } from './services/user.service';\r\nimport { CustomerService } from './services/customer.service';\r\nimport { VersionService } from './services/version.service';\r\nimport { SecurityService } from './services/security.service';\r\nimport { CertificateService } from './services/certificate.service';\r\nimport { CertificateRotationService } from './services/certificate-rotation.service';\r\nimport { ServerCertificateService } from './services/server-certificate.service';\r\nimport { InstanceService } from './services/instance.service';\r\nimport { ModalService } from './services/modal.service';\r\nimport { ChartService } from './services/chart.service';\r\nimport { SystemStatusService } from './services/system-status.service';\r\nimport { AlertService } from './services/alert.service';\r\nimport { SignalRService } from './services/signalr.service';\r\nimport { MonitoringService } from './services/monitoring.service';\r\nimport { DashboardConfigService } from './services/dashboard-config.service';\r\nimport { ChartModalService } from './services/chart-modal.service';\r\nimport { JwtInterceptor } from './services/jwt.interceptor';\r\nimport { ErrorInterceptor } from './interceptors/error.interceptor';\r\nimport { AuthGuard } from './services/auth.guard';\r\nimport { ChangelogModalComponent } from './versions/changelog-modal/changelog-modal.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    NavMenuComponent,\r\n    HomeComponent,\r\n    LoginComponent,\r\n    UsersComponent,\r\n    UserDetailComponent,\r\n    CustomersComponent,\r\n    CustomerDetailComponent,\r\n    VersionsComponent,\r\n    VersionFormComponent,\r\n    ProfileComponent,\r\n    // SecurityComponent, // Nyní je standalone komponenta\r\n    // CertificatesComponent, // Nyní je standalone komponenta\r\n    InstanceWizardComponent,\r\n    DashboardComponent,\r\n    TwoFactorSetupComponent,\r\n    AlertsComponent,\r\n    AlertRuleFormComponent,\r\n    CertificateRotationSettingsComponent,\r\n    InstanceCertificateSettingsComponent,\r\n    IpWhitelistingComponent,\r\n    ChangelogModalComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule.withServerTransition({ appId: 'ng-cli-universal' }),\r\n    CommonModule,\r\n    HttpClientModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    RouterModule.forRoot(routes),\r\n    BrowserAnimationsModule,\r\n    NgbModule,\r\n    NgChartsModule,\r\n    ToastrModule.forRoot({\r\n      timeOut: 3000,\r\n      positionClass: 'toast-top-right',\r\n      preventDuplicates: false, // Změna na false, aby se zobrazovaly všechny notifikace\r\n      progressBar: true,\r\n      closeButton: false,\r\n      tapToDismiss: true,\r\n      newestOnTop: true,\r\n      maxOpened: 5,\r\n      autoDismiss: true,\r\n      toastClass: 'ngx-toastr',\r\n      iconClasses: {\r\n        error: 'toast-error',\r\n        info: 'toast-info',\r\n        success: 'toast-success',\r\n        warning: 'toast-warning'\r\n      }\r\n    }),\r\n    DragDropModule,\r\n    BreadcrumbsComponent,\r\n    SharedModule,\r\n    SecurityComponent, // Přidáme standalone komponentu\r\n    CertificatesComponent // Přidáme standalone komponentu\r\n  ],\r\n  providers: [\r\n    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },\r\n    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\r\n    { provide: LOCALE_ID, useValue: 'cs-CZ' },\r\n    AuthService,\r\n    UserService,\r\n    CustomerService,\r\n    VersionService,\r\n    SecurityService,\r\n    CertificateService,\r\n    CertificateRotationService,\r\n    ServerCertificateService,\r\n    InstanceService,\r\n    ModalService,\r\n    ChartService,\r\n    AlertService,\r\n    SignalRService,\r\n    MonitoringService,\r\n    DashboardConfigService,\r\n    ChartModalService,\r\n    SystemStatusService,\r\n    AuthGuard\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}