import { Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { LoginComponent } from './login/login.component';
import { UsersComponent } from './users/users.component';
import { UserDetailComponent } from './users/user-detail/user-detail.component';
import { CustomersComponent } from './customers/customers.component';
import { CustomerDetailComponent } from './customers/customer-detail/customer-detail.component';
import { VersionsComponent } from './versions/versions.component';
import { VersionFormComponent } from './versions/version-form/version-form.component';
import { ProfileComponent } from './profile/profile.component';
import { SecurityComponent } from './security/security.component';
import { CertificatesComponent } from './certificates/certificates.component';
import { InstanceWizardComponent } from './instance-wizard/instance-wizard.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { MonitoringComponent } from './monitoring/monitoring.component';
import { InstanceMetricsComponent } from './instance-metrics/instance-metrics.component';
import { AlertsComponent } from './alerts/alerts.component';
import { AlertRuleFormComponent } from './alerts/alert-rule-form/alert-rule-form.component';
import { CertificateRotationSettingsComponent } from './certificate-rotation/certificate-rotation-settings.component';
import { InstanceCertificateSettingsComponent } from './certificate-rotation/instance-certificate-settings.component';
import { IpWhitelistingComponent } from './ip-whitelisting/ip-whitelisting.component';
import { AuthGuard } from './services/auth.guard';

export const routes: Routes = [
  { path: '', component: HomeComponent, pathMatch: 'full', canActivate: [AuthGuard], data: { breadcrumb: 'Domů', icon: 'house-fill' } },
  { path: 'login', component: LoginComponent, data: { breadcrumb: 'Přihlášení', icon: 'box-arrow-in-right' } },
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Dashboard', icon: 'grid-fill' } },
  { path: 'users', component: UsersComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Uživatelé', icon: 'people-fill' } },
  { path: 'users/add', component: UserDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat uživatele', icon: 'plus-circle-fill' } },
  { path: 'users/:id', component: UserDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Detail uživatele', icon: 'person-fill' } },
  { path: 'customers', component: CustomersComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Zákazníci', icon: 'building-fill' } },
  { path: 'customers/add', component: CustomerDetailComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat zákazníka', icon: 'plus-circle-fill' } },
  { path: 'customers/:id', component: CustomerDetailComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Detail zákazníka', icon: 'info-circle-fill' } },
  { path: 'versions', component: VersionsComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Verze DIS', icon: 'tag-fill' } },
  { path: 'versions/add', component: VersionFormComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Přidat verzi', icon: 'plus-circle-fill' } },
  { path: 'versions/:id', component: VersionFormComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Detail verze', icon: 'info-circle-fill' } },
  { path: 'profile', component: ProfileComponent, canActivate: [AuthGuard], data: { breadcrumb: 'Profil', icon: 'person-fill' } },
  { path: 'security', component: SecurityComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Bezpečnostní události', icon: 'shield-exclamation-fill' } },
  { path: 'certificates', component: CertificatesComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Certifikáty', icon: 'file-earmark-lock-fill' } },
  { path: 'instance-wizard', component: InstanceWizardComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Průvodce instancí', icon: 'magic' } },
  { path: 'monitoring', loadChildren: () => import('./monitoring/monitoring.module').then(m => m.MonitoringModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Monitoring', icon: 'graph-up' } },
  { path: 'performance', loadChildren: () => import('./performance/performance.module').then(m => m.PerformanceModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Výkon DIS', icon: 'speedometer' } },
  { path: 'instance-metrics/:id', loadChildren: () => import('./instance-metrics/instance-metrics.module').then(m => m.InstanceMetricsModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Metriky instance', icon: 'speedometer2' } },
  { path: 'alerts', component: AlertsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Upozornění', icon: 'bell-fill' } },
  { path: 'alerts/rules/add', component: AlertRuleFormComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Přidat pravidlo', icon: 'plus-circle-fill' } },
  { path: 'alerts/rules/:id', component: AlertRuleFormComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Upravit pravidlo', icon: 'pencil-fill' } },
  { path: 'certificate-rotation', component: CertificateRotationSettingsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Nastavení automatické rotace DIS certifikátů', icon: 'arrow-repeat' } },
  { path: 'certificate-rotation/instance/:id', component: InstanceCertificateSettingsComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Nastavení certifikátů instance', icon: 'gear-fill' } },
  { path: 'ip-whitelisting/:id', component: IpWhitelistingComponent, canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'IP Whitelist', icon: 'list-check' } },
  { path: 'logs', loadChildren: () => import('./logs/logs.module').then(m => m.LogsModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Logy systému', icon: 'journal-text' } },
  { path: 'admin', loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule), canActivate: [AuthGuard], data: { adminOnly: true, breadcrumb: 'Administrace', icon: 'gear-fill' } },
  // Fallback route
  { path: '**', redirectTo: '' }
];
