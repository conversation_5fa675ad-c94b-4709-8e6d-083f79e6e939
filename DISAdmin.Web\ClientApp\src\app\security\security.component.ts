import { Component, OnInit } from '@angular/core';
import { SecurityService } from '../services/security.service';
import { AlertResponse, SecurityEventResponse, FailedConnectionStatsResponse } from '../models/security.model';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModalService } from '../services/modal.service';
import * as bootstrap from 'bootstrap';
import { LocalDatePipe } from '../shared/pipes/local-date.pipe';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security.component.css'],
  imports: [LocalDatePipe, CommonModule, FormsModule, ReactiveFormsModule],
  standalone: true
})
export class SecurityComponent implements OnInit {
  loading: boolean = false;
  error: string | null = null;

  securityEvents: SecurityEventResponse[] = [];
  activeAlerts: AlertResponse[] = [];
  failedConnectionStats: FailedConnectionStatsResponse[] = [];

  selectedAlert: AlertResponse | null = null;
  resolveAlertForm: FormGroup;

  constructor(
    private securityService: SecurityService,
    private fb: FormBuilder,
    private modalService: ModalService
  ) {
    this.resolveAlertForm = this.fb.group({
      resolution: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadSecurityDashboard();
  }

  loadSecurityDashboard(): void {
    this.loading = true;
    this.error = null;

    this.securityService.getSecurityDashboard().subscribe({
      next: (response) => {
        // Převod časů na lokální čas
        this.securityEvents = response.recentSecurityEvents.map(event => ({
          ...event,
          timestamp: new Date(event.timestamp)
        }));

        this.activeAlerts = response.activeAlerts.map(alert => ({
          ...alert,
          timestamp: new Date(alert.timestamp),
          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined
        }));

        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({
          ...stat,
          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,
          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined
        }));

        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading security dashboard', err);
        this.error = 'Chyba při načítání bezpečnostního dashboardu';
        this.loading = false;
      }
    });
  }

  openResolveAlertModal(alert: AlertResponse): void {
    this.selectedAlert = alert;
    this.resolveAlertForm.reset({
      resolution: ''
    });

    this.modalService.open('resolveAlertModal');
  }

  resolveAlert(): void {
    if (this.resolveAlertForm.invalid || !this.selectedAlert) {
      return;
    }

    const formData = this.resolveAlertForm.value;

    this.securityService.resolveAlert(this.selectedAlert.id, formData.resolution).subscribe({
      next: () => {
        // Zavření modálu
        this.modalService.close('resolveAlertModal');

        // Aktualizace dat
        this.loadSecurityDashboard();
      },
      error: (err) => {
        console.error('Error resolving alert', err);
        this.error = 'Chyba při řešení upozornění';
      }
    });
  }

  getSeverityClass(severity: number): string {
    switch (severity) {
      case 5:
        return 'text-danger fw-bold';
      case 4:
        return 'text-danger';
      case 3:
        return 'text-warning';
      case 2:
        return 'text-info';
      case 1:
        return 'text-success';
      default:
        return 'text-muted';
    }
  }

  getSeverityIcon(severity: number): string {
    switch (severity) {
      case 5:
      case 4:
        return 'bi-exclamation-triangle-fill';
      case 3:
        return 'bi-exclamation-circle-fill';
      case 2:
        return 'bi-info-circle-fill';
      default:
        return 'bi-check-circle-fill';
    }
  }

  getSeverityText(severity: number): string {
    switch (severity) {
      case 5:
        return 'Kritická';
      case 4:
        return 'Vysoká';
      case 3:
        return 'Střední';
      case 2:
        return 'Nízká';
      case 1:
        return 'Informační';
      default:
        return 'Neznámá';
    }
  }

  getEventTypeClass(eventType: string): string {
    switch (eventType) {
      case 'SuspiciousActivity':
        return 'text-danger';
      case 'CertificateValidationFailure':
        return 'text-warning';
      case 'IpBlocked':
        return 'text-danger';
      case 'FailedAccessAttempt':
        return 'text-warning';
      default:
        return 'text-info';
    }
  }

  getEventTypeIcon(eventType: string): string {
    switch (eventType) {
      case 'SuspiciousActivity':
        return 'bi-exclamation-triangle-fill';
      case 'CertificateValidationFailure':
        return 'bi-shield-exclamation';
      case 'IpBlocked':
        return 'bi-slash-circle-fill';
      case 'FailedAccessAttempt':
        return 'bi-x-circle-fill';
      case 'ApiKeyMisuse':
        return 'bi-key-fill';
      case 'UnauthorizedAccess':
        return 'bi-shield-x';
      case 'Other':
        return 'bi-question-circle-fill';
      default:
        return 'bi-info-circle-fill';
    }
  }

  getEventTypeText(eventType: string): string {
    switch (eventType) {
      case 'FailedAccessAttempt':
        return 'Neúspěšný pokus o přístup';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      case 'IpBlocked':
        return 'Blokovaná IP adresa';
      case 'CertificateValidationFailure':
        return 'Selhání validace certifikátu';
      case 'ApiKeyMisuse':
        return 'Nesprávné použití API klíče';
      case 'UnauthorizedAccess':
        return 'Neautorizovaný přístup';
      case 'Other':
        return 'Ostatní';
      default:
        return eventType;
    }
  }

  getAlertTypeClass(alertType: string): string {
    switch (alertType) {
      case 'CertificateExpiring':
        return 'text-warning';
      case 'FailedConnectionAttempts':
        return 'text-danger';
      case 'SuspiciousActivity':
        return 'text-danger';
      case 'ApiKeyMisuse':
        return 'text-danger';
      case 'SystemError':
        return 'text-danger';
      case 'Error':
        return 'text-danger';
      case 'Warning':
        return 'text-warning';
      case 'Information':
        return 'text-info';
      case 'Other':
        return 'text-secondary';
      default:
        return 'text-info';
    }
  }

  getAlertTypeIcon(alertType: string): string {
    switch (alertType) {
      case 'CertificateExpiring':
        return 'bi-clock-history';
      case 'FailedConnectionAttempts':
        return 'bi-x-circle-fill';
      case 'SuspiciousActivity':
        return 'bi-exclamation-triangle-fill';
      case 'ApiKeyMisuse':
        return 'bi-key-fill';
      case 'SystemError':
        return 'bi-exclamation-octagon-fill';
      case 'Other':
        return 'bi-question-circle-fill';
      case 'Information':
        return 'bi-info-circle-fill';
      case 'Warning':
        return 'bi-exclamation-triangle-fill';
      case 'Error':
        return 'bi-x-octagon-fill';
      default:
        return 'bi-info-circle-fill';
    }
  }

  getAlertTypeText(alertType: string): string {
    switch (alertType) {
      case 'CertificateExpiring':
        return 'Expirující certifikát';
      case 'FailedConnectionAttempts':
        return 'Neúspěšné připojení';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      case 'ApiKeyMisuse':
        return 'Chyba použití API klíče';
      case 'SystemError':
        return 'Systémová chyba';
      case 'Other':
        return 'Ostatní';
      case 'Information':
        return 'Informace';
      case 'Warning':
        return 'Varování';
      case 'Error':
        return 'Chyba';
      default:
        return alertType;
    }
  }
}
