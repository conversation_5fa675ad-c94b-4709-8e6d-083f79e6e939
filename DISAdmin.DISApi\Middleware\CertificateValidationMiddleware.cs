using System.Security.Cryptography.X509Certificates;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using System.Reflection;
using DISAdmin.DISApi.Attributes;

namespace DISAdmin.DISApi.Middleware;

public class CertificateValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<CertificateValidationMiddleware> _logger;
    private readonly DISAdminDbContext _dbContext;
    private readonly SecurityEventFilterService _filterService;
    private readonly bool _requireCertificate;

    public CertificateValidationMiddleware(RequestDelegate next, ILogger<CertificateValidationMiddleware> logger, DISAdminDbContext dbContext, SecurityEventFilterService filterService, bool requireCertificate = true)
    {
        _next = next;
        _logger = logger;
        _dbContext = dbContext;
        _filterService = filterService;
        _requireCertificate = requireCertificate;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Rozšířené získání klientského certifikátu pro IIS prostředí
        var clientCertificate = await GetClientCertificateAsync(context);

        var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        var instanceId = context.Items["InstanceId"] as int?;

        _logger.LogInformation("CertificateValidationMiddleware: Certifikát {Status}, IP: {IpAddress}, Path: {Path}",
            clientCertificate != null ? "nalezen" : "nenalezen", ipAddress, context.Request.Path);

        // Pokud nepožadujeme certifikát, jde o health check nebo endpoint má atribut AllowAnonymous, přeskočíme validaci
        if (!_requireCertificate ||
            context.Request.Path.StartsWithSegments("/api/health") ||
            HasAllowAnonymousAttribute(context))
        {
            await _next(context);
            return;
        }

        if (clientCertificate == null)
        {
            _logger.LogWarning("Požadavek bez klientského certifikátu");
            await LogSecurityEvent(context, "MISSING_CERTIFICATE", ipAddress, instanceId);
            context.Response.StatusCode = 401; // Unauthorized
            await context.Response.WriteAsync("Klientský certifikát je povinný");
            return;
        }

        var thumbprint = clientCertificate.Thumbprint;
        var instance = await ValidateCertificate(thumbprint, instanceId);

        if (instance == null)
        {
            _logger.LogWarning("Neplatný klientský certifikát: {Thumbprint}", thumbprint);
            await LogSecurityEvent(context, "INVALID_CERTIFICATE", ipAddress, instanceId, thumbprint);
            context.Response.StatusCode = 401; // Unauthorized
            await context.Response.WriteAsync("Neplatný klientský certifikát");
            return;
        }

        // Kontrola expirace certifikátu - používáme lokální čas místo UTC
        if (clientCertificate.NotAfter < DateTime.Now)
        {
            _logger.LogWarning("Expirovaný klientský certifikát: {Thumbprint}, Expirace: {Expiration}, Aktuální čas: {CurrentTime}",
                thumbprint, clientCertificate.NotAfter, DateTime.Now);
            await LogSecurityEvent(context, "EXPIRED_CERTIFICATE", ipAddress, instanceId, thumbprint);
            context.Response.StatusCode = 401; // Unauthorized
            await context.Response.WriteAsync("Klientský certifikát expiroval");
            return;
        }

        // Aktualizace informací o certifikátu
        await UpdateCertificateInfo(instance, clientCertificate);

        await _next(context);
    }

    private async Task<DISInstance?> ValidateCertificate(string thumbprint, int? instanceId)
    {
        try
        {
            DISInstance? instance;

            if (instanceId.HasValue)
            {
                // Pokud známe ID instance (z API klíče), ověříme, zda certifikát patří této instanci
                instance = await _dbContext.DISInstances
                    .FirstOrDefaultAsync(i => i.Id == instanceId.Value && i.ClientCertificateThumbprint == thumbprint);
            }
            else
            {
                // Jinak hledáme instanci podle thumbprintu certifikátu
                instance = await _dbContext.DISInstances
                    .FirstOrDefaultAsync(i => i.ClientCertificateThumbprint == thumbprint);
            }

            return instance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při validaci certifikátu");
            return null;
        }
    }

    private async Task UpdateCertificateInfo(DISInstance instance, X509Certificate2 certificate)
    {
        try
        {
            instance.LastCertificateValidation = DateTime.UtcNow;

            // Aktualizace informací o certifikátu, pokud se změnily
            if (instance.ClientCertificateSubject != certificate.Subject ||
                instance.ClientCertificateIssuer != certificate.Issuer ||
                instance.CertificateExpirationDate != certificate.NotAfter)
            {
                instance.ClientCertificateSubject = certificate.Subject;
                instance.ClientCertificateIssuer = certificate.Issuer;
                instance.CertificateExpirationDate = certificate.NotAfter;

                _logger.LogInformation("Aktualizace informací o certifikátu pro instanci {InstanceId}: {Subject}, Expirace: {Expiration}",
                    instance.Id, certificate.Subject, certificate.NotAfter);
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při aktualizaci informací o certifikátu");
        }
    }

    private static bool HasAllowAnonymousAttribute(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        if (endpoint == null)
            return false;

        // Kontrola atributu AllowAnonymous na endpointu
        if (endpoint.Metadata.GetMetadata<IAllowAnonymous>() != null)
            return true;

        // Kontrola atributu SkipCertificateValidation na endpointu
        if (endpoint.Metadata.GetMetadata<SkipCertificateValidationAttribute>() != null)
            return true;

        // Kontrola atributu AllowAnonymous nebo SkipCertificateValidation na kontroleru nebo akci
        if (endpoint.Metadata.GetMetadata<ControllerActionDescriptor>() is ControllerActionDescriptor controllerActionDescriptor)
        {
            // Kontrola atributu na akci
            if (controllerActionDescriptor.MethodInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null ||
                controllerActionDescriptor.MethodInfo.GetCustomAttribute<SkipCertificateValidationAttribute>() != null)
                return true;

            // Kontrola atributu na kontroleru
            if (controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null ||
                controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<SkipCertificateValidationAttribute>() != null)
                return true;
        }

        return false;
    }

    /// <summary>
    /// Rozšířené získání klientského certifikátu pro IIS prostředí
    /// </summary>
    private async Task<X509Certificate2?> GetClientCertificateAsync(HttpContext context)
    {
        // 1. Pokus o získání certifikátu z Connection.ClientCertificate (standardní způsob)
        var clientCertificate = context.Connection.ClientCertificate;
        if (clientCertificate != null)
        {
            _logger.LogInformation("Certifikát nalezen v Connection.ClientCertificate: {Subject}", clientCertificate.Subject);
            return clientCertificate;
        }

        // 2. Pokus o získání certifikátu z ASP.NET Core Certificate Authentication
        try
        {
            var certificateFeature = context.Features.Get<Microsoft.AspNetCore.Http.Features.ITlsConnectionFeature>();
            if (certificateFeature?.ClientCertificate != null)
            {
                _logger.LogInformation("Certifikát nalezen v TlsConnectionFeature: {Subject}", certificateFeature.ClientCertificate.Subject);
                return certificateFeature.ClientCertificate;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Chyba při získávání certifikátu z TlsConnectionFeature");
        }

        // 3. Pokus o získání certifikátu z IIS hlaviček (různé možné názvy)
        var certHeaders = new[] { "X-Client-Cert", "X-ARR-ClientCert", "MS-ASPNETCORE-CLIENTCERT", "X-Forwarded-Client-Cert" };

        foreach (var headerName in certHeaders)
        {
            if (context.Request.Headers.ContainsKey(headerName))
            {
                try
                {
                    var certHeader = context.Request.Headers[headerName].ToString();
                    if (!string.IsNullOrEmpty(certHeader))
                    {
                        // Pokus o dekódování Base64
                        var certBytes = Convert.FromBase64String(certHeader);
                        var cert = new X509Certificate2(certBytes);
                        _logger.LogInformation("Certifikát úspěšně načten z hlavičky {Header}: {Subject}", headerName, cert.Subject);
                        return cert;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Chyba při načítání certifikátu z hlavičky {Header}", headerName);
                }
            }
        }

        // 4. Pokus o získání certifikátu pomocí IIS Server Variables
        try
        {
            var serverVariables = context.Features.Get<Microsoft.AspNetCore.Http.Features.IServerVariablesFeature>();
            if (serverVariables != null)
            {
                var certEncoded = serverVariables["CERT_CERTIFICATE"];
                if (!string.IsNullOrEmpty(certEncoded))
                {
                    var certBytes = Convert.FromBase64String(certEncoded);
                    var cert = new X509Certificate2(certBytes);
                    _logger.LogInformation("Certifikát nalezen v IIS Server Variables: {Subject}", cert.Subject);
                    return cert;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Chyba při získávání certifikátu z IIS Server Variables");
        }

        // 5. Pokus o renegociaci SSL připojení (pouze pro IIS)
        var isIIS = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_IIS_PHYSICAL_PATH"));
        if (isIIS && context.Connection.ClientCertificate == null)
        {
            try
            {
                _logger.LogInformation("Pokus o renegociaci SSL připojení pro získání klientského certifikátu");
                clientCertificate = await context.Connection.GetClientCertificateAsync();
                if (clientCertificate != null)
                {
                    _logger.LogInformation("Certifikát získán po renegociaci SSL: {Subject}", clientCertificate.Subject);
                    return clientCertificate;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Chyba při renegociaci SSL připojení");
            }
        }

        _logger.LogWarning("Klientský certifikát nebyl nalezen žádným způsobem");
        return null;
    }

    private async Task LogSecurityEvent(HttpContext context, string eventType, string ipAddress, int? instanceId = null, string? thumbprint = null)
    {
        try
        {
            var description = $"{eventType}: {context.Request.Method} {context.Request.Path}" +
                             (thumbprint != null ? $", Thumbprint: {thumbprint}" : "");

            // Kontrola, zda událost odpovídá některému z filtrů
            var shouldFilter = await _filterService.ShouldFilterEventAsync(
                SecurityEventType.CertificateValidationFailure, description, ipAddress);

            if (shouldFilter)
            {
                _logger.LogDebug("Bezpečnostní událost byla filtrována: {EventType}, IP: {IpAddress}", eventType, ipAddress);
                return;
            }

            var securityEvent = new SecurityEvent
            {
                Timestamp = DateTime.UtcNow,
                EventType = SecurityEventType.CertificateValidationFailure,
                IpAddress = ipAddress,
                Description = description,
                Username = instanceId.HasValue ? $"Instance #{instanceId}" : "unknown",
                Severity = 2, // Střední závažnost
                IsResolved = false
            };

            _dbContext.SecurityEvents.Add(securityEvent);
            await _dbContext.SaveChangesAsync();

            // Pokud známe instanci, aktualizujeme počítadlo neúspěšných pokusů
            if (instanceId.HasValue)
            {
                var instance = await _dbContext.DISInstances.FindAsync(instanceId.Value);
                if (instance != null)
                {
                    instance.FailedCertificateValidationCount++;
                    instance.LastFailedCertificateValidation = DateTime.UtcNow;
                    await _dbContext.SaveChangesAsync();
                }
            }

            _logger.LogWarning("Bezpečnostní incident: {EventType}, IP: {IpAddress}", eventType, ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při logování bezpečnostního incidentu");
        }
    }
}
