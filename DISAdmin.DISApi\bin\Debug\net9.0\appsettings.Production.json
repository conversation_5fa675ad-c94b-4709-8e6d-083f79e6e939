{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.Hosting": "Information", "Microsoft.AspNetCore.Server.IISIntegration": "Information", "Microsoft.EntityFrameworkCore": "Warning"}, "Database": {"Enabled": true, "LogLevel": "Information", "Source": "DISApi"}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Information", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}", "FileNamePrefix": "disapi"}, "ConsoleLogging": {"Enabled": true, "LogDirectory": "Logs", "FileNamePrefix": "console", "IncludeTimestamp": true, "RedirectErrors": true}, "AllowedHosts": "*", "Security": {"SkipCertValidation": false}, "IIS": {"RequireClientCertificate": true, "ClientCertificateMode": "AcceptCertificate"}, "Kestrel": {"Endpoints": {"Https": {"Url": "https://0.0.0.0:7177", "ClientCertificateMode": "AllowCertificate"}}}, "SSL": {"EnableDetailedLogging": true, "CertificateValidationTimeout": 30, "RetryAttempts": 3, "RetryDelay": 1000}}