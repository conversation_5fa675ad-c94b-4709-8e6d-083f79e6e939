{"Logging": {"Database": {"Enabled": true, "LogLevel": "Warning", "Source": "DISApi", "Filters": {"Microsoft.EntityFrameworkCore": "Warning"}}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Information", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}", "FileNamePrefix": "disapi", "Filters": {"Microsoft.EntityFrameworkCore": "Warning"}}, "ConsoleLogging": {"Enabled": true, "LogDirectory": "Logs", "FileNamePrefix": "console", "IncludeTimestamp": true, "RedirectErrors": true}, "AllowedHosts": "*", "Security": {"SkipCertValidation": false}, "IIS": {"RequireClientCertificate": true, "ClientCertificateMode": "AcceptCertificate"}, "Kestrel": {"Endpoints": {"Https": {"Url": "https://0.0.0.0:40443", "ClientCertificateMode": "AllowCertificate"}}}, "SSL": {"EnableDetailedLogging": true, "CertificateValidationTimeout": 30, "RetryAttempts": 3, "RetryDelay": 1000}}