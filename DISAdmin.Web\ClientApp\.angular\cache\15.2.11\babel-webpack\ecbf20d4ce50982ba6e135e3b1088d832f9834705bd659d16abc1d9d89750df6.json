{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-toastr\";\nexport let ClipboardService = /*#__PURE__*/(() => {\n  class ClipboardService {\n    constructor(toastr) {\n      this.toastr = toastr;\n    }\n    /**\r\n     * Zkopíruje text do schránky s automatickým fallback a toast notifikací\r\n     * @param text Text k zkopírování\r\n     * @param successMessage Zpráva při <PERSON> (volitelná)\r\n     * @param errorMessage Zpráva při chybě (volitelná)\r\n     * @returns Promise<boolean> - true při <PERSON>, false při chybě\r\n     */\n    copyToClipboard(text, successMessage = 'Text byl zkopírován do schránky', errorMessage = 'Nepodařilo se zkopírovat text do schránky') {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!text || text.trim() === '') {\n          _this.toastr.error('Není co kopírovat', 'Chyba');\n          return false;\n        }\n        try {\n          // Pokusíme se použít moderní Clipboard API\n          if (navigator.clipboard && window.isSecureContext) {\n            yield navigator.clipboard.writeText(text);\n            _this.toastr.success(successMessage, 'Úspěch');\n            return true;\n          } else {\n            // Fallback pro starší prohlížeče nebo nezabezpečené kontexty\n            return _this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n          }\n        } catch (err) {\n          console.error('Clipboard API selhalo:', err);\n          // Pokus o fallback\n          return _this.fallbackCopyToClipboard(text, successMessage, errorMessage);\n        }\n      })();\n    }\n    /**\r\n     * Fallback metoda pro kopírování do schránky pomocí execCommand\r\n     * @param text Text k zkopírování\r\n     * @param successMessage Zpráva při úspěchu\r\n     * @param errorMessage Zpráva při chybě\r\n     * @returns boolean - true při úspěchu, false při chybě\r\n     */\n    fallbackCopyToClipboard(text, successMessage, errorMessage) {\n      try {\n        // Vytvoříme dočasný textarea element\n        const textArea = document.createElement('textarea');\n        textArea.value = text;\n        // Nastavíme styly pro skrytí elementu\n        textArea.style.position = 'fixed';\n        textArea.style.left = '-999999px';\n        textArea.style.top = '-999999px';\n        textArea.style.width = '2em';\n        textArea.style.height = '2em';\n        textArea.style.padding = '0';\n        textArea.style.border = 'none';\n        textArea.style.outline = 'none';\n        textArea.style.boxShadow = 'none';\n        textArea.style.background = 'transparent';\n        document.body.appendChild(textArea);\n        // Označíme text a zkopírujeme\n        textArea.focus();\n        textArea.select();\n        textArea.setSelectionRange(0, text.length); // Pro mobilní zařízení\n        const successful = document.execCommand('copy');\n        document.body.removeChild(textArea);\n        if (successful) {\n          this.toastr.success(successMessage, 'Úspěch');\n          return true;\n        } else {\n          throw new Error('execCommand returned false');\n        }\n      } catch (err) {\n        console.error('Fallback kopírování selhalo:', err);\n        this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n        return false;\n      }\n    }\n    /**\r\n     * Zkopíruje text z input elementu do schránky\r\n     * @param inputElement HTML input element\r\n     * @param successMessage Zpráva při úspěchu (volitelná)\r\n     * @param errorMessage Zpráva při chybě (volitelná)\r\n     * @returns Promise<boolean> - true při úspěchu, false při chybě\r\n     */\n    copyFromInput(inputElement, successMessage = 'Text byl zkopírován do schránky', errorMessage = 'Nepodařilo se zkopírovat text do schránky') {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const text = inputElement.value;\n        if (!text || text.trim() === '') {\n          _this2.toastr.error('Input element neobsahuje žádný text', 'Chyba');\n          return false;\n        }\n        try {\n          // Pokusíme se použít moderní Clipboard API\n          if (navigator.clipboard && window.isSecureContext) {\n            yield navigator.clipboard.writeText(text);\n            _this2.toastr.success(successMessage, 'Úspěch');\n            return true;\n          } else {\n            // Fallback s použitím input elementu\n            return _this2.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n          }\n        } catch (err) {\n          console.error('Clipboard API selhalo:', err);\n          // Pokus o fallback\n          return _this2.fallbackCopyFromInput(inputElement, successMessage, errorMessage);\n        }\n      })();\n    }\n    /**\r\n     * Fallback metoda pro kopírování z input elementu\r\n     * @param inputElement HTML input element\r\n     * @param successMessage Zpráva při úspěchu\r\n     * @param errorMessage Zpráva při chybě\r\n     * @returns boolean - true při úspěchu, false při chybě\r\n     */\n    fallbackCopyFromInput(inputElement, successMessage, errorMessage) {\n      try {\n        // Označíme text v input elementu\n        inputElement.focus();\n        inputElement.select();\n        inputElement.setSelectionRange(0, inputElement.value.length); // Pro mobilní zařízení\n        const successful = document.execCommand('copy');\n        if (successful) {\n          this.toastr.success(successMessage, 'Úspěch');\n          return true;\n        } else {\n          throw new Error('execCommand returned false');\n        }\n      } catch (err) {\n        console.error('Fallback kopírování z input selhalo:', err);\n        this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');\n        return false;\n      }\n    }\n    /**\r\n     * Kontrola, zda je Clipboard API dostupné\r\n     * @returns boolean - true pokud je Clipboard API dostupné\r\n     */\n    isClipboardApiAvailable() {\n      return !!(navigator.clipboard && window.isSecureContext);\n    }\n    static {\n      this.ɵfac = function ClipboardService_Factory(t) {\n        return new (t || ClipboardService)(i0.ɵɵinject(i1.ToastrService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ClipboardService,\n        factory: ClipboardService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ClipboardService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}