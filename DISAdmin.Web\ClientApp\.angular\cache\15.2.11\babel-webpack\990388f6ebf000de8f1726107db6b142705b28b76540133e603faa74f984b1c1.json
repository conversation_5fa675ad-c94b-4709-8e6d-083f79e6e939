{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/alert.service\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"../services/signalr.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../services/customer.service\";\nimport * as i6 from \"../services/instance.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"ngx-toastr\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"../shared/advanced-filter/advanced-filter.component\";\nimport * as i11 from \"../shared/pipes/local-date.pipe\";\nfunction AlertsComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.addRule());\n    });\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"span\", 15);\n    i0.ɵɵtext(3, \"P\\u0159idat pravidlo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 16);\n    i0.ɵɵtext(5, \"P\\u0159idat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AlertsComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction AlertsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"span\", 21);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AlertsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 alerty nebyly nalezeny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_14_tr_21_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_14_tr_21_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const alert_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.resolveAlert(alert_r11.id));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"severity-high\": a0,\n    \"severity-medium\": a1,\n    \"severity-low\": a2\n  };\n};\nfunction AlertsComponent_div_14_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\", 28);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtemplate(19, AlertsComponent_div_14_tr_21_button_19_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const alert_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 10, alert_r11.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(alert_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(13, _c0, alert_r11.severity === \"critical\", alert_r11.severity === \"warning\", alert_r11.severity === \"info\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getSeverityText(alert_r11.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.getStatusClass(alert_r11.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getStatusText(alert_r11.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.instanceName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.customerName || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(alert_r11.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", alert_r11.status === \"active\");\n  }\n}\nfunction AlertsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"table\", 24)(2, \"thead\", 25)(3, \"tr\", 26)(4, \"th\");\n    i0.ɵɵtext(5, \"Datum a \\u010Das\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Zpr\\u00E1va\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_14_tr_21_Template, 20, 17, \"tr\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.alerts);\n  }\n}\nfunction AlertsComponent_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E1 pravidla nebyla nalezena. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" Email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AlertsComponent_div_15_div_6_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 33);\n    i0.ɵɵelement(5, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 28);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 36)(17, \"input\", 37);\n    i0.ɵɵlistener(\"change\", function AlertsComponent_div_15_div_6_tr_21_Template_input_change_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.toggleRuleStatus(rule_r19));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"label\", 38);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_15_div_6_tr_21_span_21_Template, 3, 0, \"span\", 39);\n    i0.ɵɵtemplate(22, AlertsComponent_div_15_div_6_tr_21_span_22_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\")(24, \"div\", 40)(25, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_15_div_6_tr_21_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.editRule(rule_r19));\n    });\n    i0.ɵɵelement(26, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function AlertsComponent_div_15_div_6_tr_21_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const rule_r19 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.deleteRule(rule_r19.id));\n    });\n    i0.ɵɵelement(28, \"i\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const rule_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(rule_r19.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r18.getMetricTypeText(rule_r19.metricType));\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getMetricTypeIcon(rule_r19.metricType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getMetricTypeText(rule_r19.metricType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.getConditionText(rule_r19.condition));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getThresholdClass(rule_r19.metricType, rule_r19.threshold));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.formatThreshold(rule_r19.metricType, rule_r19.threshold), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(13, _c0, rule_r19.severity === \"critical\", rule_r19.severity === \"warning\", rule_r19.severity === \"info\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getSeverityText(rule_r19.severity), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", rule_r19.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(rule_r19.enabled ? \"Aktivn\\u00ED\" : \"Neaktivn\\u00ED\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", rule_r19.notifyByEmail);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rule_r19.notifyByEmail);\n  }\n}\nfunction AlertsComponent_div_15_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"table\", 24)(2, \"thead\", 25)(3, \"tr\", 26)(4, \"th\");\n    i0.ɵɵtext(5, \"N\\u00E1zev\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Metrika\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Podm\\u00EDnka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Threshold\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Notifikace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, AlertsComponent_div_15_div_6_tr_21_Template, 29, 17, \"tr\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.alertRules);\n  }\n}\nfunction AlertsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"h5\", 7);\n    i0.ɵɵtext(3, \"Pravidla pro alerty\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 8);\n    i0.ɵɵtemplate(5, AlertsComponent_div_15_div_5_Template, 2, 0, \"div\", 10);\n    i0.ɵɵtemplate(6, AlertsComponent_div_15_div_6_Template, 22, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.alertRules.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.alertRules.length > 0);\n  }\n}\nexport let AlertsComponent = /*#__PURE__*/(() => {\n  class AlertsComponent {\n    constructor(alertService, authService, signalRService, fb, customerService, instanceService, router, toastr) {\n      this.alertService = alertService;\n      this.authService = authService;\n      this.signalRService = signalRService;\n      this.fb = fb;\n      this.customerService = customerService;\n      this.instanceService = instanceService;\n      this.router = router;\n      this.toastr = toastr;\n      this.alerts = [];\n      this.alertRules = [];\n      this.loading = true;\n      this.error = null;\n      this.isAdmin = false;\n      // Vybraný záznam\n      this.selectedRule = null;\n      this.filterFields = [];\n      // Aktualizace dat\n      this.signalRSubscriptions = [];\n      this.updateSubscription = null;\n      // Data pro filtry\n      this.customers = [];\n      this.instances = [];\n      this.authService.currentUser.subscribe(user => {\n        this.currentUser = user;\n        this.isAdmin = user?.isAdmin || false;\n      });\n      this.alertRuleForm = this.fb.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        description: ['', Validators.maxLength(500)],\n        metricType: ['', Validators.required],\n        condition: ['', [Validators.required, Validators.maxLength(50)]],\n        threshold: ['', [Validators.required, Validators.min(0)]],\n        severity: ['warning', [Validators.required, Validators.maxLength(20)]],\n        enabled: [true],\n        notifyByEmail: [false],\n        emailRecipients: ['', Validators.maxLength(500)],\n        customerId: [''],\n        instanceId: ['']\n      });\n      // Výchozí hodnoty filtru podle požadavků - odpovídají obrázku\n      this.filterForm = this.fb.group({\n        severity: ['all'],\n        status: ['all'],\n        dateRange: ['7'],\n        customerId: [''],\n        instanceId: ['']\n      });\n    }\n    ngOnInit() {\n      // Inicializace polí pro advancedFilter\n      this.initFilterFields();\n      // Načtení zákazníků pro filtry\n      this.loadCustomers();\n      this.loadAlerts();\n      this.loadAlertRules();\n      // Inicializace SignalR připojení\n      this.initSignalR();\n      // Validace emailů při změně notifyByEmail\n      this.alertRuleForm.get('notifyByEmail')?.valueChanges.subscribe(value => {\n        const emailRecipientsControl = this.alertRuleForm.get('emailRecipients');\n        if (value) {\n          emailRecipientsControl?.setValidators([Validators.required, Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(,\\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})*$/), Validators.maxLength(500)]);\n        } else {\n          emailRecipientsControl?.setValidators([Validators.maxLength(500)]);\n        }\n        emailRecipientsControl?.updateValueAndValidity();\n      });\n      // Sledování změny zákazníka pro načtení instancí\n      this.filterForm.get('customerId')?.valueChanges.subscribe(customerId => {\n        console.log('Změna zákazníka v hlavním formuláři:', customerId);\n        if (customerId) {\n          // Převod na číslo, protože hodnota z formuláře může být řetězec\n          this.loadInstances(Number(customerId));\n        } else {\n          this.instances = [];\n          this.updateInstanceOptions();\n        }\n      });\n      // Sledování změny zákazníka ve formuláři pro pravidla\n      this.alertRuleForm.get('customerId')?.valueChanges.subscribe(customerId => {\n        if (customerId) {\n          this.instanceService.getInstancesByCustomerId(Number(customerId)).subscribe({\n            next: instances => {\n              // Aktualizace seznamu instancí\n              this.instances = instances;\n              // Reset výběru instance\n              const instanceControl = this.alertRuleForm.get('instanceId');\n              instanceControl?.setValue('');\n            },\n            error: err => {\n              console.error('Chyba při načítání instancí', err);\n            }\n          });\n        } else {\n          this.instances = [];\n        }\n      });\n    }\n    /**\r\n     * Načtení seznamu zákazníků\r\n     */\n    loadCustomers() {\n      this.customerService.getCustomers().subscribe({\n        next: data => {\n          console.log('Načtení zákazníků:', data);\n          this.customers = data;\n          this.updateCustomerOptions();\n          // Pokud máme uložené ID zákazníka, načteme jeho instance\n          const customerId = this.filterForm.get('customerId')?.value;\n          if (customerId) {\n            console.log('Načítání instancí pro uložené ID zákazníka:', customerId);\n            this.loadInstances(Number(customerId));\n          }\n        },\n        error: err => {\n          console.error('Chyba při načítání zákazníků', err);\n        }\n      });\n    }\n    /**\r\n     * Načtení seznamu instancí pro vybraného zákazníka\r\n     */\n    loadInstances(customerId) {\n      console.log('Načítání instancí pro zákazníka ID:', customerId);\n      this.instanceService.getInstancesByCustomerId(customerId).subscribe({\n        next: data => {\n          console.log('Načtené instance:', data);\n          this.instances = data;\n          this.updateInstanceOptions();\n        },\n        error: err => {\n          console.error('Chyba při načítání instancí', err);\n        }\n      });\n    }\n    /**\r\n     * Aktualizace možností pro filtr zákazníků\r\n     */\n    updateCustomerOptions() {\n      // Najdeme pole pro zákazníka\n      const customerFieldIndex = this.filterFields.findIndex(f => f.name === 'customerId');\n      if (customerFieldIndex !== -1) {\n        // Vytvoříme nové pole options\n        const options = [{\n          value: '',\n          label: 'Všichni zákazníci'\n        }];\n        // Přidáme zákazníky\n        this.customers.forEach(customer => {\n          options.push({\n            value: customer.id,\n            label: customer.name\n          });\n        });\n        // Aktualizujeme pole\n        this.filterFields[customerFieldIndex].options = options;\n        // Aktualizace seznamu zákazníků v modálním okně pro pravidla\n        const modalCustomerSelect = document.getElementById('customerId');\n        if (modalCustomerSelect) {\n          // Vyčištění stávajících možností kromě první (Všichni zákazníci)\n          while (modalCustomerSelect.options.length > 1) {\n            modalCustomerSelect.remove(1);\n          }\n          // Přidání nových možností\n          this.customers.forEach(customer => {\n            const option = document.createElement('option');\n            option.value = customer.id.toString();\n            option.text = customer.name;\n            modalCustomerSelect.add(option);\n          });\n        }\n      }\n    }\n    /**\r\n     * Aktualizace možností pro filtr instancí\r\n     */\n    updateInstanceOptions() {\n      // Najdeme pole pro instance\n      const instanceFieldIndex = this.filterFields.findIndex(f => f.name === 'instanceId');\n      if (instanceFieldIndex !== -1) {\n        // Vytvoříme nové pole options\n        const options = [{\n          value: '',\n          label: 'Všechny instance'\n        }];\n        // Přidáme instance\n        this.instances.sort((a, b) => {\n          // Nejprve seřadíme podle zkratky zákazníka\n          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);\n          // Pokud jsou zkratky stejné, seřadíme podle názvu instance\n          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);\n        }).forEach(instance => {\n          options.push({\n            value: instance.id,\n            label: `${instance.customerAbbreviation} - ${instance.name}`\n          });\n        });\n        // Aktualizujeme pole\n        this.filterFields[instanceFieldIndex].options = options;\n      }\n    }\n    /**\r\n     * Inicializace polí pro advancedFilter\r\n     */\n    initFilterFields() {\n      this.filterFields = [{\n        name: 'severity',\n        label: 'Závažnost',\n        type: 'select',\n        options: [{\n          value: 'all',\n          label: 'Všechny'\n        }, {\n          value: 'critical',\n          label: 'Kritické'\n        }, {\n          value: 'warning',\n          label: 'Varování'\n        }, {\n          value: 'info',\n          label: 'Informace'\n        }]\n      }, {\n        name: 'status',\n        label: 'Stav',\n        type: 'select',\n        options: [{\n          value: 'all',\n          label: 'Všechny'\n        }, {\n          value: 'active',\n          label: 'Aktivní'\n        }, {\n          value: 'resolved',\n          label: 'Vyřešené'\n        }, {\n          value: 'acknowledged',\n          label: 'Potvrzené'\n        }]\n      }, {\n        name: 'dateRange',\n        label: 'Časové období',\n        type: 'select',\n        options: [{\n          value: '1',\n          label: 'Posledních 1 den'\n        }, {\n          value: '7',\n          label: 'Posledních 7 dní'\n        }, {\n          value: '30',\n          label: 'Posledních 30 dní'\n        }, {\n          value: '90',\n          label: 'Posledních 90 dní'\n        }]\n      }, {\n        name: 'customerId',\n        label: 'Zákazník',\n        type: 'select',\n        options: [{\n          value: '',\n          label: 'Všichni zákazníci'\n        }\n        // Zde budou dynamicky načteni zákazníci\n        ]\n      }, {\n        name: 'instanceId',\n        label: 'Instance',\n        type: 'select',\n        options: [{\n          value: '',\n          label: 'Všechny instance'\n        }\n        // Zde budou dynamicky načteny instance\n        ]\n      }];\n    }\n\n    ngOnDestroy() {\n      // Zrušení SignalR subscription\n      this.signalRSubscriptions.forEach(sub => sub.unsubscribe());\n      this.signalRService.stopConnection();\n      // Zrušení subscription pro aktualizaci dat\n      if (this.updateSubscription) {\n        this.updateSubscription.unsubscribe();\n      }\n    }\n    /**\r\n     * Načtení alertů\r\n     */\n    loadAlerts() {\n      this.loading = true;\n      const filters = this.getFilters();\n      this.alertService.getAlerts(filters.severity, filters.status, parseInt(filters.dateRange), filters.customerId, filters.instanceId).subscribe({\n        next: data => {\n          this.alerts = data;\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání alertů', err);\n          this.error = 'Nepodařilo se načíst alerty';\n          this.loading = false;\n        }\n      });\n    }\n    /**\r\n     * Načtení pravidel pro alerty\r\n     */\n    loadAlertRules() {\n      this.alertService.getAlertRules().subscribe({\n        next: data => {\n          this.alertRules = data;\n        },\n        error: err => {\n          console.error('Chyba při načítání pravidel pro alerty', err);\n          this.error = 'Nepodařilo se načíst pravidla pro alerty';\n        }\n      });\n    }\n    /**\r\n     * Získání aktuálních filtrů\r\n     */\n    getFilters() {\n      const formValues = this.filterForm.value;\n      return {\n        severity: formValues.severity,\n        status: formValues.status,\n        dateRange: formValues.dateRange,\n        customerId: formValues.customerId || undefined,\n        instanceId: formValues.instanceId || undefined\n      };\n    }\n    /**\r\n     * Zpracování změny filtru z komponenty advancedFilter\r\n     */\n    onFilterChange(filters) {\n      // Nastavení výchozích hodnot, pokud nejsou definovány\n      const filterValues = {\n        severity: filters.severity || 'all',\n        status: filters.status || 'all',\n        dateRange: filters.dateRange || '7',\n        customerId: filters.customerId || '',\n        instanceId: filters.instanceId || ''\n      };\n      console.log('Změna filtru:', filterValues);\n      // Uložení aktuální hodnoty customerId\n      const oldCustomerId = this.filterForm.get('customerId')?.value;\n      // Aktualizace formuláře\n      this.filterForm.patchValue(filterValues, {\n        emitEvent: false\n      });\n      // Pokud se změnil zákazník, načteme instance\n      if (oldCustomerId !== filterValues.customerId && filterValues.customerId) {\n        console.log('Změna zákazníka v onFilterChange:', oldCustomerId, '->', filterValues.customerId);\n        this.loadInstances(Number(filterValues.customerId));\n      }\n      // Načtení dat s novými filtry\n      this.loadAlerts();\n    }\n    /**\r\n     * Přechod na stránku pro přidání nového pravidla\r\n     */\n    addRule() {\n      this.router.navigate(['/alerts/rules/add']);\n    }\n    /**\r\n     * Přechod na stránku pro úpravu pravidla\r\n     */\n    editRule(rule) {\n      this.router.navigate(['/alerts/rules', rule.id]);\n    }\n    /**\r\n     * Smazání pravidla\r\n     */\n    deleteRule(ruleId) {\n      if (confirm('Opravdu chcete smazat toto pravidlo?')) {\n        this.alertService.deleteAlertRule(ruleId).subscribe({\n          next: () => {\n            this.toastr.success('Pravidlo bylo úspěšně smazáno', 'Úspěch');\n            this.loadAlertRules();\n          },\n          error: err => {\n            console.error('Chyba při mazání pravidla', err);\n            this.error = 'Nepodařilo se smazat pravidlo';\n          }\n        });\n      }\n    }\n    /**\r\n     * Změna stavu pravidla (povoleno/zakázáno)\r\n     */\n    toggleRuleStatus(rule) {\n      const updatedRule = {\n        ...rule,\n        enabled: !rule.enabled\n      };\n      this.alertService.updateAlertRule(updatedRule).subscribe({\n        next: () => {\n          this.toastr.success(`Pravidlo bylo ${updatedRule.enabled ? 'aktivováno' : 'deaktivováno'}`, 'Úspěch');\n          this.loadAlertRules();\n        },\n        error: err => {\n          console.error('Chyba při změně stavu pravidla', err);\n          this.error = 'Nepodařilo se změnit stav pravidla';\n        }\n      });\n    }\n    /**\r\n     * Vyřešení alertu\r\n     */\n    resolveAlert(alertId) {\n      this.alertService.resolveAlert(alertId).subscribe({\n        next: () => {\n          this.toastr.success('Alert byl úspěšně vyřešen', 'Úspěch');\n          this.loadAlerts();\n        },\n        error: err => {\n          console.error('Chyba při řešení alertu', err);\n          this.error = 'Nepodařilo se vyřešit alert';\n        }\n      });\n    }\n    /**\r\n     * Inicializace SignalR připojení\r\n     */\n    initSignalR() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          console.log('Initializing SignalR connection...');\n          yield _this.signalRService.startConnection();\n          // Otestování připojení\n          const isConnected = yield _this.signalRService.testConnection();\n          if (!isConnected) {\n            console.warn('SignalR connection test failed. Falling back to interval-based updates.');\n            return;\n          }\n          console.log('SignalR connection test successful. Joining groups...');\n          // Připojení do skupin pro odebírání alertů\n          yield _this.signalRService.joinGroup('alerts');\n          // Registrace subscription pro real-time aktualizace\n          _this.signalRSubscriptions.push(_this.signalRService.alerts$.subscribe(data => {\n            if (Object.keys(data).length > 0) {\n              _this.loadAlerts();\n            }\n          }));\n          console.log('SignalR initialization completed successfully.');\n        } catch (error) {\n          console.error('Error during SignalR initialization:', error);\n          console.warn('Falling back to interval-based updates.');\n        }\n      })();\n    }\n    /**\r\n     * Získání CSS třídy pro závažnost alertu\r\n     * @deprecated Použijte místo toho přímo třídy severity-high, severity-medium, severity-low\r\n     */\n    getSeverityClass(severity) {\n      switch (severity) {\n        case 'critical':\n          return 'severity-high';\n        case 'warning':\n          return 'severity-medium';\n        case 'info':\n          return 'severity-low';\n        default:\n          return 'bg-secondary';\n      }\n    }\n    /**\r\n     * Získání textu pro závažnost alertu\r\n     */\n    getSeverityText(severity) {\n      switch (severity) {\n        case 'critical':\n          return 'Kritický';\n        case 'warning':\n          return 'Varování';\n        case 'info':\n          return 'Informace';\n        default:\n          return severity;\n      }\n    }\n    /**\r\n     * Získání CSS třídy pro stav alertu\r\n     */\n    getStatusClass(status) {\n      switch (status) {\n        case 'active':\n          return 'bg-danger';\n        case 'resolved':\n          return 'bg-success';\n        case 'acknowledged':\n          return 'bg-warning text-dark';\n        default:\n          return 'bg-secondary';\n      }\n    }\n    /**\r\n     * Získání textu pro stav alertu\r\n     */\n    getStatusText(status) {\n      switch (status) {\n        case 'active':\n          return 'Aktivní';\n        case 'resolved':\n          return 'Vyřešený';\n        case 'acknowledged':\n          return 'Potvrzený';\n        default:\n          return status;\n      }\n    }\n    /**\r\n     * Získání textu pro typ metriky\r\n     */\n    getMetricTypeText(metricType) {\n      switch (metricType) {\n        // Existující metriky\n        case 'apiResponseTime':\n          return 'Doba odezvy API';\n        case 'apiCallsCount':\n          return 'Počet API volání';\n        case 'errorRate':\n          return 'Míra chyb';\n        case 'certificateExpiration':\n          return 'Expirace certifikátu';\n        case 'failedConnectionAttempts':\n          return 'Neúspěšné pokusy o připojení';\n        case 'suspiciousActivities':\n          return 'Podezřelé aktivity';\n        case 'apiAccessNonWorkHours':\n          return 'Přístupy k API mimo pracovní dobu';\n        case 'unauthorizedIpAccess':\n          return 'Přístupy z nepovolených IP adres';\n        case 'apiAccessHighCount':\n          return 'Neobvykle vysoký počet přístupů k API';\n        case 'apiAccessLowCount':\n          return 'Neobvykle nízký počet přístupů k API';\n        case 'failedLogins':\n          return 'Neúspěšné pokusy o přihlášení';\n        case 'securityEvents':\n          return 'Bezpečnostní události';\n        // Metriky pro výkonnostní anomálie - absolutní hodnoty\n        case 'methodResponseTime95Percentile':\n          return '95. percentil doby odezvy metody';\n        case 'methodResponseTimeMax':\n          return 'Maximální doba odezvy metody';\n        case 'methodResponseTimeStdDev':\n          return 'Variabilita doby odezvy metody';\n        case 'methodCallCount':\n          return 'Počet volání metody';\n        // Metriky pro výkonnostní anomálie - relativní změny\n        case 'methodResponseTimeChange':\n          return 'Změna doby odezvy metody';\n        case 'methodCallCountChange':\n          return 'Změna počtu volání metody';\n        // Metriky pro výkonnostní anomálie - trendy\n        case 'methodResponseTimeTrend':\n          return 'Trend doby odezvy metody';\n        case 'methodCallCountTrend':\n          return 'Trend počtu volání metody';\n        // Metriky pro výkonnostní anomálie - korelace\n        case 'methodResponseTimeCallCountCorrelation':\n          return 'Korelace odezvy a počtu volání';\n        case 'methodResponseTimeMedianRatio':\n          return 'Poměr průměr/medián doby odezvy';\n        default:\n          return metricType;\n      }\n    }\n    /**\r\n     * Získání ikony pro typ metriky\r\n     */\n    getMetricTypeIcon(metricType) {\n      switch (metricType) {\n        // Existující metriky\n        case 'apiResponseTime':\n          return 'bi-speedometer2';\n        case 'apiCallsCount':\n          return 'bi-graph-up';\n        case 'errorRate':\n          return 'bi-exclamation-triangle';\n        case 'certificateExpiration':\n          return 'bi-shield-lock';\n        case 'failedConnectionAttempts':\n          return 'bi-x-circle';\n        case 'suspiciousActivities':\n          return 'bi-eye';\n        case 'apiAccessNonWorkHours':\n          return 'bi-clock';\n        case 'unauthorizedIpAccess':\n          return 'bi-shield-x';\n        case 'apiAccessHighCount':\n          return 'bi-arrow-up-circle';\n        case 'apiAccessLowCount':\n          return 'bi-arrow-down-circle';\n        case 'failedLogins':\n          return 'bi-person-x';\n        case 'securityEvents':\n          return 'bi-shield-exclamation';\n        // Metriky pro výkonnostní anomálie - absolutní hodnoty\n        case 'methodResponseTime95Percentile':\n          return 'bi-stopwatch';\n        case 'methodResponseTimeMax':\n          return 'bi-alarm';\n        case 'methodResponseTimeStdDev':\n          return 'bi-distribute-vertical';\n        case 'methodCallCount':\n          return 'bi-hash';\n        // Metriky pro výkonnostní anomálie - relativní změny\n        case 'methodResponseTimeChange':\n          return 'bi-arrow-left-right';\n        case 'methodCallCountChange':\n          return 'bi-arrow-repeat';\n        // Metriky pro výkonnostní anomálie - trendy\n        case 'methodResponseTimeTrend':\n          return 'bi-graph-up-arrow';\n        case 'methodCallCountTrend':\n          return 'bi-bar-chart-line';\n        // Metriky pro výkonnostní anomálie - korelace\n        case 'methodResponseTimeCallCountCorrelation':\n          return 'bi-link';\n        case 'methodResponseTimeMedianRatio':\n          return 'bi-percent';\n        default:\n          return 'bi-question-circle';\n      }\n    }\n    /**\r\n     * Formátování hodnoty threshold podle typu metriky\r\n     */\n    formatThreshold(metricType, threshold) {\n      switch (metricType) {\n        // Metriky s jednotkou ms\n        case 'apiResponseTime':\n        case 'methodResponseTime95Percentile':\n        case 'methodResponseTimeMax':\n        case 'methodResponseTimeStdDev':\n          return `${threshold} ms`;\n        // Metriky s jednotkou %\n        case 'errorRate':\n        case 'methodResponseTimeChange':\n        case 'methodCallCountChange':\n        case 'methodResponseTimeTrend':\n        case 'methodCallCountTrend':\n        case 'methodResponseTimeCallCountCorrelation':\n        case 'apiAccessNonWorkHours':\n          return `${threshold} %`;\n        // Metriky s jednotkou dnů\n        case 'certificateExpiration':\n          return `${threshold} dnů`;\n        // Metriky bez jednotky\n        case 'apiCallsCount':\n        case 'methodCallCount':\n        case 'methodResponseTimeMedianRatio':\n        case 'failedConnectionAttempts':\n        case 'suspiciousActivities':\n        case 'apiAccessHighCount':\n        case 'apiAccessLowCount':\n        case 'failedLogins':\n        case 'unauthorizedIpAccess':\n          return threshold.toString();\n        default:\n          return threshold.toString();\n      }\n    }\n    /**\r\n     * Získání CSS třídy pro hodnotu threshold podle typu metriky\r\n     */\n    getThresholdClass(metricType, threshold) {\n      let cssClass = 'threshold-value';\n      switch (metricType) {\n        case 'apiResponseTime':\n          if (threshold > 1000) cssClass += ' threshold-critical';else if (threshold > 500) cssClass += ' threshold-warning';else if (threshold > 200) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n          break;\n        case 'errorRate':\n          if (threshold > 10) cssClass += ' threshold-critical';else if (threshold > 5) cssClass += ' threshold-warning';else if (threshold > 1) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n          break;\n        case 'certificateExpiration':\n          if (threshold < 7) cssClass += ' threshold-critical';else if (threshold < 14) cssClass += ' threshold-warning';else if (threshold < 30) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n          break;\n        case 'apiCallsCount':\n          if (threshold > 10000) cssClass += ' threshold-critical';else if (threshold > 5000) cssClass += ' threshold-warning';else if (threshold > 1000) cssClass += ' threshold-info';else cssClass += ' threshold-normal';\n          break;\n        default:\n          cssClass += ' threshold-normal';\n      }\n      return cssClass;\n    }\n    /**\r\n     * Získání textu pro podmínku\r\n     */\n    getConditionText(condition) {\n      switch (condition) {\n        case 'greaterThan':\n          return 'Větší než';\n        case 'lessThan':\n          return 'Menší než';\n        case 'equals':\n          return 'Rovno';\n        case 'notEquals':\n          return 'Nerovno';\n        default:\n          return condition;\n      }\n    }\n    static {\n      this.ɵfac = function AlertsComponent_Factory(t) {\n        return new (t || AlertsComponent)(i0.ɵɵdirectiveInject(i1.AlertService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.SignalRService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.CustomerService), i0.ɵɵdirectiveInject(i6.InstanceService), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.ToastrService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AlertsComponent,\n        selectors: [[\"app-alerts\"]],\n        decls: 16,\n        vars: 8,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"entityType\", \"fields\", \"filterChange\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"card-body\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"d-none\", \"d-md-inline\"], [1, \"d-inline\", \"d-md-none\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"d-flex\", \"justify-content-center\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"dark-header\", \"table-header-override\"], [1, \"dark-header-row\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"severity-badge\", 3, \"ngClass\"], [1, \"badge\", 2, \"border-radius\", \"0.375rem\", \"padding\", \"0.5em 0.75em\", 3, \"ngClass\"], [\"class\", \"btn btn-sm btn-success\", \"title\", \"Ozna\\u010Dit jako vy\\u0159e\\u0161en\\u00E9\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Ozna\\u010Dit jako vy\\u0159e\\u0161en\\u00E9\", 1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"click\"], [1, \"bi\", \"bi-check-circle\"], [1, \"metric-type-icon\"], [1, \"bi\", 3, \"ngClass\", \"title\"], [3, \"ngClass\"], [1, \"form-check\", \"form-switch\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"form-check-label\"], [4, \"ngIf\"], [1, \"btn-group\"], [\"title\", \"Upravit\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-pencil-fill\"], [\"title\", \"Smazat\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash-fill\"], [1, \"bi\", \"bi-envelope-fill\", \"text-primary\"]],\n        template: function AlertsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Alerty a upozorn\\u011Bn\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, AlertsComponent_button_4_Template, 6, 0, \"button\", 2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, AlertsComponent_div_5_Template, 3, 1, \"div\", 3);\n            i0.ɵɵelementStart(6, \"app-advanced-filter\", 4);\n            i0.ɵɵlistener(\"filterChange\", function AlertsComponent_Template_app_advanced_filter_filterChange_6_listener($event) {\n              return ctx.onFilterChange($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"h5\", 7);\n            i0.ɵɵtext(10, \"Alerty\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵtemplate(12, AlertsComponent_div_12_Template, 4, 0, \"div\", 9);\n            i0.ɵɵtemplate(13, AlertsComponent_div_13_Template, 2, 0, \"div\", 10);\n            i0.ɵɵtemplate(14, AlertsComponent_div_14_Template, 22, 1, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(15, AlertsComponent_div_15_Template, 7, 2, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"entityType\", \"alerts\")(\"fields\", ctx.filterFields);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.alerts.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.alerts.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n          }\n        },\n        dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i10.AdvancedFilterComponent, i11.LocalDatePipe],\n        styles: [\".card[_ngcontent-%COMP%]{box-shadow:0 .125rem .25rem #00000013;border-radius:.5rem;border:1px solid rgba(0,0,0,.125)}.card-header[_ngcontent-%COMP%]{background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125)}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}.required-field[_ngcontent-%COMP%]:after{content:\\\"*\\\";color:red;margin-left:4px}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}.modal-backdrop[_ngcontent-%COMP%]{opacity:.5}@media (max-width: 768px){.card-title[_ngcontent-%COMP%]{font-size:1rem}}\"]\n      });\n    }\n  }\n  return AlertsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}